package com.haolinkyou.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haolinkyou.entity.CategoryTemplates;
import com.haolinkyou.entity.TemplateFields;

import java.util.List;
import java.util.Map;

/**
 * 分类模板服务接口
 */
public interface ICategoryTemplateService extends IService<CategoryTemplates> {

    /**
     * 根据分类ID获取模板列表
     * @param categoryId 分类ID
     * @return 模板列表
     */
    List<CategoryTemplates> getTemplatesByCategoryId(Long categoryId);

    /**
     * 获取分类的默认模板
     * @param categoryId 分类ID
     * @return 默认模板
     */
    CategoryTemplates getDefaultTemplate(Long categoryId);

    /**
     * 获取模板的完整配置（包含字段）
     * @param templateId 模板ID
     * @return 模板配置
     */
    TemplateConfig getTemplateConfig(Long templateId);

    /**
     * 保存模板配置
     * @param templateConfig 模板配置
     * @return 是否成功
     */
    boolean saveTemplateConfig(TemplateConfig templateConfig);

    /**
     * 验证模板数据
     * @param templateId 模板ID
     * @param data 提交的数据
     * @return 验证结果
     */
    ValidationResult validateTemplateData(Long templateId, Map<String, Object> data);

    /**
     * 创建默认模板
     * @param categoryId 分类ID
     * @return 是否成功
     */
    boolean createDefaultTemplate(Long categoryId);

    /**
     * 模板配置类
     */
    class TemplateConfig {
        private CategoryTemplates template;
        private List<TemplateFields> fields;

        // Getters and Setters
        public CategoryTemplates getTemplate() { return template; }
        public void setTemplate(CategoryTemplates template) { this.template = template; }

        public List<TemplateFields> getFields() { return fields; }
        public void setFields(List<TemplateFields> fields) { this.fields = fields; }
    }

    /**
     * 验证结果类
     */
    class ValidationResult {
        private boolean valid;
        private List<String> errors;
        private Map<String, Object> processedData;

        public ValidationResult(boolean valid) {
            this.valid = valid;
        }

        public ValidationResult(boolean valid, List<String> errors) {
            this.valid = valid;
            this.errors = errors;
        }

        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }

        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }

        public Map<String, Object> getProcessedData() { return processedData; }
        public void setProcessedData(Map<String, Object> processedData) { this.processedData = processedData; }
    }
}
