package com.haolinkyou.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.haolinkyou.entity.UserCollects;
import com.haolinkyou.vo.UserCollectVo;

import java.util.Map;

/**
 * 用户收藏服务接口
 */
public interface IUserCollectsService extends IService<UserCollects> {
    
    /**
     * 收藏或取消收藏
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 操作结果：true-收藏成功，false-取消收藏成功
     */
    boolean toggleCollect(Long postId, Long userId);
    
    /**
     * 检查用户是否已收藏该帖子
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return true-已收藏，false-未收藏
     */
    boolean isUserCollected(Long postId, Long userId);
    
    /**
     * 获取帖子的收藏数
     * @param postId 帖子ID
     * @return 收藏数
     */
    int getPostCollectCount(Long postId);

    /**
     * 获取用户收藏列表
     * @param userId 用户ID
     * @param page 页码
     * @param pageSize 每页大小
     * @param categoryId 分类ID（可选）
     * @param sortBy 排序方式
     * @return 收藏列表分页数据
     */
    IPage<UserCollectVo> getUserCollections(Long userId, Integer page, Integer pageSize, String categoryId, String sortBy);

    /**
     * 获取用户收藏统计数据
     * @param userId 用户ID
     * @return 统计数据（今日收藏数、本月收藏数等）
     */
    Map<String, Object> getUserCollectStats(Long userId);
}
