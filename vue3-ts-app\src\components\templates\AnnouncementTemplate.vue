<template>
  <view class="announcement-template">
    <!-- 基础模板 -->
    <BaseTemplate
      ref="baseTemplateRef"
      :titleRequired="true"
      titlePlaceholder="请输入公告标题"
      contentPlaceholder="请输入公告详细内容"
      :showMediaUpload="true"
      :mediaRequired="false"
      mediaLabel="相关图片"
      mediaHelpText="图片：最多6张，单张不超过10MB；视频：最多1个，不超过100MB"
      :maxImageCount="6"
      :showMediaTypeSwitch="true"
      :defaultMediaType="0"
      :showContact="true"
      :contactRequired="false"
      contactPlaceholder="如有疑问请联系（可选）"
      contactType="any"
      :initialData="formData"
      @update:data="handleBaseDataChange"
      @update:valid="handleBaseValidChange"
    />

    <!-- 公告特有字段 -->
    <view class="announcement-fields">
      <!-- 重要程度 -->
      <view class="form-item">
        <view class="form-label">
          <text>重要程度</text>
          <text class="required">*</text>
        </view>
        <up-radio-group v-model="formData.priority" @change="handlePriorityChange">
          <up-radio
            v-for="option in priorityOptions"
            :key="option.value"
            :name="option.value"
            :label="option.label"
            :customStyle="{ marginBottom: '8px' }"
          />
        </up-radio-group>
        <view v-if="errors.priority" class="error-text">{{ errors.priority }}</view>
      </view>

      <!-- 有效期 -->
      <view class="form-item">
        <view class="form-label">
          <text>有效期</text>
        </view>
        <up-datetime-picker
          hasInput
          v-model:show="showDatePicker"
          v-model="dateValue"
          mode="date"
          :minDate="minDate"
          placeholder="选择有效期（可选）"
          format="YYYY-MM-DD"
          @confirm="handleDateConfirm"
          @cancel="() => showDatePicker = false"

        />
        <view class="help-text">不填则长期有效</view>
      </view>

      <!-- 仅对谁可见 -->
      <view class="form-item">
        <view class="form-label">
          <text>仅对谁可见</text>
        </view>
        <up-checkbox-group v-model="formData.targetAudience" @change="handleTargetChange">
          <up-checkbox
            v-for="option in targetOptions"
            :key="option.value"
            :name="option.value"
            :label="option.label"
            :customStyle="{ marginBottom: '8px' }"
          />
        </up-checkbox-group>
        <view v-if="errors.targetAudience" class="error-text">{{ errors.targetAudience }}</view>
        <view class="help-text">不选择则所有用户可见，选择特定对象则仅对其可见</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BaseTemplate from './BaseTemplate.vue'
import { getTargetAudienceOptionsAPI, type TargetAudienceOption } from '@/services/roleService'

interface Emits {
  (e: 'update:data', data: Record<string, any>): void
  (e: 'update:valid', valid: boolean): void
}

const emit = defineEmits<Emits>()

// 基础模板引用
const baseTemplateRef = ref()

// 表单数据
const formData = ref({
  title: '',
  content: '',
  images: [] as any[], // 兼容旧版本
  fileList: [] as any[], // 新版本文件列表
  contact: '',
  priority: 'normal',
  validDate: '',
  targetAudience: [] as string[] // 默认不选择（即所有用户可见）
})

// 错误信息
const errors = ref<Record<string, string>>({})

// 基础模板是否有效
const baseValid = ref(false)

// 日期选择器
const showDatePicker = ref(false)
const dateValue = ref<number>(Date.now())
const minDate = Date.now()

// 确保日期值始终是有效的时间戳
const ensureValidTimestamp = (value: any): number => {
  if (typeof value === 'number' && !isNaN(value) && value > 0) {
    return value
  }
  if (typeof value === 'string' && value) {
    const timestamp = new Date(value).getTime()
    if (!isNaN(timestamp)) {
      return timestamp
    }
  }
  return Date.now()
}

// 重要程度选项
const priorityOptions = [
  { label: '普通', value: 'normal' },
  { label: '重要', value: 'important' },
  { label: '紧急', value: 'urgent' }
]

// 目标对象选项（从接口动态获取）
const targetOptions = ref<TargetAudienceOption[]>([])

// 加载目标对象选项
const loadTargetOptions = async () => {
  try {
    const options = await getTargetAudienceOptionsAPI()
    targetOptions.value = options
  } catch (error) {
    console.error('加载目标对象选项失败:', error)
  }
}

// 验证表单是否有效
const isValid = computed(() => {
  return baseValid.value &&
         formData.value.priority !== '' &&
         Object.keys(errors.value).length === 0
})

// 监听数据变化
watch(formData, (newData) => {
  emit('update:data', newData)
}, { deep: true })

// 监听验证状态变化
watch(isValid, (valid) => {
  emit('update:valid', valid)
})

// 监听时间值变化，确保始终是有效的时间戳
watch(dateValue, (newValue) => {
  if (typeof newValue !== 'number' || isNaN(newValue) || newValue <= 0) {
    dateValue.value = Date.now()
  }
})

// 组件挂载时加载目标对象选项
onMounted(() => {
  loadTargetOptions()

  // 确保时间值是有效的
  dateValue.value = ensureValidTimestamp(dateValue.value)
})

// 处理基础模板数据变化
const handleBaseDataChange = (data: Record<string, any>) => {
  Object.assign(formData.value, data)
}

// 处理基础模板验证状态变化
const handleBaseValidChange = (valid: boolean) => {
  baseValid.value = valid
}

// 处理重要程度变化
const handlePriorityChange = () => {
  if (formData.value.priority) {
    delete errors.value.priority
  }
}

// 处理目标对象变化
const handleTargetChange = (selectedValues: string[]) => {
  formData.value.targetAudience = selectedValues

  // 目标对象现在是非必填的，所以不需要验证
  delete errors.value.targetAudience
}

// 处理日期确认
const handleDateConfirm = (event: any) => {
  try {
    const timestamp = ensureValidTimestamp(event?.value)
    dateValue.value = timestamp

    // 格式化显示日期
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const dateStr = `${year}-${month}-${day}`

    formData.value.validDate = dateStr
    showDatePicker.value = false
  } catch (error) {
    console.error('日期确认处理失败:', error)
    showDatePicker.value = false
  }
}

// 验证公告特有字段
const validateAnnouncementFields = () => {
  // 验证重要程度
  if (!formData.value.priority) {
    errors.value.priority = '请选择重要程度'
  } else {
    delete errors.value.priority
  }

  // 目标对象现在是非必填的，不需要验证
}

// 暴露验证方法
const validate = () => {
  const baseValidResult = baseTemplateRef.value?.validate() || false
  validateAnnouncementFields()
  
  return baseValidResult && isValid.value
}

// 重置表单数据
const resetForm = () => {
  formData.value = {
    title: '',
    content: '',
    images: [],
    fileList: [],
    contact: '',
    priority: 'normal',
    validDate: '',
    targetAudience: []
  }

  // 清空错误信息
  errors.value = {}

  // 重置基础模板
  if (baseTemplateRef.value && baseTemplateRef.value.resetForm) {
    baseTemplateRef.value.resetForm()
  }
}

// 暴露给父组件
defineExpose({
  validate,
  formData,
  isValid,
  resetForm
})
</script>

<style scoped lang="scss">
.announcement-template {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.announcement-fields {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff3b30;
}

.error-text {
  font-size: 12px;
  color: #ff3b30;
}

.help-text {
  font-size: 12px;
  color: #999;
}
</style>
