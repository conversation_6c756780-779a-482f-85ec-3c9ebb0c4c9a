/** 通用的用户信息 */
type BaseProfile = {
  /** 用户ID */
  id: number | null;
  /** 头像  */
  avatar: string;
  /** 账户名  */
  account: string;
  /** 昵称 */
  nickname?: string;
  /** 手机号 */
  mobile?: string;
  /** 性别 */
  gender?: number;
  /** 生日 */
  birthday?: string;
  /** 个人简介 */
  bio?: string;
  /** 认证类型 */
  authType: number | string | null;
  /** 是否管理员 */
  isAdmin?: boolean | null;
  /** 用户角色 */
  userRole?: string | null;
};

/** 小程序登录 登录用户信息 */
export type LoginResult = BaseProfile & {
  /** 手机号 */
  mobile: string;
  /** 登录凭证 */
  token: string;
  /** 刷新凭证 */
  code: string;
  /** 用户发布的动态数量 */
  postCount?: number;
  /** 用户收藏的帖子数量 */
  favoriteCount?: number;
  /** 用户积分 */
  pointCount?: number;

  msg?: string;

  result: any;
};

/** 个人信息 用户详情信息 */
export type ProfileDetail = BaseProfile & {
  /** 性别 */
  gender?: Gender;
  /** 生日 */
  birthday?: string;
  /** 省市区 */
  fullLocation?: string;
  /** 职业 */
  profession?: string;
};
/** 性别 */
export type Gender = "女" | "男";

/** 个人信息 修改请求体参数 */
export type ProfileParams = Pick<
  ProfileDetail,
  "nickname" | "gender" | "birthday" | "profession"
> & {
  /** 省份编码 */
  provinceCode?: string;
  /** 城市编码 */
  cityCode?: string;
  /** 区/县编码 */
  countyCode?: string;
};
