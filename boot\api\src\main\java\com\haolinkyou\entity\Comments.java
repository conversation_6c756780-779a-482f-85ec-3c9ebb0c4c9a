package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName("comments")
@EqualsAndHashCode(callSuper = true)
public class Comments extends BaseEntity{
    private Long postId;
    private Long userId;

    @TableField("parent_id")
    private Long parentCommentId;

    @TableField("reply_to_user_id")
    private Long replyToUserId;

    private String content;
    private Integer likeCount;
    private Integer replyCount;
    private Integer status;
}
