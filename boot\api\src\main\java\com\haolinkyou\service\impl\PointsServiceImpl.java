package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.entity.UserPoints;
import com.haolinkyou.entity.Users;
import com.haolinkyou.mapper.UserPointsMapper;
import com.haolinkyou.service.IPointsService;
import com.haolinkyou.service.ISystemConfigService;
import com.haolinkyou.service.IUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 积分服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Service
public class PointsServiceImpl extends ServiceImpl<UserPointsMapper, UserPoints> implements IPointsService {

    private static final Logger log = LoggerFactory.getLogger(PointsServiceImpl.class);

    @Autowired
    private IUserService userService;

    @Autowired
    private ISystemConfigService systemConfigService;

    // 简单的内存缓存，用于防刷机制
    private final ConcurrentHashMap<String, Long> antiSpamCache = new ConcurrentHashMap<>();

    @Override
    public Integer getUserPointsBalance(Long userId) {
        Users user = userService.getById(userId);
        return user != null ? user.getPoints() : 0;
    }

    @Override
    @Transactional
    public boolean addPoints(Long userId, Integer points, String type, String description) {
        return addPoints(userId, points, type, description, null);
    }

    @Override
    @Transactional
    public boolean addPoints(Long userId, Integer points, String type, String description, Long relatedId) {
        if (userId == null || points == null || points <= 0) {
            return false;
        }

        try {
            // 检查每日积分限额
            if (!checkDailyLimit(userId, points)) {
                // 如果超过限额，获取剩余可得积分
                Integer remainingPoints = getRemainingDailyPoints(userId);
                if (remainingPoints <= 0) {
                    return false;
                }
                points = remainingPoints;
            }

            // 防刷检查
            if (!checkAntiSpam(userId, type)) {
                return false;
            }

            // 更新用户积分余额
            Users user = userService.getById(userId);
            if (user == null) {
                return false;
            }

            user.setPoints((user.getPoints() != null ? user.getPoints() : 0) + points);
            userService.updateById(user);

            // 记录积分变动
            UserPoints pointsRecord = new UserPoints();
            pointsRecord.setUserId(userId);
            pointsRecord.setPoints(points);
            pointsRecord.setType(type);
            pointsRecord.setDescription(description);
            pointsRecord.setRelatedId(relatedId);
            // createdTime 由 BaseEntity 的自动填充处理，不需要手动设置

            return save(pointsRecord);
        } catch (Exception e) {
            log.error("增加用户积分失败: userId=" + userId + ", points=" + points + ", type=" + type + ", error=" + e.getMessage());
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deductPoints(Long userId, Integer points, String type, String description) {
        return deductPoints(userId, points, type, description, null);
    }

    @Override
    @Transactional
    public boolean deductPoints(Long userId, Integer points, String type, String description, Long relatedId) {
        if (userId == null || points == null || points <= 0) {
            return false;
        }

        try {
            // 检查用户积分余额
            Users user = userService.getById(userId);
            if (user == null || user.getPoints() == null || user.getPoints() < points) {
                return false;
            }

            // 更新用户积分余额
            user.setPoints(user.getPoints() - points);
            userService.updateById(user);

            // 记录积分变动（负数表示扣除）
            UserPoints pointsRecord = new UserPoints();
            pointsRecord.setUserId(userId);
            pointsRecord.setPoints(-points);
            pointsRecord.setType(type);
            pointsRecord.setDescription(description);
            pointsRecord.setRelatedId(relatedId);
            // createdTime 由 BaseEntity 的自动填充处理，不需要手动设置

            return save(pointsRecord);
        } catch (Exception e) {
            log.error("扣除用户积分失败: userId=" + userId + ", points=" + points + ", type=" + type + ", error=" + e.getMessage());
            return false;
        }
    }

    @Override
    public boolean checkDailyLimit(Long userId, Integer points) {
        Integer todayTotal = getTodayPointsTotal(userId);
        Integer dailyLimit = systemConfigService.getIntValue("points_daily_limit", 100);
        return (todayTotal + points) <= dailyLimit;
    }

    @Override
    public Integer getTodayPointsTotal(Long userId) {
        LocalDate today = LocalDate.now();
        QueryWrapper<UserPoints> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .ge("created_time", today.atStartOfDay())
                   .lt("created_time", today.plusDays(1).atStartOfDay())
                   .gt("points", 0); // 只统计正积分（获得的积分）

        return list(queryWrapper).stream()
                .mapToInt(UserPoints::getPoints)
                .sum();
    }

    @Override
    public Integer getRemainingDailyPoints(Long userId) {
        Integer todayTotal = getTodayPointsTotal(userId);
        Integer dailyLimit = systemConfigService.getIntValue("points_daily_limit", 100);
        return Math.max(0, dailyLimit - todayTotal);
    }

    @Override
    public boolean checkAntiSpam(Long userId, String action) {
        String key = "points_spam:" + userId + ":" + action;
        Long currentTime = System.currentTimeMillis();
        
        // 检查缓存中是否存在该操作记录
        Long lastTime = antiSpamCache.get(key);
        if (lastTime != null && (currentTime - lastTime) < TimeUnit.MINUTES.toMillis(1)) {
            return false; // 1分钟内不能重复操作
        }
        
        // 更新缓存
        antiSpamCache.put(key, currentTime);
        
        // 清理过期缓存（简单实现）
        if (antiSpamCache.size() > 10000) {
            antiSpamCache.entrySet().removeIf(entry -> 
                (currentTime - entry.getValue()) > TimeUnit.HOURS.toMillis(1));
        }
        
        return true;
    }

    @Override
    public Page<UserPoints> getUserPointsRecords(Long userId, Integer page, Integer size) {
        return getUserPointsRecords(userId, page, size, null);
    }

    @Override
    public Page<UserPoints> getUserPointsRecords(Long userId, Integer page, Integer size, String type) {
        Page<UserPoints> pageInfo = new Page<>(page, size);
        QueryWrapper<UserPoints> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);

        // 如果指定了类型，添加类型筛选
        if (type != null && !type.trim().isEmpty()) {
            queryWrapper.eq("type", type);
        }

        queryWrapper.orderByDesc("created_time");

        return page(pageInfo, queryWrapper);
    }

    @Override
    public PointsRules getPointsRules() {
        PointsRules rules = new PointsRules();
        rules.setDailyLimit(systemConfigService.getIntValue("points_daily_limit", 100));
        rules.setSignDaily(systemConfigService.getIntValue("points_sign_daily", 5));
        rules.setPostCreate(systemConfigService.getIntValue("points_post_create", 10));
        rules.setCommentCreate(systemConfigService.getIntValue("points_comment_create", 2));
        rules.setLikeGive(systemConfigService.getIntValue("points_like_give", 1));
        rules.setCollectGive(systemConfigService.getIntValue("points_collect_give", 1));
        rules.setContinuousSignBonus(systemConfigService.getIntValue("points_continuous_sign_bonus", 2));
        rules.setPostTopCost(systemConfigService.getIntValue("points_post_top_cost", 50));
        rules.setPostTopDuration(systemConfigService.getIntValue("points_post_top_duration", 24));
        return rules;
    }
}