-- =============================================
-- 模板系统初始化数据
-- 为不同分类创建专用模板和字段配置
-- =============================================

-- 清空现有模板数据
TRUNCATE TABLE `template_fields`;
TRUNCATE TABLE `category_templates`;

-- =============================================
-- 1. 公告分类模板 (假设公告分类ID为1)
-- =============================================

-- 创建公告模板
INSERT INTO `category_templates` (`category_id`, `template_name`, `description`, `is_default`, `status`, `sort_order`) VALUES
(1, '公告模板', '用于发布社区公告的专用模板', 1, 1, 0),
(1, '紧急公告模板', '用于发布紧急公告的模板', 0, 1, 1);

-- 获取公告模板ID (这里假设为1和2，实际使用时需要查询)
-- 公告模板字段配置
INSERT INTO `template_fields` (`template_id`, `field_name`, `field_label`, `field_type`, `field_config`, `required`, `sort_order`, `status`, `placeholder`, `help_text`) VALUES
-- 基础字段
(1, 'title', '公告标题', 'text', '{"maxLength": 100}', 1, 1, 1, '请输入公告标题', '标题应简洁明了，不超过100个字符'),
(1, 'content', '公告内容', 'textarea', '{"maxLength": 2000}', 1, 2, 1, '请输入公告详细内容', '详细描述公告事项，不超过2000个字符'),
(1, 'images', '相关图片', 'image', '{"maxCount": 6, "accept": "image/*"}', 0, 3, 1, '选择相关图片', '最多可上传6张图片'),

-- 公告特殊字段
(1, 'priority', '重要程度', 'select', '{"options": [{"label": "普通", "value": "normal"}, {"label": "重要", "value": "important"}, {"label": "紧急", "value": "urgent"}]}', 1, 4, 1, '选择重要程度', '根据公告的重要性选择相应等级'),
(1, 'validDate', '有效期', 'date', '{}', 0, 5, 1, '选择有效期', '公告的有效期限，不填则长期有效'),
(1, 'targetAudience', '目标对象', 'checkbox', '{"options": [{"label": "全体业主", "value": "owners"}, {"label": "租户", "value": "tenants"}, {"label": "物业人员", "value": "property"}, {"label": "业委会成员", "value": "committee"}]}', 1, 6, 1, '选择目标对象', '选择此公告的目标受众'),
(1, 'contactInfo', '联系方式', 'text', '{"maxLength": 100}', 0, 7, 1, '请输入联系方式', '如有疑问可联系的电话或其他方式'),

-- 紧急公告模板字段 (模板ID为2)
(2, 'title', '紧急公告标题', 'text', '{"maxLength": 100}', 1, 1, 1, '请输入紧急公告标题', '标题应突出紧急性'),
(2, 'content', '紧急公告内容', 'textarea', '{"maxLength": 2000}', 1, 2, 1, '请输入紧急公告详细内容', '详细说明紧急情况和应对措施'),
(2, 'images', '相关图片', 'image', '{"maxCount": 9, "accept": "image/*"}', 0, 3, 1, '选择相关图片', '最多可上传9张图片'),
(2, 'urgencyLevel', '紧急程度', 'radio', '{"options": [{"label": "一般紧急", "value": "medium"}, {"label": "非常紧急", "value": "high"}, {"label": "特别紧急", "value": "critical"}]}', 1, 4, 1, '选择紧急程度', '根据情况紧急程度选择'),
(2, 'effectiveTime', '生效时间', 'date', '{}', 1, 5, 1, '选择生效时间', '公告开始生效的时间'),
(2, 'contactInfo', '紧急联系方式', 'text', '{"maxLength": 100}', 1, 6, 1, '请输入紧急联系方式', '紧急情况下的联系电话'),
(2, 'actionRequired', '需要采取的行动', 'textarea', '{"maxLength": 500}', 0, 7, 1, '请描述需要采取的行动', '居民需要采取的具体行动或注意事项');

-- =============================================
-- 2. 团购分类模板 (假设团购分类ID为2)
-- =============================================

-- 创建团购模板
INSERT INTO `category_templates` (`category_id`, `template_name`, `description`, `is_default`, `status`, `sort_order`) VALUES
(2, '团购模板', '用于发布团购信息的专用模板', 1, 1, 0),
(2, '生鲜团购模板', '专门用于生鲜类商品团购', 0, 1, 1);

-- 团购模板字段配置 (模板ID为3)
INSERT INTO `template_fields` (`template_id`, `field_name`, `field_label`, `field_type`, `field_config`, `required`, `sort_order`, `status`, `placeholder`, `help_text`) VALUES
-- 基础信息
(3, 'title', '商品名称', 'text', '{"maxLength": 100}', 1, 1, 1, '请输入商品名称', '简洁明了的商品名称'),
(3, 'content', '商品描述', 'textarea', '{"maxLength": 1000}', 1, 2, 1, '请详细描述商品', '包括商品特点、规格、产地等信息'),
(3, 'images', '商品图片', 'image', '{"maxCount": 9, "accept": "image/*"}', 1, 3, 1, '上传商品图片', '清晰的商品图片有助于提高团购成功率'),

-- 价格信息
(3, 'originalPrice', '原价', 'number', '{"min": 0, "max": 99999}', 1, 4, 1, '请输入商品原价', '商品的市场原价'),
(3, 'groupPrice', '团购价', 'number', '{"min": 0, "max": 99999}', 1, 5, 1, '请输入团购价格', '团购的优惠价格'),
(3, 'minQuantity', '起团数量', 'number', '{"min": 1, "max": 9999}', 1, 6, 1, '请输入起团数量', '达到此数量才能成团'),

-- 时间信息
(3, 'deadline', '截止时间', 'date', '{}', 1, 7, 1, '选择团购截止时间', '团购报名的截止时间'),
(3, 'deliveryDate', '配送时间', 'date', '{}', 0, 8, 1, '选择配送时间', '预计的商品配送时间'),

-- 联系信息
(3, 'contactPerson', '联系人', 'text', '{"maxLength": 50}', 1, 9, 1, '请输入联系人姓名', '团购组织者的姓名'),
(3, 'contactPhone', '联系电话', 'text', '{"maxLength": 20}', 1, 10, 1, '请输入联系电话', '方便其他用户联系的电话号码'),
(3, 'pickupLocation', '取货地点', 'text', '{"maxLength": 200}', 1, 11, 1, '请输入取货地点', '详细的取货地址'),

-- 其他信息
(3, 'paymentMethod', '付款方式', 'select', '{"options": [{"label": "微信支付", "value": "wechat"}, {"label": "支付宝", "value": "alipay"}, {"label": "现金", "value": "cash"}, {"label": "银行转账", "value": "bank"}]}', 1, 12, 1, '选择付款方式', '选择支持的付款方式'),
(3, 'notes', '备注说明', 'textarea', '{"maxLength": 500}', 0, 13, 1, '其他需要说明的事项', '补充说明，如退换货政策等');

-- 生鲜团购模板字段配置 (模板ID为4)
INSERT INTO `template_fields` (`template_id`, `field_name`, `field_label`, `field_type`, `field_config`, `required`, `sort_order`, `status`, `placeholder`, `help_text`) VALUES
-- 基础信息
(4, 'title', '生鲜商品名称', 'text', '{"maxLength": 100}', 1, 1, 1, '请输入生鲜商品名称', '如：有机蔬菜、新鲜水果等'),
(4, 'content', '商品详情', 'textarea', '{"maxLength": 1000}', 1, 2, 1, '请详细描述商品', '包括产地、新鲜度、营养价值等'),
(4, 'images', '商品图片', 'image', '{"maxCount": 9, "accept": "image/*"}', 1, 3, 1, '上传商品图片', '展示商品的新鲜度和品质'),

-- 规格信息
(4, 'specification', '商品规格', 'text', '{"maxLength": 100}', 1, 4, 1, '请输入商品规格', '如：500g/份、1kg/箱等'),
(4, 'origin', '产地', 'text', '{"maxLength": 100}', 1, 5, 1, '请输入产地', '商品的产地信息'),
(4, 'shelfLife', '保质期', 'text', '{"maxLength": 50}', 1, 6, 1, '请输入保质期', '如：3天、1周等'),

-- 价格信息
(4, 'groupPrice', '团购价', 'number', '{"min": 0, "max": 9999}', 1, 7, 1, '请输入团购价格', '每份/每箱的团购价格'),
(4, 'minQuantity', '起团数量', 'number', '{"min": 1, "max": 999}', 1, 8, 1, '请输入起团数量', '达到此数量才能成团'),

-- 时间信息
(4, 'deadline', '截止时间', 'date', '{}', 1, 9, 1, '选择团购截止时间', '团购报名的截止时间'),
(4, 'arrivalDate', '到货时间', 'date', '{}', 1, 10, 1, '选择到货时间', '生鲜商品的到货时间'),

-- 联系信息
(4, 'contactPerson', '团长', 'text', '{"maxLength": 50}', 1, 11, 1, '请输入团长姓名', '团购组织者的姓名'),
(4, 'contactPhone', '联系电话', 'text', '{"maxLength": 20}', 1, 12, 1, '请输入联系电话', '方便其他用户联系的电话号码'),
(4, 'pickupLocation', '取货地点', 'text', '{"maxLength": 200}', 1, 13, 1, '请输入取货地点', '详细的取货地址'),
(4, 'pickupTime', '取货时间', 'text', '{"maxLength": 100}', 1, 14, 1, '请输入取货时间', '具体的取货时间段'),

-- 特殊说明
(4, 'storageMethod', '保存方式', 'select', '{"options": [{"label": "常温保存", "value": "normal"}, {"label": "冷藏保存", "value": "refrigerated"}, {"label": "冷冻保存", "value": "frozen"}]}', 1, 15, 1, '选择保存方式', '商品的保存方式'),
(4, 'qualityGuarantee', '品质保证', 'textarea', '{"maxLength": 300}', 0, 16, 1, '品质保证说明', '如：不新鲜包退换等承诺');

-- =============================================
-- 3. 其他分类的默认模板
-- =============================================

-- 为其他分类创建默认模板 (这里以分类ID 3-10为例)
INSERT INTO `category_templates` (`category_id`, `template_name`, `description`, `is_default`, `status`, `sort_order`) VALUES
(3, '默认模板', '通用发帖模板', 1, 1, 0),
(4, '默认模板', '通用发帖模板', 1, 1, 0),
(5, '默认模板', '通用发帖模板', 1, 1, 0),
(6, '默认模板', '通用发帖模板', 1, 1, 0),
(7, '默认模板', '通用发帖模板', 1, 1, 0),
(8, '默认模板', '通用发帖模板', 1, 1, 0),
(9, '默认模板', '通用发帖模板', 1, 1, 0),
(10, '默认模板', '通用发帖模板', 1, 1, 0);

-- 为默认模板添加基础字段 (模板ID 5-12)
INSERT INTO `template_fields` (`template_id`, `field_name`, `field_label`, `field_type`, `field_config`, `required`, `sort_order`, `status`, `placeholder`, `help_text`) VALUES
-- 模板ID 5-12 的基础字段
(5, 'title', '标题', 'text', '{"maxLength": 100}', 0, 1, 1, '请输入标题（可选）', '简洁明了的标题'),
(5, 'content', '内容', 'textarea', '{"maxLength": 1000}', 1, 2, 1, '请输入内容', '详细描述您要分享的内容'),
(5, 'images', '图片', 'image', '{"maxCount": 9, "accept": "image/*"}', 0, 3, 1, '选择图片', '最多可上传9张图片'),

(6, 'title', '标题', 'text', '{"maxLength": 100}', 0, 1, 1, '请输入标题（可选）', '简洁明了的标题'),
(6, 'content', '内容', 'textarea', '{"maxLength": 1000}', 1, 2, 1, '请输入内容', '详细描述您要分享的内容'),
(6, 'images', '图片', 'image', '{"maxCount": 9, "accept": "image/*"}', 0, 3, 1, '选择图片', '最多可上传9张图片'),

(7, 'title', '标题', 'text', '{"maxLength": 100}', 0, 1, 1, '请输入标题（可选）', '简洁明了的标题'),
(7, 'content', '内容', 'textarea', '{"maxLength": 1000}', 1, 2, 1, '请输入内容', '详细描述您要分享的内容'),
(7, 'images', '图片', 'image', '{"maxCount": 9, "accept": "image/*"}', 0, 3, 1, '选择图片', '最多可上传9张图片'),

(8, 'title', '标题', 'text', '{"maxLength": 100}', 0, 1, 1, '请输入标题（可选）', '简洁明了的标题'),
(8, 'content', '内容', 'textarea', '{"maxLength": 1000}', 1, 2, 1, '请输入内容', '详细描述您要分享的内容'),
(8, 'images', '图片', 'image', '{"maxCount": 9, "accept": "image/*"}', 0, 3, 1, '选择图片', '最多可上传9张图片'),

(9, 'title', '标题', 'text', '{"maxLength": 100}', 0, 1, 1, '请输入标题（可选）', '简洁明了的标题'),
(9, 'content', '内容', 'textarea', '{"maxLength": 1000}', 1, 2, 1, '请输入内容', '详细描述您要分享的内容'),
(9, 'images', '图片', 'image', '{"maxCount": 9, "accept": "image/*"}', 0, 3, 1, '选择图片', '最多可上传9张图片'),

(10, 'title', '标题', 'text', '{"maxLength": 100}', 0, 1, 1, '请输入标题（可选）', '简洁明了的标题'),
(10, 'content', '内容', 'textarea', '{"maxLength": 1000}', 1, 2, 1, '请输入内容', '详细描述您要分享的内容'),
(10, 'images', '图片', 'image', '{"maxCount": 9, "accept": "image/*"}', 0, 3, 1, '选择图片', '最多可上传9张图片'),

(11, 'title', '标题', 'text', '{"maxLength": 100}', 0, 1, 1, '请输入标题（可选）', '简洁明了的标题'),
(11, 'content', '内容', 'textarea', '{"maxLength": 1000}', 1, 2, 1, '请输入内容', '详细描述您要分享的内容'),
(11, 'images', '图片', 'image', '{"maxCount": 9, "accept": "image/*"}', 0, 3, 1, '选择图片', '最多可上传9张图片'),

(12, 'title', '标题', 'text', '{"maxLength": 100}', 0, 1, 1, '请输入标题（可选）', '简洁明了的标题'),
(12, 'content', '内容', 'textarea', '{"maxLength": 1000}', 1, 2, 1, '请输入内容', '详细描述您要分享的内容'),
(12, 'images', '图片', 'image', '{"maxCount": 9, "accept": "image/*"}', 0, 3, 1, '选择图片', '最多可上传9张图片');

-- =============================================
-- 模板数据初始化完成
-- 包含：公告模板(2个)、团购模板(2个)、默认模板(8个)
-- =============================================
