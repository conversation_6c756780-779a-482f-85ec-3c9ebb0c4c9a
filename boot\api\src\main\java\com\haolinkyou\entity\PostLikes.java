package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 帖子点赞实体类
 */
@Data
@TableName("post_likes")
public class PostLikes implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 帖子ID
     */
    private Long postId;

    /**
     * 用户ID
     */
    private Long userId;

    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;

    @TableLogic
    private Integer delFlag;

    // 注意：这个表没有 updated_time 字段，所以不继承 BaseEntity
}
