<template>
  <view class="comment-section">
    <!-- 评论头部 -->
    <view class="comment-header">
      <view class="comment-count">评论 {{ totalComments }}</view>
      <view class="comment-sort">
        <up-dropdown>
          <up-dropdown-item
            v-model="sortType"
            title="排序"
            :options="sortOptions"
            @change="handleSortChange"
          />
        </up-dropdown>
      </view>
    </view>
    
    <!-- 快速评论输入 -->
    <view class="quick-comment">
      <up-avatar :text="currentUser.avatarText" :size="32" randomBgColor />
      <view class="comment-input" @click="showCommentModal">
        <text class="placeholder">{{ commentPlaceholder }}</text>
      </view>
      <view class="comment-actions">
        <up-icon name="photo" size="20" color="#999" />
        <up-icon name="smile" size="20" color="#999" />
      </view>
    </view>
    
    <!-- 评论列表 -->
    <view class="comment-list">
      <view v-if="comments.length === 0 && !loading" class="empty-comments">
        <up-empty
          mode="comment"
          text="暂无评论，快来抢沙发吧~"
          textColor="#999"
          textSize="14"
        />
      </view>
      
      <view v-for="comment in comments" :key="comment.commentId" class="comment-item">
        <CommentItem
          :comment="comment"
          :postAuthorId="postAuthorId"
          @reply="handleReply"
          @like="handleCommentLike"
          @delete="handleCommentDelete"
          @loadReplies="handleLoadReplies"
        />
      </view>
      
      <!-- 加载更多评论 -->
      <view v-if="hasMore" class="load-more-comments">
        <up-button
          text="加载更多评论"
          type="info"
          size="small"
          plain
          :loading="loading"
          @click="loadMoreComments"
        />
      </view>
    </view>
    
    <!-- 评论弹窗 -->
    <up-popup
      :show="showModal"
      mode="bottom"
      round="16"
      @close="closeCommentModal"
    >
      <view class="comment-modal">
        <view class="modal-header">
          <text class="modal-title">{{ replyTarget ? '回复评论' : '发表评论' }}</text>
          <up-icon name="close" size="20" @click="closeCommentModal" />
        </view>
        
        <view v-if="replyTarget" class="reply-target">
          <text class="reply-label">回复</text>
          <text class="reply-user">@{{ replyTarget.username }}</text>
          <text class="reply-content">{{ replyTarget.content }}</text>
        </view>
        
        <view class="comment-form">
          <up-textarea
            v-model="commentContent"
            placeholder="请输入评论内容..."
            :maxlength="500"
            :autoHeight="true"
            :height="120"
            @input="handleCommentInput"
          />
          
          <view class="form-footer">
            <view class="char-count">{{ commentContent.length }}/500</view>
            <up-button
              text="发布"
              type="primary"
              size="small"
              :disabled="!commentContent.trim()"
              :loading="submitting"
              @click="submitComment"
            />
          </view>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { get, post } from '@/utils/http'
import { useMemberStore } from '@/stores'
import CommentItem from './CommentItem.vue'
import type { Comments, Result } from '@/types'

interface Props {
  postId: number
  postAuthorId: number
}

const props = defineProps<Props>()
const memberStore = useMemberStore()

// 评论数据
const comments = ref<Comments[]>([])
const loading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = 10
const sortType = ref('latest')

// 评论弹窗
const showModal = ref(false)
const commentContent = ref('')
const submitting = ref(false)
const replyTarget = ref<Comments | null>(null)

// 排序选项
const sortOptions = [
  { label: '最新', value: 'latest' },
  { label: '最早', value: 'earliest' },
  { label: '最热', value: 'hot' }
]

// 当前用户信息
const currentUser = computed(() => {
  if (memberStore.profile) {
    return {
      id: memberStore.profile.id,
      avatarText: memberStore.profile.nickname?.charAt(0) || '游',
      nickname: memberStore.profile.nickname || '游客'
    }
  }
  return {
    id: 0,
    avatarText: '游',
    nickname: '游客'
  }
})

// 评论占位符
const commentPlaceholder = computed(() => {
  return replyTarget.value 
    ? `回复 @${replyTarget.value.username}` 
    : '善言结善缘，恶语伤人心...'
})

// 总评论数
const totalComments = computed(() => {
  let total = comments.value.length
  comments.value.forEach(comment => {
    if (comment.replies) {
      total += comment.replies.length
    }
  })
  return total
})

// 页面加载时获取评论
onMounted(() => {
  loadComments()
})

// 加载评论列表
const loadComments = async (reset = false) => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    const params = {
      postId: props.postId,
      page: reset ? 1 : currentPage.value,
      pageSize,
      sortBy: sortType.value
    }
    
    const res = await get<Result<Comments[]>>('/comments/getCommentsByPostId', params)
    
    if (res.success && res.data) {
      const newComments = res.data
      
      if (reset) {
        comments.value = newComments
        currentPage.value = 1
      } else {
        comments.value.push(...newComments)
      }
      
      hasMore.value = newComments.length === pageSize
      currentPage.value++
    }
  } catch (error) {
    console.error('加载评论失败:', error)
    uni.showToast({
      title: '加载评论失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 排序变更
const handleSortChange = () => {
  currentPage.value = 1
  hasMore.value = true
  loadComments(true)
}

// 加载更多评论
const loadMoreComments = () => {
  if (hasMore.value && !loading.value) {
    loadComments()
  }
}

// 显示评论弹窗
const showCommentModal = () => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  showModal.value = true
}

// 关闭评论弹窗
const closeCommentModal = () => {
  showModal.value = false
  commentContent.value = ''
  replyTarget.value = null
}

// 处理回复
const handleReply = (comment: Comments) => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  replyTarget.value = comment
  showModal.value = true
}

// 评论输入处理
const handleCommentInput = () => {
  // 可以在这里添加实时字数统计等逻辑
}

// 提交评论
const submitComment = async () => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  const content = commentContent.value.trim()
  if (!content) {
    uni.showToast({
      title: '请输入评论内容',
      icon: 'none'
    })
    return
  }
  
  submitting.value = true
  
  try {
    const params = {
      postId: props.postId,
      userId: memberStore.profile.id,
      content: content,
      parentCommentId: replyTarget.value?.commentId || null
    }
    
    const res = await post<Result<any>>('/comments/addComment', params)
    
    if (res.success) {
      uni.showToast({
        title: replyTarget.value ? '回复成功' : '评论成功',
        icon: 'success'
      })
      
      closeCommentModal()
      
      // 重新加载评论列表
      currentPage.value = 1
      hasMore.value = true
      loadComments(true)
      
      // 通知父组件更新评论数
      emit('commentAdded')
    } else {
      uni.showToast({
        title: res.message || '发布失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('发布评论失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    submitting.value = false
  }
}

// 评论点赞
const handleCommentLike = async (comment: Comments) => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  // 这里应该调用评论点赞API
  console.log('点赞评论:', comment.commentId)
}

// 删除评论
const handleCommentDelete = async (comment: Comments) => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条评论吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await post<Result<any>>('/comments/deleteComment', {
            id: comment.commentId
          })
          
          if (result.success) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
            
            // 重新加载评论列表
            currentPage.value = 1
            hasMore.value = true
            loadComments(true)
            
            // 通知父组件更新评论数
            emit('commentDeleted')
          } else {
            uni.showToast({
              title: result.message || '删除失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('删除评论失败:', error)
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 加载回复
const handleLoadReplies = (comment: Comments) => {
  // 这里可以实现懒加载回复的逻辑
  console.log('加载回复:', comment.commentId)
}

// 事件定义
const emit = defineEmits<{
  commentAdded: []
  commentDeleted: []
}>()

// 暴露方法给父组件
defineExpose({
  loadComments: () => loadComments(true),
  showCommentModal
})
</script>

<style scoped lang="scss">
.comment-section {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.comment-count {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.quick-comment {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.comment-input {
  flex: 1;
  padding: 8px 12px;
  background-color: white;
  border-radius: 20px;
  border: 1px solid #e9ecef;
}

.placeholder {
  font-size: 14px;
  color: #999;
}

.comment-actions {
  display: flex;
  gap: 8px;
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-comments {
  padding: 40px 20px;
  text-align: center;
}

.load-more-comments {
  text-align: center;
  padding: 16px;
}

.comment-modal {
  padding: 20px;
  max-height: 60vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.reply-target {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.reply-label {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.reply-user {
  font-size: 14px;
  color: #007bff;
  font-weight: 500;
  margin-right: 8px;
}

.reply-content {
  font-size: 14px;
  color: #666;
  display: block;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.comment-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.char-count {
  font-size: 12px;
  color: #999;
}
</style>