<template>
  <view class="media-preview">
    <!-- 媒体预览弹窗 -->
    <up-popup
      :show="show"
      mode="center"
      :round="0"
      :closeable="true"
      :closeOnClickOverlay="true"
      @close="handleClose"
      :customStyle="{ 
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        width: '100%',
        height: '100%',
        overflow: 'hidden'
      }"
    >
      <view class="preview-container">
        <!-- 顶部导航 -->
        <view class="preview-header" :style="{ paddingTop: safeAreaTop + 'px' }">
          <view class="header-content">
            <view class="nav-left" @click="handleClose">
              <up-icon name="close" size="24" color="white" />
            </view>
            <view class="nav-center">
              <text class="current-index">{{ currentIndex + 1 }} / {{ mediaList.length }}</text>
              <text class="media-type">{{ getMediaTypeText(mediaList[currentIndex]) }}</text>
            </view>
            <view class="nav-right" @click="showMoreActions">
              <up-icon name="more-dot-fill" size="24" color="white" />
            </view>
          </view>
        </view>

        <!-- 媒体内容区域 -->
        <view class="preview-content">
          <swiper
            :current="currentIndex"
            @change="handleSwiperChange"
            :indicator-dots="false"
            :autoplay="false"
            :circular="false"
            class="media-swiper"
          >
            <swiper-item
              v-for="(media, index) in mediaList"
              :key="index"
              class="swiper-item"
            >
              <view class="media-item">
                <!-- 图片预览 -->
                <image
                  v-if="getMediaType(media) === 'image'"
                  :src="media"
                  mode="aspectFit"
                  class="preview-image"
                  @click="handleImageTap"
                  @load="handleImageLoad"
                  @error="handleImageError"
                  :lazy-load="false"
                  :show-menu-by-longpress="true"
                  :webp="true"
                />
                
                <!-- 视频预览 -->
                <view v-else-if="getMediaType(media) === 'video'" class="video-container">
                  <video
                    :src="media"
                    :poster="getVideoPoster(media)"
                    controls
                    :autoplay="false"
                    :loop="false"
                    :muted="false"
                    :show-center-play-btn="true"
                    :show-play-btn="true"
                    :show-fullscreen-btn="true"
                    :show-progress="true"
                    :enable-progress-gesture="true"
                    :object-fit="videoObjectFit"
                    class="preview-video"
                    @play="handleVideoPlay"
                    @pause="handleVideoPause"
                    @ended="handleVideoEnded"
                    @error="handleVideoError"
                    @fullscreenchange="handleFullscreenChange"
                  />
                  
                  <!-- 视频控制按钮 -->
                  <view class="video-controls">
                    <view class="control-btn" @click="toggleVideoObjectFit">
                      <up-icon name="crop" size="20" color="white" />
                      <text class="control-text">{{ videoObjectFit === 'contain' ? '填充' : '适应' }}</text>
                    </view>
                  </view>
                </view>
                
                <!-- 未知类型 -->
                <view v-else class="unknown-media">
                  <up-icon name="file" size="60" color="#999" />
                  <text class="unknown-text">不支持的文件类型</text>
                </view>
              </view>
            </swiper-item>
          </swiper>
        </view>

        <!-- 底部指示器 -->
        <view class="preview-footer">
          <view class="media-indicators">
            <view
              v-for="(media, index) in mediaList"
              :key="index"
              class="indicator-item"
              :class="{ active: index === currentIndex }"
              @click="jumpToMedia(index)"
            >
              <view class="indicator-thumb">
                <image
                  v-if="getMediaType(media) === 'image'"
                  :src="media"
                  mode="aspectFill"
                  class="thumb-image"
                />
                <view v-else-if="getMediaType(media) === 'video'" class="thumb-video">
                  <video
                    :src="media"
                    :poster="getVideoPoster(media)"
                    :show-center-play-btn="false"
                    :show-play-btn="false"
                    :show-fullscreen-btn="false"
                    :show-progress="false"
                    :controls="false"
                    :autoplay="false"
                    muted
                    class="thumb-video-element"
                  />
                  <view class="video-icon">
                    <up-icon name="play-circle" size="16" color="white" />
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </up-popup>

    <!-- 更多操作弹窗 -->
    <up-action-sheet
      :show="showActions"
      :actions="actionList"
      @close="showActions = false"
      @select="handleActionSelect"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useSafeArea } from '@/utils/safeArea'

const { safeAreaTop } = useSafeArea()

interface Props {
  show: boolean
  mediaList: string[]
  initialIndex?: number
}

interface Emits {
  (e: 'close'): void
  (e: 'change', index: number): void
}

const props = withDefaults(defineProps<Props>(), {
  initialIndex: 0
})

const emit = defineEmits<Emits>()

// 响应式数据
const currentIndex = ref(props.initialIndex)
const videoObjectFit = ref<'contain' | 'fill' | 'cover'>('contain')
const showActions = ref(false)

// 操作列表
const actionList = [
  { name: '保存到相册' },
  { name: '下载文件' },
  { name: '复制链接' },
  { name: '分享' },
  { name: '举报' }
]

// 监听初始索引变化
watch(() => props.initialIndex, (newIndex) => {
  currentIndex.value = newIndex
})

// 监听显示状态
watch(() => props.show, (newShow) => {
  if (newShow) {
    currentIndex.value = props.initialIndex
  }
})

// 获取媒体类型
const getMediaType = (url: string): 'image' | 'video' | 'unknown' => {
  if (!url) return 'unknown'
  
  const lowerUrl = url.toLowerCase()
  
  if (lowerUrl.match(/\.(jpg|jpeg|png|gif|webp|bmp)(\?.*)?$/)) {
    return 'image'
  }
  
  if (lowerUrl.match(/\.(mp4|mov|avi|wmv|flv|webm|mkv)(\?.*)?$/)) {
    return 'video'
  }
  
  return 'unknown'
}

// 获取媒体类型文本
const getMediaTypeText = (url: string): string => {
  const type = getMediaType(url)
  switch (type) {
    case 'image': return '图片'
    case 'video': return '视频'
    default: return '文件'
  }
}

// 获取视频缩略图
const getVideoPoster = (videoUrl: string): string => {
  if (!videoUrl) return ''
  
  // 方案1: 如果服务器提供缩略图服务，可以通过URL参数获取
  // 例如: videoUrl + '?thumbnail=true' 或 videoUrl.replace('.mp4', '_thumb.jpg')
  
  // 方案2: 使用默认的视频缩略图占位符
  // 这里可以返回一个默认的视频封面图片URL
  
  // 方案3: 让video组件自动生成第一帧作为缩略图（当前方案）
  // 返回空字符串，video组件会自动显示第一帧
  return ''
  
  // 如果有专门的缩略图服务，可以这样实现：
  // const baseUrl = videoUrl.substring(0, videoUrl.lastIndexOf('.'))
  // return baseUrl + '_thumb.jpg'
}

// 事件处理
const handleClose = () => {
  emit('close')
}

const handleSwiperChange = (e: any) => {
  const newIndex = e.detail.current
  currentIndex.value = newIndex
  emit('change', newIndex)
}

const jumpToMedia = (index: number) => {
  currentIndex.value = index
  emit('change', index)
}

const handleImageTap = () => {
  // 图片点击事件，可以用于切换控制栏显示等
  console.log('Image tapped, current media:', props.mediaList[currentIndex.value])
}

// 图片加载成功
const handleImageLoad = (e: any) => {
  console.log('Image loaded successfully:', e)
}

// 图片加载失败
const handleImageError = (e: any) => {
  console.error('Image load error:', e)
  uni.showToast({
    title: '图片加载失败',
    icon: 'none'
  })
}

const showMoreActions = () => {
  showActions.value = true
}

const handleActionSelect = (action: { name: string }) => {
  showActions.value = false
  const currentMedia = props.mediaList[currentIndex.value]
  
  switch (action.name) {
    case '保存到相册':
      saveToAlbum(currentMedia)
      break
    case '下载文件':
      downloadFile(currentMedia)
      break
    case '复制链接':
      copyLink(currentMedia)
      break
    case '分享':
      shareMedia(currentMedia)
      break
    case '举报':
      reportMedia(currentMedia)
      break
  }
}

// 保存到相册
const saveToAlbum = (mediaUrl: string) => {
  const mediaType = getMediaType(mediaUrl)
  
  if (mediaType === 'image') {
    // 对于网络图片，需要先下载到本地再保存
    if (mediaUrl.startsWith('http')) {
      uni.downloadFile({
        url: mediaUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                uni.showToast({
                  title: '保存成功',
                  icon: 'success'
                })
              },
              fail: (err) => {
                console.error('保存图片失败:', err)
                uni.showToast({
                  title: '保存失败',
                  icon: 'none'
                })
              }
            })
          }
        },
        fail: (err) => {
          console.error('下载图片失败:', err)
          uni.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
      })
    } else {
      // 本地图片直接保存
      uni.saveImageToPhotosAlbum({
        filePath: mediaUrl,
        success: () => {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
        },
        fail: (err) => {
          console.error('保存图片失败:', err)
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          })
        }
      })
    }
  } else if (mediaType === 'video') {
    // 对于网络视频，需要先下载到本地再保存
    if (mediaUrl.startsWith('http')) {
      uni.showLoading({
        title: '下载中...'
      })
      
      uni.downloadFile({
        url: mediaUrl,
        success: (res) => {
          uni.hideLoading()
          if (res.statusCode === 200) {
            uni.saveVideoToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                uni.showToast({
                  title: '保存成功',
                  icon: 'success'
                })
              },
              fail: (err) => {
                console.error('保存视频失败:', err)
                uni.showToast({
                  title: '保存失败',
                  icon: 'none'
                })
              }
            })
          }
        },
        fail: (err) => {
          uni.hideLoading()
          console.error('下载视频失败:', err)
          uni.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
      })
    } else {
      // 本地视频直接保存
      uni.saveVideoToPhotosAlbum({
        filePath: mediaUrl,
        success: () => {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
        },
        fail: (err) => {
          console.error('保存视频失败:', err)
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          })
        }
      })
    }
  } else {
    uni.showToast({
      title: '不支持的文件类型',
      icon: 'none'
    })
  }
}

// 下载文件
const downloadFile = (mediaUrl: string) => {
  if (!mediaUrl.startsWith('http')) {
    uni.showToast({
      title: '本地文件无需下载',
      icon: 'none'
    })
    return
  }

  uni.showLoading({
    title: '下载中...'
  })

  uni.downloadFile({
    url: mediaUrl,
    success: (res) => {
      uni.hideLoading()
      if (res.statusCode === 200) {
        // 获取文件名
        const fileName = mediaUrl.split('/').pop() || 'download_file'
        
        uni.showToast({
          title: '下载完成',
          icon: 'success'
        })
        
        // 可以在这里添加更多下载后的处理逻辑
        console.log('文件下载完成:', res.tempFilePath)
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    },
    fail: (err) => {
      uni.hideLoading()
      console.error('下载失败:', err)
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    }
  })
}

// 复制链接
const copyLink = (mediaUrl: string) => {
  uni.setClipboardData({
    data: mediaUrl,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success'
      })
    }
  })
}

// 分享媒体
const shareMedia = (mediaUrl: string) => {
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  })
}

// 举报媒体
const reportMedia = (mediaUrl: string) => {
  uni.showToast({
    title: '举报功能开发中',
    icon: 'none'
  })
}

// 视频事件处理
const handleVideoPlay = () => {
  console.log('视频开始播放')
}

const handleVideoPause = () => {
  console.log('视频暂停')
}

const handleVideoEnded = () => {
  console.log('视频播放结束')
}

const handleVideoError = (e: any) => {
  console.error('视频播放错误:', e)
  uni.showToast({
    title: '视频播放失败',
    icon: 'none'
  })
}

const handleFullscreenChange = (e: any) => {
  console.log('全屏状态变化:', e)
}

// 切换视频显示模式
const toggleVideoObjectFit = () => {
  videoObjectFit.value = videoObjectFit.value === 'contain' ? 'fill' : 'contain'
  uni.showToast({
    title: `已切换到${videoObjectFit.value === 'contain' ? '适应' : '填充'}模式`,
    icon: 'none'
  })
}
</script>

<style scoped lang="scss">
.media-preview {
  .preview-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: rgba(0, 0, 0, 0.9);
    position: relative;
    overflow: hidden;
  }

  .preview-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    padding: 0 16px;
  }

  .nav-left, .nav-right {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.3);
  }

  .nav-center {
    flex: 1;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
  }

  .current-index {
    color: white;
    font-size: 16px;
    font-weight: 500;
  }

  .media-type {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
  }

  .preview-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: calc(100vh - 120px);
    position: relative;
  }

  .media-swiper {
    width: 100%;
    height: 100%;
    background: transparent;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .swiper-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: transparent;
    position: relative;
  }

  .media-item {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: transparent;
    min-height: 100vh;
  }

  .preview-image {
    width: 100vw;
    height: 100vh;
    max-width: 100vw;
    max-height: 100vh;
    object-fit: contain;
    background: transparent;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
  }

  .video-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .preview-video {
    width: 100%;
    height: 100%;
    max-width: 100%;
    max-height: 100%;
  }

  .video-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 12px;
  }

  .control-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    min-width: 50px;
  }

  .control-text {
    color: white;
    font-size: 10px;
  }

  .unknown-media {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .unknown-text {
    color: #999;
    font-size: 14px;
  }

  .preview-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
    padding: 20px 16px;
    padding-bottom: calc(20px + env(safe-area-inset-bottom));
  }

  .media-indicators {
    display: flex;
    justify-content: center;
    gap: 8px;
    overflow-x: auto;
    padding: 0 16px;
  }

  .indicator-item {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    
    &.active {
      border-color: #007bff;
      transform: scale(1.1);
    }
  }

  .indicator-thumb {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .thumb-image {
    width: 100%;
    height: 100%;
  }

  .thumb-video {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .thumb-video-element {
    width: 100%;
    height: 100%;
  }

  .video-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>