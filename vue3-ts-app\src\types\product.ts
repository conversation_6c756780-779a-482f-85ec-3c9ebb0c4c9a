// 积分商品相关类型定义

// 积分商品
export interface Product {
  id: number;
  name: string;
  description?: string;
  image?: string;
  points: number; // 所需积分
  stock: number; // 库存数量
  salesCount: number; // 销售数量
  sortOrder: number;
  status: number; // 0-下架 1-上架
  createdTime: string;
  updatedTime?: string;
}

// 商品表单（用于添加/编辑）
export interface ProductForm {
  name: string;
  description?: string;
  image?: string;
  points: number;
  stock: number;
  sortOrder?: number;
  status?: number;
}

// 商品列表查询参数
export interface ProductListParams {
  page: number;
  pageSize: number;
  status?: number;
  keyword?: string;
}

// 商品兑换表单
export interface RedeemForm {
  productId: number;
  quantity: number;
  shippingAddress?: string;
  contactPhone?: string;
  remark?: string;
}
