package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.entity.SystemConfig;
import com.haolinkyou.mapper.SystemConfigMapper;
import com.haolinkyou.service.ISystemConfigService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 系统配置服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Service
public class SystemConfigServiceImpl extends ServiceImpl<SystemConfigMapper, SystemConfig> implements ISystemConfigService {

    // 简单的内存缓存
    private final ConcurrentHashMap<String, String> configCache = new ConcurrentHashMap<>();

    @Override
    public String getConfigValue(String configKey) {
        return getConfigValue(configKey, null);
    }

    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        // 先从缓存中获取
        String cachedValue = configCache.get(configKey);
        if (cachedValue != null) {
            return cachedValue;
        }

        // 从数据库中获取
        QueryWrapper<SystemConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_key", configKey);
        SystemConfig config = getOne(queryWrapper);

        String value = (config != null) ? config.getConfigValue() : defaultValue;
        
        // 缓存结果
        if (value != null) {
            configCache.put(configKey, value);
        }

        return value;
    }

    @Override
    public Integer getIntValue(String configKey, Integer defaultValue) {
        String value = getConfigValue(configKey);
        if (value == null) {
            return defaultValue;
        }

        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    @Override
    public Boolean getBooleanValue(String configKey, Boolean defaultValue) {
        String value = getConfigValue(configKey);
        if (value == null) {
            return defaultValue;
        }

        return "true".equalsIgnoreCase(value) || "1".equals(value);
    }

    @Override
    public boolean updateConfigValue(String configKey, String configValue) {
        try {
            QueryWrapper<SystemConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("config_key", configKey);
            SystemConfig config = getOne(queryWrapper);

            if (config != null) {
                config.setConfigValue(configValue);
                boolean result = updateById(config);
                
                // 更新缓存
                if (result) {
                    configCache.put(configKey, configValue);
                }
                
                return result;
            } else {
                // 配置不存在，创建新配置
                SystemConfig newConfig = new SystemConfig();
                newConfig.setConfigKey(configKey);
                newConfig.setConfigValue(configValue);
                newConfig.setConfigType("string");
                newConfig.setGroupName("custom");
                newConfig.setIsSystem(0);
                
                boolean result = save(newConfig);
                
                // 更新缓存
                if (result) {
                    configCache.put(configKey, configValue);
                }
                
                return result;
            }
        } catch (Exception e) {
            log.error("更新配置失败: key=" + configKey + ", value=" + configValue + ", error=" + e.getMessage());
            return false;
        }
    }

    @Override
    public IPage<SystemConfig> getConfigPage(Integer page, Integer pageSize, String groupName, String keyword) {
        Page<SystemConfig> pageInfo = new Page<>(page, pageSize);
        QueryWrapper<SystemConfig> queryWrapper = new QueryWrapper<>();

        if (StringUtils.hasText(groupName)) {
            queryWrapper.eq("group_name", groupName);
        }

        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like("config_key", keyword)
                .or()
                .like("description", keyword)
            );
        }

        queryWrapper.orderByAsc("group_name", "sort_order", "config_key");
        return page(pageInfo, queryWrapper);
    }

    @Override
    public List<SystemConfig> getConfigsByGroup(String groupName) {
        QueryWrapper<SystemConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_name", groupName);
        queryWrapper.orderByAsc("sort_order", "config_key");
        return list(queryWrapper);
    }

    @Override
    public List<String> getAllGroups() {
        QueryWrapper<SystemConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT group_name");
        queryWrapper.orderByAsc("group_name");
        List<SystemConfig> configs = list(queryWrapper);
        return configs.stream()
                .map(SystemConfig::getGroupName)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, String> getConfigMap(String groupName) {
        QueryWrapper<SystemConfig> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasText(groupName)) {
            queryWrapper.eq("group_name", groupName);
        }
        List<SystemConfig> configs = list(queryWrapper);
        return configs.stream()
                .collect(Collectors.toMap(
                    SystemConfig::getConfigKey,
                    SystemConfig::getConfigValue,
                    (existing, replacement) -> replacement
                ));
    }

    @Override
    public boolean addConfig(SystemConfig config) {
        try {
            return save(config);
        } catch (Exception e) {
            log.error("添加配置失败: " + e.getMessage());
            return false;
        }
    }

    @Override
    public boolean batchUpdateConfigs(List<SystemConfig> configs) {
        try {
            boolean result = updateBatchById(configs);
            // 清除缓存
            if (result) {
                configCache.clear();
            }
            return result;
        } catch (Exception e) {
            log.error("批量更新配置失败: " + e.getMessage());
            return false;
        }
    }

    @Override
    public boolean deleteConfig(Long id) {
        try {
            SystemConfig config = getById(id);
            if (config != null) {
                boolean result = removeById(id);
                // 清除缓存
                if (result) {
                    configCache.remove(config.getConfigKey());
                }
                return result;
            }
            return false;
        } catch (Exception e) {
            log.error("删除配置失败: " + e.getMessage());
            return false;
        }
    }
}