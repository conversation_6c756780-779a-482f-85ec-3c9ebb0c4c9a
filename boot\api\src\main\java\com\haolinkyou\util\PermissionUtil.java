package com.haolinkyou.util;

import com.haolinkyou.entity.Users;
import com.haolinkyou.service.IUserService;
import com.haolinkyou.service.UserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 权限验证工具类
 * 提供统一的权限验证方法，作为AOP的替代方案
 */
@Component
public class PermissionUtil {
    
    @Autowired
    private IUserService userService;
    
    @Autowired
    private UserRoleService userRoleService;
    
    /**
     * 验证用户是否有指定权限
     * @param request HTTP请求对象
     * @param permission 权限代码
     * @return 是否有权限
     */
    public boolean hasPermission(HttpServletRequest request, String permission) {
        try {
            // 获取当前用户ID
            Long currentUserId = (Long) request.getAttribute("userId");
            if (currentUserId == null) {
                return false;
            }
            
            // 获取当前用户信息
            Users currentUser = userService.getById(currentUserId);
            if (currentUser == null) {
                return false;
            }
            
            String userRole = currentUser.getUserRole();
            if (userRole == null) {
                return false;
            }
            
            // 检查权限
            return userRoleService.hasPermission(userRole, permission);
        } catch (Exception e) {
            System.err.println("权限验证失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 验证用户是否为管理员
     * @param request HTTP请求对象
     * @return 是否为管理员
     */
    public boolean isAdmin(HttpServletRequest request) {
        try {
            // 获取当前用户ID
            Long currentUserId = (Long) request.getAttribute("userId");
            if (currentUserId == null) {
                return false;
            }
            
            // 获取当前用户信息
            Users currentUser = userService.getById(currentUserId);
            if (currentUser == null) {
                return false;
            }
            
            String userRole = currentUser.getUserRole();
            return userRoleService.isAdmin(userRole);
        } catch (Exception e) {
            System.err.println("管理员权限验证失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 验证权限并返回错误响应
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param permission 权限代码
     * @param requireAdmin 是否需要管理员权限
     * @return 是否有权限
     */
    public boolean checkPermissionWithResponse(HttpServletRequest request, HttpServletResponse response, 
                                             String permission, boolean requireAdmin) {
        try {
            // 获取当前用户ID
            Long currentUserId = (Long) request.getAttribute("userId");
            if (currentUserId == null) {
                sendUnauthorizedResponse(response, "用户未登录");
                return false;
            }
            
            // 获取当前用户信息
            Users currentUser = userService.getById(currentUserId);
            if (currentUser == null) {
                sendUnauthorizedResponse(response, "用户不存在");
                return false;
            }
            
            String userRole = currentUser.getUserRole();
            if (userRole == null) {
                sendUnauthorizedResponse(response, "用户角色未设置");
                return false;
            }
            
            // 检查管理员权限
            if (requireAdmin && !userRoleService.isAdmin(userRole)) {
                sendForbiddenResponse(response, "需要管理员权限");
                return false;
            }
            
            // 检查具体权限
            if (permission != null && !permission.isEmpty()) {
                if (!userRoleService.hasPermission(userRole, permission)) {
                    sendForbiddenResponse(response, "权限不足");
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            System.err.println("权限验证失败: " + e.getMessage());
            sendForbiddenResponse(response, "权限验证失败");
            return false;
        }
    }
    
    /**
     * 验证多个权限（满足任一即可）
     * @param request HTTP请求对象
     * @param permissions 权限代码数组
     * @return 是否有权限
     */
    public boolean hasAnyPermission(HttpServletRequest request, String[] permissions) {
        if (permissions == null || permissions.length == 0) {
            return true;
        }
        
        for (String permission : permissions) {
            if (hasPermission(request, permission)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 验证多个权限（需要全部满足）
     * @param request HTTP请求对象
     * @param permissions 权限代码数组
     * @return 是否有权限
     */
    public boolean hasAllPermissions(HttpServletRequest request, String[] permissions) {
        if (permissions == null || permissions.length == 0) {
            return true;
        }
        
        for (String permission : permissions) {
            if (!hasPermission(request, permission)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 获取当前用户信息
     * @param request HTTP请求对象
     * @return 用户信息，如果未登录返回null
     */
    public Users getCurrentUser(HttpServletRequest request) {
        try {
            Long currentUserId = (Long) request.getAttribute("userId");
            if (currentUserId == null) {
                return null;
            }
            return userService.getById(currentUserId);
        } catch (Exception e) {
            System.err.println("获取当前用户失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 发送401未授权响应
     */
    private void sendUnauthorizedResponse(HttpServletResponse response, String message) {
        try {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(String.format(
                "{\"success\":false,\"message\":\"%s\",\"code\":401}", 
                message
            ));
        } catch (IOException e) {
            System.err.println("发送401响应失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送403禁止访问响应
     */
    private void sendForbiddenResponse(HttpServletResponse response, String message) {
        try {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(String.format(
                "{\"success\":false,\"message\":\"%s\",\"code\":403}", 
                message
            ));
        } catch (IOException e) {
            System.err.println("发送403响应失败: " + e.getMessage());
        }
    }
}
