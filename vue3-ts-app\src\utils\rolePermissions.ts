/**
 * 角色权限相关工具函数
 */

// 角色配置
export const USER_ROLES = {
  guest: { name: '游客', color: '#999999', bgColor: '#f5f5f5' },
  owner: { name: '业主', color: '#52c41a', bgColor: '#f6ffed' },
  tenant: { name: '租户', color: '#52c41a', bgColor: '#f6ffed' },
  property: { name: '物业', color: '#722ed1', bgColor: '#f9f0ff' },
  committee: { name: '业委会', color: '#fa541c', bgColor: '#fff2e8' },
  community: { name: '社区管理员', color: '#fa541c', bgColor: '#fff2e8' },
  admin: { name: '系统管理员', color: '#fa541c', bgColor: '#fff2e8' }
}

// 权限列表
export const PERMISSIONS = {
  VIEW_POSTS: 'view_posts',
  VIEW_COMMENTS: 'view_comments',
  CREATE_POSTS: 'create_posts',
  CREATE_COMMENTS: 'create_comments',
  LIKE_POSTS: 'like_posts',
  COLLECT_POSTS: 'collect_posts',
  DELETE_OWN_POSTS: 'delete_own_posts',
  DELETE_ANY_POSTS: 'delete_any_posts',
  MANAGE_USERS: 'manage_users',
  MANAGE_POSTS: 'manage_posts',
  MANAGE_COMMENTS: 'manage_comments',
  ADMIN_ACCESS: 'admin_access'
}

// 角色状态标签接口
export interface RoleStatusTag {
  text: string;
  color: string;
  bgColor: string;
  type: 'verified' | 'unverified';
}

/**
 * 根据用户认证状态和角色获取标签信息
 * @param isVerified 是否已认证
 * @param userRole 用户角色
 * @returns 用户状态标签
 */
export function getRoleStatusTag(isVerified?: boolean, userRole?: string): RoleStatusTag {
  if (isVerified && userRole) {
    // 已认证用户：显示角色名称
    const roleConfig = USER_ROLES[userRole as keyof typeof USER_ROLES] || USER_ROLES.guest;
    return {
      text: roleConfig.name,
      color: roleConfig.color,
      bgColor: roleConfig.bgColor,
      type: 'verified'
    };
  } else {
    // 未认证用户：显示"未认证"
    return {
      text: '未认证',
      color: '#999999',
      bgColor: '#f5f5f5',
      type: 'unverified'
    };
  }
}

/**
 * 获取用户角色名称
 * @param userRole 用户角色
 * @returns 角色名称
 */
export function getRoleName(userRole?: string): string {
  if (!userRole) return '游客';
  return USER_ROLES[userRole as keyof typeof USER_ROLES]?.name || '游客';
}

/**
 * 判断是否为管理员
 * @param userRole 用户角色
 * @returns 是否为管理员
 */
export function isAdmin(userRole?: string): boolean {
  return userRole === 'admin' || userRole === 'community';
}

/**
 * 判断是否为认证用户
 * @param userRole 用户角色
 * @returns 是否为认证用户
 */
export function isVerifiedUser(userRole?: string): boolean {
  return userRole !== 'guest' && userRole !== undefined && userRole !== null;
}

/**
 * 获取用户权限描述
 * @param isVerified 是否已认证
 * @param userRole 用户角色
 * @returns 权限描述
 */
export function getRolePermissionDesc(isVerified?: boolean, userRole?: string): string {
  if (!isVerified || !userRole || userRole === 'guest') {
    return '游客用户，权限受限';
  }
  
  switch (userRole) {
    case 'owner':
      return '认证业主，享有业主权益';
    case 'tenant':
      return '认证租户，可发帖评论';
    case 'property':
      return '物业中心，管理物业事务';
    case 'committee':
      return '业委会委员，拥有管理权限';
    case 'community':
      return '社区管理员，拥有社区管理权限';
    case 'admin':
      return '系统管理员，拥有全部权限';
    default:
      return '普通用户';
  }
}

/**
 * 检查用户是否有指定权限（前端简单判断，实际权限验证在后端）
 * @param userRole 用户角色
 * @param permission 权限代码
 * @returns 是否有权限
 */
export function hasPermission(userRole?: string, permission?: string): boolean {
  if (!userRole || !permission) return false;
  
  // 管理员拥有所有权限
  if (isAdmin(userRole)) return true;
  
  // 基础权限判断
  switch (permission) {
    case PERMISSIONS.VIEW_POSTS:
    case PERMISSIONS.VIEW_COMMENTS:
      return true; // 所有用户都可以查看
    case PERMISSIONS.CREATE_POSTS:
    case PERMISSIONS.CREATE_COMMENTS:
    case PERMISSIONS.LIKE_POSTS:
    case PERMISSIONS.COLLECT_POSTS:
    case PERMISSIONS.DELETE_OWN_POSTS:
      return isVerifiedUser(userRole); // 认证用户可以操作
    case PERMISSIONS.DELETE_ANY_POSTS:
    case PERMISSIONS.MANAGE_USERS:
    case PERMISSIONS.MANAGE_POSTS:
    case PERMISSIONS.MANAGE_COMMENTS:
    case PERMISSIONS.ADMIN_ACCESS:
      return isAdmin(userRole); // 只有管理员可以操作
    default:
      return false;
  }
}

// 帖子状态相关工具函数
export interface PostStatusInfo {
  text: string;
  color: string;
  bgColor: string;
}

/**
 * 获取帖子状态信息
 * @param status 帖子状态 (0-待审核 1-已通过 2-已拒绝)
 * @returns 状态信息对象
 */
export const getPostStatusInfo = (status: number): PostStatusInfo => {
  const statusMap: Record<number, PostStatusInfo> = {
    0: {
      text: '审核中',
      color: '#ff9500',
      bgColor: '#fff7e6'
    },
    1: {
      text: '已发布',
      color: '#52c41a',
      bgColor: '#f6ffed'
    },
    2: {
      text: '已拒绝',
      color: '#ff4d4f',
      bgColor: '#fff2f0'
    }
  };

  return statusMap[status] || {
    text: '未知',
    color: '#666666',
    bgColor: '#f5f5f5'
  };
};

/**
 * 判断是否应该显示帖子状态
 * @param post 帖子对象
 * @param currentUserId 当前用户ID
 * @param isAdmin 是否为管理员
 * @returns 是否显示状态
 */
export const shouldShowPostStatus = (post: any, currentUserId?: number | null, isAdmin?: boolean): boolean => {
  if (!currentUserId) return false; // 未登录用户不显示状态

  // 对于已发布状态(status = 1)的帖子，只对发帖人和管理员显示
  // 对于其他状态(审核中、已拒绝)，也只对发帖人和管理员显示
  const isAuthor = post.userId === currentUserId;
  const canSeeStatus = isAdmin || isAuthor;

  if (!canSeeStatus) return false;

  // 如果是已发布状态(status = 1)，不显示状态标签（因为这是默认状态）
  if (post.status === 1) return false;

  // 其他状态(审核中、已拒绝)需要显示
  return true;
};
