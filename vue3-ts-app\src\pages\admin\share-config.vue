<template>
  <view class="share-config-page">
    <!-- 页面头部 -->
    <up-navbar
      title="分享配置"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 配置内容 -->
    <view class="config-section" :style="{ marginTop: mainContentPaddingTop }">
      <view class="section-title">
        <up-icon name="setting" color="#1890ff" size="16"></up-icon>
        <text class="title-text">普通分享配置</text>
      </view>

      <!-- 分享图片预览 -->
      <view class="share-preview">
        <view class="preview-container">
          <image 
            :src="shareConfig.image || '/static/images/default-share.png'"
            class="preview-image"
            mode="aspectFit"
          ></image>
          <view class="preview-overlay">
            <view class="share-title">{{ shareConfig.title || '咱们恒大自己的信息共享平台' }}</view>
            <view class="share-subtitle">好邻友社区</view>
          </view>
        </view>
      </view>

      <!-- 配置表单 -->
      <view class="config-form">
        <up-form :model="shareConfig" ref="formRef" :rules="rules">
          <!-- 分享图片 -->
          <up-form-item label="分享图片" prop="image" :border-bottom="true">
            <view class="upload-section">
              <up-upload
                :fileList="fileList"
                @afterRead="afterRead"
                @delete="deleteFile"
                name="shareImage"
                :maxCount="1"
                :previewImage="true"
                width="120"
                height="80"
              >
                <view class="upload-placeholder">
                  <up-icon name="plus" size="24" color="#c0c4cc"></up-icon>
                  <text class="upload-text">上传分享图</text>
                </view>
              </up-upload>
            </view>
            <view class="upload-tip">
              <text>建议尺寸: 1200x630px，支持jpg、png格式</text>
            </view>
          </up-form-item>

          <!-- 分享标题 -->
          <up-form-item label="分享标题" prop="title" :border-bottom="false">
            <up-input 
              v-model="shareConfig.title" 
              placeholder="请输入分享标题"
              :clearable="true"
              :maxlength="50"
              @input="updatePreview"
            ></up-input>
            <template #right>
              <text class="char-count">{{ shareConfig.title.length }}/50</text>
            </template>
          </up-form-item>
        </up-form>
      </view>

      <!-- 分享说明 -->
      <view class="share-description">
        <view class="desc-title">分享说明</view>
        <view class="desc-content">
          <text>• 分享图片将在用户分享应用时显示</text>
          <text>• 分享标题会作为分享链接的标题</text>
          <text>• 建议使用高质量的图片以获得更好的分享效果</text>
          <text>• 配置保存后会立即生效</text>
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-section">
      <up-button 
        text="保存配置" 
        type="primary" 
        :loading="saving"
        @click="handleSave"
        :customStyle="{ 
          width: '100%', 
          height: '50px',
          borderRadius: '25px',
          fontSize: '16px'
        }"
      ></up-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useSafeArea } from '@/utils/safeArea';

const { mainContentPaddingTop } = useSafeArea();

// 分享配置数据
const shareConfig = reactive({
  image: '',
  title: '咱们恒大自己的信息共享平台'
});

// 文件列表
const fileList = ref([]);

// 表单引用
const formRef = ref();

// 保存状态
const saving = ref(false);

// 表单验证规则
const rules = {
  title: [
    {
      required: true,
      message: '请输入分享标题',
      trigger: ['blur', 'change']
    },
    {
      max: 50,
      message: '标题长度不能超过50个字符',
      trigger: ['blur', 'change']
    }
  ]
};

// 初始化数据
const initData = async () => {
  try {
    // TODO: 调用真实API获取分享配置
    // const res = await getShareConfig();
    // shareConfig.image = res.data.image;
    // shareConfig.title = res.data.title;

    // 暂时使用默认配置
    shareConfig.image = '';
    shareConfig.title = '';
  } catch (error) {
    console.error('加载分享配置失败:', error);
  }
};

onMounted(() => {
  initData();
});

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 文件上传后处理
const afterRead = (event: any) => {
  const { file } = event;
  // 这里可以添加文件上传到服务器的逻辑
  shareConfig.image = file.url;
  updatePreview();
  console.log('上传分享图:', file);
};

// 删除文件
const deleteFile = (event: any) => {
  const { index } = event;
  fileList.value.splice(index, 1);
  shareConfig.image = '';
  updatePreview();
};

// 更新预览
const updatePreview = () => {
  // 这里可以添加实时预览更新逻辑
  console.log('更新预览:', shareConfig);
};

// 保存配置
const handleSave = async () => {
  try {
    // 表单验证
    await formRef.value.validate();
    
    saving.value = true;
    
    // 这里应该调用实际的API保存配置
    console.log('保存分享配置:', shareConfig);
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    uni.showToast({
      title: '配置保存成功',
      icon: 'success'
    });
    
  } catch (error) {
    console.error('表单验证失败:', error);
    uni.showToast({
      title: '请完善必填信息',
      icon: 'none'
    });
  } finally {
    saving.value = false;
  }
};
</script>

<style lang="scss">
.share-config-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  // 配置区域
  .config-section {
    padding: 20px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .title-text {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-left: 8px;
      }
    }

    // 分享预览
    .share-preview {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;

      .preview-container {
        position: relative;
        width: 100%;
        height: 200px;
        border-radius: 8px;
        overflow: hidden;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

        .preview-image {
          width: 100%;
          height: 100%;
        }

        .preview-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
          padding: 20px;
          color: #fff;

          .share-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
            line-height: 1.3;
          }

          .share-subtitle {
            font-size: 14px;
            opacity: 0.9;
          }
        }
      }
    }

    // 配置表单
    .config-form {
      background: #fff;
      border-radius: 8px;
      padding: 0 20px;
      margin-bottom: 20px;

      .upload-section {
        padding: 15px 0;

        .upload-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 120px;
          height: 80px;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
          background: #fafafa;

          .upload-text {
            font-size: 12px;
            color: #999;
            margin-top: 6px;
          }
        }
      }

      .upload-tip {
        margin-top: 8px;

        text {
          font-size: 12px;
          color: #999;
        }
      }

      .char-count {
        font-size: 12px;
        color: #999;
      }
    }

    // 分享说明
    .share-description {
      background: #fff;
      border-radius: 8px;
      padding: 20px;

      .desc-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 12px;
      }

      .desc-content {
        text {
          display: block;
          font-size: 14px;
          color: #666;
          line-height: 1.6;
          margin-bottom: 6px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  // 保存按钮区域
  .save-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 20px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
