<template>
  <view class="rich-text-renderer">
    <!-- 使用uni-app的rich-text组件渲染HTML内容 -->
    <rich-text
      :nodes="processedNodes"
      :selectable="selectable"
      @itemclick="handleItemClick"
      class="rich-text-content"
    />
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  content?: string // HTML内容，可选
  selectable?: boolean // 是否可选择文本
  maxLength?: number // 最大长度限制
  showMore?: boolean // 是否显示展开/收起
}

const props = withDefaults(defineProps<Props>(), {
  content: '', // 默认为空字符串
  selectable: true,
  maxLength: 0,
  showMore: false
})

const emit = defineEmits<{
  linkClick: [url: string]
  imageClick: [src: string]
}>()

// 处理HTML内容，转换为rich-text组件可识别的节点
const processedNodes = computed(() => {
  const content = props.content || ''
  if (!content) return []
  
  let processedContent = content
  
  // 长度限制处理
  if (props.maxLength > 0 && processedContent.length > props.maxLength) {
    processedContent = processedContent.substring(0, props.maxLength) + '...'
  }
  
  // 安全处理：移除危险标签和属性
  processedContent = sanitizeHtml(processedContent)
  
  // 处理换行符
  processedContent = processedContent.replace(/\n/g, '<br/>')
  
  // 处理链接，添加点击事件标识
  processedContent = processedContent.replace(
    /<a\s+([^>]*?)href=["']([^"']*?)["']([^>]*?)>/gi,
    '<a $1href="$2"$3 data-type="link" data-url="$2">'
  )
  
  // 处理图片，添加点击事件标识
  processedContent = processedContent.replace(
    /<img\s+([^>]*?)src=["']([^"']*?)["']([^>]*?)>/gi,
    '<img $1src="$2"$3 data-type="image" data-src="$2">'
  )
  
  return processedContent
})

// HTML内容安全处理
const sanitizeHtml = (html: string): string => {
  // 允许的标签
  const allowedTags = [
    'p', 'div', 'span', 'br', 'strong', 'b', 'em', 'i', 'u', 's', 'del',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li',
    'a', 'img',
    'blockquote', 'pre', 'code',
    'table', 'thead', 'tbody', 'tr', 'th', 'td'
  ]
  
  // 允许的属性
  const allowedAttrs = [
    'href', 'src', 'alt', 'title', 'width', 'height',
    'data-type', 'data-url', 'data-src'
  ]
  
  // 移除不允许的标签和属性
  let sanitized = html
  
  // 移除script标签
  sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  
  // 移除style标签
  sanitized = sanitized.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
  
  // 移除事件处理属性
  sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '')
  
  // 移除javascript:协议
  sanitized = sanitized.replace(/javascript:/gi, '')
  
  return sanitized
}

// 处理富文本中的点击事件
const handleItemClick = (event: any) => {
  const { node } = event.detail
  
  if (node.attrs) {
    const dataType = node.attrs['data-type']
    
    if (dataType === 'link') {
      const url = node.attrs['data-url'] || node.attrs.href
      if (url) {
        emit('linkClick', url)
      }
    } else if (dataType === 'image') {
      const src = node.attrs['data-src'] || node.attrs.src
      if (src) {
        emit('imageClick', src)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.rich-text-renderer {
  width: 100%;
}

.rich-text-content {
  width: 100%;
  line-height: 1.6;
  
  // 富文本内容样式
  :deep(.rich-text) {
    // 段落样式
    p {
      margin: 8px 0;
      line-height: 1.6;
    }
    
    // 标题样式
    h1, h2, h3, h4, h5, h6 {
      margin: 16px 0 8px 0;
      font-weight: bold;
      line-height: 1.4;
    }
    
    h1 { font-size: 24px; }
    h2 { font-size: 22px; }
    h3 { font-size: 20px; }
    h4 { font-size: 18px; }
    h5 { font-size: 16px; }
    h6 { font-size: 14px; }
    
    // 强调样式
    strong, b {
      font-weight: bold;
    }
    
    em, i {
      font-style: italic;
    }
    
    u {
      text-decoration: underline;
    }
    
    s, del {
      text-decoration: line-through;
    }
    
    // 链接样式
    a {
      color: #007bff;
      text-decoration: underline;
    }
    
    // 图片样式
    img {
      max-width: 100%;
      height: auto;
      border-radius: 8px;
      margin: 8px 0;
    }
    
    // 列表样式
    ul, ol {
      margin: 8px 0;
      padding-left: 20px;
    }
    
    li {
      margin: 4px 0;
      line-height: 1.6;
    }
    
    // 引用样式
    blockquote {
      margin: 16px 0;
      padding: 12px 16px;
      background-color: #f8f9fa;
      border-left: 4px solid #007bff;
      border-radius: 4px;
      font-style: italic;
    }
    
    // 代码样式
    code {
      padding: 2px 4px;
      background-color: #f8f9fa;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 14px;
    }
    
    pre {
      margin: 16px 0;
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 8px;
      overflow-x: auto;
      
      code {
        padding: 0;
        background-color: transparent;
      }
    }
    
    // 表格样式
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 16px 0;
    }
    
    th, td {
      padding: 8px 12px;
      border: 1px solid #e9ecef;
      text-align: left;
    }
    
    th {
      background-color: #f8f9fa;
      font-weight: bold;
    }
  }
}
</style>