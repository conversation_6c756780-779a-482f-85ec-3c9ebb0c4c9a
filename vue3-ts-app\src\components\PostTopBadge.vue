<template>
  <view v-if="isTopped" class="post-top-badge">
    <view class="top-badge">
      <up-icon name="arrow-up-circle-fill" color="#ff6b35" size="12"></up-icon>
      <text class="badge-text">置顶</text>
    </view>
    <view v-if="showExpireTime && expireTimeText" class="expire-time">
      <text class="expire-text">{{ expireTimeText }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  isTopped?: boolean
  topExpireTime?: string
  showExpireTime?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isTopped: false,
  showExpireTime: true
})

// 计算过期时间显示文本
const expireTimeText = computed(() => {
  if (!props.topExpireTime) return ''
  
  const expireTime = new Date(props.topExpireTime)
  const now = new Date()
  const diffMs = expireTime.getTime() - now.getTime()
  
  if (diffMs <= 0) {
    return '已过期'
  }
  
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
  
  if (diffHours >= 24) {
    const diffDays = Math.floor(diffHours / 24)
    return `剩余${diffDays}天`
  } else if (diffHours > 0) {
    return `剩余${diffHours}小时`
  } else if (diffMinutes > 0) {
    return `剩余${diffMinutes}分钟`
  } else {
    return '即将过期'
  }
})
</script>

<style scoped lang="scss">
.post-top-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.top-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(255, 107, 53, 0.3);
}

.badge-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

.expire-time {
  padding: 2px 6px;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.expire-text {
  font-size: 10px;
  color: #999;
}
</style>
