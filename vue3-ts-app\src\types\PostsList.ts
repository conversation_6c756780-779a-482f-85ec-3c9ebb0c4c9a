/*
 * @Author: Rock
 * @Date: 2025-06-29 10:39:33
 * @LastEditors: Rock
 * @LastEditTime: 2025-07-27 17:48:11
 * @Description:
 */
// 定义帖子数据类型
export interface PostsList {
  id: number;
  userId: number;
  title: null | string;
  content: string;
  userGender: number;
  nickname: string;
  userNickname?: string; // 兼容字段
  userIsVerified?: boolean;
  userRole?: string;
  fileType: null | number | string; // 支持字符串类型
  fileList: null | string;
  fileListData: string[]; // 处理后的文件列表
  templateData?: any; // 模板数据
  categoryId: number;
  categoryName: string;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  collectCount: number;
  status: number;
  topStatus: number;
  isTop?: boolean;
  topExpireTime?: string;
  createdTime: string;
  updatedTime: null | string;
  isLiked?: boolean;
  isCollected?: boolean;
  isProcessing?: boolean; // 添加处理状态属性
}

export interface Comments {
  id: number;
  commentId: number;
  username: string;
  avatarText?: string;
  content: string;
  time?: string;
  createdTime: string;
  userId: number | string | null;
  parentUsername?: string; // 被回复用户的用户名
  replies?: Comments[];
  likeCount?: number; // 评论点赞数
  replyCount?: number; // 回复数量
}

// 导入媒体文件类型
import type { MediaFile } from './common';
