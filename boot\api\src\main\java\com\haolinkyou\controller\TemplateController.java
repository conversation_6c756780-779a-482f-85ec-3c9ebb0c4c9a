package com.haolinkyou.controller;

import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.CategoryTemplates;
import com.haolinkyou.service.ICategoryTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 模板管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/templates")
public class TemplateController {

    @Autowired
    private ICategoryTemplateService categoryTemplateService;

    /**
     * 根据分类ID获取模板列表
     */
    @GetMapping("/category/{categoryId}")
    public Result<List<CategoryTemplates>> getTemplatesByCategory(@PathVariable Long categoryId) {
        try {
            List<CategoryTemplates> templates = categoryTemplateService.getTemplatesByCategoryId(categoryId);
            return Result.success(templates);
        } catch (Exception e) {
            log.error("获取分类模板失败，分类ID: {}", categoryId, e);
            return Result.error("获取模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取分类的默认模板
     */
    @GetMapping("/category/{categoryId}/default")
    public Result<CategoryTemplates> getDefaultTemplate(@PathVariable Long categoryId) {
        try {
            CategoryTemplates template = categoryTemplateService.getDefaultTemplate(categoryId);
            if (template == null) {
                return Result.error("未找到默认模板");
            }
            return Result.success(template);
        } catch (Exception e) {
            log.error("获取默认模板失败，分类ID: {}", categoryId, e);
            return Result.error("获取默认模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板的完整配置（包含字段）
     */
    @GetMapping("/{templateId}/config")
    public Result<ICategoryTemplateService.TemplateConfig> getTemplateConfig(@PathVariable Long templateId) {
        try {
            ICategoryTemplateService.TemplateConfig config = categoryTemplateService.getTemplateConfig(templateId);
            if (config == null) {
                return Result.error("模板不存在");
            }
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取模板配置失败，模板ID: {}", templateId, e);
            return Result.error("获取模板配置失败: " + e.getMessage());
        }
    }

    /**
     * 保存模板配置
     */
    @PostMapping("/config")
    public Result<Boolean> saveTemplateConfig(
            @RequestBody ICategoryTemplateService.TemplateConfig templateConfig,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 这里可以添加权限检查，只有管理员才能保存模板配置
            // if (!isAdmin(userId)) {
            //     return Result.error("权限不足");
            // }

            boolean saved = categoryTemplateService.saveTemplateConfig(templateConfig);
            if (saved) {
                return Result.success(true, "保存成功");
            } else {
                return Result.error("保存失败");
            }
        } catch (Exception e) {
            log.error("保存模板配置失败", e);
            return Result.error("保存失败: " + e.getMessage());
        }
    }

    /**
     * 验证模板数据
     */
    @PostMapping("/{templateId}/validate")
    public Result<ICategoryTemplateService.ValidationResult> validateTemplateData(
            @PathVariable Long templateId,
            @RequestBody Map<String, Object> data,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            ICategoryTemplateService.ValidationResult result = categoryTemplateService.validateTemplateData(templateId, data);
            return Result.success(result);
        } catch (Exception e) {
            log.error("验证模板数据失败，模板ID: {}", templateId, e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 创建分类的默认模板
     */
    @PostMapping("/category/{categoryId}/default")
    public Result<Boolean> createDefaultTemplate(
            @PathVariable Long categoryId,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 这里可以添加权限检查，只有管理员才能创建模板
            // if (!isAdmin(userId)) {
            //     return Result.error("权限不足");
            // }

            boolean created = categoryTemplateService.createDefaultTemplate(categoryId);
            if (created) {
                return Result.success(true, "创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建默认模板失败，分类ID: {}", categoryId, e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板详情
     */
    @GetMapping("/{templateId}")
    public Result<CategoryTemplates> getTemplate(@PathVariable Long templateId) {
        try {
            CategoryTemplates template = categoryTemplateService.getById(templateId);
            if (template == null) {
                return Result.error("模板不存在");
            }
            return Result.success(template);
        } catch (Exception e) {
            log.error("获取模板详情失败，模板ID: {}", templateId, e);
            return Result.error("获取模板失败: " + e.getMessage());
        }
    }

    /**
     * 删除模板
     */
    @DeleteMapping("/{templateId}")
    public Result<Boolean> deleteTemplate(
            @PathVariable Long templateId,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 这里可以添加权限检查，只有管理员才能删除模板
            // if (!isAdmin(userId)) {
            //     return Result.error("权限不足");
            // }

            // 检查是否为默认模板
            CategoryTemplates template = categoryTemplateService.getById(templateId);
            if (template != null && template.getIsDefault()) {
                return Result.error("不能删除默认模板");
            }

            boolean deleted = categoryTemplateService.removeById(templateId);
            if (deleted) {
                return Result.success(true, "删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除模板失败，模板ID: {}", templateId, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 更新模板状态
     */
    @PutMapping("/{templateId}/status")
    public Result<Boolean> updateTemplateStatus(
            @PathVariable Long templateId,
            @RequestParam Integer status,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 这里可以添加权限检查，只有管理员才能更新模板状态
            // if (!isAdmin(userId)) {
            //     return Result.error("权限不足");
            // }

            CategoryTemplates template = new CategoryTemplates();
            template.setId(templateId);
            template.setStatus(status);

            boolean updated = categoryTemplateService.updateById(template);
            if (updated) {
                return Result.success(true, "更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新模板状态失败，模板ID: {}", templateId, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }
}
