<template>
  <view class="product-add-page">
    <!-- 页面头部 -->
    <up-navbar
      :title="isEdit ? '编辑商品' : '添加商品'"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 表单内容 -->
    <view class="form-section" :style="{ marginTop: mainContentPaddingTop }">
      <up-form :model="formData" ref="formRef" :rules="rules">
        <!-- 商品图片 -->
        <up-form-item label="商品图片" prop="image" :border-bottom="true">
          <view class="upload-section">
            <up-upload
              :fileList="fileList"
              @afterRead="afterRead"
              @delete="deleteFile"
              name="image"
              :maxCount="1"
              :previewImage="true"
              width="100"
              height="100"
            >
              <view class="upload-placeholder">
                <up-icon name="plus" size="30" color="#c0c4cc"></up-icon>
                <text class="upload-text">上传图片</text>
              </view>
            </up-upload>
          </view>
        </up-form-item>

        <!-- 商品名称 -->
        <up-form-item label="商品名称" prop="name" :border-bottom="true">
          <up-input 
            v-model="formData.name" 
            placeholder="请输入商品名称"
            :clearable="true"
          ></up-input>
        </up-form-item>

        <!-- 商品描述 -->
        <up-form-item label="商品描述" prop="description" :border-bottom="true">
          <up-textarea 
            v-model="formData.description"
            placeholder="请输入商品描述"
            :maxlength="200"
            count
            :autoHeight="true"
            :height="80"
          ></up-textarea>
        </up-form-item>

        <!-- 所需积分 -->
        <up-form-item label="所需积分" prop="points" :border-bottom="true">
          <up-input 
            v-model="formData.points" 
            placeholder="请输入所需积分"
            type="number"
            :clearable="true"
          ></up-input>
        </up-form-item>

        <!-- 库存数量 -->
        <up-form-item label="库存数量" prop="stock" :border-bottom="false">
          <up-input 
            v-model="formData.stock" 
            placeholder="请输入库存数量"
            type="number"
            :clearable="true"
          ></up-input>
        </up-form-item>
      </up-form>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <up-button 
        :text="isEdit ? '保存修改' : '确认添加'" 
        type="primary" 
        :loading="submitting"
        @click="handleSubmit"
        :customStyle="{ 
          width: '100%', 
          height: '50px',
          borderRadius: '25px',
          fontSize: '16px'
        }"
      ></up-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useSafeArea } from '@/utils/safeArea';

const { mainContentPaddingTop } = useSafeArea();

// 是否为编辑模式
const isEdit = ref(false);
const productId = ref('');

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  points: '',
  stock: '',
  image: ''
});

// 文件列表
const fileList = ref([]);

// 表单引用
const formRef = ref();

// 提交状态
const submitting = ref(false);

// 表单验证规则
const rules = {
  name: [
    {
      required: true,
      message: '请输入商品名称',
      trigger: ['blur', 'change']
    }
  ],
  description: [
    {
      required: true,
      message: '请输入商品描述',
      trigger: ['blur', 'change']
    }
  ],
  points: [
    {
      required: true,
      message: '请输入所需积分',
      trigger: ['blur', 'change']
    },
    {
      pattern: /^\d+$/,
      message: '积分必须为正整数',
      trigger: ['blur', 'change']
    }
  ],
  stock: [
    {
      required: true,
      message: '请输入库存数量',
      trigger: ['blur', 'change']
    },
    {
      pattern: /^\d+$/,
      message: '库存数量必须为正整数',
      trigger: ['blur', 'change']
    }
  ]
};

// 初始化数据
const initData = () => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options;
  
  if (options.id) {
    isEdit.value = true;
    productId.value = options.id;
    loadProductData(options.id);
  }
};

// 加载商品数据（编辑模式）
const loadProductData = async (id: string) => {
  try {
    // TODO: 调用真实API获取商品详情
    // const res = await getProductDetail(id);
    // formData.name = res.data.name;
    // formData.description = res.data.description;
    // formData.points = res.data.points.toString();
    // formData.stock = res.data.stock.toString();
    // formData.image = res.data.image;

    // 暂时清空表单，等待真实API接入
    console.log('编辑商品ID:', id);
  } catch (error) {
    console.error('加载商品数据失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  }
};

onMounted(() => {
  initData();
});

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 文件上传后处理
const afterRead = (event: any) => {
  const { file } = event;
  // 这里可以添加文件上传到服务器的逻辑
  formData.image = file.url;
  console.log('上传文件:', file);
};

// 删除文件
const deleteFile = (event: any) => {
  const { index } = event;
  fileList.value.splice(index, 1);
  formData.image = '';
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate();
    
    submitting.value = true;
    
    // 这里应该调用实际的API提交商品信息
    const submitData = {
      ...formData,
      points: parseInt(formData.points),
      stock: parseInt(formData.stock)
    };
    
    console.log('提交数据:', submitData);
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    uni.showToast({
      title: isEdit.value ? '修改成功' : '添加成功',
      icon: 'success'
    });
    
    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
    
  } catch (error) {
    console.error('表单验证失败:', error);
    uni.showToast({
      title: '请完善必填信息',
      icon: 'none'
    });
  } finally {
    submitting.value = false;
  }
};
</script>

<style lang="scss">
.product-add-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  // 表单区域
  .form-section {
    background: #fff;
    margin: 10px 20px;
    border-radius: 8px;
    padding: 0 20px;

    .upload-section {
      padding: 10px 0;

      .upload-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        background: #fafafa;

        .upload-text {
          font-size: 12px;
          color: #999;
          margin-top: 8px;
        }
      }
    }
  }

  // 提交按钮区域
  .submit-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 20px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
