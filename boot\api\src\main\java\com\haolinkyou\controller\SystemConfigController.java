package com.haolinkyou.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.SystemConfig;
import com.haolinkyou.entity.Users;
import com.haolinkyou.service.ISystemConfigService;
import com.haolinkyou.service.IUserService;
import com.haolinkyou.service.UserRoleService;
import com.haolinkyou.util.PermissionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/system-config")
public class SystemConfigController {

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private IUserService userService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private PermissionUtil permissionUtil;

    /**
     * 验证管理员权限
     */
    private boolean validateAdminPermission(HttpServletRequest request) {
        Long currentUserId = (Long) request.getAttribute("userId");
        if (currentUserId == null) {
            return false;
        }

        Users currentUser = userService.getById(currentUserId);
        return currentUser != null && userRoleService.isAdmin(currentUser.getUserRole());
    }

    /**
     * 分页查询系统配置
     */
    @GetMapping("/page")
    public Result<IPage<SystemConfig>> getConfigPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String groupName,
            @RequestParam(required = false) String keyword,
            HttpServletRequest request) {

        // 验证管理员权限
        if (!permissionUtil.isAdmin(request)) {
            return Result.error("权限不足，仅管理员可访问");
        }

        try {
            IPage<SystemConfig> result = systemConfigService.getConfigPage(page, pageSize, groupName, keyword);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("获取配置列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据分组获取配置列表
     */
    @GetMapping("/group/{groupName}")
    public Result<List<SystemConfig>> getConfigsByGroup(@PathVariable String groupName) {
        try {
            List<SystemConfig> configs = systemConfigService.getConfigsByGroup(groupName);
            return Result.success(configs);
        } catch (Exception e) {
            return Result.error("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 根据配置键获取配置值
     */
    @GetMapping("/value/{configKey}")
    public Result<String> getConfigValue(@PathVariable String configKey) {
        try {
            String value = systemConfigService.getConfigValue(configKey);
            return Result.success(value);
        } catch (Exception e) {
            return Result.error("获取配置值失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有配置分组
     */
    @GetMapping("/groups")
    public Result<List<String>> getAllGroups() {
        try {
            List<String> groups = systemConfigService.getAllGroups();
            return Result.success(groups);
        } catch (Exception e) {
            return Result.error("获取分组列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取配置的键值对映射
     */
    @GetMapping("/map")
    public Result<Map<String, String>> getConfigMap(@RequestParam(required = false) String groupName) {
        try {
            Map<String, String> configMap = systemConfigService.getConfigMap(groupName);
            return Result.success(configMap);
        } catch (Exception e) {
            return Result.error("获取配置映射失败: " + e.getMessage());
        }
    }

    /**
     * 添加新配置
     */
    @PostMapping
    public Result<Boolean> addConfig(@RequestBody SystemConfig config, HttpServletRequest request) {
        // 验证管理员权限
        if (!permissionUtil.isAdmin(request)) {
            return Result.error("权限不足，仅管理员可访问");
        }

        try {
            boolean success = systemConfigService.addConfig(config);
            return success ? Result.success(true) : Result.error("添加配置失败");
        } catch (Exception e) {
            return Result.error("添加配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新配置
     */
    @PutMapping("/{id}")
    public Result<Boolean> updateConfig(@PathVariable Long id, @RequestBody SystemConfig config, HttpServletRequest request) {
        // 验证管理员权限
        if (!validateAdminPermission(request)) {
            return Result.error("权限不足，仅管理员可访问");
        }

        try {
            config.setId(id);
            boolean success = systemConfigService.updateById(config);
            return success ? Result.success(true) : Result.error("更新配置失败");
        } catch (Exception e) {
            return Result.error("更新配置失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新配置
     */
    @PutMapping("/batch")
    public Result<Boolean> batchUpdateConfigs(@RequestBody List<SystemConfig> configs, HttpServletRequest request) {
        // 验证管理员权限
        if (!validateAdminPermission(request)) {
            return Result.error("权限不足，仅管理员可访问");
        }

        try {
            boolean success = systemConfigService.batchUpdateConfigs(configs);
            return success ? Result.success(true) : Result.error("批量更新配置失败");
        } catch (Exception e) {
            return Result.error("批量更新配置失败: " + e.getMessage());
        }
    }

    /**
     * 删除配置
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteConfig(@PathVariable Long id, HttpServletRequest request) {
        // 验证管理员权限
        if (!validateAdminPermission(request)) {
            return Result.error("权限不足，仅管理员可访问");
        }

        try {
            boolean success = systemConfigService.deleteConfig(id);
            return success ? Result.success(true) : Result.error("删除配置失败");
        } catch (Exception e) {
            return Result.error("删除配置失败: " + e.getMessage());
        }
    }
}
