<template>
  <view class="survey-form-template">
    <!-- 基础信息 -->
    <BaseTemplate
      ref="baseTemplateRef"
      :titleRequired="true"
      :titlePlaceholder="`请填写调查问卷主题`"
      :contentPlaceholder="`请详细说明调查问卷的发起背景和目的，以便大家更好的参与`"
      :showContact="false"
      :showMediaUpload="false"
      :initialData="formData"
      @update:data="handleBaseDataChange"
      @update:valid="handleBaseValidChange"
    />
    
    <!-- 调查问题 -->
    <view class="questions-section">
      <view class="section-title">调查问题</view>
      
      <view class="questions-list">
        <view 
          v-for="(question, questionIndex) in formData.questions" 
          :key="questionIndex"
          class="question-item"
        >
          <view class="question-header">
            <text class="question-number">问题 {{ questionIndex + 1 }}</text>
            <view
              v-if="formData.questions.length > 1"
              class="delete-question-btn"
              @click="removeQuestion(questionIndex)"
            >
              <up-icon name="trash" color="#ff4757" size="18"></up-icon>
            </view>
          </view>
          
          <!-- 问题标题 -->
          <view class="form-item">
            <view class="form-label">
              <text>问题标题</text>
              <text class="required">*</text>
            </view>
            <up-input
              v-model="question.title"
              placeholder="请输入问题标题"
              @input="validateQuestions"
              @blur="validateQuestions"
            />
            <view v-if="errors.questions && errors.questions[questionIndex]?.title" class="error-text">
              {{ errors.questions[questionIndex].title }}
            </view>
          </view>
          
          <!-- 问题类型 -->
          <view class="form-item">
            <view class="form-label">
              <text>问题类型</text>
              <text class="required">*</text>
            </view>
            <up-radio-group 
              v-model="question.type" 
              @change="handleQuestionTypeChange(questionIndex)"
            >
              <up-radio
                name="single"
                label="单选题"
                :customStyle="{ marginBottom: '8px' }"
              />
              <up-radio
                name="multiple"
                label="多选题"
                :customStyle="{ marginBottom: '8px' }"
              />
              <up-radio
                name="text"
                label="文本输入题"
                :customStyle="{ marginBottom: '8px' }"
              />
            </up-radio-group>
          </view>
          
          <!-- 是否必答 -->
          <view class="form-item">
            <view class="form-label">
              <text>是否必答</text>
            </view>
            <up-switch 
              v-model="question.required" 
              activeText="必答" 
              inactiveText="非必答"
            />
          </view>
          
          <!-- 选项设置（仅单选和多选题显示） -->
          <view v-if="question.type === 'single' || question.type === 'multiple'" class="form-item">
            <view class="form-label">
              <text>选项设置</text>
              <text class="required">*</text>
            </view>
            
            <view class="options-list">
              <view 
                v-for="(option, optionIndex) in question.options" 
                :key="optionIndex"
                class="option-item"
              >
                <view class="option-input-wrapper">
                  <up-input
                    v-model="option.text"
                    :placeholder="`选项 ${optionIndex + 1}`"
                    @input="validateQuestions"
                    @blur="validateQuestions"
                  />
                  <view
                    v-if="question.options.length > 2"
                    class="delete-option-btn"
                    @click="removeOption(questionIndex, optionIndex)"
                  >
                    <up-icon name="close-circle-fill" color="#ff4757" size="20"></up-icon>
                  </view>
                </view>
              </view>
            </view>
            
            <view class="option-actions">
              <up-button
                v-if="question.options.length < 10"
                type="primary"
                size="small"
                @click="addOption(questionIndex)"
              >
                添加选项
              </up-button>
            </view>
            
            <view v-if="errors.questions && errors.questions[questionIndex]?.options" class="error-text">
              {{ errors.questions[questionIndex].options }}
            </view>
            <view class="help-text">至少需要2个选项，最多10个选项</view>
          </view>
          
          <!-- 文本题提示 -->
          <view v-if="question.type === 'text'" class="text-question-tip">
            <up-icon name="info-circle" color="#3c9cff" size="16"></up-icon>
            <text>用户将通过文本输入框回答此问题</text>
          </view>
        </view>
      </view>
      
      <!-- 添加问题按钮 -->
      <view class="question-actions">
        <up-button
          v-if="formData.questions.length < 20"
          type="primary"
          size="normal"
          @click="addQuestion"
        >
          <up-icon name="plus" size="16" style="margin-right: 4px;"></up-icon>
          添加问题
        </up-button>
      </view>
      
      <view v-if="errors.questions && typeof errors.questions === 'string'" class="error-text">
        {{ errors.questions }}
      </view>
      <view class="help-text">最多可添加20个问题</view>
    </view>
    
    <!-- 调查设置 -->
    <view class="survey-settings">
      <view class="section-title">调查设置</view>
      
      <view class="form-item">
        <view class="form-label">
          <text>是否匿名</text>
        </view>
        <up-radio-group v-model="formData.anonymous">
          <up-radio
            name="true"
            label="匿名调查（不显示回答人）"
            :customStyle="{ marginBottom: '8px' }"
          />
          <up-radio
            name="false"
            label="实名调查（显示回答人）"
            :customStyle="{ marginBottom: '8px' }"
          />
        </up-radio-group>
      </view>
    </view>
    
    <!-- 截止时间 -->
    <view class="form-item">
      <view class="form-label">
        <text>截止时间</text>
      </view>
      <up-datetime-picker
        hasInput
        v-model:show="showDeadlinePicker"
        v-model="deadlineValue"
        mode="datetime"
        :minDate="minDate"
        placeholder="选择截止时间（可选）"
        format="YYYY-MM-DD HH:mm"
        @confirm="handleDeadlineConfirm"
        @cancel="() => showDeadlinePicker = false"
      />
      <view class="help-text">不填则长期有效</view>
    </view>
    
    <!-- 仅对谁可见 -->
    <view class="form-item">
      <view class="form-label">
        <text>仅对谁可见</text>
      </view>
      <up-checkbox-group v-model="formData.targetAudience" @change="handleTargetChange">
        <up-checkbox
          v-for="option in targetOptions"
          :key="option.value"
          :name="option.value"
          :label="option.label"
          :customStyle="{ marginBottom: '8px' }"
        />
      </up-checkbox-group>
      <view v-if="errors.targetAudience" class="error-text">{{ errors.targetAudience }}</view>
      <view class="help-text">不选择则所有用户可参与，选择特定对象则仅其可参与</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BaseTemplate from './BaseTemplate.vue'
import { getTargetAudienceOptionsAPI, type TargetAudienceOption } from '@/services/roleService'

// 问题接口
interface SurveyQuestion {
  title: string
  type: 'single' | 'multiple' | 'text'
  required: boolean
  options: { text: string }[]
}

// 表单数据
const formData = ref({
  title: '',
  content: '',
  images: [] as any[], // 兼容旧版本
  fileList: [] as any[], // 新版本文件列表
  questions: [
    {
      title: '',
      type: 'single' as 'single' | 'multiple' | 'text',
      required: false,
      options: [
        { text: '' },
        { text: '' }
      ]
    }
  ] as SurveyQuestion[],
  anonymous: 'true',
  deadline: '',
  targetAudience: [] as string[]
})

// 基础模板引用
const baseTemplateRef = ref()

// 验证错误
const errors = ref<any>({})

// 基础模板验证状态
const baseValid = ref(false)

// 目标对象选项（从接口动态获取）
const targetOptions = ref<TargetAudienceOption[]>([])

// 日期选择器
const showDeadlinePicker = ref(false)
const deadlineValue = ref<number>(Date.now() + 7 * 24 * 60 * 60 * 1000) // 默认一周后
const minDate = Date.now()

// 确保时间值始终是有效的时间戳
const ensureValidTimestamp = (value: any): number => {
  if (typeof value === 'number' && !isNaN(value) && value > 0) {
    return value
  }
  if (typeof value === 'string' && value) {
    const timestamp = new Date(value).getTime()
    if (!isNaN(timestamp)) {
      return timestamp
    }
  }
  return Date.now()
}

// 加载目标对象选项
const loadTargetOptions = async () => {
  try {
    const options = await getTargetAudienceOptionsAPI()
    targetOptions.value = options
  } catch (error) {
    console.error('加载目标对象选项失败:', error)
    targetOptions.value = []
  }
}

// 组件挂载时加载目标对象选项
onMounted(() => {
  loadTargetOptions()
  
  // 确保时间值是有效的
  deadlineValue.value = ensureValidTimestamp(deadlineValue.value)
})

// 监听时间值变化，确保始终是有效的时间戳
watch(deadlineValue, (newValue) => {
  if (typeof newValue !== 'number' || isNaN(newValue) || newValue <= 0) {
    deadlineValue.value = Date.now() + 7 * 24 * 60 * 60 * 1000
  }
})

// 处理截止时间确认
const handleDeadlineConfirm = (event: any) => {
  try {
    const timestamp = ensureValidTimestamp(event?.value)
    deadlineValue.value = timestamp
    
    // 格式化显示时间
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    const dateTimeStr = `${year}-${month}-${day} ${hour}:${minute}`
    
    formData.value.deadline = dateTimeStr
    showDeadlinePicker.value = false
  } catch (error) {
    console.error('截止时间确认处理失败:', error)
    showDeadlinePicker.value = false
  }
}

// 处理目标对象变化
const handleTargetChange = (selectedValues: string[]) => {
  formData.value.targetAudience = selectedValues
  delete errors.value.targetAudience
}

// 处理基础模板数据变化
const handleBaseDataChange = (data: Record<string, any>) => {
  Object.assign(formData.value, data)
  
  // 如果content为空，自动生成基于问题的内容
  if (!data.content || data.content.trim() === '') {
    generateContentFromQuestions()
  }
}

// 根据问题自动生成content内容
const generateContentFromQuestions = () => {
  if (formData.value.questions && formData.value.questions.length > 0) {
    const questionTitles = formData.value.questions
      .filter(q => q.title && q.title.trim())
      .map((q, index) => `${index + 1}. ${q.title}`)
      .join('\n')
    
    if (questionTitles) {
      formData.value.content = `本调查问卷包含以下问题：\n${questionTitles}`
    }
  }
}

// 处理基础模板验证状态变化
const handleBaseValidChange = (valid: boolean) => {
  baseValid.value = valid
}

// 添加问题
const addQuestion = () => {
  if (formData.value.questions.length < 20) {
    formData.value.questions.push({
      title: '',
      type: 'single',
      required: false,
      options: [
        { text: '' },
        { text: '' }
      ]
    })
  }
}

// 删除问题
const removeQuestion = (questionIndex: number) => {
  if (formData.value.questions.length > 1) {
    formData.value.questions.splice(questionIndex, 1)
    validateQuestions()
  }
}

// 处理问题类型变化
const handleQuestionTypeChange = (questionIndex: number) => {
  const question = formData.value.questions[questionIndex]
  
  // 如果切换到文本题，清空选项
  if (question.type === 'text') {
    question.options = []
  } else {
    // 如果切换到选择题，确保至少有2个选项
    if (question.options.length < 2) {
      question.options = [
        { text: '' },
        { text: '' }
      ]
    }
  }
  
  validateQuestions()
}

// 添加选项
const addOption = (questionIndex: number) => {
  const question = formData.value.questions[questionIndex]
  if (question.options.length < 10) {
    question.options.push({ text: '' })
  }
}

// 删除选项
const removeOption = (questionIndex: number, optionIndex: number) => {
  const question = formData.value.questions[questionIndex]
  if (question.options.length > 2) {
    question.options.splice(optionIndex, 1)
    validateQuestions()
  }
}

// 验证问题
const validateQuestions = () => {
  const questionErrors: any = {}
  let hasError = false
  
  formData.value.questions.forEach((question, questionIndex) => {
    const questionError: any = {}
    
    // 验证问题标题
    if (!question.title.trim()) {
      questionError.title = '请输入问题标题'
      hasError = true
    }
    
    // 验证选项（仅选择题）
    if (question.type === 'single' || question.type === 'multiple') {
      if (question.options.length < 2) {
        questionError.options = '至少需要2个选项'
        hasError = true
      } else {
        const validOptions = question.options.filter(option => option.text.trim())
        if (validOptions.length < 2) {
          questionError.options = '至少需要2个有效选项'
          hasError = true
        }
      }
    }
    
    if (Object.keys(questionError).length > 0) {
      questionErrors[questionIndex] = questionError
    }
  })
  
  if (hasError) {
    errors.value.questions = questionErrors
  } else {
    delete errors.value.questions
  }
  
  // 问题变化时，重新生成content
  generateContentFromQuestions()
  
  return !hasError
}

// 验证整个表单
const validate = () => {
  let isValid = true
  
  // 验证基础模板
  if (baseTemplateRef.value) {
    const baseData = baseTemplateRef.value.getFormData()
    Object.assign(formData.value, baseData)
    isValid = baseTemplateRef.value.validate() && isValid
  }
  
  // 验证问题
  isValid = validateQuestions() && isValid
  
  // 验证至少有一个问题
  if (formData.value.questions.length === 0) {
    errors.value.questions = '至少需要添加一个问题'
    isValid = false
  }
  
  return isValid
}

// 计算验证状态
const isValid = computed(() => {
  return baseValid.value && 
         formData.value.questions.length > 0 &&
         !errors.value.questions
})

// 监听验证状态变化
watch(isValid, (valid) => {
  emit('update:valid', valid)
})

// 监听表单数据变化，向父组件发送数据
watch(formData, (newData) => {
  emit('update:data', newData)
}, { deep: true })

// 组件接口
const emit = defineEmits<{
  'update:data': [Record<string, any>]
  'update:valid': [boolean]
}>()

// 重置表单数据
const resetForm = () => {
  formData.value = {
    title: '',
    content: '',
    images: [],
    fileList: [],
    questions: [
      {
        title: '',
        type: 'single' as 'single' | 'multiple' | 'text',
        required: false,
        options: [
          { text: '' },
          { text: '' }
        ]
      }
    ] as SurveyQuestion[],
    anonymous: 'true',
    deadline: '',
    targetAudience: []
  }

  // 清空错误信息
  errors.value = {}
}

defineExpose({
  validate,
  formData,
  isValid,
  resetForm
})
</script>

<style scoped lang="scss">
.survey-form-template {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #3c9cff;
}

.questions-section {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.questions-list {
  margin-bottom: 16px;
}

.question-item {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border-left: 4px solid #3c9cff;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.question-number {
  font-size: 16px;
  font-weight: bold;
  color: #3c9cff;
}

.delete-question-btn {
  padding: 4px;
  cursor: pointer;

  &:hover {
    opacity: 0.7;
  }

  &:active {
    transform: scale(0.95);
  }
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  margin-bottom: 12px;
}

.option-input-wrapper {
  display: flex;
  align-items: center;
  position: relative;
}

.option-input-wrapper :deep(.up-input) {
  flex: 1;
  padding-right: 40px;
}

.delete-option-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  padding: 4px;
  cursor: pointer;
  z-index: 10;

  &:hover {
    opacity: 0.7;
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }
}

.option-actions {
  margin-top: 12px;
}

.question-actions {
  text-align: center;
  margin-top: 16px;
}

.text-question-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #e3f2fd;
  border-radius: 6px;
  color: #1976d2;
  font-size: 14px;
}

.survey-settings {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff4757;
  margin-left: 4px;
}

.error-text {
  color: #ff4757;
  font-size: 12px;
  margin-top: 4px;
}

.help-text {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}
</style>
