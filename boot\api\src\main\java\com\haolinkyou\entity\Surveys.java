package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("surveys")
public class Surveys {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long postId;
    
    private String title;
    
    private String description;
    
    private String questions; // JSON格式存储问题列表
    
    private Boolean isAnonymous;
    
    private LocalDateTime endTime;
    
    private Integer status;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
    
    @TableLogic
    private Integer delFlag;
}