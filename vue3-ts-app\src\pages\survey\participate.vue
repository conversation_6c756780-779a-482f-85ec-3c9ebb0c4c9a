<template>
  <view class="survey-participate-page">
    <!-- 页面头部 -->
    <up-navbar
      title="参与调查"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 内容区域 -->
    <view class="content-area" :style="{ marginTop: mainContentPaddingTop }">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <up-loading-icon mode="spinner" size="40"></up-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 调查问卷内容 -->
      <view v-else-if="surveyData" class="survey-container">
        <!-- 问卷标题和描述 -->
        <view class="survey-header">
          <view class="survey-title">{{ surveyData.title }}</view>
          <view v-if="surveyData.description" class="survey-description">
            {{ surveyData.description }}
          </view>
          <view class="survey-info">
            <text class="info-item">发起人：{{ surveyData.creatorName }}</text>
            <text class="info-item" v-if="surveyData.endTime">
              截止时间：{{ formatDateTime(surveyData.endTime) }}
            </text>
            <text class="info-item">
              参与人数：{{ surveyData.participantCount || 0 }}
            </text>
          </view>
        </view>

        <!-- 已参与提示 -->
                <view v-if="surveyData.hasParticipated" class="participated-notice">
          <up-icon name="checkmark-circle-fill" size="24" color="#52c41a"></up-icon>
          <text class="notice-text">您已完成该问卷，感谢您的参与！目前共有 {{ surveyData.participantCount }} 人完成有效问卷</text>
        </view>

        <!-- 问题列表 -->
        <view class="questions-container">
          <view
            v-for="(question, index) in surveyData.questions"
            :key="question.id"
            class="question-item"
          >
            <view class="question-header">
              <text class="question-number">{{ index + 1 }}.</text>
              <text class="question-text">{{ question.questionText }}</text>
              <text v-if="question.isRequired" class="required-mark">*</text>
            </view>

            <!-- 单选题 -->
            <view v-if="question.questionType === 'single'" class="answer-container">
              <up-radio-group
                v-model="answers[question.id]"
                placement="column"
                @change="handleAnswerChange(question.id, $event)"
                :disabled="surveyData.hasParticipated"
              >
                <up-radio
                  v-for="option in question.options"
                  :key="option.id"
                  :name="option.id"
                  :label="option.text"
                  :disabled="surveyData.hasParticipated"
                  customStyle="margin-bottom: 12px;"
                ></up-radio>
              </up-radio-group>
            </view>

            <!-- 多选题 -->
            <view v-else-if="question.questionType === 'multiple'" class="answer-container">
              <up-checkbox-group
                v-model="answers[question.id]"
                placement="column"
                @change="handleAnswerChange(question.id, $event)"
                :disabled="surveyData.hasParticipated"
              >
                <up-checkbox
                  v-for="option in question.options"
                  :key="option.id"
                  :name="option.id"
                  :label="option.text"
                  :disabled="surveyData.hasParticipated"
                  customStyle="margin-bottom: 12px;"
                ></up-checkbox>
              </up-checkbox-group>
            </view>

            <!-- 文本输入题 -->
            <view v-else-if="question.questionType === 'text'" class="answer-container">
              <up-textarea
                v-model="answers[question.id]"
                :placeholder="surveyData.hasParticipated ? '您的答案' : '请输入您的答案...'"
                :maxlength="500"
                :showWordLimit="!surveyData.hasParticipated"
                :autoHeight="true"
                :disabled="surveyData.hasParticipated"
                border="surround"
                @input="handleAnswerChange(question.id, $event)"
              ></up-textarea>
            </view>
          </view>
        </view>

        <!-- 提交按钮 -->
                <view class="submit-container" v-if="!surveyData.hasParticipated">
          <up-button
            type="primary"
            size="large"
            @click="handleSubmit"
            :loading="submitting"
            :disabled="!canSubmit || surveyData.hasParticipated"
            customStyle="background-color: #5677fc; border-radius: 12px;"
          >
            {{ submitting ? '提交中...' : '提交问卷' }}
          </up-button>
        </view>
      </view>

      <!-- 错误状态 -->
      <view v-else class="error-container">
        <up-icon name="warning-fill" size="60" color="#ff6b6b"></up-icon>
        <text class="error-text">{{ errorMessage || '加载失败，请重试' }}</text>
        <up-button
          type="primary"
          size="small"
          @click="loadSurveyData"
          customStyle="margin-top: 20px;"
        >
          重新加载
        </up-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useSafeArea } from '@/utils/safeArea'
import { get, post } from '@/utils/http'
import { useMemberStore } from '@/stores'
import { formatDateTime } from '@/utils/timeFormat'

// 安全区域
const { mainContentPaddingTop } = useSafeArea()

// 用户状态
const memberStore = useMemberStore()

// 页面参数
const postId = ref<number>(0)

// 页面状态
const loading = ref(true)
const submitting = ref(false)
const errorMessage = ref('')

// 调查数据
const surveyData = ref<any>(null)
const answers = ref<Record<number, any>>({})

// 获取页面参数
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.id) {
    postId.value = parseInt(options.id)
    loadSurveyData()
  } else {
    errorMessage.value = '参数错误'
    loading.value = false
  }
})

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 加载调查数据
const loadSurveyData = async () => {
  loading.value = true
  errorMessage.value = ''

  try {
    // 首先获取调查详情和用户参与状态
    const surveyDetailResponse = await get(`/survey/detail?postId=${postId.value}`)
    
    if (surveyDetailResponse.success && surveyDetailResponse.data) {
      const surveyDetail = surveyDetailResponse.data
      const survey = surveyDetail.survey
      
      // 解析问题数据
      let questions = []
      if (typeof survey.questions === 'string') {
        try {
          questions = JSON.parse(survey.questions)
        } catch (error) {
          console.error('解析问题数据失败:', error)
        }
      } else if (survey.questions) {
        questions = survey.questions
      }

      // 构建调查数据
      surveyData.value = {
        id: survey.id,
        title: survey.title,
        description: survey.description,
        creatorName: '系统', // 可以从帖子信息获取
        endTime: survey.endTime,
        participantCount: surveyDetail.participantCount || 0,
        questions: questions,
        isAnonymous: survey.isAnonymous,
        hasParticipated: surveyDetail.hasParticipated
      }

      // 如果用户已参与，回显答案
      if (surveyDetail.hasParticipated && surveyDetail.userAnswer) {
        const userAnswers = surveyDetail.userAnswer
        if (Array.isArray(userAnswers)) {
          userAnswers.forEach((answerItem: any) => {
            answers.value[answerItem.questionId] = answerItem.answer
          })
        }
      } else {
        // 初始化答案对象
        surveyData.value.questions.forEach((question: any) => {
          if (question.questionType === 'multiple') {
            answers.value[question.id] = []
          } else {
            answers.value[question.id] = ''
          }
        })
      }

      console.log('调查数据加载成功:', surveyData.value)
    } else {
      throw new Error(surveyDetailResponse.message || '获取调查数据失败')
    }
  } catch (error) {
    console.error('加载调查数据失败:', error)
    errorMessage.value = error.message || '网络错误，请重试'
  } finally {
    loading.value = false
  }
}

// 处理答案变化
const handleAnswerChange = (questionId: number, value: any) => {
  console.log('答案变化:', questionId, value)
  answers.value[questionId] = value
}

// 检查是否可以提交
const canSubmit = computed(() => {
  if (!surveyData.value || surveyData.value.hasParticipated) return false

  // 检查必填问题是否都已回答
  for (const question of surveyData.value.questions) {
    if (question.isRequired) {
      const answer = answers.value[question.id]
      
      if (question.questionType === 'multiple') {
        if (!Array.isArray(answer) || answer.length === 0) {
          return false
        }
      } else {
        if (!answer || (typeof answer === 'string' && !answer.trim())) {
          return false
        }
      }
    }
  }

  return true
})

// 提交问卷
const handleSubmit = async () => {
  // 检查登录状态
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  // 检查是否已参与
  if (surveyData.value.hasParticipated) {
    uni.showToast({
      title: '您已参与过此调查',
      icon: 'none'
    })
    return
  }

  // 检查是否可以提交
  if (!canSubmit.value) {
    uni.showToast({
      title: '请完成所有必填问题',
      icon: 'none'
    })
    return
  }

  submitting.value = true

  try {
    // 构建提交数据
    const submitData = {
      postId: postId.value,
      surveyId: surveyData.value.id,
      answers: Object.keys(answers.value).map(questionId => ({
        questionId: parseInt(questionId),
        answer: answers.value[parseInt(questionId)]
      }))
    }

    console.log('提交调查数据:', submitData)

    const response = await post('/survey/submit', submitData)

    if (response.success) {
      uni.showToast({
        title: '提交成功',
        icon: 'success'
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      throw new Error(response.message || '提交失败')
    }
  } catch (error) {
    console.error('提交调查失败:', error)
    uni.showToast({
      title: error.message || '提交失败，请重试',
      icon: 'none'
    })
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped lang="scss">
.survey-participate-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.content-area {
  padding: 0 20px 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #666;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-text {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}

.survey-container {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.survey-header {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.survey-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.4;
}

.survey-description {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
}

.survey-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-item {
  font-size: 12px;
  color: #999;
}

.questions-container {
  padding: 0;
}

.question-item {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.question-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.question-number {
  font-size: 16px;
  font-weight: 600;
  color: #5677fc;
  margin-right: 8px;
  flex-shrink: 0;
}

.question-text {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

.required-mark {
  color: #ff4757;
  font-size: 16px;
  margin-left: 4px;
}

.answer-container {
  margin-left: 24px;
}

.participated-notice {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
  margin-bottom: 20px;
}

.notice-text {
  margin-left: 8px;
  font-size: 14px;
  color: #52c41a;
}

.submit-container {
  padding: 24px;
  background-color: #f8f9fa;
}
</style>