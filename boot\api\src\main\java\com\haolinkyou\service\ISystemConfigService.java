package com.haolinkyou.service;

import com.haolinkyou.entity.SystemConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Map;

/**
 * 系统配置服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
public interface ISystemConfigService extends IService<SystemConfig> {

    /**
     * 根据配置键获取配置值
     * 
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);

    /**
     * 根据配置键获取配置值，如果不存在则返回默认值
     * 
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 根据配置键获取整数配置值
     * 
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 整数配置值
     */
    Integer getIntValue(String configKey, Integer defaultValue);

    /**
     * 根据配置键获取布尔配置值
     * 
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 布尔配置值
     */
    Boolean getBooleanValue(String configKey, Boolean defaultValue);

    /**
     * 更新配置值
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 是否成功
     */
    boolean updateConfigValue(String configKey, String configValue);

    /**
     * 分页查询系统配置
     *
     * @param page 页码
     * @param pageSize 页大小
     * @param groupName 分组名称
     * @param keyword 关键词
     * @return 分页结果
     */
    IPage<SystemConfig> getConfigPage(Integer page, Integer pageSize, String groupName, String keyword);

    /**
     * 根据分组获取配置列表
     *
     * @param groupName 分组名称
     * @return 配置列表
     */
    List<SystemConfig> getConfigsByGroup(String groupName);

    /**
     * 获取所有配置分组
     *
     * @return 分组列表
     */
    List<String> getAllGroups();

    /**
     * 获取配置的键值对映射
     *
     * @param groupName 分组名称（可选）
     * @return 配置映射
     */
    Map<String, String> getConfigMap(String groupName);

    /**
     * 添加新配置
     *
     * @param config 配置对象
     * @return 是否成功
     */
    boolean addConfig(SystemConfig config);

    /**
     * 批量更新配置
     *
     * @param configs 配置列表
     * @return 是否成功
     */
    boolean batchUpdateConfigs(List<SystemConfig> configs);

    /**
     * 删除配置
     *
     * @param id 配置ID
     * @return 是否成功
     */
    boolean deleteConfig(Long id);
}