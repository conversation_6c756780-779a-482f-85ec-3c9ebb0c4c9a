package com.haolinkyou.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.UserPoints;
import com.haolinkyou.service.IPointsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 积分管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@RestController
@RequestMapping("/api/points")
public class PointsController {

    @Autowired
    private IPointsService pointsService;

    /**
     * 获取用户积分余额
     */
    @GetMapping("/balance")
    public Result<Integer> getPointsBalance(HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            Integer balance = pointsService.getUserPointsBalance(userId);
            return Result.success(balance);
        } catch (Exception e) {
            return Result.error("获取积分余额失败: " + e.getMessage());
        }
    }

    /**
     * 获取积分记录（分页）
     */
    @GetMapping("/records")
    public Result<Page<UserPoints>> getPointsRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String type,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 参数验证
            if (page < 1) {
                return Result.error("页码必须大于0");
            }
            if (size < 1 || size > 100) {
                return Result.error("每页大小必须在1-100之间");
            }

            Page<UserPoints> records = pointsService.getUserPointsRecords(userId, page, size, type);
            return Result.success(records);
        } catch (Exception e) {
            return Result.error("获取积分记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取积分规则
     */
    @GetMapping("/rules")
    public Result<IPointsService.PointsRules> getPointsRules() {
        try {
            IPointsService.PointsRules rules = pointsService.getPointsRules();
            return Result.success(rules);
        } catch (Exception e) {
            return Result.error("获取积分规则失败: " + e.getMessage());
        }
    }

    /**
     * 获取今日积分统计
     */
    @GetMapping("/today")
    public Result<TodayPointsInfo> getTodayPointsInfo(HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            Integer todayTotal = pointsService.getTodayPointsTotal(userId);
            Integer remaining = pointsService.getRemainingDailyPoints(userId);
            IPointsService.PointsRules rules = pointsService.getPointsRules();

            TodayPointsInfo info = new TodayPointsInfo();
            info.setTodayTotal(todayTotal);
            info.setRemaining(remaining);
            info.setDailyLimit(rules.getDailyLimit());

            return Result.success(info);
        } catch (Exception e) {
            return Result.error("获取今日积分信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取积分统计信息
     */
    @GetMapping("/statistics")
    public Result<PointsStatistics> getPointsStatistics(HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            Integer balance = pointsService.getUserPointsBalance(userId);
            Integer todayTotal = pointsService.getTodayPointsTotal(userId);
            Integer remaining = pointsService.getRemainingDailyPoints(userId);
            IPointsService.PointsRules rules = pointsService.getPointsRules();

            PointsStatistics statistics = new PointsStatistics();
            statistics.setBalance(balance);
            statistics.setTodayTotal(todayTotal);
            statistics.setRemaining(remaining);
            statistics.setDailyLimit(rules.getDailyLimit());

            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取积分统计失败: " + e.getMessage());
        }
    }

    /**
     * 今日积分信息
     */
    public static class TodayPointsInfo {
        private Integer todayTotal;
        private Integer remaining;
        private Integer dailyLimit;

        // Getters and Setters
        public Integer getTodayTotal() { return todayTotal; }
        public void setTodayTotal(Integer todayTotal) { this.todayTotal = todayTotal; }

        public Integer getRemaining() { return remaining; }
        public void setRemaining(Integer remaining) { this.remaining = remaining; }

        public Integer getDailyLimit() { return dailyLimit; }
        public void setDailyLimit(Integer dailyLimit) { this.dailyLimit = dailyLimit; }
    }

    /**
     * 积分统计信息
     */
    public static class PointsStatistics {
        private Integer balance;
        private Integer todayTotal;
        private Integer remaining;
        private Integer dailyLimit;

        // Getters and Setters
        public Integer getBalance() { return balance; }
        public void setBalance(Integer balance) { this.balance = balance; }

        public Integer getTodayTotal() { return todayTotal; }
        public void setTodayTotal(Integer todayTotal) { this.todayTotal = todayTotal; }

        public Integer getRemaining() { return remaining; }
        public void setRemaining(Integer remaining) { this.remaining = remaining; }

        public Integer getDailyLimit() { return dailyLimit; }
        public void setDailyLimit(Integer dailyLimit) { this.dailyLimit = dailyLimit; }
    }
}