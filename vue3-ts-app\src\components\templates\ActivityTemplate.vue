<template>
  <view class="activity-template">
    <!-- 基础信息 -->
    <BaseTemplate
      ref="baseTemplateRef"
      :titleRequired="true"
      titlePlaceholder="请输入活动标题（如：户外运动、沙龙等）"
      contentPlaceholder="请详细描述活动内容、意义和注意事项，让大家更好地了解活动"
      :showMediaUpload="true"
      :mediaRequired="false"
      mediaLabel="活动图片"
      mediaHelpText="图片：最多9张，单张不超过10MB；视频：最多1个，不超过100MB"
      :maxImageCount="9"
      :showMediaTypeSwitch="true"
      :defaultMediaType="0"
      :initialData="formData"
      @update:data="handleBaseDataChange"
      @update:valid="handleBaseValidChange"
    />
    
    <!-- 活动信息 -->
    <view class="activity-info">
      <view class="section-title">活动信息</view>
      
      <!-- 活动类型 -->
      <view class="form-item">
        <view class="form-label">
          <text>活动类型</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper" @click="showActivityTypeSheet = true">
          <up-input
            v-model="activityTypeText"
            disabled
            disabledColor="#ffffff"
            placeholder="请选择活动类型"
            border="none"
            readonly
          >
            <template #suffix>
              <up-icon name="arrow-right"></up-icon>
            </template>
          </up-input>
        </view>
        <view v-if="errors.activityType" class="error-text">{{ errors.activityType }}</view>
      </view>

      <!-- 活动类型选择弹窗 -->
      <up-action-sheet
        :show="showActivityTypeSheet"
        :actions="activityTypeActions"
        title="请选择活动类型"
        @close="showActivityTypeSheet = false"
        @select="handleActivityTypeSelect"
      />
      
      <!-- 活动地点 -->
      <view class="form-item">
        <view class="form-label">
          <text>活动地点</text>
          <text class="required">*</text>
        </view>
        <up-input
          v-model="formData.location"
          placeholder="请输入具体的活动地点"
          @input="validateLocation"
          @blur="validateLocation"
        />
        <view v-if="errors.location" class="error-text">{{ errors.location }}</view>
        <view class="help-text">请提供详细地址，方便大家找到</view>
      </view>
      
      <!-- 活动时间 -->
      <view class="form-item">
        <view class="form-label">
          <text>活动时间</text>
          <text class="required">*</text>
        </view>
        <view class="time-section">
          <!-- 活动日期 -->
          <view class="time-row">
            <view class="time-label">活动日期：</view>
            <up-datetime-picker
              v-model="dateValue"
              mode="date"
              :minDate="minDate"
              hasInput
              placeholder="选择活动日期"
              @confirm="handleDateConfirm"
            />
          </view>

          <!-- 开始时间 -->
          <view class="time-row">
            <view class="time-label">开始时间：</view>
            <up-datetime-picker
              v-model="startTimeValue"
              mode="time"
              hasInput
              placeholder="选择开始时间"
              @confirm="handleStartTimeConfirm"
            />
          </view>

          <!-- 结束时间 -->
          <view class="time-row">
            <view class="time-label">结束时间：</view>
            <up-datetime-picker
              v-model="endTimeValue"
              mode="time"
              hasInput
              placeholder="选择结束时间"
              @confirm="handleEndTimeConfirm"
            />
          </view>
        </view>
        <view v-if="errors.activityDate" class="error-text">{{ errors.activityDate }}</view>
        <view v-if="errors.startTime" class="error-text">{{ errors.startTime }}</view>
        <view v-if="errors.endTime" class="error-text">{{ errors.endTime }}</view>
      </view>
      
      <!-- 参与人数 -->
      <view class="form-item">
        <view class="form-label">
          <text>参与人数</text>
          <text class="required">*</text>
        </view>
        <view class="participant-section">
          <view class="participant-row">
            <view class="participant-label">最少人数：</view>
            <up-input
              v-model="formData.minParticipants"
              type="number"
              placeholder="最少参与人数"
              @input="validateParticipants"
              @blur="validateParticipants"
            />
          </view>
          <view class="participant-row">
            <view class="participant-label">最多人数：</view>
            <up-input
              v-model="formData.maxParticipants"
              type="number"
              placeholder="最多参与人数"
              @input="validateParticipants"
              @blur="validateParticipants"
            />
          </view>
        </view>
        <view v-if="errors.minParticipants" class="error-text">{{ errors.minParticipants }}</view>
        <view v-if="errors.maxParticipants" class="error-text">{{ errors.maxParticipants }}</view>
        <view class="help-text">设置合理的人数范围，确保活动顺利进行</view>
      </view>
      
      <!-- 费用信息 -->
      <view class="form-item">
        <view class="form-label">
          <text>费用说明</text>
        </view>
        <view class="input-wrapper" @click="showFeeTypeSheet = true">
          <up-input
            v-model="feeTypeText"
            disabled
            disabledColor="#ffffff"
            placeholder="请选择费用类型"
            border="none"
            readonly
          >
            <template #suffix>
              <up-icon name="arrow-right"></up-icon>
            </template>
          </up-input>
        </view>

        <!-- 费用详情 -->
        <view v-if="formData.feeType !== 'free'" class="fee-details">
          <up-input
            v-model="formData.feeAmount"
            type="number"
            :placeholder="formData.feeType === 'aa' ? '预估人均费用（元）' : '每人费用（元）'"
            @input="validateFee"
            @blur="validateFee"
          />
          <up-textarea
            v-model="formData.feeDescription"
            placeholder="费用说明（包含哪些项目、如何支付等）"
            :maxlength="200"
            count
          />
        </view>
        <view v-if="errors.feeAmount" class="error-text">{{ errors.feeAmount }}</view>
      </view>

      <!-- 费用类型选择弹窗 -->
      <up-action-sheet
        :show="showFeeTypeSheet"
        :actions="feeTypeActions"
        title="请选择费用类型"
        @close="showFeeTypeSheet = false"
        @select="handleFeeTypeSelect"
      />
    </view>
    
    <!-- 报名设置 -->
    <view class="registration-settings">
      <view class="section-title">报名设置</view>
      
      <!-- 报名截止时间 -->
      <view class="form-item">
        <view class="form-label">
          <text>报名截止时间</text>
        </view>
        <up-datetime-picker
          v-model="deadlineValue"
          mode="datetime"
          :minDate="minDate"
          hasInput
          placeholder="选择报名截止时间（可选）"
          @confirm="handleDeadlineConfirm"
        />
        <view class="help-text">不填则活动开始前都可报名</view>
      </view>
      
      <!-- 联系方式 -->
      <view class="form-item">
        <view class="form-label">
          <text>联系方式</text>
          <text class="required">*</text>
        </view>
        <up-input
          v-model="formData.contactInfo"
          placeholder="请输入联系电话或微信号"
          @input="validateContact"
          @blur="validateContact"
        />
        <view v-if="errors.contactInfo" class="error-text">{{ errors.contactInfo }}</view>
        <view class="help-text">方便参与者联系和沟通</view>
      </view>
      
      <!-- 特殊要求 -->
      <view class="form-item">
        <view class="form-label">
          <text>特殊要求</text>
        </view>
        <up-textarea
          v-model="formData.requirements"
          placeholder="请说明参与活动的特殊要求（如：需要自带装备、身体条件要求等）"
          :maxlength="300"
          count
        />
        <view class="help-text">帮助参与者提前准备</view>
      </view>
    </view>
    
    <!-- 仅对谁可见 -->
    <view class="form-item">
      <view class="form-label">
        <text>仅对谁可见</text>
      </view>
      <up-checkbox-group v-model="formData.targetAudience" @change="handleTargetChange">
        <up-checkbox
          v-for="option in targetOptions"
          :key="option.value"
          :name="option.value"
          :label="option.label"
          :customStyle="{ marginBottom: '8px' }"
        />
      </up-checkbox-group>
      <view v-if="errors.targetAudience" class="error-text">{{ errors.targetAudience }}</view>
      <view class="help-text">不选择则所有用户可见，选择特定对象则仅对其可见</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BaseTemplate from './BaseTemplate.vue'
import { getTargetAudienceOptionsAPI, type TargetAudienceOption } from '@/services/roleService'


// 表单数据
const formData = ref({
  title: '',
  content: '',
  images: [] as any[], // 兼容旧版本
  fileList: [] as any[], // 新版本文件列表
  contact: '',
  activityType: '',
  location: '',
  activityDate: '',
  startTime: '',
  endTime: '',
  minParticipants: '',
  maxParticipants: '',
  feeType: 'free',
  feeAmount: '',
  feeDescription: '',
  deadline: '',
  contactInfo: '',
  requirements: '',
  targetAudience: [] as string[]
})

// 基础模板引用
const baseTemplateRef = ref()

// 验证错误
const errors = ref<any>({})

// 基础模板验证状态
const baseValid = ref(false)

// 目标对象选项（从接口动态获取）
const targetOptions = ref<TargetAudienceOption[]>([])

// 活动类型选项 - 用于action-sheet
const activityTypeActions = [
  { name: '户外运动（徒步、骑行、跑步等）', value: 'outdoor' },
  { name: '室内活动（沙龙、读书会、手工等）', value: 'indoor' },
  { name: '体育运动（篮球、羽毛球、游泳等）', value: 'sports' },
  { name: '文化活动（观影、展览、演出等）', value: 'cultural' },
  { name: '其他活动', value: 'other' }
]

// 活动类型弹窗显示状态
const showActivityTypeSheet = ref(false)

// 活动类型显示文本
const activityTypeText = computed(() => {
  const action = activityTypeActions.find(item => item.value === formData.value.activityType)
  return action ? action.name : ''
})

// 处理活动类型选择
const handleActivityTypeSelect = (item: any) => {
  console.log('活动类型选择:', item)
  formData.value.activityType = item.value
  showActivityTypeSheet.value = false
  delete errors.value.activityType
}

// 费用类型选项 - 用于action-sheet
const feeTypeActions = [
  { name: '免费活动', value: 'free' },
  { name: 'AA制（费用平摊）', value: 'aa' },
  { name: '固定费用', value: 'fixed' }
]

// 费用类型弹窗显示状态
const showFeeTypeSheet = ref(false)

// 费用类型显示文本
const feeTypeText = computed(() => {
  const action = feeTypeActions.find(item => item.value === formData.value.feeType)
  return action ? action.name : ''
})

// 处理费用类型选择
const handleFeeTypeSelect = (item: any) => {
  console.log('费用类型选择:', item)
  formData.value.feeType = item.value
  showFeeTypeSheet.value = false

  // 如果选择免费活动，清空费用相关字段
  if (item.value === 'free') {
    formData.value.feeAmount = ''
    formData.value.feeDescription = ''
  }

  delete errors.value.feeAmount
}

// 时间选择器的值

// 时间选择器的值 - 根据uview-plus文档，time模式需要字符串格式
const dateValue = ref<number>(Date.now() + 24 * 60 * 60 * 1000) // 默认明天
const startTimeValue = ref<string>('09:00') // 默认上午9点
const endTimeValue = ref<string>('11:00') // 默认上午11点
const deadlineValue = ref<number>(Date.now() + 12 * 60 * 60 * 1000) // 默认12小时后

const minDate = Date.now()

// 确保时间值始终是有效的时间戳
const ensureValidTimestamp = (value: any): number => {
  if (typeof value === 'number' && !isNaN(value) && value > 0) {
    return value
  }
  if (typeof value === 'string' && value) {
    const timestamp = new Date(value).getTime()
    if (!isNaN(timestamp)) {
      return timestamp
    }
  }
  return Date.now()
}

// 加载目标对象选项
const loadTargetOptions = async () => {
  try {
    const options = await getTargetAudienceOptionsAPI()
    targetOptions.value = options
  } catch (error) {
    console.error('加载目标对象选项失败:', error)
    targetOptions.value = []
  }
}

// 组件挂载时加载目标对象选项
onMounted(() => {
  loadTargetOptions()
})



// 格式化日期
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}



// 格式化日期时间
const formatDateTime = (timestamp: number) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hour}:${minute}`
}

// 处理日期确认
const handleDateConfirm = (event: any) => {
  try {
    const timestamp = ensureValidTimestamp(event?.value)
    dateValue.value = timestamp
    formData.value.activityDate = formatDate(timestamp)
    validateActivityDate()
  } catch (error) {
    console.error('日期确认处理失败:', error)
  }
}

// 处理开始时间确认
const handleStartTimeConfirm = (event: any) => {
  try {
    const timeStr = event?.value || event
    if (typeof timeStr === 'string' && timeStr.includes(':')) {
      startTimeValue.value = timeStr
      formData.value.startTime = timeStr
    }
    validateStartTime()
  } catch (error) {
    console.error('开始时间确认处理失败:', error)
  }
}

// 处理结束时间确认
const handleEndTimeConfirm = (event: any) => {
  try {
    const timeStr = event?.value || event
    if (typeof timeStr === 'string' && timeStr.includes(':')) {
      endTimeValue.value = timeStr
      formData.value.endTime = timeStr
    }
    validateEndTime()
  } catch (error) {
    console.error('结束时间确认处理失败:', error)
  }
}

// 处理截止时间确认
const handleDeadlineConfirm = (event: any) => {
  try {
    const timestamp = ensureValidTimestamp(event?.value)
    deadlineValue.value = timestamp
    formData.value.deadline = formatDateTime(timestamp)
  } catch (error) {
    console.error('截止时间确认处理失败:', error)
  }
}

// 处理基础模板数据变化
const handleBaseDataChange = (data: Record<string, any>) => {
  Object.assign(formData.value, data)
}

// 处理基础模板验证状态变化
const handleBaseValidChange = (valid: boolean) => {
  baseValid.value = valid
}





// 处理目标对象变化
const handleTargetChange = (selectedValues: string[]) => {
  formData.value.targetAudience = selectedValues
  delete errors.value.targetAudience
}

// 验证函数
const validateLocation = () => {
  if (!formData.value.location) {
    errors.value.location = '请输入活动地点'
    return false
  } else {
    delete errors.value.location
    return true
  }
}

const validateActivityDate = () => {
  if (!formData.value.activityDate) {
    errors.value.activityDate = '请选择活动日期'
    return false
  } else {
    delete errors.value.activityDate
    return true
  }
}

const validateStartTime = () => {
  if (!formData.value.startTime) {
    errors.value.startTime = '请选择开始时间'
    return false
  } else {
    delete errors.value.startTime
    return true
  }
}

const validateEndTime = () => {
  if (!formData.value.endTime) {
    errors.value.endTime = '请选择结束时间'
    return false
  } else {
    // 验证结束时间是否晚于开始时间
    if (formData.value.startTime && formData.value.endTime <= formData.value.startTime) {
      errors.value.endTime = '结束时间必须晚于开始时间'
      return false
    } else {
      delete errors.value.endTime
      return true
    }
  }
}

const validateParticipants = () => {
  let isValid = true
  
  // 验证最少人数
  if (!formData.value.minParticipants) {
    errors.value.minParticipants = '请输入最少参与人数'
    isValid = false
  } else if (parseInt(formData.value.minParticipants) < 1) {
    errors.value.minParticipants = '最少人数不能小于1'
    isValid = false
  } else {
    delete errors.value.minParticipants
  }
  
  // 验证最多人数
  if (!formData.value.maxParticipants) {
    errors.value.maxParticipants = '请输入最多参与人数'
    isValid = false
  } else if (parseInt(formData.value.maxParticipants) < 1) {
    errors.value.maxParticipants = '最多人数不能小于1'
    isValid = false
  } else if (formData.value.minParticipants && parseInt(formData.value.maxParticipants) < parseInt(formData.value.minParticipants)) {
    errors.value.maxParticipants = '最多人数不能小于最少人数'
    isValid = false
  } else {
    delete errors.value.maxParticipants
  }
  
  return isValid
}

const validateFee = () => {
  if (formData.value.feeType !== 'free' && !formData.value.feeAmount) {
    errors.value.feeAmount = '请输入费用金额'
    return false
  } else {
    delete errors.value.feeAmount
    return true
  }
}

const validateContact = () => {
  if (!formData.value.contactInfo) {
    errors.value.contactInfo = '请输入联系方式'
    return false
  } else {
    delete errors.value.contactInfo
    return true
  }
}

// 验证整个表单
const validate = () => {
  let isValid = true
  
  // 验证基础模板
  if (baseTemplateRef.value) {
    const baseData = baseTemplateRef.value.getFormData()
    Object.assign(formData.value, baseData)
    isValid = baseTemplateRef.value.validate() && isValid
  }
  
  // 验证活动类型
  if (!formData.value.activityType) {
    errors.value.activityType = '请选择活动类型'
    isValid = false
  } else {
    delete errors.value.activityType
  }
  
  // 验证其他字段
  isValid = validateLocation() && isValid
  isValid = validateActivityDate() && isValid
  isValid = validateStartTime() && isValid
  isValid = validateEndTime() && isValid
  isValid = validateParticipants() && isValid
  isValid = validateFee() && isValid
  isValid = validateContact() && isValid
  
  return isValid
}

// 计算验证状态
const isValid = computed(() => {
  const valid = baseValid.value &&
         !!formData.value.activityType &&
         !!formData.value.location &&
         !!formData.value.activityDate &&
         !!formData.value.startTime &&
         !!formData.value.endTime &&
         !!formData.value.minParticipants &&
         !!formData.value.maxParticipants &&
         !!formData.value.contactInfo &&
         Object.keys(errors.value).length === 0
  return valid
})

// 监听验证状态变化
watch(isValid, (valid) => {
  emit('update:valid', valid)
})

// 组件接口
const emit = defineEmits<{
  'update:valid': [boolean]
}>()

defineExpose({
  validate,
  formData,
  isValid
})
</script>

<style scoped lang="scss">
.activity-template {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #52c41a;
}

.activity-info, .registration-settings {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.time-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.time-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-label {
  min-width: 80px;
  font-size: 14px;
  color: #666;
}

.participant-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.participant-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.participant-label {
  min-width: 80px;
  font-size: 14px;
  color: #666;
}

.fee-details {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff4757;
  margin-left: 4px;
}

.error-text {
  color: #ff4757;
  font-size: 12px;
  margin-top: 4px;
}

.help-text {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}
</style>
