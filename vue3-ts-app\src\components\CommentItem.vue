<template>
  <view class="comment-item">
    <!-- 主评论 -->
    <view class="main-comment">
      <view class="comment-avatar">
        <up-avatar :text="comment.avatarText" :size="36" randomBgColor />
      </view>
      
      <view class="comment-content">
        <view class="comment-header">
          <view class="user-info">
            <text class="username">{{ comment.username }}</text>
            <up-tag
              v-if="comment.userId === postAuthorId"
              text="作者"
              color="#ff6b6b"
              size="mini"
              plain
              plainFill
              borderColor="transparent"
            />
            <up-tag
              v-if="comment.userRole && comment.userRole !== 'guest'"
              :text="getRoleText(comment.userRole)"
              color="#007bff"
              size="mini"
              plain
              plainFill
              borderColor="transparent"
            />
          </view>
          
          <view class="comment-time">
            {{ formatTime(comment.createdTime) }}
          </view>
        </view>
        
        <view class="comment-text">
          {{ comment.content }}
        </view>
        
        <view class="comment-actions">
          <view class="action-item" @click="handleLike">
            <up-icon
              :name="comment.isLiked ? 'thumb-up-fill' : 'thumb-up'"
              size="14"
              :color="comment.isLiked ? '#ff6b6b' : '#999'"
            />
            <text class="action-text">{{ comment.likeCount || 0 }}</text>
          </view>
          
          <view class="action-item" @click="handleReply">
            <up-icon name="chat" size="14" color="#999" />
            <text class="action-text">回复</text>
          </view>
          
          <view v-if="canDelete" class="action-item" @click="handleDelete">
            <up-icon name="trash" size="14" color="#ff6b6b" />
            <text class="action-text delete-text">删除</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 回复列表 -->
    <view v-if="comment.replies && comment.replies.length > 0" class="replies-section">
      <view class="replies-list">
        <!-- 显示的回复（默认显示前2条） -->
        <view
          v-for="(reply, index) in displayedReplies"
          :key="reply.commentId"
          class="reply-item"
        >
          <view class="reply-avatar">
            <up-avatar :text="reply.avatarText" :size="28" randomBgColor />
          </view>
          
          <view class="reply-content">
            <view class="reply-header">
              <text class="reply-username">{{ reply.username }}</text>
              <up-tag
                v-if="reply.userId === postAuthorId"
                text="作者"
                color="#ff6b6b"
                size="mini"
                plain
                plainFill
                borderColor="transparent"
              />
              <text class="reply-time">{{ formatTime(reply.createdTime) }}</text>
            </view>
            
            <view class="reply-text">
              <text v-if="reply.replyToUsername" class="reply-target">
                回复 @{{ reply.replyToUsername }}:
              </text>
              {{ reply.content }}
            </view>
            
            <view class="reply-actions">
              <view class="action-item" @click="handleReplyLike(reply)">
                <up-icon
                  :name="reply.isLiked ? 'thumb-up-fill' : 'thumb-up'"
                  size="12"
                  :color="reply.isLiked ? '#ff6b6b' : '#999'"
                />
                <text class="action-text">{{ reply.likeCount || 0 }}</text>
              </view>
              
              <view class="action-item" @click="handleReplyToReply(reply)">
                <up-icon name="chat" size="12" color="#999" />
                <text class="action-text">回复</text>
              </view>
              
              <view v-if="canDeleteReply(reply)" class="action-item" @click="handleDeleteReply(reply)">
                <up-icon name="trash" size="12" color="#ff6b6b" />
                <text class="action-text delete-text">删除</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 展开/收起按钮 -->
      <view v-if="comment.replies.length > 2" class="toggle-replies">
        <view class="toggle-btn" @click="toggleReplies">
          <up-icon
            :name="showAllReplies ? 'arrow-up' : 'arrow-down'"
            size="12"
            color="#007bff"
          />
          <text class="toggle-text">
            {{ showAllReplies ? '收起回复' : `展开${comment.replies.length - 2}条回复` }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useMemberStore } from '@/stores'
import type { Comments } from '@/types'

interface Props {
  comment: Comments
  postAuthorId: number
}

const props = defineProps<Props>()
const memberStore = useMemberStore()

const showAllReplies = ref(false)

// 事件定义
const emit = defineEmits<{
  reply: [comment: Comments]
  like: [comment: Comments]
  delete: [comment: Comments]
  loadReplies: [comment: Comments]
}>()

// 显示的回复列表
const displayedReplies = computed(() => {
  if (!props.comment.replies) return []
  
  if (showAllReplies.value || props.comment.replies.length <= 2) {
    return props.comment.replies
  }
  
  return props.comment.replies.slice(0, 2)
})

// 是否可以删除主评论
const canDelete = computed(() => {
  if (!memberStore.profile?.id) return false
  
  // 评论作者、帖子作者或管理员可以删除
  return props.comment.userId === memberStore.profile.id ||
         props.postAuthorId === memberStore.profile.id ||
         memberStore.profile.isAdmin
})

// 是否可以删除回复
const canDeleteReply = (reply: Comments) => {
  if (!memberStore.profile?.id) return false
  
  // 回复作者、帖子作者或管理员可以删除
  return reply.userId === memberStore.profile.id ||
         props.postAuthorId === memberStore.profile.id ||
         memberStore.profile.isAdmin
}

// 获取角色文本
const getRoleText = (role: string) => {
  const roleMap = {
    'owner': '业主',
    'tenant': '租户',
    'property': '物业',
    'committee': '业委会',
    'community': '社区',
    'admin': '管理员'
  }
  return roleMap[role as keyof typeof roleMap] || role
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else {
    return time.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

// 切换回复显示
const toggleReplies = () => {
  showAllReplies.value = !showAllReplies.value
  
  if (showAllReplies.value && props.comment.replies && props.comment.replies.length > 10) {
    // 如果回复很多，可能需要懒加载
    emit('loadReplies', props.comment)
  }
}

// 事件处理
const handleLike = () => {
  emit('like', props.comment)
}

const handleReply = () => {
  emit('reply', props.comment)
}

const handleDelete = () => {
  emit('delete', props.comment)
}

const handleReplyLike = (reply: Comments) => {
  emit('like', reply)
}

const handleReplyToReply = (reply: Comments) => {
  emit('reply', reply)
}

const handleDeleteReply = (reply: Comments) => {
  emit('delete', reply)
}
</script>

<style scoped lang="scss">
.comment-item {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
  
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.main-comment {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.comment-avatar {
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.username {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.comment-time {
  font-size: 12px;
  color: #999;
  flex-shrink: 0;
}

.comment-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
  word-break: break-word;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  
  &:active {
    background-color: #f5f5f5;
  }
}

.action-text {
  font-size: 12px;
  color: #666;
  
  &.delete-text {
    color: #ff6b6b;
  }
}

.replies-section {
  margin-top: 12px;
  margin-left: 48px;
  padding-left: 12px;
  border-left: 2px solid #f0f0f0;
}

.replies-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reply-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.reply-avatar {
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
  min-width: 0;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.reply-username {
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

.reply-time {
  font-size: 11px;
  color: #999;
}

.reply-text {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 6px;
  word-break: break-word;
}

.reply-target {
  color: #007bff;
  font-weight: 500;
}

.reply-actions {
  display: flex;
  gap: 12px;
}

.toggle-replies {
  margin-top: 8px;
  text-align: center;
}

.toggle-btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  
  &:active {
    background-color: #f5f5f5;
  }
}

.toggle-text {
  font-size: 12px;
  color: #007bff;
}
</style>