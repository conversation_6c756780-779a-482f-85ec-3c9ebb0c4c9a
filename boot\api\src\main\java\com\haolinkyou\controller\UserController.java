package com.haolinkyou.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.Users;
import com.haolinkyou.service.IUserService;
import com.haolinkyou.service.UserRoleService;
import com.haolinkyou.util.PermissionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private IUserService userService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private PermissionUtil permissionUtil;

    /**
     * 获取用户统计信息
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getUserStats(HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            System.out.println("UserController - 获取用户统计信息，用户ID: " + userId);

            if (userId == null) {
                System.out.println("UserController - 用户ID为空，返回未登录错误");
                return Result.error("用户未登录");
            }

            Map<String, Object> stats = userService.getUserStats(userId);
            System.out.println("UserController - 获取统计信息成功: " + stats);
            return Result.success(stats);
        } catch (Exception e) {
            System.out.println("UserController - 获取统计信息异常: " + e.getMessage());
            return Result.error("获取用户统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 管理员获取用户列表（分页查询）
     */
    @GetMapping("/admin/list")
    public Result<IPage<Users>> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String userRole,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Boolean isVerified,
            HttpServletRequest request) {
        try {
            // 验证管理员权限和manage_users权限
            if (!permissionUtil.isAdmin(request) || !permissionUtil.hasPermission(request, "manage_users")) {
                return Result.error("权限不足，仅管理员可查看用户列表");
            }

            // 构建查询条件
            QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("del_flag", 0); // 未删除的用户

            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.and(wrapper -> wrapper
                    .like("nickname", keyword)
                    .or().like("username", keyword)
                    .or().like("mobile", keyword)
                    .or().like("real_name", keyword));
            }

            if (userRole != null && !userRole.trim().isEmpty()) {
                queryWrapper.eq("user_role", userRole);
            }

            if (status != null) {
                queryWrapper.eq("status", status);
            }

            if (isVerified != null) {
                queryWrapper.eq("is_verified", isVerified);
            }

            queryWrapper.orderByDesc("created_time");

            // 分页查询
            Page<Users> pageParam = new Page<>(page, pageSize);
            IPage<Users> userPage = userService.page(pageParam, queryWrapper);

            return Result.success(userPage);
        } catch (Exception e) {
            return Result.error("获取用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 管理员获取用户统计数据
     */
    @GetMapping("/admin/stats")
    public Result<Map<String, Object>> getAdminStats(HttpServletRequest request) {
        try {
            // 验证管理员权限
            Long currentUserId = (Long) request.getAttribute("userId");
            if (currentUserId == null) {
                return Result.error("用户未登录");
            }

            Users currentUser = userService.getById(currentUserId);
            if (currentUser == null || !userRoleService.isAdmin(currentUser.getUserRole())) {
                return Result.error("权限不足");
            }

            Map<String, Object> stats = new HashMap<>();

            // 总用户数
            QueryWrapper<Users> totalWrapper = new QueryWrapper<>();
            totalWrapper.eq("del_flag", 0);
            long totalUsers = userService.count(totalWrapper);

            // 已认证用户数
            QueryWrapper<Users> verifiedWrapper = new QueryWrapper<>();
            verifiedWrapper.eq("del_flag", 0).eq("is_verified", 1);
            long verifiedUsers = userService.count(verifiedWrapper);

            // 未认证用户数
            long unverifiedUsers = totalUsers - verifiedUsers;

            // 被封禁用户数
            QueryWrapper<Users> bannedWrapper = new QueryWrapper<>();
            bannedWrapper.eq("del_flag", 0).eq("status", 0);
            long bannedUsers = userService.count(bannedWrapper);

            stats.put("total", totalUsers);
            stats.put("verified", verifiedUsers);
            stats.put("unverified", unverifiedUsers);
            stats.put("banned", bannedUsers);

            return Result.success(stats);
        } catch (Exception e) {
            return Result.error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 管理员获取用户详情
     */
    @GetMapping("/admin/{userId}")
    public Result<Users> getUserDetail(@PathVariable Long userId, HttpServletRequest request) {
        try {
            // 验证管理员权限和manage_users权限
            if (!permissionUtil.isAdmin(request) || !permissionUtil.hasPermission(request, "manage_users")) {
                return Result.error("权限不足，仅管理员可查看用户详情");
            }

            Users user = userService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            return Result.success(user);
        } catch (Exception e) {
            return Result.error("获取用户详情失败：" + e.getMessage());
        }
    }

    /**
     * 管理员更新用户状态（封禁/解封）
     */
    @PutMapping("/admin/{userId}/status")
    public Result<String> updateUserStatus(
            @PathVariable Long userId,
            @RequestBody Map<String, Object> requestBody,
            HttpServletRequest request) {
        try {
            // 验证管理员权限和manage_users权限
            if (!permissionUtil.isAdmin(request) || !permissionUtil.hasPermission(request, "manage_users")) {
                return Result.error("权限不足，仅管理员可修改用户状态");
            }

            // 获取当前用户ID
            Long currentUserId = (Long) request.getAttribute("userId");
            Users user = userService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 不能操作自己的状态
            if (userId.equals(currentUserId)) {
                return Result.error("不能修改自己的状态");
            }

            // 从请求体中获取status参数
            Integer status = (Integer) requestBody.get("status");
            if (status == null) {
                return Result.error("状态参数不能为空");
            }

            user.setStatus(status);
            boolean success = userService.updateById(user);

            if (success) {
                String action = status == 1 ? "解封" : "封禁";
                return Result.success(action + "成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            return Result.error("更新用户状态失败：" + e.getMessage());
        }
    }

    /**
     * 管理员更新用户角色
     */
    @PutMapping("/admin/{userId}/role")
    public Result<String> updateUserRole(
            @PathVariable Long userId,
            @RequestBody Map<String, Object> requestBody,
            HttpServletRequest request) {
        try {
            // 验证管理员权限
            Long currentUserId = (Long) request.getAttribute("userId");
            if (currentUserId == null) {
                return Result.error("用户未登录");
            }

            Users currentUser = userService.getById(currentUserId);
            if (currentUser == null || !userRoleService.isAdmin(currentUser.getUserRole())) {
                return Result.error("权限不足");
            }

            Users user = userService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 不能修改自己的角色
            if (userId.equals(currentUserId)) {
                return Result.error("不能修改自己的角色");
            }

            // 从请求体中获取userRole参数
            String userRole = (String) requestBody.get("userRole");
            if (userRole == null || userRole.trim().isEmpty()) {
                return Result.error("角色参数不能为空");
            }

            // 验证角色是否存在
            if (userRoleService.getRoleByCode(userRole) == null) {
                return Result.error("角色不存在");
            }

            user.setUserRole(userRole);
            boolean success = userService.updateById(user);

            if (success) {
                return Result.success("角色更新成功");
            } else {
                return Result.error("角色更新失败");
            }
        } catch (Exception e) {
            return Result.error("更新用户角色失败：" + e.getMessage());
        }
    }

    /**
     * 管理员删除用户
     */
    @DeleteMapping("/admin/{userId}")
    public Result<String> deleteUser(@PathVariable Long userId, HttpServletRequest request) {
        try {
            // 验证管理员权限和manage_users权限
            if (!permissionUtil.isAdmin(request) || !permissionUtil.hasPermission(request, "manage_users")) {
                return Result.error("权限不足，仅管理员可删除用户");
            }

            // 获取当前用户ID
            Long currentUserId = (Long) request.getAttribute("userId");
            Users user = userService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 不能删除自己
            if (userId.equals(currentUserId)) {
                return Result.error("不能删除自己");
            }

            // 软删除：设置del_flag为1
            user.setDelFlag(1);
            boolean success = userService.updateById(user);

            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            return Result.error("删除用户失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户资料
     */
    @GetMapping("/profile")
    public Result<Map<String, Object>> getCurrentUserProfile(HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 使用原生SQL查询，只查询已存在的字段
            String sql = "SELECT id, username, nickname, avatar AS avatarUrl, mobile, email, gender, birthday, points, " +
                        "auth_type, verification_type, last_login_time, last_login_ip, is_verified, user_role, " +
                        "permissions, house_number, real_name, status, created_time, updated_time " +
                        "FROM users WHERE id = ? AND del_flag = 0";

            Users user = userService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 构建返回的Map，包含所有字段
            Map<String, Object> userProfile = new HashMap<>();
            userProfile.put("id", user.getId());
            userProfile.put("username", user.getUsername());
            userProfile.put("nickname", user.getNickname());
            userProfile.put("avatarUrl", user.getAvatarUrl());
            userProfile.put("avatar", user.getAvatarUrl()); // 兼容前端
            userProfile.put("mobile", user.getMobile());
            userProfile.put("email", user.getEmail());
            userProfile.put("gender", user.getGender());
            userProfile.put("birthday", user.getBirthday());
            userProfile.put("points", user.getPoints());
            userProfile.put("authType", user.getAuthType());
            userProfile.put("verificationType", user.getVerificationType());
            userProfile.put("isVerified", user.getIsVerified());
            userProfile.put("userRole", user.getUserRole());
            userProfile.put("houseNumber", user.getHouseNumber());
            userProfile.put("realName", user.getRealName());
            userProfile.put("status", user.getStatus());

            // 新字段设置默认值
            userProfile.put("bio", "");
            userProfile.put("region", "");
            userProfile.put("profession", "");
            userProfile.put("school", "");
            userProfile.put("redBookId", "");
            userProfile.put("backgroundImage", "");

            return Result.success(userProfile);
        } catch (Exception e) {
            return Result.error("获取用户资料失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户资料
     */
    @PutMapping("/profile")
    public Result<Map<String, Object>> updateUserProfile(@RequestBody Map<String, Object> requestBody, HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            Users user = userService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 只更新基础字段，新字段暂时忽略
            boolean needUpdate = false;

            if (requestBody.containsKey("nickname")) {
                user.setNickname((String) requestBody.get("nickname"));
                needUpdate = true;
            }
            if (requestBody.containsKey("gender")) {
                user.setGender((Integer) requestBody.get("gender"));
                needUpdate = true;
            }
            if (requestBody.containsKey("birthday")) {
                String birthdayStr = (String) requestBody.get("birthday");
                if (birthdayStr != null && !birthdayStr.isEmpty()) {
                    try {
                        java.sql.Date birthday = java.sql.Date.valueOf(birthdayStr);
                        user.setBirthday(birthday);
                        needUpdate = true;
                    } catch (Exception e) {
                        return Result.error("生日格式错误");
                    }
                }
            }

            // 尝试更新新字段，如果失败则忽略
            try {
                if (requestBody.containsKey("bio")) {
                    user.setBio((String) requestBody.get("bio"));
                    needUpdate = true;
                }
                if (requestBody.containsKey("region")) {
                    user.setRegion((String) requestBody.get("region"));
                    needUpdate = true;
                }
                if (requestBody.containsKey("profession")) {
                    user.setProfession((String) requestBody.get("profession"));
                    needUpdate = true;
                }
                if (requestBody.containsKey("school")) {
                    user.setSchool((String) requestBody.get("school"));
                    needUpdate = true;
                }
                if (requestBody.containsKey("redBookId")) {
                    user.setRedBookId((String) requestBody.get("redBookId"));
                    needUpdate = true;
                }
                if (requestBody.containsKey("backgroundImage")) {
                    user.setBackgroundImage((String) requestBody.get("backgroundImage"));
                    needUpdate = true;
                }
            } catch (Exception e) {
                // 新字段更新失败，继续处理基础字段
                System.out.println("新字段更新失败，可能需要更新数据库表结构: " + e.getMessage());
            }

            if (needUpdate) {
                boolean success = userService.updateById(user);
                if (success) {
                    // 返回更新后的用户信息
                    Map<String, Object> result = new HashMap<>();
                    result.put("id", user.getId());
                    result.put("nickname", user.getNickname());
                    result.put("gender", user.getGender());
                    result.put("birthday", user.getBirthday());
                    result.put("message", "基础资料更新成功");
                    return Result.success(result);
                } else {
                    return Result.error("更新失败");
                }
            } else {
                return Result.error("没有需要更新的字段");
            }
        } catch (Exception e) {
            return Result.error("更新用户资料失败：" + e.getMessage());
        }
    }
}
