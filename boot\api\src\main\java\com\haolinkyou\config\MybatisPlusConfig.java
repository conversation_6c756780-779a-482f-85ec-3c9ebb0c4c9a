package com.haolinkyou.config;


import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MybatisPlusConfig {
    @Bean
    // 定义一个MybatisPlusInterceptor类型的Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        // 创建一个MybatisPlusInterceptor对象
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 添加一个BlockAttackInnerInterceptor类型的拦截器，防止全表更新和删除
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
        // 添加一个PaginationInnerInterceptor类型的拦截器，指定数据库类型为MYSQL
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        // 返回MybatisPlusInterceptor对象
        return interceptor;
    }
}
