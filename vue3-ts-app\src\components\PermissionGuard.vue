<template>
  <view v-if="!loading && hasPermission">
    <slot></slot>
  </view>
  <view v-else-if="loading && showLoading">
    <slot name="loading">
      <view class="permission-loading">
        <up-loading-icon mode="circle" size="16"></up-loading-icon>
        <text class="loading-text">权限验证中...</text>
      </view>
    </slot>
  </view>
  <view v-else-if="!hasPermission && showFallback">
    <slot name="fallback">
      <view class="permission-denied">
        <up-icon name="lock" color="#ccc" size="16"></up-icon>
        <text class="denied-text">权限不足</text>
      </view>
    </slot>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { checkPermission, checkMultiplePermissions, checkBackendAccess } from '@/services/permissionService'
import { useMemberStore } from '@/stores'

interface Props {
  // 需要的权限（单个权限）
  permission?: string
  // 需要的权限列表（多个权限，满足任一即可）
  permissions?: string[]
  // 是否需要管理后台权限
  requireBackendAccess?: boolean
  // 权限逻辑：'and' 表示需要所有权限，'or' 表示满足任一权限即可
  logic?: 'and' | 'or'
  // 是否显示加载状态
  showLoading?: boolean
  // 是否显示权限不足的占位内容
  showFallback?: boolean
  // 是否在权限不足时自动隐藏（不显示任何内容）
  autoHide?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  logic: 'or',
  showLoading: false,
  showFallback: false,
  autoHide: true
})

const loading = ref(true)
const hasPermission = ref(false)
const memberStore = useMemberStore()

// 检查权限
const checkPermissions = async () => {
  loading.value = true
  
  try {
    // 如果用户未登录，直接返回false
    if (!memberStore.profile?.id) {
      hasPermission.value = false
      return
    }
    
    let result = true
    
    // 检查管理后台权限
    if (props.requireBackendAccess) {
      const backendAccess = await checkBackendAccess()
      if (!backendAccess) {
        result = false
      }
    }
    
    // 检查单个权限
    if (result && props.permission) {
      const singlePermission = await checkPermission(props.permission)
      result = result && singlePermission
    }
    
    // 检查多个权限
    if (result && props.permissions && props.permissions.length > 0) {
      const multiplePermissions = await checkMultiplePermissions(props.permissions)
      const permissionResults = Object.values(multiplePermissions)
      
      if (props.logic === 'and') {
        // 需要所有权限
        result = result && permissionResults.every(p => p)
      } else {
        // 满足任一权限即可
        result = result && permissionResults.some(p => p)
      }
    }
    
    hasPermission.value = result
  } catch (error) {
    console.error('权限检查失败:', error)
    hasPermission.value = false
  } finally {
    loading.value = false
  }
}

// 监听用户登录状态变化
watch(() => memberStore.profile?.id, () => {
  checkPermissions()
}, { immediate: false })

onMounted(() => {
  checkPermissions()
})

// 暴露刷新权限的方法
const refreshPermissions = () => {
  checkPermissions()
}

defineExpose({
  refreshPermissions,
  hasPermission,
  loading
})
</script>

<style scoped>
.permission-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  color: #999;
}

.loading-text {
  margin-left: 8px;
  font-size: 12px;
}

.permission-denied {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  color: #ccc;
}

.denied-text {
  margin-left: 8px;
  font-size: 12px;
}
</style>
