-- =============================================
-- 好邻居社区平台系统初始化数据脚本
-- 包含角色配置和测试用户数据
-- 注意：所有统计数据（评论数、点赞数、收藏数）都与实际数据保持一致
-- 创建时间: 2025-07-13
-- =============================================

-- ----------------------------
-- 初始化用户角色数据
-- ----------------------------
-- 清空表数据，避免重复插入
TRUNCATE TABLE `user_roles`;

INSERT INTO `user_roles` (`role_code`, `role_name`, `role_description`, `permissions`, `sort_order`, `status`) VALUES
('guest', '游客', '未认证用户，权限受限',
 '["view_posts", "view_comments"]',
 1, 1),

('owner', '业主', '认证业主，享有业主权益',
 '["view_posts", "view_comments", "create_posts", "create_comments", "like_posts", "collect_posts", "delete_own_posts"]',
 2, 1),

('tenant', '租户', '认证租户，可发帖评论',
 '["view_posts", "view_comments", "create_posts", "create_comments", "like_posts", "collect_posts", "delete_own_posts"]',
 3, 1),

('property', '物业', '物业管理人员，拥有物业管理权限',
 '["view_posts", "view_comments", "create_posts", "create_comments", "like_posts", "collect_posts", "delete_own_posts", "manage_posts", "manage_comments"]',
 4, 1),

('committee', '业委会', '业委会委员，拥有社区管理权限',
 '["view_posts", "view_comments", "create_posts", "create_comments", "like_posts", "collect_posts", "delete_own_posts", "manage_posts", "manage_comments", "manage_users", "delete_any_posts"]',
 5, 1),

('community', '社区管理员', '社区管理员，拥有社区管理权限',
 '["view_posts", "view_comments", "create_posts", "create_comments", "like_posts", "collect_posts", "delete_own_posts", "manage_posts", "manage_comments", "manage_users", "delete_any_posts", "admin_access"]',
 6, 1),

('admin', '系统管理员', '系统管理员，拥有全部权限',
 '["view_posts", "view_comments", "create_posts", "create_comments", "like_posts", "collect_posts", "delete_own_posts", "manage_posts", "manage_comments", "manage_users", "delete_any_posts", "admin_access", "system_config"]',
 7, 1);

-- ----------------------------
-- 初始化分类数据
-- ----------------------------
-- 清空表数据，避免重复插入
TRUNCATE TABLE `categories`;

INSERT INTO `categories` (`id`, `category_code`, `category_name`, `icon`, `description`, `sort_order`, `status`, `created_time`, `updated_time`, `del_flag`) VALUES
(1, 'notice', '三期公告', 'notice.png', '小区重要通知公告', 1, 1, '2025-05-05 14:17:19', NULL, 0),
(2, 'help', '邻友互助', 'help.png', '邻里之间互帮互助', 2, 1, '2025-05-05 14:17:19', NULL, 0),
(3, 'group', '组团邀约', 'gropu.png', '团购、活动邀约', 3, 1, '2025-05-05 14:17:19', NULL, 0),
(4, 'secondhand', '二手闲置', 'secondhand.png', '二手物品交易', 4, 1, '2025-05-05 14:17:19', NULL, 0),
(5, 'rent', '房东直租', 'rent.png', '房东直接出租信息', 5, 1, '2025-05-05 14:17:19', NULL, 0);

-- ----------------------------
-- 初始化分类权限数据
-- ----------------------------
-- 清空表数据，避免重复插入
TRUNCATE TABLE `category_permissions`;

INSERT INTO `category_permissions` (`category_id`, `user_role`, `can_view`, `can_post`, `can_comment`) VALUES
-- 三期公告 - 只有管理员可发帖
(1, 'guest', 1, 0, 0),
(1, 'owner', 1, 0, 1),
(1, 'tenant', 1, 0, 1),
(1, 'property', 1, 1, 1),
(1, 'committee', 1, 1, 1),
(1, 'community', 1, 1, 1),
(1, 'admin', 1, 1, 1),

-- 邻友互助 - 所有认证用户可发帖
(2, 'guest', 1, 0, 0),
(2, 'owner', 1, 1, 1),
(2, 'tenant', 1, 1, 1),
(2, 'property', 1, 1, 1),
(2, 'committee', 1, 1, 1),
(2, 'community', 1, 1, 1),
(2, 'admin', 1, 1, 1),

-- 组团邀约 - 所有认证用户可发帖
(3, 'guest', 1, 0, 0),
(3, 'owner', 1, 1, 1),
(3, 'tenant', 1, 1, 1),
(3, 'property', 1, 1, 1),
(3, 'committee', 1, 1, 1),
(3, 'community', 1, 1, 1),
(3, 'admin', 1, 1, 1),

-- 二手闲置 - 所有认证用户可发帖
(4, 'guest', 1, 0, 0),
(4, 'owner', 1, 1, 1),
(4, 'tenant', 1, 1, 1),
(4, 'property', 1, 1, 1),
(4, 'committee', 1, 1, 1),
(4, 'community', 1, 1, 1),
(4, 'admin', 1, 1, 1),

-- 房东直租 - 所有认证用户可发帖
(5, 'guest', 1, 0, 0),
(5, 'owner', 1, 1, 1),
(5, 'tenant', 1, 1, 1),
(5, 'property', 1, 1, 1),
(5, 'committee', 1, 1, 1),
(5, 'community', 1, 1, 1),
(5, 'admin', 1, 1, 1);

-- ----------------------------
-- 初始化测试用户数据
-- ----------------------------
-- 清空表数据，避免重复插入
TRUNCATE TABLE `users`;

INSERT INTO `users` (`username`, `password`, `nickname`, `avatar`, `mobile`, `user_role`, `is_verified`, `verification_type`, `house_number`, `real_name`, `points`, `status`) VALUES
-- 业主用户
('owner_test', 'e10adc3949ba59abbe56e057f20f883e', '张业主', 'http://yjy-xiaotuxian-dev.oss-cn-beijing.aliyuncs.com/picture/2021-04-06/db628d42-88a7-46e7-abb8-659448c33081.png', '13800138001', 'owner', 1, 'owner', '1-2-301', '张三', 100, 1),

-- 租户用户
('tenant_test', 'e10adc3949ba59abbe56e057f20f883e', '李租户', 'http://yjy-xiaotuxian-dev.oss-cn-beijing.aliyuncs.com/picture/2021-04-06/db628d42-88a7-46e7-abb8-659448c33081.png', '13800138002', 'tenant', 1, 'tenant', '2-1-205', '李四', 80, 1),

-- 物业用户
('property_test', 'e10adc3949ba59abbe56e057f20f883e', '王物业', 'http://yjy-xiaotuxian-dev.oss-cn-beijing.aliyuncs.com/picture/2021-04-06/db628d42-88a7-46e7-abb8-659448c33081.png', '13800138003', 'property', 1, 'property', NULL, '王五', 200, 1),

-- 业委会用户
('committee_test', 'e10adc3949ba59abbe56e057f20f883e', '赵委员', 'http://yjy-xiaotuxian-dev.oss-cn-beijing.aliyuncs.com/picture/2021-04-06/db628d42-88a7-46e7-abb8-659448c33081.png', '13800138004', 'committee', 1, 'committee', '3-1-101', '赵六', 300, 1),

-- 社区管理员
('community_test', 'e10adc3949ba59abbe56e057f20f883e', '钱管理员', 'http://yjy-xiaotuxian-dev.oss-cn-beijing.aliyuncs.com/picture/2021-04-06/db628d42-88a7-46e7-abb8-659448c33081.png', '13800138005', 'community', 1, 'community', NULL, '钱七', 500, 1),

-- 系统管理员
('admin_test', 'e10adc3949ba59abbe56e057f20f883e', '孙管理员', 'http://yjy-xiaotuxian-dev.oss-cn-beijing.aliyuncs.com/picture/2021-04-06/db628d42-88a7-46e7-abb8-659448c33081.png', '13800138000', 'admin', 1, 'admin', NULL, '孙八', 1000, 1);

-- ----------------------------
-- 初始化系统配置数据
-- ----------------------------
-- 清空表数据，避免重复插入
TRUNCATE TABLE `system_config`;

INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `group_name`, `sort_order`, `is_system`) VALUES
('site_name', '好邻居社区平台', 'string', '网站名称', 'basic', 1, 1),
('site_description', '构建和谐邻里关系，共享美好社区生活', 'string', '网站描述', 'basic', 2, 1),
('default_avatar', 'http://yjy-xiaotuxian-dev.oss-cn-beijing.aliyuncs.com/picture/2021-04-06/db628d42-88a7-46e7-abb8-659448c33081.png', 'string', '默认头像', 'basic', 3, 1),
('post_review_required', 'false', 'boolean', '帖子是否需要审核', 'content', 1, 0),
('comment_review_required', 'false', 'boolean', '评论是否需要审核', 'content', 2, 0),
('max_file_size', '10485760', 'number', '最大文件上传大小(字节)', 'upload', 1, 0),
('allowed_file_types', '["jpg", "jpeg", "png", "gif", "mp4", "mov"]', 'json', '允许的文件类型', 'upload', 2, 0),
('points_sign_daily', '5', 'number', '每日签到积分', 'points', 1, 0),
('points_post_create', '10', 'number', '发帖获得积分', 'points', 2, 0),
('points_comment_create', '2', 'number', '评论获得积分', 'points', 3, 0),
('points_like_give', '1', 'number', '点赞获得积分', 'points', 4, 0);

-- ----------------------------
-- 初始化积分商品数据
-- ----------------------------
-- 清空表数据，避免重复插入
TRUNCATE TABLE `products`;

INSERT INTO `products` (`name`, `description`, `image`, `points`, `stock`, `sort_order`, `status`) VALUES
('精美水杯', '高品质不锈钢保温杯，容量500ml，保温效果佳', '', 100, 50, 1, 1),
('环保购物袋', '可重复使用的环保购物袋，承重能力强，绿色环保', '', 50, 100, 2, 1),
('蓝牙耳机', '无线蓝牙耳机，音质清晰，续航持久，运动必备', '', 300, 20, 3, 1),
('小夜灯', 'LED智能感应小夜灯，节能环保，温馨照明', '', 80, 80, 4, 1),
('手机支架', '多功能手机支架，可调节角度，办公居家两用', '', 60, 60, 5, 1);

-- ----------------------------
-- 初始化示例帖子数据
-- ----------------------------
-- 清空表数据，避免重复插入
TRUNCATE TABLE `posts`;

INSERT INTO `posts` (`user_id`, `category_id`, `title`, `content`, `status`, `view_count`, `like_count`, `comment_count`, `collect_count`) VALUES
-- 三期公告分类
(6, 1, '【重要通知】小区停车位重新分配公告', '各位业主：根据业主大会决议，小区停车位将于下月1日起重新分配。请各位业主携带相关证件到物业办公室办理登记手续。具体分配原则：1.优先保障一户一车位；2.多车家庭按购买时间排序；3.租户需提供房东授权书。办理时间：工作日9:00-17:00。', 1, 156, 6, 12, 3),
(5, 1, '【物业通知】春节期间服务安排', '尊敬的业主们：春节期间（2月8日-2月16日）物业服务安排如下：1.保安24小时值班；2.保洁服务调整为隔日一次；3.维修服务仅处理紧急情况；4.快递代收正常进行。祝大家春节快乐！', 1, 89, 5, 6, 2),

-- 邻友互助分类
(1, 2, '寻找拼车伙伴 - 每日上下班', '大家好！我每天早上8:00从小区出发到CBD上班，下午6:00下班回来。有同路的邻居可以一起拼车，既环保又能节省费用。我的车是白色本田雅阁，驾龄8年，安全驾驶。有意向的朋友请联系我！', 1, 67, 4, 8, 1),
(2, 2, '帮忙照看小猫咪几天', '各位邻居好！因为要出差一周，家里有只小猫咪需要照看。猫咪很乖，不挑食，只需要每天喂食和换水就行。愿意帮忙的邻居我会支付一定的照看费用。非常感谢！', 1, 43, 6, 5, 0),
(3, 2, '求助：家里水管漏水，有推荐的师傅吗？', '今天发现厨房水管漏水，需要找个靠谱的维修师傅。有邻居推荐吗？最好是之前合作过的，手艺好价格公道的。急需，谢谢大家！', 1, 78, 3, 9, 0),

-- 组团邀约分类
(1, 3, '【团购】进口水果团购，新鲜直达', '大家好！联系到一个进口水果供应商，品质很好价格实惠。这次团购包括：新西兰奇异果、智利车厘子、泰国榴莲等。团购价比市场价便宜30%左右。需要的邻居请跟帖报名，满20单发货！', 1, 92, 6, 15, 8),
(4, 3, '周末爬山活动邀请', '天气不错，想组织大家周末去附近的凤凰山爬山。时间：本周六早上7:00小区门口集合。路线不难，适合全家参与。自带水和简单食物，预计下午3点返回。有兴趣的邻居请报名！', 1, 54, 6, 11, 2),

-- 二手闲置分类
(2, 4, '转让九成新儿童自行车', '孩子长大了，转让一辆九成新的儿童自行车。品牌：捷安特，适合5-8岁儿童。原价680元，现价300元转让。车况很好，只是孩子不骑了。有需要的邻居可以来看车。', 1, 38, 2, 3, 1),
(3, 4, '出售闲置健身器材', '搬家清理，出售以下健身器材：1.跑步机（九成新）1200元；2.哑铃套装（全新）200元；3.瑜伽垫（八成新）50元。都是品牌货，质量很好。打包优惠，有意者请联系！', 1, 61, 5, 4, 2),

-- 房东直租分类
(1, 5, '精装两室一厅出租，拎包入住', '房源信息：2室1厅1卫，85平米，精装修，家具家电齐全。位置：3号楼6层，朝南采光好。租金：3500元/月，押一付三。要求：爱护房屋，不养宠物，不吸烟。看房请提前预约！', 1, 125, 6, 5, 3),
(4, 5, '单间出租，适合单身人士', '独立单间出租，20平米，有独立卫生间，简单装修，基本家具齐全。租金：1200元/月，包水电网络。适合单身上班族，要求干净整洁。随时可以看房，有意者请联系！', 1, 87, 3, 3, 1);

-- ----------------------------
-- 初始化示例评论数据（包含子评论）
-- ----------------------------
-- 清空表数据，避免重复插入
TRUNCATE TABLE `comments`;

INSERT INTO `comments` (`post_id`, `user_id`, `parent_id`, `reply_to_user_id`, `content`, `like_count`, `reply_count`) VALUES
-- 停车位分配公告的评论
(1, 1, NULL, NULL, '终于要重新分配了，希望能更公平一些！', 3, 2),
(1, 2, 1, 1, '是的，之前的分配确实有些不合理', 1, 0),
(1, 3, 1, 1, '支持！我家一直没有固定车位', 2, 0),
(1, 4, NULL, NULL, '请问具体的办理流程是什么？', 1, 1),
(1, 6, 4, 4, '流程在公告里有详细说明，可以仔细看看', 0, 0),
(1, 5, NULL, NULL, '什么时候可以开始办理？', 0, 0),

-- 春节服务安排的评论
(2, 1, NULL, NULL, '辛苦物业工作人员了，春节还要值班', 4, 1),
(2, 2, 7, 1, '确实辛苦，我们也要理解和配合', 2, 0),
(2, 3, NULL, NULL, '保洁调整为隔日一次可以理解', 1, 0),

-- 拼车帖子的评论
(3, 2, NULL, NULL, '我也是去CBD上班，可以一起！', 2, 2),
(3, 1, 10, 2, '太好了！我们可以私聊具体安排', 1, 0),
(3, 4, 10, 2, '我也想加入，三个人一起更划算', 0, 0),
(3, 3, NULL, NULL, '路线经过国贸吗？', 1, 1),
(3, 1, 13, 3, '经过的，正好顺路', 0, 0),

-- 照看猫咪的评论
(4, 1, NULL, NULL, '我可以帮忙照看，我也养猫有经验', 3, 1),
(4, 2, 15, 1, '太感谢了！我们可以详细聊聊', 1, 0),
(4, 3, NULL, NULL, '猫咪多大了？需要特别注意什么吗？', 1, 0),

-- 水管维修求助的评论
(5, 1, NULL, NULL, '我推荐李师傅，手艺好价格公道：138****1234', 5, 2),
(5, 3, 18, 1, '谢谢推荐！我联系一下', 2, 0),
(5, 4, 18, 1, '李师傅确实不错，我家也是他修的', 1, 0),
(5, 2, NULL, NULL, '如果急的话，物业也有应急维修服务', 2, 0),

-- 水果团购的评论
(6, 2, NULL, NULL, '车厘子多少钱一斤？', 1, 1),
(6, 1, 22, 2, '车厘子团购价45元/斤，比超市便宜很多', 2, 0),
(6, 3, NULL, NULL, '我要2斤奇异果，1斤车厘子', 0, 0),
(6, 4, NULL, NULL, '榴莲有吗？价格怎么样？', 1, 1),
(6, 1, 25, 4, '榴莲35元/斤，很新鲜的', 0, 0),

-- 爬山活动的评论
(7, 1, NULL, NULL, '好主意！我们一家三口都参加', 3, 1),
(7, 4, 27, 1, '太好了！到时候一起出发', 1, 0),
(7, 2, NULL, NULL, '路程大概多长时间？', 1, 1),
(7, 4, 29, 2, '单程大概1.5小时，不算太累', 0, 0),

-- 儿童自行车转让的评论
(8, 1, NULL, NULL, '车子还在吗？可以看看实物', 1, 1),
(8, 2, 31, 1, '还在的，你什么时候方便来看？', 0, 0),
(8, 3, NULL, NULL, '300元可以再便宜点吗？', 0, 0),

-- 健身器材出售的评论
(9, 1, NULL, NULL, '跑步机什么品牌的？', 2, 1),
(9, 3, 34, 1, '是乔山品牌的，质量很好', 1, 0),
(9, 2, NULL, NULL, '哑铃套装包括哪些重量？', 1, 0),
(9, 4, NULL, NULL, '全部打包多少钱？', 0, 0),

-- 两室一厅出租的评论
(10, 2, NULL, NULL, '房子什么时候可以入住？', 2, 1),
(10, 1, 38, 2, '随时可以入住，看房满意就能签约', 1, 0),
(10, 3, NULL, NULL, '附近交通方便吗？', 1, 1),
(10, 1, 40, 3, '很方便，步行5分钟到地铁站', 0, 0),
(10, 4, NULL, NULL, '可以养小型宠物吗？', 0, 0),

-- 单间出租的评论
(11, 2, NULL, NULL, '1200包水电网络很划算！', 1, 0),
(11, 3, NULL, NULL, '什么时候可以看房？', 1, 1),
(11, 4, 44, 3, '我也想看房，可以约个时间', 0, 0);

-- ----------------------------
-- 初始化示例点赞数据
-- ----------------------------
-- 清空表数据，避免重复插入
TRUNCATE TABLE `post_likes`;

INSERT INTO `post_likes` (`post_id`, `user_id`) VALUES
-- 停车位分配公告点赞 (6个点赞)
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6),
-- 春节服务安排点赞 (5个点赞)
(2, 1), (2, 2), (2, 3), (2, 4), (2, 6),
-- 拼车帖子点赞 (4个点赞)
(3, 2), (3, 3), (3, 4), (3, 5),
-- 照看猫咪点赞 (6个点赞)
(4, 1), (4, 2), (4, 3), (4, 4), (4, 5), (4, 6),
-- 水管维修求助点赞 (3个点赞)
(5, 1), (5, 2), (5, 4),
-- 水果团购点赞 (6个点赞)
(6, 1), (6, 2), (6, 3), (6, 4), (6, 5), (6, 6),
-- 爬山活动点赞 (6个点赞)
(7, 1), (7, 2), (7, 3), (7, 4), (7, 5), (7, 6),
-- 儿童自行车点赞 (2个点赞)
(8, 1), (8, 3),
-- 健身器材点赞 (5个点赞)
(9, 1), (9, 2), (9, 4), (9, 5), (9, 6),
-- 两室一厅出租点赞 (6个点赞)
(10, 1), (10, 2), (10, 3), (10, 4), (10, 5), (10, 6),
-- 单间出租点赞 (3个点赞)
(11, 2), (11, 3), (11, 5);

-- 清空表数据，避免重复插入
TRUNCATE TABLE `comment_likes`;

INSERT INTO `comment_likes` (`comment_id`, `user_id`) VALUES
-- 各种评论的点赞
(1, 2), (1, 3), (1, 4),
(2, 1), (3, 1), (3, 2),
(4, 6), (7, 2), (7, 3), (7, 4), (7, 5),
(8, 1), (8, 3),
(10, 3), (10, 4),
(12, 2), (13, 4),
(15, 2), (15, 3), (15, 4),
(18, 2), (18, 3), (18, 4), (18, 5), (18, 6),
(19, 1), (19, 3),
(21, 1), (21, 4),
(23, 1), (23, 2),
(25, 2),
(27, 2), (27, 3), (27, 5),
(29, 2),
(31, 3),
(34, 1), (34, 4),
(35, 3),
(38, 1), (38, 3),
(40, 2),
(43, 4),
(44, 1);

-- ----------------------------
-- 初始化示例收藏数据
-- ----------------------------
-- 清空表数据，避免重复插入
TRUNCATE TABLE `user_collects`;

INSERT INTO `user_collects` (`user_id`, `post_id`) VALUES
-- 用户1的收藏
(1, 2), (1, 6), (1, 10),
-- 用户2的收藏
(2, 1), (2, 7), (2, 11),
-- 用户3的收藏
(3, 1), (3, 6), (3, 9), (3, 10),
-- 用户4的收藏
(4, 3), (4, 6),
-- 用户5的收藏
(5, 6), (5, 7), (5, 8), (5, 9), (5, 10),
-- 用户6的收藏
(6, 3), (6, 4), (6, 6);



-- =============================================
-- 系统初始化数据完成
-- 包含：7个角色、6个测试用户、5个分类、11篇文章、45条评论
-- 以及相应的点赞、收藏等互动数据
-- 注意：所有统计数据（评论数、点赞数、收藏数）都与实际数据保持一致
-- 所有测试用户的密码都是 123456 (MD5加密后)
-- 管理员账号: admin_test / 123456
-- =============================================
