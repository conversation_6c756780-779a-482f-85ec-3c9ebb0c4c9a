from flask import Flask, request, jsonify, send_from_directory
from werkzeug.utils import secure_filename
import os
from flask_cors import CORS

# 配置上传文件夹
UPLOAD_FOLDER = 'uploads'
if not os.path.exists(UPLOAD_FOLDER):
    try:
        os.makedirs(UPLOAD_FOLDER)
        print(f"Created upload folder: {UPLOAD_FOLDER}")
    except Exception as e:
        print(f"Error creating upload folder: {e}")

app = Flask(__name__, static_folder=UPLOAD_FOLDER, static_url_path='/uploads')
# 显式设置 UPLOAD_FOLDER 到 app.config 中
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
CORS(app)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov'}


def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400
    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400
    if file and allowed_file(file.filename):
        try:
            filename = secure_filename(file.filename)
            print(f"Saving file: {filename}")
            file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
            return jsonify({"data": f"/uploads/{filename}"}), 200
        except Exception as e:
            print(f"Error saving file: {e}")
            return jsonify({"error": f"Error saving file: {str(e)}"}), 500
    else:
        return jsonify({"error": "File type not allowed"}), 400


@app.errorhandler(Exception)
def handle_exception(e):
    print(f"An unexpected error occurred: {e}")
    return jsonify({"error": "An unexpected error occurred. Please try again later."}), 500


if __name__ == '__main__':
    app.run(debug=True)
