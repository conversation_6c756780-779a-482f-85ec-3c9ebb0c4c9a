package com.haolinkyou.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.Posts;
import com.haolinkyou.entity.Users;
import com.haolinkyou.service.IPostsService;
import com.haolinkyou.service.IUserService;
import com.haolinkyou.service.UserRoleService;
import com.haolinkyou.util.PermissionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/api/admin")
public class AdminController {
    
    @Autowired
    private IUserService userService;
    
    @Autowired
    private IPostsService postsService;
    
    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private PermissionUtil permissionUtil;
    
    /**
     * 检查管理后台访问权限
     */
    @GetMapping("/check-access")
    public Result<Boolean> checkBackendAccess(HttpServletRequest request) {
        try {
            // 验证用户登录状态
            Long currentUserId = (Long) request.getAttribute("userId");
            if (currentUserId == null) {
                return Result.success(false);
            }

            // 验证管理员权限
            Users currentUser = userService.getById(currentUserId);
            boolean hasAccess = currentUser != null && userRoleService.isAdmin(currentUser.getUserRole());

            return Result.success(hasAccess);
        } catch (Exception e) {
            System.err.println("检查管理后台访问权限失败: " + e.getMessage());
            return Result.success(false);
        }
    }

    /**
     * 获取管理员统计数据
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getAdminStats(HttpServletRequest request) {
        try {
            // 验证管理员权限
            if (!permissionUtil.isAdmin(request)) {
                return Result.error("权限不足，仅管理员可访问");
            }

            Map<String, Object> stats = new HashMap<>();
            
            // 总用户数
            QueryWrapper<Users> userWrapper = new QueryWrapper<>();
            userWrapper.eq("del_flag", 0);
            long totalUsers = userService.count(userWrapper);
            
            // 总帖子数
            QueryWrapper<Posts> postWrapper = new QueryWrapper<>();
            postWrapper.eq("del_flag", 0);
            long totalPosts = postsService.count(postWrapper);
            
            // 待审核帖子数
            QueryWrapper<Posts> pendingWrapper = new QueryWrapper<>();
            pendingWrapper.eq("del_flag", 0).eq("status", 0); // 0-待审核
            long pendingReviews = postsService.count(pendingWrapper);
            
            // 今日活跃用户数（今日登录的用户）
            LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
            QueryWrapper<Users> todayActiveWrapper = new QueryWrapper<>();
            todayActiveWrapper.eq("del_flag", 0)
                .between("last_login_time", todayStart, todayEnd);
            long todayActive = userService.count(todayActiveWrapper);
            
            // 待认证用户数
            QueryWrapper<Users> pendingAuthWrapper = new QueryWrapper<>();
            pendingAuthWrapper.eq("del_flag", 0).eq("is_verified", 0);
            long pendingAuth = userService.count(pendingAuthWrapper);
            
            // 反馈数量（这里暂时设为0，后续可以添加反馈表）
            long feedbackCount = 0;

            stats.put("totalUsers", totalUsers);
            stats.put("totalPosts", totalPosts);
            stats.put("pendingReviews", pendingReviews);
            stats.put("todayActive", todayActive);
            stats.put("pendingAuth", pendingAuth);
            stats.put("feedbackCount", feedbackCount);

            return Result.success(stats);
        } catch (Exception e) {
            return Result.error("获取统计数据失败：" + e.getMessage());
        }
    }
}
