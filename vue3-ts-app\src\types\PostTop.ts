// 置顶配置
export interface TopConfig {
  costPerHour: number
  maxHours: number
  minHours: number
  defaultHours: number
}

// 置顶检查结果
export interface TopCheckResult {
  canTop: boolean
  reason: string
  requiredPoints?: number
  userPoints?: number
}

// 置顶结果
export interface TopResult {
  success: boolean
  message: string
  pointsUsed?: number
  expireTime?: number
}

// 置顶费用计算结果
export interface TopCostResult {
  cost: number
  userPoints: number
  canAfford: boolean
}
