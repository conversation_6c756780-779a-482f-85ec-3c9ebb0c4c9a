// 用户相关类型定义

// 用户基本信息
export interface User {
  id: number;
  username?: string;
  nickname: string;
  avatar?: string;
  mobile?: string;
  email?: string;
  gender?: number; // 0-未知 1-男 2-女
  birthday?: string;
  points: number;
  authType?: number; // 0-未认证 1-手机认证 2-实名认证
  verificationType?: string; // owner/tenant/property/committee
  userRole: string; // guest/owner/tenant/property/committee/community/admin
  isVerified: boolean;
  status: number; // 0-禁用 1-正常 2-冻结
  houseNumber?: string;
  realName?: string;
  lastLoginTime?: string;
  createdTime: string;
  updatedTime?: string;
}

// 用户角色
export interface UserRole {
  id: number;
  roleCode: string;
  roleName: string;
  roleDescription?: string;
  permissions: string[];
  sortOrder: number;
  status: number;
  createdTime: string;
  updatedTime?: string;
}

// 用户积分记录
export interface PointRecord {
  id: number;
  userId: number;
  points: number; // 正数为增加，负数为减少
  type: string; // sign-签到 post-发帖 comment-评论 like-点赞 redeem-兑换
  description?: string;
  relatedId?: number; // 关联ID(如帖子ID、评论ID等)
  createdTime: string;
}

// 用户兑换记录
export interface RedemptionRecord {
  id: number;
  userId: number;
  productId: number;
  productName: string;
  pointsUsed: number;
  quantity: number;
  status: number; // 0-待发货 1-已发货 2-已完成 3-已取消
  shippingAddress?: string;
  contactPhone?: string;
  remark?: string;
  createdTime: string;
  updatedTime?: string;
}

// 用户认证申请
export interface UserAuthApplication {
  id: number;
  userId: number;
  realName: string;
  phone: string;
  identityType: string; // owner-业主 tenant-租户 property-物业 committee-业委会
  houseNumber?: string;
  documents?: string[]; // 证明文件列表
  remark?: string;
  status: number; // 0-待审核 1-已通过 2-已拒绝
  reviewTime?: string;
  reviewUserId?: number;
  reviewNote?: string;
  createdTime: string;
  updatedTime?: string;
}

// 反馈信息
export interface Feedback {
  id: number;
  userId: number;
  content: string;
  contactInfo?: string;
  status: number; // 0-待处理 1-已采纳 2-已处理 3-已拒绝
  processTime?: string;
  processUserId?: number;
  processNote?: string;
  createdTime: string;
  updatedTime?: string;
}

// 用户状态标签
export interface UserStatusTag {
  text: string;
  color: string;
  bgColor: string;
}

// 登录相关
export interface LoginForm {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
  expiresIn: number;
}

// 个人资料编辑
export interface ProfileEditForm {
  nickname: string;
  gender?: number;
  birthday?: string;
  email?: string;
  houseNumber?: string;
}
