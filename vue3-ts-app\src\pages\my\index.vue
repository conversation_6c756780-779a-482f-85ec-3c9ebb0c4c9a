<template>
  <view class="my-page">
    <!-- 用户信息头部 -->
    <view class="user-header" :style="{ paddingTop: pageHeaderPaddingTop }">
      <!-- 已登录状态 -->
      <view v-if="isLoggedIn" class="logged-in-header">
        <view class="user-profile">
          <up-avatar
            :src="userInfo.avatar"
            shape="circle"
            :size="70"
            @click="handleAvatarClick"
            class="user-avatar"
          ></up-avatar>
          <view class="user-details">
            <view class="user-name-row">
              <text class="user-name">{{ userInfo.nickname }}</text>
              <view class="auth-badge" v-if="userInfo.authType">
                <up-icon name="checkmark-circle-fill" color="#52c41a" size="16"></up-icon>
                <text class="auth-text">已认证</text>
              </view>
            </view>
            <view class="user-meta">
              <text class="user-id">邻友ID: {{ userInfo.id }}</text>
              <text class="user-role">{{ getRoleName(userInfo.userRole || undefined) }}</text>
            </view>
            <view class="user-mobile" v-if="userInfo.mobile">
              <up-icon name="phone" size="12" color="#999"></up-icon>
              <text class="mobile-text">{{ userInfo.mobile }}</text>
            </view>
          </view>
        </view>
        <view class="header-actions">
          <view class="action-btn" @click="handleEditProfile">
            <up-icon name="edit-pen" size="16" color="#5677fc"></up-icon>
          </view>
          <view class="action-btn" @click="showMoreActions">
            <up-icon name="more-dot-fill" size="16" color="#666"></up-icon>
          </view>
        </view>
      </view>

      <!-- 未登录状态 -->
      <view v-else class="not-logged-in-header">
        <view class="login-prompt">
          <up-avatar
            src=""
            shape="circle"
            :size="70"
            class="default-avatar"
          ></up-avatar>
          <view class="login-content">
            <text class="welcome-text">欢迎来到好邻友</text>
            <text class="login-tip">登录后享受更多社区服务</text>
            <up-button
              text="立即登录"
              type="primary"
              size="normal"
              :customStyle="{ marginTop: '12px', borderRadius: '20px' }"
              @click="handleLogin"
            ></up-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 签到功能区域 -->
    <view class="sign-section" v-if="isLoggedIn">
      <SignInComponent />
    </view>

    <!-- 统计数据 -->
    <view class="stats-section">
      <view class="stats-item" @click="handleMyPosts">
        <text class="stats-number">{{ userStats.postCount }}</text>
        <text class="stats-label">动态</text>
      </view>
      <view class="stats-item" @click="handleMyCollections">
        <text class="stats-number">{{ userStats.favoriteCount }}</text>
        <text class="stats-label">收藏</text>
      </view>
      <view class="stats-item" @click="handleMyPoints">
        <text class="stats-number" :style="{ color: '#ff6b35' }">{{ userStats.pointCount }}</text>
        <text class="stats-label">积分</text>
      </view>
    </view>

    <!-- 业主百宝箱 -->
    <view class="treasure-box" v-if="false">
      <view class="treasure-header">
        <up-icon name="gift" color="#ff6b35" size="20"></up-icon>
        <text class="treasure-title">业主百宝箱</text>
        <view class="new-badge">NEW</view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <up-cell-group :border="false">
        <up-cell
          title="业主认证"
          :value="getAuthStatus()"
          isLink
          @click="handleOwnerVerification"
          :border="false"
        >
          <template #icon>
            <up-icon name="checkmark-circle" color="#52c41a" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="分享用户"
          isLink
          @click="handleShare"
          :border="false"
        >
          <template #icon>
            <up-icon name="share" color="#52c41a" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="提个意见"
          isLink
          @click="handleFeedback"
          :border="false"
        >
          <template #icon>
            <up-icon name="edit-pen" color="#faad14" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="联系开发者"
          isLink
          @click="handleContactDeveloper"
          :border="false"
        >
          <template #icon>
            <up-icon name="account" color="#722ed1" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          v-permission="{ requireBackendAccess: true }"
          title="管理后台"
          isLink
          @click="handleAdmin"
          :border="false"
        >
          <template #icon>
            <up-icon name="setting" color="#f5222d" size="20"></up-icon>
          </template>
        </up-cell>
      </up-cell-group>
    </view>

    <!-- 意见反馈弹窗 -->
    <up-popup
      v-model:show="showFeedbackModal"
      mode="center"
      :round="10"
      :closeable="true"
      @close="closeFeedbackModal"
    >
      <view class="feedback-modal">
        <view class="modal-header">
          <text class="modal-title">意见反馈</text>
        </view>
        <view class="modal-content">
          <up-textarea
            v-model="feedbackContent"
            placeholder="请输入您的建议或意见..."
            :maxlength="500"
            count
            :autoHeight="true"
            :height="120"
          ></up-textarea>
        </view>
        <view class="modal-footer">
          <up-button
            text="提交"
            type="primary"
            :disabled="!feedbackContent.trim()"
            @click="submitFeedback"
            :loading="submittingFeedback"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import { useMemberStore } from '@/stores';
import type { LoginResult } from "@/types/member";
import type { AuthApplication } from '@/types/user';
import { setReactive } from '@/utils/util';
import { useSafeArea } from '@/utils/safeArea';
import { getRoleName } from '@/utils/rolePermissions';
import { getUserStatsAPI } from '@/services/login';
import { checkBackendAccess } from '@/services/permissionService';
import { getVerificationStatusAPI } from '@/services/verification';
import SignInComponent from '@/components/SignInComponent.vue';
const { pageHeaderPaddingTop } = useSafeArea()

// 登录状态
const isLoggedIn = ref(false);
// 用户信息
const userInfo = reactive<Partial<LoginResult>>({
  avatar: '',
  account: '',
  nickname: '',
  mobile: '',
  authType: null,
  isAdmin: null,
})

// 用户统计信息
const userStats = ref({
  postCount: 0,
  favoriteCount: 0,
  pointCount: 0
});

// 意见反馈相关
const showFeedbackModal = ref(false);
const feedbackContent = ref('');
const submittingFeedback = ref(false);

// 权限控制
const canAccessBackend = ref(false);

// 用户认证状态
const userAuthStatus = ref<AuthApplication | null>(null);

// 获取会员信息
const memberStore = useMemberStore()

// 添加标记避免重复请求
const isInitializing = ref(false)
const hasCheckedBackendAccess = ref(false)
const lastUserId = ref<number | null>(null)

const initData = async (forceRefresh = false) => {
  if (isInitializing.value && !forceRefresh) {
    console.log('initData - 正在初始化中，跳过重复调用');
    return;
  }

  isInitializing.value = true;
  const profile = memberStore.profile;
  console.log('initData - 当前 memberStore.profile:', profile);

  if (profile && profile.id) {
    // 检查是否是同一个用户
    const userChanged = lastUserId.value !== profile.id;
    lastUserId.value = profile.id;

    // 已登录状态
    setReactive(userInfo, profile);
    isLoggedIn.value = true;

    // 从用户信息中获取统计数据
    userStats.value = {
      postCount: profile.postCount || 0,
      favoriteCount: profile.favoriteCount || 0,
      pointCount: profile.pointCount || 0
    };
    console.log('initData - 设置为已登录状态，统计数据:', userStats.value);

    // 检查管理后台访问权限（只在用户变化或强制刷新时检查）
    if (!hasCheckedBackendAccess.value || forceRefresh || userChanged) {
      try {
        canAccessBackend.value = await checkBackendAccess();
        hasCheckedBackendAccess.value = true;
        console.log('initData - 管理后台访问权限:', canAccessBackend.value);
      } catch (error) {
        console.error('检查管理后台权限失败:', error);
        canAccessBackend.value = false;
      }
    } else {
      console.log('initData - 跳过权限检查，使用缓存结果:', canAccessBackend.value);
    }

    // 获取用户认证状态
    await fetchUserAuthStatus();
  } else {
    // 未登录状态，清空所有用户信息
    Object.assign(userInfo, {
      avatar: '',
      account: '',
      nickname: '',
      mobile: '',
      authType: null,
      isAdmin: null,
      id: undefined
    });
    isLoggedIn.value = false;
    canAccessBackend.value = false;
    hasCheckedBackendAccess.value = false; // 重置检查标记
    lastUserId.value = null; // 重置用户ID
    userAuthStatus.value = null; // 未登录用户无管理后台权限

    // 未登录时重置统计信息
    userStats.value = {
      postCount: 0,
      favoriteCount: 0,
      pointCount: 0
    };
    console.log('initData - 设置为未登录状态');
  }

  isInitializing.value = false;
}

// 防止短时间内重复调用的标志
let lastRefreshTime = 0;
const REFRESH_INTERVAL = 1000; // 1秒内不重复调用

// 刷新用户统计数据
const refreshUserStats = async () => {
  if (!isLoggedIn.value) {
    console.log('用户未登录，跳过刷新统计数据');
    return;
  }

  // 防止短时间内重复调用
  const now = Date.now();
  if (now - lastRefreshTime < REFRESH_INTERVAL) {
    console.log('距离上次刷新时间太短，跳过重复调用');
    return;
  }
  lastRefreshTime = now;

  // 检查token是否存在
  const memberStore = useMemberStore();
  const token = memberStore.profile?.token;
  console.log('当前用户token:', token ? '存在' : '不存在');
  console.log('当前用户ID:', memberStore.profile?.id);

  if (!token) {
    console.log('没有token，跳过刷新统计数据');
    return;
  }

  try {
    console.log('开始调用用户统计API...');
    const res = await getUserStatsAPI();
    console.log('用户统计API响应:', res);

    if (res.success && res.data) {
      userStats.value = {
        postCount: res.data.postCount || 0,
        favoriteCount: res.data.favoriteCount || 0,
        pointCount: res.data.pointCount || 0
      };
      console.log('刷新统计数据成功:', userStats.value);
    } else {
      console.log('API返回失败:', res);
    }
  } catch (error) {
    console.error('刷新统计数据失败:', error);
  }
}

// 删除未使用的 checkLoginStatus 函数

onShow(() => {
  // 页面显示时只刷新统计数据，不重复初始化
  if (isLoggedIn.value) {
    setTimeout(() => {
      refreshUserStats();
    }, 100);
  } else {
    // 如果未登录，则初始化数据
    initData();
  }
});

// 监听 memberStore.profile 的变化
watch(() => memberStore.profile, (newProfile, oldProfile) => {
  console.log('监听到 memberStore.profile 变化:', newProfile);
  // 只有在用户真正变化时才重新初始化
  if (newProfile?.id !== oldProfile?.id) {
    console.log('用户ID变化，重新初始化:', oldProfile?.id, '->', newProfile?.id);
    hasCheckedBackendAccess.value = false; // 重置检查标记
    initData(true); // 强制刷新
  } else {
    console.log('用户ID未变化，跳过重新初始化');
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 监听用户退出登录事件
  uni.$on('userLogout', () => {
    console.log('我的页面收到用户退出登录事件');
    // 立即重新初始化数据
    initData();
  });

  // 监听用户登录成功事件
  uni.$on('userLogin', () => {
    console.log('我的页面收到用户登录成功事件');
    // 立即重新初始化数据并刷新统计
    initData();
    refreshUserStats();
  });

  // 监听刷新我的页面事件
  uni.$on('refreshMyPage', () => {
    console.log('我的页面收到刷新页面事件');
    // 立即重新初始化数据并刷新统计
    initData();
    refreshUserStats();
  });

  // 监听签到成功事件
  uni.$on('userSignSuccess', (data) => {
    console.log('我的页面收到签到成功事件:', data);
    // 签到成功后刷新用户统计数据
    refreshUserStats();
  });
});

onUnmounted(() => {
  // 移除事件监听
  uni.$off('userLogout');
  uni.$off('userLogin');
  uni.$off('refreshMyPage');
  uni.$off('userSignSuccess');
});

// 处理登录逻辑
const handleLogin = () => {
  // 跳转到登录页面
  uni.navigateTo({
    url: `/pages/login/index`
  });
};

// 处理退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除用户信息
        memberStore.clearProfile();

        // 立即清除页面数据
        Object.assign(userInfo, {
          avatar: '',
          account: '',
          nickname: '',
          mobile: '',
          authType: null,
          isAdmin: null,
          id: undefined
        });
        isLoggedIn.value = false;
        userStats.value = {
          postCount: 0,
          favoriteCount: 0,
          pointCount: 0
        };

        // 显示退出成功提示
        uni.showToast({
          title: '退出成功',
          icon: 'success'
        });

        // 发送全局事件通知其他页面更新状态
        uni.$emit('userLogout');

        // 延迟跳转到首页并刷新
        // setTimeout(() => {
        //   uni.switchTab({
        //     url: '/pages/index/index',
        //     success: () => {
        //       // 发送刷新事件
        //       uni.$emit('refreshAfterLogout');
        //     }
        //   });
        // }, 1500);
      }
    }
  });
};


// 处理业主认证逻辑
const handleOwnerVerification = () => {
  if (!isLoggedIn.value) {
    handleLogin();
    return;
  }
  uni.navigateTo({
    url: '/pages/owner-verification/index'
  });
};

// 处理分享逻辑
const handleShare = () => {
  // 这里应添加实际的分享逻辑
  console.log('分享');
};

// 获取用户认证状态
const fetchUserAuthStatus = async () => {
  try {
    console.log('=== 获取用户认证状态 ===');
    const response = await getVerificationStatusAPI();

    if (response.success) {
      userAuthStatus.value = response.data || null;
      console.log('用户认证状态:', response.data);
    } else {
      console.log('获取认证状态失败:', response.message);
      userAuthStatus.value = null;
    }
  } catch (error) {
    console.error('获取用户认证状态失败:', error);
    userAuthStatus.value = null;
  }
};

// 获取认证状态文本
const getAuthStatus = () => {
  if (!isLoggedIn.value) return '请先登录';

  if (!userAuthStatus.value) {
    return '未认证';
  }

  switch (userAuthStatus.value.status) {
    case 0:
      return '审核中';
    case 1:
      return '已认证';
    case 2:
      return '认证失败';
    default:
      return '未认证';
  }
};

// 处理头像点击
const handleAvatarClick = () => {
  if (!isLoggedIn.value) {
    handleLogin();
  } else {
    handleEditProfile();
  }
};

// 显示更多操作
const showMoreActions = () => {
  uni.showActionSheet({
    itemList: ['设置', '关于我们', '退出登录'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          console.log('设置');
          break;
        case 1:
          console.log('关于我们');
          break;
        case 2:
          if (isLoggedIn.value) {
            handleLogout();
          }
          break;
      }
    }
  });
};

// 编辑个人资料
const handleEditProfile = () => {
  if (!isLoggedIn.value) {
    handleLogin();
    return;
  }
  uni.navigateTo({
    url: '/pages/profile/edit'
  });
};

// 我的动态
const handleMyPosts = () => {
  if (!isLoggedIn.value) {
    handleLogin();
    return;
  }
  uni.navigateTo({
    url: '/pages/my/posts'
  });
};

// 我的收藏
const handleMyCollections = () => {
  if (!isLoggedIn.value) {
    handleLogin();
    return;
  }
  uni.navigateTo({
    url: '/pages/my/collections'
  });
};

// 我的积分
const handleMyPoints = () => {
  if (!isLoggedIn.value) {
    handleLogin();
    return;
  }
  uni.navigateTo({
    url: '/pages/my/points'
  });
};

// 联系开发者
const handleContactDeveloper = () => {
  uni.showModal({
    title: '联系开发者',
    content: '如有问题或建议，请通过意见反馈功能联系我们',
    showCancel: false
  });
};

// 处理意见反馈逻辑
const handleFeedback = () => {
  if (!isLoggedIn.value) {
    handleLogin();
    return;
  }
  showFeedbackModal.value = true;
};

// 关闭反馈弹窗
const closeFeedbackModal = () => {
  showFeedbackModal.value = false;
  feedbackContent.value = '';
};

// 提交反馈
const submitFeedback = async () => {
  if (!feedbackContent.value.trim()) {
    uni.showToast({
      title: '请输入反馈内容',
      icon: 'none'
    });
    return;
  }

  submittingFeedback.value = true;

  try {
    // 这里应该调用实际的API
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用

    uni.showToast({
      title: '反馈提交成功',
      icon: 'success'
    });

    closeFeedbackModal();
  } catch (error) {
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none'
    });
  } finally {
    submittingFeedback.value = false;
  }
};

// 处理后台管理逻辑
const handleAdmin = async () => {
  try {
    // 二次验证权限，确保安全性
    const hasAccess = await checkBackendAccess();
    if (!hasAccess) {
      uni.showToast({
        title: '权限不足，无法访问管理后台',
        icon: 'none'
      });
      return;
    }

    uni.navigateTo({
      url: '/pages/admin/index'
    });
  } catch (error) {
    console.error('访问管理后台失败:', error);
    uni.showToast({
      title: '访问失败，请稍后重试',
      icon: 'none'
    });
  }
};

// 暴露刷新统计数据的方法，供其他页面调用
defineExpose({
  refreshUserStats
});
</script>

<style lang="scss">
.my-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  font-size: 14px;

  // 用户信息头部
  .user-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    margin-bottom: 10px;
    border-radius: 0 0 20px 20px;
    color: white;

    .logged-in-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .user-profile {
        display: flex;
        align-items: center;
        flex: 1;

        .user-avatar {
          border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .user-details {
          margin-left: 16px;
          flex: 1;

          .user-name-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .user-name {
              font-size: 20px;
              font-weight: bold;
              margin-right: 10px;
            }

            .auth-badge {
              display: flex;
              align-items: center;
              background-color: rgba(255, 255, 255, 0.2);
              padding: 2px 8px;
              border-radius: 12px;

              .auth-text {
                font-size: 12px;
                margin-left: 4px;
              }
            }
          }

          .user-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 6px;

            .user-id {
              font-size: 12px;
              opacity: 0.8;
            }

            .user-level {
              font-size: 12px;
              background-color: rgba(255, 255, 255, 0.2);
              padding: 2px 8px;
              border-radius: 10px;
            }
          }

          .user-mobile {
            display: flex;
            align-items: center;
            gap: 4px;
            opacity: 0.8;

            .mobile-text {
              font-size: 12px;
            }
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 8px;

        .action-btn {
          width: 36px;
          height: 36px;
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .not-logged-in-header {
      text-align: center;
      padding: 20px 0;

      .login-prompt {
        display: flex;
        flex-direction: column;
        align-items: center;

        .default-avatar {
          border: 3px solid rgba(255, 255, 255, 0.3);
          margin-bottom: 16px;
        }

        .login-content {
          display: flex;
          flex-direction: column;
          align-items: center;

          .welcome-text {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
          }

          .login-tip {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 16px;
          }
        }
      }
    }
  }

    .header-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .edit-profile {
        display: flex;
        align-items: center;
        color: #666;
        font-size: 14px;

        text {
          margin-right: 4px;
        }
      }
    }
  }

  // 签到功能区域
  .sign-section {
    background: #fff;
    margin-bottom: 10px;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  // 统计数据
  .stats-section {
    background: #fff;
    display: flex;
    padding: 20px 0;
    margin-bottom: 10px;

    .stats-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;

      .stats-number {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        margin-bottom: 4px;
      }

      .stats-label {
        font-size: 14px;
        color: #666;
      }

      &:active {
        opacity: 0.7;
      }
    }
  }

  // 业主百宝箱
  .treasure-box {
    background: #fff;
    margin-bottom: 10px;
    padding: 15px 20px;

    .treasure-header {
      display: flex;
      align-items: center;

      .treasure-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-left: 8px;
        flex: 1;
      }

      .new-badge {
        background: #ff4d4f;
        color: #fff;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 8px;
      }
    }
  }

  // 功能菜单
  .menu-section {
    background: #fff;
    margin-bottom: 10px;

    :deep(.u-cell) {
      padding: 15px 20px;

      .u-cell__title {
        font-size: 16px;
        color: #333;
      }

      .u-cell__value {
        font-size: 14px;
        color: #999;
      }
    }
  }

  // 意见反馈弹窗
  .feedback-modal {
    width: 300px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;

      .modal-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }

    .modal-content {
      padding: 20px;

      :deep(.u-textarea) {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        padding: 10px;
      }
    }

    .modal-footer {
      padding: 0 20px 20px;

      :deep(.u-button) {
        width: 100%;
        height: 44px;
        border-radius: 22px;
      }
    }
  }
</style>