package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@TableName("post_files")
@EqualsAndHashCode(callSuper = true)
public class PostFiles extends BaseEntity{

    private Long postId;

    private Long userId;

    private String fileName;

    private String filePath;

    private Long fileSize;

    private String fileType;

    private String mimeType;

    private Integer sortOrder;
}