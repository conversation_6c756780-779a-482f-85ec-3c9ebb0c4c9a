package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.haolinkyou.entity.Categories;
import com.haolinkyou.mapper.CategoriesMapper;
import com.haolinkyou.service.ICategoriesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CategoriesServiceImpl implements ICategoriesService {
    @Autowired
    private CategoriesMapper categoriesMapper;
    @Override
    public List<Categories> selectList() {
        LambdaQueryWrapper<Categories> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Categories::getStatus ,1);
        return categoriesMapper.selectList(queryWrapper);
    }
}
