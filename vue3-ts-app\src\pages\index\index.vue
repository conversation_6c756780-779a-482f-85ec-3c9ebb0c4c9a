<!--
 * @Author: Rock
 * @Date: 2025-04-09 19:56:09
 * @LastEditors: Rock
 * @LastEditTime: 2025-07-30 14:08:51
 * @Description: 
-->
<template>
  <view class="index-page">
    <view class="page-header" :style="{ paddingTop: pageHeaderPaddingTop }">
      <up-icon name="home" color="#fff" size="22"></up-icon>
      <view class="header-title">大唐世家三期好邻友</view>
      <up-icon name="search" color="#fff" size="22" @click="handleSearchClick"></up-icon>
    </view>
    <view class="main-content" :style="{ paddingTop: mainContentPaddingTop }">
      <view class="top-nav-box-wrap">
        <view class="left">
          <view class="nav-item-title"> 三期商圈</view>
          <view class="nav-item-desc">
            这里是三期商圈:包含商铺、业主自营店铺,共同营造良好的商业氛围
          </view>
          <view class="nav-item-btn">
            <view class="custom-btn">查看详情</view>
          </view>
        </view>
        <view class="gap"></view>
        <view class="right">
          <view class="right-top">
            <view class="nav-item-title">三期微信群</view>
            <view class="nav-item-desc"> </view>
            <view class="nav-item-btn">
              <view class="custom-btn">去加群</view>
            </view>
          </view>
          <view class="right-bottom">
            <view class="nav-item-title">三期红黑榜</view>
            <view class="nav-item-desc"></view>
            <view class="nav-item-btn">
              <view class="custom-btn">去查看</view>
            </view>
          </view>
        </view>
      </view>
      <view class="category-tabs wrap">
        <up-tabs :list="categoryListData" :current="currentTab" @change="handleTabClick"></up-tabs>
      </view>
    </view>

    <view class="post-list-wrap">
      <up-list @scrolltolower="loadMore" :show-scrollbar="false">
        <view v-for="(post, index) in postListData" :key="index" class="post-wrap wrap">
          <view class="post-header">
            <up-avatar :text="post.nickname" fontSize="18" randomBgColor></up-avatar>
            <view class="info">
              <view class="user-info">
                <view class="name-row">
                  <view class="name">{{ post.nickname }}</view>
                  <view class="sex">
                    <up-icon v-if="post.userGender !== 0" :name="post.userGender === 1 ? 'man' : 'woman'" size="18"
                      :color="post.userGender === 1 ? '#4396F7' : '#FF2442'"></up-icon>
                  </view>
                  <!-- 用户认证状态标签 -->
                  <up-tag :text="getRoleStatusTag(post.userIsVerified, post.userRole).text" size="mini"
                    :color="getRoleStatusTag(post.userIsVerified, post.userRole).color"
                    :bgColor="getRoleStatusTag(post.userIsVerified, post.userRole).bgColor" borderColor="transparent"
                    shape="circle" plain plainFill />

                  <!-- 帖子状态标签（仅对发帖人和管理员显示） -->
                  <up-tag
                    v-if="shouldShowPostStatus(post, memberStore.profile?.id, memberStore.profile?.isAdmin ?? false)"
                    :text="getPostStatusInfo(post.status).text" size="mini"
                    :color="getPostStatusInfo(post.status).color" :bgColor="getPostStatusInfo(post.status).bgColor"
                    borderColor="transparent" shape="circle" plain plainFill style="margin-left: 4px;" />
                </view>
              </view>
              <view class="post-time">{{ formatPostTime(post) }}</view>
              <!-- 置顶标识 -->
              <PostTopBadge :isTopped="post.isTop" :topExpireTime="post.topExpireTime" :showExpireTime="false" />
            </view>
          </view>
          <view class="post-content-wrap" @click="goToPostDetail(post.id)">
            <up-parse class="post-content" :content="post.content"></up-parse>
            <!-- 媒体文件展示区域（支持图片、视频、混合） -->
            <view class="post-media" v-if="post.mediaFiles && post.mediaFiles.length > 0">
              <!-- 单个媒体文件 -->
              <template v-if="post.mediaFiles.length === 1">
                <!-- 单张图片 -->
                <up-image v-if="post.mediaFiles[0].type === 'image'" :src="post.mediaFiles[0].url" width="100%"
                  :height="getImageHeight(1)" radius="8" :fade="true" duration="450" :lazy-load="true" mode="aspectFill"
                  :show-loading="true" class="single-media-item" @click="previewMedia(post.mediaFiles, 0)">
                  <template v-slot:loading>
                    <up-loading-icon />
                  </template>
                </up-image>

                <!-- 单个视频 -->
                <view v-else-if="post.mediaFiles[0].type === 'video'" class="single-video-container">
                  <video :src="post.mediaFiles[0].url" class="single-video" :show-center-play-btn="true"
                    :controls="true" :show-play-btn="true" :show-fullscreen-btn="true" :show-progress="true"
                    object-fit="cover" preload="metadata" @click="playVideo(post.mediaFiles[0].url)" 
                    @error="handleVideoError" />
                </view>
              </template>

              <!-- 多个媒体文件网格布局 -->
              <template v-else>
                <view class="media-grid" :class="getGridClass(post.mediaFiles.length)">
                  <view v-for="(media, index) in post.mediaFiles.slice(0, 9)" :key="index" class="grid-item"
                    :class="getItemClass(post.mediaFiles.length)" @click="previewMedia(post.mediaFiles, index)">
                    <!-- 网格中的图片 -->
                    <up-image v-if="media.type === 'image'" :src="media.url" width="100%" height="100%" radius="6"
                      :fade="true" duration="450" :lazy-load="true" mode="aspectFill" :show-loading="true">
                      <template v-slot:loading>
                        <up-loading-icon />
                      </template>
                    </up-image>

                    <!-- 网格中的视频缩略图 -->
                    <view v-else-if="media.type === 'video'" class="grid-video-item">
                      <video :src="media.url" width="100%" height="100%" :show-center-play-btn="false" :controls="false"
                        :show-play-btn="false" muted object-fit="cover" preload="metadata"
                        style="border-radius: 6px;" @error="handleVideoError" />
                      <view class="video-play-icon">
                        <up-icon name="play-circle-fill" size="20" color="rgba(255,255,255,0.9)"></up-icon>
                      </view>
                    </view>

                    <!-- 超过9个文件时显示数量 -->
                    <view v-if="index === 8 && post.mediaFiles.length > 9" class="more-count">
                      +{{ post.mediaFiles.length - 9 }}
                    </view>
                  </view>
                </view>
              </template>
            </view>
          </view>
          <view class="post-foot">
            <up-row>
              <up-col span="6" class="post-type">
                <up-tag :text="post.categoryName" size="mini" plain plainFill borderColor="transparent"
                  :autoBgColor="95" @click="handleTagClick(post.categoryId)" v-if="currentTab === 0"></up-tag>
              </up-col>
              <up-col span="5" class="toolbar" offset="1">
                <!-- 点赞按钮 -->
                <view class="action-item">
                  <up-icon stop :name="post.isLiked ? 'thumb-up-fill' : 'thumb-up'" size="22"
                    :color="post.isLiked ? '#FF2442' : '#9fa0a3'" @click="handleLike(post)"></up-icon>
                  <text class="count-text">{{ post.likeCount || 0 }}</text>
                </view>

                <!-- 收藏按钮 -->
                <view class="action-item">
                  <up-icon stop :name="post.isCollected ? 'star-fill' : 'star'" size="22"
                    :color="post.isCollected ? '#EBAF58' : '#9fa0a3'" @click="handleCollect(post)"></up-icon>
                  <text class="count-text">{{ post.collectCount || 0 }}</text>
                </view>

                <!-- 评论按钮 -->
                <view class="action-item" @click="goToPostDetailWithComment(post.id)">
                  <up-icon name="chat" size="22" color="#9fa0a3"></up-icon>
                  <text class="count-text">{{ post.commentCount || 0 }}</text>
                </view>

                <!-- 删除按钮：只有帖子作者或管理员可见 -->
                <up-icon v-if="canDeletePost(post)" stop name="trash" size="22" color="#FF2442"
                  @click="handleDelete(post)"></up-icon>
              </up-col>
            </up-row>
          </view>
        </view>

        <!-- 加载状态 -->
        <up-loadmore :status="loadStatus" :loading-text="loadingText" :loadmore-text="loadmoreText"
          :nomore-text="nomoreText" />
      </up-list>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useSafeArea } from '@/utils/safeArea'
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { onShow, onHide, onLaunch } from '@dcloudio/uni-app';
import { get } from '@/utils/http';
import { setFileList } from '@/utils/util';
import { toggleLike, toggleCollect, deletePost } from '@/services/likeCollectService';
import { useMemberStore } from '@/stores';
import { getRoleStatusTag, isAdmin, getPostStatusInfo, shouldShowPostStatus } from '@/utils/rolePermissions';
import { formatPostTime } from '@/utils/timeFormat';
import PostTopBadge from '@/components/PostTopBadge.vue';
import { processMediaFiles, safePlayVideo, checkServerStatus } from '@/utils/mediaUtils';
import type { PostsList, Categories, ApiResponse, MediaFile } from '@/types';


const { pageHeaderPaddingTop, mainContentPaddingTop } = useSafeArea()

// 获取用户信息
const memberStore = useMemberStore();



// 创建一个字典类型
interface Dict {
  id: string | number;
  name: string;
}


// 分类列表数据
const categoryListData = ref<Dict[]>([]);
// 获取分类列表
const fetchCategoryList = async (): Promise<void> => {
  //清空分类数据
  categoryListData.value = [{ id: 0, name: "全部" }];
  try {
    const res = await get<ApiResponse<Categories[]>>('/categories/list');
    if (res.success) {
      const listData = res.data || [];
      if (listData.length > 0) {
        // 将data记录的id跟categoryName作为键值对存储到字典中
        res.data?.forEach((item: Categories) => {
          categoryListData.value.push({
            id: item.id.toString(),
            name: item.categoryName,
          })
        });
      }
      console.log('获取分类列表成功:', res, categoryListData.value);
    } else {
      uni.showToast({
        title: res.msg || '获取分类列表失败',
        icon: 'none'
      });
    }

  }
  catch (error) {
    console.error('获取分类列表失败:', error);
  }
}

// 帖子列表数据
const postListData = ref<PostsList[]>([]);

// 分页相关变量
const currentPage = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const loadStatus = ref<'loadmore' | 'loading' | 'nomore'>('loadmore');
const loadingText = ref('正在加载...');
const loadmoreText = ref('上拉加载更多');
const nomoreText = ref('已经到底了');

// 获取帖子列表（首次加载）
const fetchPostList = async (): Promise<void> => {
  currentPage.value = 1;
  hasMore.value = true;
  loadStatus.value = 'loading';
  postListData.value = [];

  await loadPostList();
};

// 加载帖子列表数据
const loadPostList = async (): Promise<void> => {
  const params: Record<string, string | number> = {
    categoryId: currentTab.value === 0 ? '' : currentTab.value,
    page: currentPage.value,
    pageSize: pageSize.value
  };

  // 用户ID现在由后端从JWT token中获取，不需要前端传递

  try {
    const res = await get<ApiResponse<PostsList[]>>('/posts/listAll', params);
    if (res.success) {
      const listData: PostsList[] = res.data.records || [];

      // 处理帖子数据，包括文件列表和用户状态
      listData.forEach((item: PostsList) => {
        // 处理媒体文件列表（支持图片、视频、混合）
        if (item.fileList !== null && item.fileList !== '') {
          item.mediaFiles = processMediaFiles(item.fileList, item.fileType);
          // 保持向后兼容
          if (item.fileType === 0) {
            item.fileListData = setFileList(item.fileList);
          }
        }

        // 处理用户状态字段类型转换
        if (memberStore.profile?.id) {
          // 已登录用户：确保字段为布尔类型
          item.isLiked = Boolean(item.isLiked);
          item.isCollected = Boolean(item.isCollected);
        } else {
          // 未登录用户：设置为未激活状态
          item.isLiked = false;
          item.isCollected = false;
        }

        // 确保数字字段为数字类型
        item.likeCount = Number(item.likeCount) || 0;
        item.collectCount = Number(item.collectCount) || 0;
        item.commentCount = Number(item.commentCount) || 0;
      });

      if (currentPage.value === 1) {
        postListData.value = listData || [];
      } else {
        postListData.value.push(...(listData || []));
      }

      // 判断是否还有更多数据
      if (!listData || listData.length < pageSize.value) {
        hasMore.value = false;
        loadStatus.value = 'nomore';
      } else {
        loadStatus.value = 'loadmore';
      }

      console.log('获取帖子列表成功:', res);

      // 调试：检查用户状态字段
      if (listData && listData.length > 0) {
        console.log('第一个帖子的原始数据:', listData[0]);
        console.log('第一个帖子的状态字段:', {
          id: listData[0].id,
          isLiked: listData[0].isLiked,
          isCollected: listData[0].isCollected,
          likeCount: listData[0].likeCount,
          collectCount: listData[0].collectCount,
          loginStatus: memberStore.profile?.id ? '已登录' : '未登录',
          userToken: memberStore.profile?.token ? '有token' : '无token'
        });
      }

      console.log('处理后的帖子列表数据:', postListData.value.slice(0, 2)); // 只显示前2个帖子的数据
    } else {
      loadStatus.value = 'loadmore';
      uni.showToast({
        title: res.msg || '获取帖子列表失败',
        icon: 'none'
      });
    }
  } catch (error) {
    loadStatus.value = 'loadmore';
    console.error('获取帖子列表失败:', error);
    uni.showToast({
      title: '获取帖子列表失败',
      icon: 'none'
    });
  }
};

// 上拉加载更多
const loadMore = async (): Promise<void> => {
  if (!hasMore.value || loadStatus.value === 'loading') {
    return;
  }

  loadStatus.value = 'loading';
  currentPage.value++;
  await loadPostList();
};


// 当前选中的标签页
const currentTab = ref(0);

// onLaunch 生命周期
onLaunch(() => {
  console.log("onLaunch");
  // 移除 uni.hideTabBar() 调用，保持 tabBar 可见
})

// 处理标签页点击
const handleTabClick = (item: { index: number; name: string }) => {
  currentTab.value = item.index;
  console.log(`切换到第 ${item.index + 1} 个标签页:${item.name}`);
  // 这里可以添加切换内容的逻辑
  fetchPostList();
};

// 监听用户登录状态变化
watch(() => memberStore.profile?.id, (newUserId, oldUserId) => {
  console.log('监听到用户登录状态变化:', { newUserId, oldUserId });
  // 当用户登录状态发生变化时，重新获取帖子列表
  if (newUserId !== oldUserId) {
    console.log('用户登录状态改变，重新获取帖子列表');
    fetchPostList();
  }
});

onMounted(() => {
  fetchCategoryList();
  fetchPostList();

  // 监听发布成功事件，刷新列表
  uni.$on('refreshPostList', () => {
    console.log('收到刷新列表事件')
    fetchPostList();
  })

  // 监听用户登录成功事件，刷新页面状态
  uni.$on('userLogin', () => {
    console.log('收到用户登录成功事件，刷新页面状态')
    // 重新获取帖子列表（此时会以登录用户身份获取，包含用户状态）
    fetchPostList();
  })

  // 监听用户退出登录事件，刷新页面状态
  uni.$on('userLogout', () => {
    console.log('收到用户退出登录事件，刷新页面状态')
    // 重新获取帖子列表（此时会以游客身份获取，不包含用户状态）
    fetchPostList();
  })

  // 监听退出登录后的刷新事件
  uni.$on('refreshAfterLogout', () => {
    console.log('收到退出登录后刷新事件')
    // 重新获取帖子列表
    fetchPostList();
  })

  // 添加页面可见性监听（用于处理从其他页面返回的情况）
  // #ifdef H5
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      console.log('页面重新可见，滚动到顶部')
      scrollToTop()
    }
  })
  // #endif

  // 添加窗口焦点监听（备用方案）
  // #ifdef H5
  window.addEventListener('focus', () => {
    console.log('窗口重新获得焦点，滚动到顶部')
    scrollToTop()
  })
  // #endif
})

onUnmounted(() => {
  // 移除事件监听
  uni.$off('refreshPostList')
  uni.$off('userLogin')
  uni.$off('userLogout')
  uni.$off('refreshAfterLogout')
  uni.$off('needRefreshHome')
});

// 标记是否是首次加载
const isFirstLoad = ref(true)
// 标记页面是否被隐藏过
const wasHidden = ref(false)

// 滚动到顶部的通用方法
const scrollToTop = () => {
  console.log('执行滚动到顶部')
  setTimeout(() => {
    uni.pageScrollTo({
      scrollTop: 0,
      duration: 300
    })
  }, 100)
}

// 页面隐藏时标记状态
onHide(() => {
  console.log('首页onHide触发')
  wasHidden.value = true
})

// 页面显示时检查是否需要刷新
onShow(() => {
  console.log('首页onShow触发，检查是否需要刷新')

  // 如果是首次加载，跳过数据获取（因为onMounted已经加载了）
  if (isFirstLoad.value) {
    console.log('首次加载，跳过onShow中的数据获取')
    isFirstLoad.value = false
    return
  }

  // 检查全局刷新标记
  const globalData = getApp().globalData || {}
  const needRefresh = globalData.needRefreshHome

  if (needRefresh) {
    console.log('检测到全局刷新标记，执行刷新')

    // 清空列表并重新加载
    postListData.value = []
    currentPage.value = 1
    hasMore.value = true
    loadStatus.value = 'loading'

    // 重新获取数据
    fetchPostList()

    // 清除全局刷新标记
    globalData.needRefreshHome = false
    console.log('清除全局刷新标记')
  }

  // 每次页面显示都滚动到顶部
  scrollToTop()

  // 如果页面被隐藏过，重置状态
  if (wasHidden.value) {
    wasHidden.value = false
  }

  // 清除滚动标记（如果存在）
  if (globalData.scrollToTop) {
    globalData.scrollToTop = false
  }
});




// 点击列表中主题分类标签
const handleTagClick = (categoryId: number) => {
  currentTab.value = categoryId;
  fetchPostList();
}

// 处理搜索点击
const handleSearchClick = () => {
  uni.navigateTo({
    url: '/pages/search/search'
  })
}

// 获取屏幕宽度
const screenWidth = ref(0);
uni.getSystemInfo({
  success: (res) => {
    screenWidth.value = res.screenWidth;
  }
});



// 处理媒体文件列表
const processMediaFiles = (fileList: string, fileType: number): MediaFile[] => {
  if (!fileList) return [];

  const files = fileList.split(',').filter(file => file.trim());
  return files.map(file => {
    const relativePath = file.trim();
    const url = `http://localhost:3205/${relativePath}`; // 使用完整URL
    const extension = relativePath.toLowerCase().split('.').pop() || '';

    // 判断文件类型
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv'];

    let type: 'image' | 'video' | 'unknown' = 'unknown';
    if (imageExtensions.includes(extension)) {
      type = 'image';
    } else if (videoExtensions.includes(extension)) {
      type = 'video';
    }

    return {
      url,
      type,
      extension,
      poster: undefined // 让video组件自动生成缩略图
    };
  });
};

// 预览媒体文件
const previewMedia = (mediaFiles: MediaFile[], currentIndex: number) => {
  const currentMedia = mediaFiles[currentIndex];

  if (currentMedia.type === 'image') {
    // 图片预览
    const imageUrls = mediaFiles
      .filter(media => media.type === 'image')
      .map(media => media.url);

    const currentImageIndex = imageUrls.indexOf(currentMedia.url);

    uni.previewImage({
      urls: imageUrls,
      current: currentImageIndex >= 0 ? currentImageIndex : 0
    });
  } else if (currentMedia.type === 'video') {
    // 视频播放
    playVideo(currentMedia.url);
  }
};

// 播放视频
const playVideo = (videoUrl: string) => {
  // 检查视频URL是否可访问
  const checkVideoAccess = () => {
    return new Promise((resolve, reject) => {
      // 创建一个临时的video元素来测试视频是否可以加载
      const video = document.createElement('video');
      video.onloadedmetadata = () => resolve(true);
      video.onerror = () => reject(false);
      video.src = videoUrl;
      video.load();
    });
  };

  // 使用uni.previewMedia播放视频（如果支持）
  if (uni.previewMedia) {
    checkVideoAccess()
      .then(() => {
        uni.previewMedia({
          sources: [{
            url: videoUrl,
            type: 'video'
          }]
        });
      })
      .catch(() => {
        uni.showToast({
          title: '视频文件无法访问，请检查服务器状态',
          icon: 'none',
          duration: 3000
        });
      });
  } else {
    // 降级方案：在H5环境下直接播放
    // #ifdef H5
    checkVideoAccess()
      .then(() => {
        window.open(videoUrl, '_blank');
      })
      .catch(() => {
        uni.showToast({
          title: '视频文件无法访问，请检查服务器状态',
          icon: 'none',
          duration: 3000
        });
      });
    // #endif
    
    // #ifndef H5
    uni.showToast({
      title: '当前平台不支持视频预览',
      icon: 'none'
    });
    // #endif
  }
};

// 获取图片高度
const getImageHeight = (count: number) => {
  if (count === 1) return '200px';
  return '120px';
};

// 获取网格布局类名
const getGridClass = (count: number) => {
  if (count === 2) return 'grid-2';
  if (count === 3) return 'grid-3';
  if (count === 4) return 'grid-4';
  if (count >= 5) return 'grid-9';
  return 'grid-3';
};

// 获取网格项类名
const getItemClass = (totalCount: number) => {
  const classes = [];

  // 2张图片时的特殊处理
  if (totalCount === 2) {
    classes.push('item-2');
  }
  // 4张图片时的特殊处理
  else if (totalCount === 4) {
    classes.push('item-4');
  }

  return classes.join(' ');
};
// 处理点赞
const handleLike = async (post: PostsList): Promise<void> => {
  console.log('点赞按钮被点击，帖子:', post);

  // 检查登录状态
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    return;
  }

  // 防止重复点击
  if (post.isProcessing) {
    return;
  }
  post.isProcessing = true;

  try {
    console.log('发送点赞请求，帖子ID:', post.id);
    const result = await toggleLike(Number(post.id));
    console.log('点赞API响应:', result);

    // 处理后端响应格式 {success: boolean, message: string, data: T}
    if (result.success) {
      const data = result.data;
      if (data) {
        // 确保数据类型正确
        const isLiked = Boolean(data.isLiked);
        const likeCount = Number(data.likeCount) || 0;

        // 更新帖子的点赞状态
        post.isLiked = isLiked;
        post.likeCount = likeCount;

        // 同时更新postListData中对应的帖子数据
        const postIndex = postListData.value.findIndex(p => p.id === post.id);
        if (postIndex !== -1) {
          postListData.value[postIndex].isLiked = isLiked;
          postListData.value[postIndex].likeCount = likeCount;
        }

        uni.showToast({
          title: data.message || (isLiked ? '点赞成功' : '取消点赞'),
          icon: 'success'
        });
      }
    } else {
      console.error('点赞操作失败:', result);
      uni.showToast({
        title: result.message || '操作失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('点赞操作异常:', error);
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    });
  } finally {
    // 重置处理状态
    post.isProcessing = false;
  }
};

// 处理收藏
const handleCollect = async (post: PostsList): Promise<void> => {
  console.log('收藏按钮被点击，帖子:', post);

  // 检查登录状态
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    return;
  }

  // 防止重复点击
  if (post.isProcessing) {
    return;
  }
  post.isProcessing = true;

  try {
    console.log('发送收藏请求，帖子ID:', post.id);
    const result = await toggleCollect(Number(post.id));
    console.log('收藏API响应:', result);

    // 处理后端响应格式 {success: boolean, message: string, data: T}
    if (result.success) {
      const data = result.data;
      if (data) {
        // 确保数据类型正确
        const isCollected = Boolean(data.isCollected);
        const collectCount = Number(data.collectCount) || 0;

        // 更新帖子的收藏状态
        post.isCollected = isCollected;
        post.collectCount = collectCount;

        // 同时更新postListData中对应的帖子数据
        const postIndex = postListData.value.findIndex(p => p.id === post.id);
        if (postIndex !== -1) {
          postListData.value[postIndex].isCollected = isCollected;
          postListData.value[postIndex].collectCount = collectCount;
        }

        uni.showToast({
          title: data.message || (isCollected ? '收藏成功' : '取消收藏'),
          icon: 'success'
        });
      }
    } else {
      console.error('收藏操作失败:', result);
      uni.showToast({
        title: result.message || '操作失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('收藏操作异常:', error);
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    });
  } finally {
    // 重置处理状态
    post.isProcessing = false;
  }
};

// 跳转到帖子详情
const goToPostDetail = (postId?: number) => {
  if (postId === undefined) return;
  console.log(`跳转到帖子详情：${postId}`);

  uni.navigateTo({
    url: `/pages/post/detail?id=${postId}`
  });
};

// 跳转到帖子详情并滚动到评论区域
const goToPostDetailWithComment = (postId?: number) => {
  if (postId === undefined) return;
  console.log(`跳转到帖子详情并滚动到评论区域：${postId}`);

  uni.navigateTo({
    url: `/pages/post/detail?id=${postId}&scrollToComment=true`
  });
};

// 判断是否可以删除帖子
const canDeletePost = (post: PostsList): boolean => {
  // 检查用户是否已登录
  if (!memberStore.profile?.id) {
    return false;
  }

  const profile = memberStore.profile;

  // 帖子作者可以删除自己的帖子
  const isAuthor = post.userId === profile.id;

  // 管理员可以删除任意帖子（优先检查 userRole，fallback 到 isAdmin 字段）
  const isAdminUser = isAdmin(profile.userRole ?? undefined) || profile.isAdmin === true;

  return isAuthor || isAdminUser;
};

// 处理删除帖子
const handleDelete = async (post: PostsList): Promise<void> => {
  // 检查用户登录状态
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    return;
  }

  // 检查删除权限
  if (!canDeletePost(post)) {
    uni.showToast({
      title: '无权限删除此帖子',
      icon: 'none'
    });
    return;
  }

  // 确认删除
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条帖子吗？删除后无法恢复。',
    success: async (res) => {
      if (res.confirm) {
        try {
          console.log('发送删除请求，帖子ID:', post.id);
          const result = await deletePost(Number(post.id));
          console.log('删除API响应:', result);

          if (result.success) {
            // 从列表中移除已删除的帖子
            const postIndex = postListData.value.findIndex((p: PostsList) => p.id === post.id);
            if (postIndex !== -1) {
              postListData.value.splice(postIndex, 1);
            }

            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          } else {
            console.error('删除失败:', result);
            uni.showToast({
              title: result.message || '删除失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('删除异常:', error);
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 处理视频加载错误
const handleVideoError = (event: any) => {
  console.warn('视频加载失败:', event);
  // 可以在这里添加更多的错误处理逻辑，比如显示占位图
};

</script>

<style lang="scss">
.index-page {
  background-color: #F3F4F6;

  .wrap {
    background-color: #fff;
    border-radius: 8px;
    // 阴影
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  }

  .page-header {
    background: linear-gradient(to right,
        #4396f7 0%,
        #00e2fa 80%,
        #00e2fa 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 10px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    .header-title {
      flex: 1;
      margin-left: 5px;
      font-size: 16px;
      font-weight: bold;
      color: #fff;
    }

    .test-btn {
      padding: 8px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .main-content {
    margin-bottom: 10px;
    padding: 0 10px;

    .top-nav-box-wrap {
      padding: 10px 0;
      height: 140px;
      display: flex;
      color: #fff;

      .nav-item-title {
        font-size: 16px;
        font-weight: bold;
      }

      .nav-item-desc {
        font-size: 14px;
      }

      .left {
        justify-content: space-between;
        display: inline-flex;
        flex-direction: column;
        padding: 10px;
        flex: 1;
        border-radius: 6px;

        background: linear-gradient(to left,
            #52a0fd 0%,
            #00e2fa 80%,
            #00e2fa 100%);
      }

      .gap {
        width: 12px;
      }

      .right {
        height: 140px;
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: space-between;

        .right-top,
        .right-bottom {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: normal;
          padding: 10px;
          border-radius: 6px;
        }

        .right-top {
          background: linear-gradient(to left,
              #E45AA5 0%,
              #F270B6 80%,
              #F270B6 100%);
        }

        .right-bottom {
          background: linear-gradient(to left,
              #852FDD 0%,
              #9132E8 80%,
              #9132E8 100%);
        }

      }

      .nav-item-btn {
        .custom-btn {
          float: right;
          width: 80px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-radius: 10px;
          background-color: rgba(255, 255, 255, 0.3);
          color: #fff;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:active {
            background-color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }

    .category-tabs {
      padding: 0 10px;
    }

  }

  .post-list-wrap {
    padding: 0 10px;

    .post-wrap {
      padding: 10px;
      margin-bottom: 10px;

      .post-header {
        display: flex;
        align-items: center;
        font-size: 12px;

        .info {
          display: flex;
          flex-direction: column;
          margin-left: 12px;

          .user-info {
            display: flex;
            flex-direction: column;

            .name-row {
              display: flex;
              align-items: center;
              flex-wrap: wrap;

              .name {
                font-size: 14px;
                font-weight: bold;
                margin-right: 8px;
              }

              .sex {
                margin-right: 8px;
              }
            }
          }

          .post-time {
            color: #9fa0a3;
          }
        }
      }

      .post-content-wrap {
        padding-left: 50px;
        font-size: 14px;

        .post-content {
          margin: 5px 0;
        }

        .post-media {
          margin-top: 10px;

          .single-media-item {
            width: 100%;
            border-radius: 8px;
            overflow: hidden;

            :deep(.u-image__image) {
              border-radius: 8px;
            }
          }

          .single-video-container {
            position: relative;
            width: 100%;
            height: 200px;
            border-radius: 8px;
            overflow: hidden;
            background-color: #000;

            .single-video {
              width: 100%;
              height: 100%;
              border-radius: 8px;
            }

            .video-overlay {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              pointer-events: none;
              z-index: 2;
            }
          }

          .media-grid {
            display: grid;
            gap: 4px;
            border-radius: 8px;
            overflow: hidden;

            &.grid-2 {
              grid-template-columns: 1fr 1fr;

              .grid-item {
                aspect-ratio: 1;
              }
            }

            &.grid-3 {
              grid-template-columns: 1fr 1fr 1fr;

              .grid-item {
                aspect-ratio: 1;
              }
            }

            &.grid-4 {
              grid-template-columns: 1fr 1fr;

              .grid-item {
                aspect-ratio: 1;
              }
            }

            &.grid-9 {
              grid-template-columns: 1fr 1fr 1fr;

              .grid-item {
                aspect-ratio: 1;
              }
            }

            .grid-item {
              position: relative;
              overflow: hidden;
              border-radius: 6px;

              :deep(.u-image__image) {
                border-radius: 6px;
              }

              .grid-video-item {
                position: relative;
                width: 100%;
                height: 100%;

                .video-play-icon {
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  pointer-events: none;
                  z-index: 2;
                  background-color: rgba(0, 0, 0, 0.3);
                  border-radius: 50%;
                  padding: 4px;
                }
              }

              .more-count {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.6);
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                font-weight: bold;
                border-radius: 6px;
              }
            }
          }
        }
      }

      .post-foot {
        padding: 10px;

        .toolbar {
          display: flex;
          justify-content: space-between !important;
          align-items: center;

          .action-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 4px;

            .count-text {
              font-size: 12px;
              color: #9fa0a3;
              line-height: 1;
            }
          }
        }
      }
    }
  }

  // 加载状态样式
  .post-list-wrap {
    .up-list {
      min-height: 200px;
    }

    .up-loadmore {
      padding: 20px 0;
      text-align: center;
      color: #999;
      font-size: 14px;
    }
  }

}
</style>
