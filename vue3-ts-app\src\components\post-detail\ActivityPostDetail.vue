<template>
  <view class="activity-post-detail">
    <!-- 活动标题和类型 -->
    <view class="activity-header">
      <view class="title-row">
        <view class="activity-title">{{ post.title }}</view>
        <view class="activity-type-badge">
          {{ formatActivityType(post.activityType) }}
        </view>
      </view>
      
      <!-- 活动状态 -->
      <view class="activity-status" :class="statusClass">
        <up-icon :name="statusIcon" size="16" :color="statusColor"></up-icon>
        <text class="status-text">{{ statusText }}</text>
      </view>
    </view>
    
    <!-- 活动信息 -->
    <view class="activity-info">
      <view class="info-grid">
        <!-- 活动时间 -->
        <view class="info-item">
          <view class="info-icon">
            <up-icon name="clock" size="18" color="#007bff"></up-icon>
          </view>
          <view class="info-content">
            <text class="info-label">活动时间</text>
            <text class="info-value">{{ formatActivityTime() }}</text>
          </view>
        </view>
        
        <!-- 活动地点 -->
        <view class="info-item">
          <view class="info-icon">
            <up-icon name="map" size="18" color="#28a745"></up-icon>
          </view>
          <view class="info-content">
            <text class="info-label">活动地点</text>
            <text class="info-value">{{ post.location }}</text>
          </view>
        </view>
        
        <!-- 参与人数 -->
        <view class="info-item">
          <view class="info-icon">
            <up-icon name="account" size="18" color="#ffc107"></up-icon>
          </view>
          <view class="info-content">
            <text class="info-label">参与人数</text>
            <text class="info-value">{{ formatParticipantCount() }}</text>
          </view>
        </view>
        
        <!-- 活动费用 -->
        <view class="info-item">
          <view class="info-icon">
            <up-icon name="rmb-circle" size="18" color="#dc3545"></up-icon>
          </view>
          <view class="info-content">
            <text class="info-label">活动费用</text>
            <text class="info-value">{{ formatFeeInfo() }}</text>
          </view>
        </view>
      </view>
      
      <!-- 报名截止时间 -->
      <view v-if="post.registrationDeadline" class="deadline-info">
        <view class="deadline-row">
          <text class="deadline-label">报名截止：</text>
          <text class="deadline-value">{{ formatDateTime(post.registrationDeadline) }}</text>
          <text v-if="isRegistrationExpired" class="expired-tag">已截止</text>
        </view>
        <view v-if="!isRegistrationExpired && registrationTimeRemaining" class="time-remaining">
          <text class="remaining-text">剩余时间：{{ registrationTimeRemaining }}</text>
        </view>
      </view>
    </view>
    
    <!-- 基础内容 -->
    <BasePostDetail :post="post" />
    
    <!-- 联系信息 -->
    <view class="contact-section">
      <view class="section-title">联系信息</view>
      <view class="contact-grid">
        <view v-if="post.contactPerson" class="contact-item">
          <text class="contact-label">联系人：</text>
          <text class="contact-value">{{ post.contactPerson }}</text>
        </view>
        <view v-if="post.contactPhone" class="contact-item">
          <text class="contact-label">联系电话：</text>
          <text class="contact-value">{{ post.contactPhone }}</text>
        </view>
      </view>
    </view>
    
    <!-- 特殊要求 -->
    <view v-if="post.specialRequirements" class="requirements-section">
      <view class="section-title">特殊要求</view>
      <view class="requirements-content">{{ post.specialRequirements }}</view>
    </view>
    
    <!-- 参与者列表（如果有的话） -->
    <view v-if="post.participants && post.participants.length > 0" class="participants-section">
      <view class="section-title">已报名参与者</view>
      <view class="participants-list">
        <view v-for="participant in post.participants" :key="participant.id" class="participant-item">
          <up-avatar :text="participant.nickname" :size="32" randomBgColor></up-avatar>
          <text class="participant-name">{{ participant.nickname }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BasePostDetail from './BasePostDetail.vue'

interface Props {
  post: {
    id?: number
    title: string
    content: string
    images?: string[]
    activityType: string
    location: string
    activityDate: string
    startTime: string
    endTime: string
    minParticipants: string
    maxParticipants: string
    feeType: string
    feeAmount?: string
    registrationDeadline?: string
    contactPerson?: string
    contactPhone?: string
    specialRequirements?: string
    participants?: Array<{ id: number; nickname: string }>
    createdTime: string
    updatedTime?: string
    [key: string]: any
  }
}

const props = defineProps<Props>()

// 活动类型映射
const activityTypeMap = {
  'sports': '体育运动',
  'social': '社交聚会',
  'education': '教育培训',
  'volunteer': '志愿服务',
  'entertainment': '娱乐活动'
}

// 费用类型映射
const feeTypeMap = {
  'free': '免费',
  'aa': 'AA制',
  'fixed': '固定费用'
}

// 格式化活动类型
const formatActivityType = (type: string) => {
  return activityTypeMap[type as keyof typeof activityTypeMap] || type
}

// 活动状态计算
const activityStatus = computed(() => {
  const now = new Date()
  const activityDateTime = new Date(`${props.post.activityDate} ${props.post.startTime}`)
  const endDateTime = new Date(`${props.post.activityDate} ${props.post.endTime}`)
  
  if (now > endDateTime) {
    return 'ended'
  } else if (now >= activityDateTime) {
    return 'ongoing'
  } else if (props.post.registrationDeadline && now > new Date(props.post.registrationDeadline)) {
    return 'registration_closed'
  } else {
    return 'upcoming'
  }
})

// 状态文本、图标和颜色
const statusText = computed(() => {
  switch (activityStatus.value) {
    case 'ended': return '活动已结束'
    case 'ongoing': return '活动进行中'
    case 'registration_closed': return '报名已截止'
    case 'upcoming': return '即将开始'
    default: return '未知状态'
  }
})

const statusIcon = computed(() => {
  switch (activityStatus.value) {
    case 'ended': return 'checkmark-circle'
    case 'ongoing': return 'play-circle'
    case 'registration_closed': return 'close-circle'
    case 'upcoming': return 'time'
    default: return 'help-circle'
  }
})

const statusColor = computed(() => {
  switch (activityStatus.value) {
    case 'ended': return '#6c757d'
    case 'ongoing': return '#28a745'
    case 'registration_closed': return '#dc3545'
    case 'upcoming': return '#007bff'
    default: return '#6c757d'
  }
})

const statusClass = computed(() => {
  return activityStatus.value.replace('_', '-')
})

// 格式化活动时间
const formatActivityTime = () => {
  const date = new Date(props.post.activityDate).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
  return `${date} ${props.post.startTime} - ${props.post.endTime}`
}

// 格式化参与人数
const formatParticipantCount = () => {
  const current = props.post.participants?.length || 0
  const min = parseInt(props.post.minParticipants)
  const max = parseInt(props.post.maxParticipants)
  
  if (max > 0) {
    return `${current}/${max}人 (最少${min}人)`
  } else {
    return `${current}人 (最少${min}人)`
  }
}

// 格式化费用信息
const formatFeeInfo = () => {
  const feeType = feeTypeMap[props.post.feeType as keyof typeof feeTypeMap] || props.post.feeType
  
  if (props.post.feeType === 'fixed' && props.post.feeAmount) {
    return `${feeType} ¥${props.post.feeAmount}`
  }
  
  return feeType
}

// 报名是否过期
const isRegistrationExpired = computed(() => {
  if (!props.post.registrationDeadline) return false
  return new Date(props.post.registrationDeadline) < new Date()
})

// 报名剩余时间
const registrationTimeRemaining = computed(() => {
  if (!props.post.registrationDeadline || isRegistrationExpired.value) return null
  
  const now = new Date()
  const deadline = new Date(props.post.registrationDeadline)
  const diff = deadline.getTime() - now.getTime()
  
  if (diff <= 0) return null
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) return `${days}天${hours}小时`
  if (hours > 0) return `${hours}小时${minutes}分钟`
  return `${minutes}分钟`
})

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped lang="scss">
.activity-post-detail {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.activity-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 16px;
}

.title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.activity-title {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.activity-type-badge {
  padding: 4px 12px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.activity-status {
  display: flex;
  align-items: center;
  gap: 6px;
  
  &.upcoming {
    color: #e3f2fd;
  }
  
  &.ongoing {
    color: #e8f5e8;
  }
  
  &.registration-closed {
    color: #ffebee;
  }
  
  &.ended {
    color: #f5f5f5;
  }
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

.activity-info {
  padding: 16px;
  background-color: #f8f9fa;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.info-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.info-label {
  font-size: 12px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.deadline-info {
  border-top: 1px solid #e9ecef;
  padding-top: 12px;
}

.deadline-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.deadline-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.deadline-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.expired-tag {
  margin-left: 8px;
  padding: 2px 6px;
  background-color: #dc3545;
  color: white;
  border-radius: 4px;
  font-size: 12px;
}

.time-remaining {
  text-align: center;
}

.remaining-text {
  font-size: 14px;
  color: #dc3545;
  font-weight: bold;
}

.contact-section, .requirements-section, .participants-section {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.contact-item {
  display: flex;
  flex-direction: column;
}

.contact-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.contact-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.requirements-content {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
}

.participants-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.participant-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.participant-name {
  font-size: 12px;
  color: #666;
  text-align: center;
}
</style>