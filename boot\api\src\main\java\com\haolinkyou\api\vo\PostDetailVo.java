package com.haolinkyou.api.vo;

import lombok.Data;

import java.util.Date;

@Data
public class PostDetailVo {
    private Long id;                 // 帖子ID
    private Long userId;             // 发帖用户ID
    private String nickname;         // 发帖用户昵称
    private Integer userGender;      // 发帖用户性别
    private Boolean userIsVerified;  // 发帖用户是否认证
    private String userRole;         // 发帖用户角色
    private String title;            // 帖子标题
    private String content;          // 帖子正文内容
    private Integer fileType;        // 文件类型
    private String fileList;         // 附件路径
    private Long categoryId;         // 分类ID
    private String categoryName;     // 分类名称
    private Integer viewCount;       // 浏览数
    private Integer likeCount;       // 点赞数
    private Integer commentCount;    // 评论数
    private Integer collectCount;    // 收藏数
    private Integer status;          // 帖子状态
    private Long templateId;         // 模板ID
    private String templateType;     // 模板类型
    private String templateData;     // 模板数据JSON
    private Date createdTime;        // 创建时间
    private Date updatedTime;        // 更新时间
}
