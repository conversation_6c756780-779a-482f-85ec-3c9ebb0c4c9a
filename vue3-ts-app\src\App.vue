<!--
 * @Author: Rock
 * @Date: 2025-04-02 08:30:10
 * @LastEditors: Rock
 * @LastEditTime: 2025-07-26 22:04:33
 * @Description: 
-->
<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";

onLaunch(() => {
  console.log("App Launch");
});
onShow(() => {
  console.log("App Show");
  // #ifdef MP-WEIXIN
    console.log("只在微信小程序环境运行")
  // #endif

  // #ifdef MP-ALIPAY
      console.log("只在支付宝小程序环境运行")
  // #endif

  // #ifdef MP-QQ
      console.log("只在QQ小程序环境运行")
  // #endif

  // #ifdef H5
      console.log("只在h5环境运行")
  // #endif

  // #ifdef APP-NVUE
      console.log("只在APP-NVUE环境运行")
  // #endif
});
onHide(() => {
  console.log("App Hide");
});
</script>
<style lang="scss">
@import "uview-plus/index.scss";
</style>
