package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.entity.UserSignRecords;
import com.haolinkyou.entity.Users;
import com.haolinkyou.mapper.UserSignRecordsMapper;
import com.haolinkyou.service.ISignService;
import com.haolinkyou.service.IPointsService;
import com.haolinkyou.service.ISystemConfigService;
import com.haolinkyou.service.IUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 签到服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Service
public class SignServiceImpl extends ServiceImpl<UserSignRecordsMapper, UserSignRecords> implements ISignService {

    private static final Logger log = LoggerFactory.getLogger(SignServiceImpl.class);

    @Autowired
    private IUserService userService;

    @Autowired
    private IPointsService pointsService;

    @Autowired
    private ISystemConfigService systemConfigService;

    @Override
    @Transactional
    public SignResult dailySign(Long userId) {
        try {
            // 检查今日是否已签到
            if (hasSignedToday(userId)) {
                return new SignResult(false, "今日已签到");
            }

            // 计算连续签到天数
            int continuousDays = calculateContinuousDays(userId);

            // 计算签到积分（基础积分 + 连续奖励）
            int basePoints = systemConfigService.getIntValue("points_sign_daily", 5);
            int bonusPoints = calculateSignBonus(continuousDays);
            int totalPoints = basePoints + bonusPoints;

            // 检查每日积分限额
            if (!pointsService.checkDailyLimit(userId, totalPoints)) {
                Integer remainingPoints = pointsService.getRemainingDailyPoints(userId);
                if (remainingPoints <= 0) {
                    return new SignResult(false, "今日积分已达上限，无法获得签到奖励");
                }
                totalPoints = remainingPoints;
            }

            // 记录签到
            UserSignRecords record = new UserSignRecords();
            record.setUserId(userId);
            record.setSignDate(LocalDate.now());
            record.setPointsEarned(totalPoints);
            record.setContinuousDays(continuousDays + 1);
            // createdTime 由 BaseEntity 的自动填充处理，不需要手动设置

            boolean saveResult = save(record);
            if (!saveResult) {
                return new SignResult(false, "签到记录保存失败");
            }

            // 更新用户表的签到信息
            Users user = userService.getById(userId);
            if (user != null) {
                user.setLastSignDate(LocalDate.now());
                user.setContinuousSignDays(continuousDays + 1);
                userService.updateById(user);
            }

            // 增加积分
            boolean pointsAdded = pointsService.addPoints(userId, totalPoints, "sign", "每日签到");
            if (!pointsAdded) {
                log.warn("签到积分发放失败，用户ID: " + userId + ", 积分: " + totalPoints);
            }

            return new SignResult(true, "签到成功", totalPoints, continuousDays + 1);

        } catch (Exception e) {
            log.error("签到失败，用户ID: " + userId + ", error=" + e.getMessage());
            return new SignResult(false, "签到失败，请重试");
        }
    }

    @Override
    public boolean hasSignedToday(Long userId) {
        LocalDate today = LocalDate.now();
        QueryWrapper<UserSignRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("sign_date", today);
        
        return count(queryWrapper) > 0;
    }

    @Override
    public SignStatus getSignStatus(Long userId) {
        SignStatus status = new SignStatus();
        
        // 检查今日是否已签到
        status.setHasSignedToday(hasSignedToday(userId));
        
        // 获取连续签到天数
        status.setContinuousDays(calculateContinuousDays(userId));
        
        // 获取最后签到日期
        QueryWrapper<UserSignRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("sign_date")
                   .last("LIMIT 1");
        
        UserSignRecords lastRecord = getOne(queryWrapper);
        if (lastRecord != null) {
            status.setLastSignDate(lastRecord.getSignDate());
        }
        
        // 计算今日可获得积分
        if (!status.isHasSignedToday()) {
            int basePoints = systemConfigService.getIntValue("points_sign_daily", 5);
            int bonusPoints = calculateSignBonus(status.getContinuousDays());
            int totalPoints = basePoints + bonusPoints;
            
            // 检查每日积分限额
            Integer remainingPoints = pointsService.getRemainingDailyPoints(userId);
            if (remainingPoints < totalPoints) {
                totalPoints = Math.max(0, remainingPoints);
            }
            
            status.setTodayPoints(totalPoints);
        }
        
        // 计算明日奖励预览
        int nextDayBonus = calculateSignBonus(status.getContinuousDays() + 1);
        status.setNextDayBonus(systemConfigService.getIntValue("points_sign_daily", 5) + nextDayBonus);
        
        return status;
    }

    @Override
    public int calculateContinuousDays(Long userId) {
        // 获取用户最近的签到记录
        QueryWrapper<UserSignRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("sign_date")
                   .last("LIMIT 30"); // 最多查询30天
        
        List<UserSignRecords> records = list(queryWrapper);
        if (records.isEmpty()) {
            return 0;
        }
        
        int continuousDays = 0;
        LocalDate currentDate = LocalDate.now();
        LocalDate checkDate = currentDate;
        
        // 如果今天还没签到，从昨天开始检查
        if (!hasSignedToday(userId)) {
            checkDate = currentDate.minusDays(1);
        }
        
        // 从最近的日期开始向前检查连续性
        for (UserSignRecords record : records) {
            if (record.getSignDate().equals(checkDate)) {
                continuousDays++;
                checkDate = checkDate.minusDays(1);
            } else {
                break; // 不连续，停止计算
            }
        }
        
        return continuousDays;
    }

    @Override
    public Page<UserSignRecords> getUserSignRecords(Long userId, Integer page, Integer size) {
        Page<UserSignRecords> pageInfo = new Page<>(page, size);
        QueryWrapper<UserSignRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("sign_date");
        
        return page(pageInfo, queryWrapper);
    }

    @Override
    public int calculateSignBonus(int continuousDays) {
        int bonusPerDay = systemConfigService.getIntValue("points_continuous_sign_bonus", 2);
        
        // 连续签到奖励规则：
        // 连续3天：+2积分
        // 连续7天：+5积分
        // 连续15天：+10积分
        // 连续30天：+20积分
        if (continuousDays >= 30) {
            return 20;
        } else if (continuousDays >= 15) {
            return 10;
        } else if (continuousDays >= 7) {
            return 5;
        } else if (continuousDays >= 3) {
            return bonusPerDay;
        } else {
            return 0;
        }
    }
}