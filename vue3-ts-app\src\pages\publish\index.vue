<template>
  <view class="publish-page">
    <!-- 安全区域顶部 -->
    <view :style="{ paddingTop: safeAreaTop + 'px' }"></view>

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 分类选择 -->
      <view class="category-section">
        <view class="section-title">选择分类</view>
        <view class="category-list">
          <view
            v-for="category in categories"
            :key="category.id"
            class="category-item"
            :class="{ active: selectedCategoryId === category.id }"
            @click="selectCategory(category.id)"
          >
            {{ category.categoryName }}
          </view>
        </view>
      </view>

      <!-- 模板选择 -->
      <view v-if="selectedCategoryId" class="template-section">
        <view class="section-title">选择模板</view>
        <view class="template-list">

          <!-- 组团邀约分类的特殊处理 -->
          <template v-if="selectedCategoryName === '组团邀约'">
            <view
              class="template-item"
              :class="{ active: selectedTemplate === 'groupbuy' }"
              @click="selectTemplate('groupbuy')"
            >
              团购
            </view>
            <view
              class="template-item"
              :class="{ active: selectedTemplate === 'activity' }"
              @click="selectTemplate('activity')"
            >
              活动发起
            </view>
          </template>

          <!-- 其他分类的专用模板 -->
          <template v-else>
            <view
              v-if="categoryTemplate"
              class="template-item"
              :class="{ active: selectedTemplate === 'category' }"
              @click="selectTemplate('category')"
            >
              {{ categoryTemplate.name }}
            </view>
          </template>

          <!-- 通用模板 -->
          <view
            class="template-item"
            :class="{ active: selectedTemplate === 'survey' }"
            @click="selectTemplate('survey')"
          >
            调查问卷
          </view>
          <view
            class="template-item"
            :class="{ active: selectedTemplate === 'vote' }"
            @click="selectTemplate('vote')"
          >
            投票表决
          </view>
        </view>
      </view>

      <!-- 模板内容 -->
      <view class="template-content">
        <!-- 默认基础模板（未选择分类时显示） -->
        <BaseTemplate
          v-if="!selectedCategoryId"
          ref="templateRef"
          :titleRequired="true"
          titlePlaceholder="请输入标题"
          contentPlaceholder="请输入内容"
          :mediaRequired="false"
          mediaLabel="媒体文件"
          mediaHelpText="支持jpg、png图片和mp4、avi视频混合上传，总数最多9个"
          :maxImageCount="9"
          :showContact="false"
          @update:data="handleTemplateDataChange"
          @update:valid="handleTemplateValidChange"
        />

        <!-- 选择分类后的模板 -->
        <template v-else-if="selectedTemplate">
          <!-- 公告模板 -->
          <AnnouncementTemplate
            v-if="selectedTemplate === 'category' && categoryTemplate?.type === 'announcement'"
            ref="templateRef"
            @update:data="handleTemplateDataChange"
            @update:valid="handleTemplateValidChange"
          />

          <!-- 团购模板 -->
          <GroupBuyTemplate
            v-else-if="selectedTemplate === 'groupbuy'"
            ref="templateRef"
            @update:data="handleTemplateDataChange"
            @update:valid="handleTemplateValidChange"
          />

          <!-- 活动发起模板 -->
          <ActivityTemplate
            v-else-if="selectedTemplate === 'activity'"
            ref="templateRef"
            @update:data="handleTemplateDataChange"
            @update:valid="handleTemplateValidChange"
          />

          <!-- 基础模板 -->
          <BaseTemplate
            v-else-if="selectedTemplate === 'category'"
            ref="templateRef"
            :titleRequired="true"
            titlePlaceholder="请输入标题"
            contentPlaceholder="请输入内容"
            :mediaRequired="false"
            mediaLabel="图片/视频"
            mediaHelpText="支持jpg、png图片和mp4、avi视频混合上传，总数最多9个"
            :maxImageCount="9"
            :showContact="false"
            @update:data="handleTemplateDataChange"
            @update:valid="handleTemplateValidChange"
          />

          <!-- 调查问卷模板 -->
          <SurveyFormTemplate
            v-else-if="selectedTemplate === 'survey'"
            ref="templateRef"
            @update:data="handleTemplateDataChange"
            @update:valid="handleTemplateValidChange"
          />

          <!-- 投票表决模板 -->
          <VoteTemplate
            v-else-if="selectedTemplate === 'vote'"
            ref="templateRef"
            @update:data="handleTemplateDataChange"
            @update:valid="handleTemplateValidChange"
          />
        </template>
      </view>
    </view>

    <!-- 底部发布按钮 -->
    <view class="publish-footer">
      <up-button
        type="primary"
        size="small"
        :loading="publishing"
        :disabled="!canPublish"
        @click="handlePublish"
        class="publish-btn"
      >
        {{ publishing ? '发布中...' : '发布' }}
      </up-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { useMemberStore } from '@/stores'
import { get } from '@/utils/http'
import type { Categories } from '@/types/Categories'
import BaseTemplate from '@/components/templates/BaseTemplate.vue'
import AnnouncementTemplate from '@/components/templates/AnnouncementTemplate.vue'
import GroupBuyTemplate from '@/components/templates/GroupBuyTemplate.vue'
import ActivityTemplate from '@/components/templates/ActivityTemplate.vue'
import SurveyFormTemplate from '@/components/templates/SurveyFormTemplate.vue'
import VoteTemplate from '@/components/templates/VoteTemplate.vue'

// 安全区域
const safeAreaTop = ref(0)

// 用户信息
const memberStore = useMemberStore()

// 分类数据
const categories = ref<Categories[]>([])
const selectedCategoryId = ref<number>(0)

// 模板选择
const selectedTemplate = ref<string>('')
const templateRef = ref()

// 表单数据
const templateData = ref<Record<string, any>>({})
const templateValid = ref(false)

// 发布状态
const publishing = ref(false)
const justPublished = ref(false) // 标记是否刚刚发布成功

// 分类模板映射
const categoryTemplateMap: Record<string, { name: string; type: string }> = {
  '三期公告': { name: '图文', type: 'announcement' },
  '邻友互助': { name: '图文', type: 'basic' },
  '二手闲置': { name: '图文', type: 'basic' },
  '房东直租': { name: '图文', type: 'basic' }
  // 注意：组团邀约不在这里映射，因为它有特殊的多模板处理
}

// 当前选中的分类名称
const selectedCategoryName = computed(() => {
  if (!selectedCategoryId.value) return ''
  const category = categories.value.find(c => c.id === selectedCategoryId.value)
  return category?.categoryName || ''
})

// 当前分类的模板
const categoryTemplate = computed(() => {
  if (!selectedCategoryId.value) return null

  const category = categories.value.find(c => c.id === selectedCategoryId.value)
  if (!category) return null

  // 组团邀约分类使用特殊的多模板处理，不返回单一模板
  if (category.categoryName === '组团邀约') return null

  return categoryTemplateMap[category.categoryName] || { name: '基础发帖模板', type: 'basic' }
})

// 是否可以发布
const canPublish = computed(() => {
  // 如果没有选择分类，只要模板有效就可以发布（使用默认基础模板）
  if (!selectedCategoryId.value) {
    return templateValid.value && !publishing.value
  }

  // 如果选择了分类，需要选择模板且模板有效
  return selectedCategoryId.value > 0 &&
         selectedTemplate.value !== '' &&
         templateValid.value &&
         !publishing.value
})

// 页面加载
onLoad(() => {
  // 获取安全区域
  const systemInfo = uni.getSystemInfoSync()
  safeAreaTop.value = systemInfo.safeAreaInsets?.top || 0
})

// 检查登录状态
const checkLoginStatus = () => {
  // 检查用户是否登录
  if (!memberStore.profile?.id) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再发布内容',
      showCancel: false,
      success: () => {
        uni.switchTab({
          url: '/pages/my/index'
        })
      }
    })
    return false
  }

  // 检查token是否存在
  const token = memberStore.profile?.token || uni.getStorageSync('token')
  if (!token) {
    uni.showModal({
      title: '提示', 
      content: '登录状态已过期，请重新登录',
      showCancel: false,
      success: () => {
        // 清除过期的用户信息
        memberStore.clearProfile()
        uni.switchTab({
          url: '/pages/my/index'
        })
      }
    })
    return false
  }

  return true
}

// 页面显示时检查是否需要重置
onShow(() => {
  // 检查登录状态
  if (!checkLoginStatus()) {
    return
  }

  // 如果刚刚发布成功，不需要重置
  if (justPublished.value) {
    justPublished.value = false
    return
  }

  // 检查是否有未完成的表单数据，如果没有则重置到默认状态
  if (!selectedCategoryId.value && categories.value.length > 0) {
    const neighborHelpCategory = categories.value.find(c => c.categoryName === '邻友互助')
    if (neighborHelpCategory) {
      selectCategory(neighborHelpCategory.id)
    }
  }
})

// 页面挂载
onMounted(() => {
  // 检查登录状态
  if (!checkLoginStatus()) {
    return
  }
  
  loadCategories()
})

// 加载分类列表
const loadCategories = async () => {
  try {
    const res: any = await get('/categories/list')
    if (res.success && res.data) {
      categories.value = res.data

      // 默认选择"邻友互助"分类
      const neighborHelpCategory = categories.value.find(c => c.categoryName === '邻友互助')
      if (neighborHelpCategory && !selectedCategoryId.value) {
        selectCategory(neighborHelpCategory.id)
      }
    }
  } catch (error) {
    console.error('加载分类失败:', error)
    uni.showToast({
      title: '加载分类失败',
      icon: 'none'
    })
  }
}

// 选择分类
const selectCategory = (categoryId: number) => {
  selectedCategoryId.value = categoryId
  templateData.value = {} // 重置表单数据

  // 根据分类自动选择第一个模板
  const category = categories.value.find(c => c.id === categoryId)
  if (category?.categoryName === '组团邀约') {
    selectedTemplate.value = 'groupbuy' // 组团邀约默认选择团购模板
  } else if (categoryTemplate.value) {
    selectedTemplate.value = 'category'
  } else {
    selectedTemplate.value = 'survey'
  }
}

// 选择模板
const selectTemplate = (template: string) => {
  selectedTemplate.value = template
  templateData.value = {} // 重置表单数据
}

// 处理模板数据变化
const handleTemplateDataChange = (data: Record<string, any>) => {
  templateData.value = data
}

// 处理模板验证状态变化
const handleTemplateValidChange = (valid: boolean) => {
  templateValid.value = valid
}

// 处理发布
const handlePublish = async () => {
  // 检查登录状态
  if (!memberStore.profile?.token) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再发布',
      confirmText: '去登录',
      success: (res: any) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/index'
          })
        }
      }
    })
    return
  }

  // 验证表单
  if (!templateRef.value?.validate()) {
    uni.showToast({
      title: '请完善必填选项',
      icon: 'none'
    })
    return
  }

  publishing.value = true

  try {
    // 使用FormData进行智能发布
    const formData = new FormData()

    // 基础数据
    formData.append('category_id', (selectedCategoryId.value || 0).toString())
    
    // 处理content字段，确保不为空
    let contentValue = templateData.value.content || ''
    
    // 对于调查问卷类型，如果content为空，自动生成
    if (selectedTemplate.value === 'survey' && (!contentValue || contentValue.trim() === '')) {
      if (templateData.value.questions && templateData.value.questions.length > 0) {
        const questionTitles = templateData.value.questions
          .filter((q: any) => q.title && q.title.trim())
          .map((q: any, index: number) => `${index + 1}. ${q.title}`)
          .join('\n')
        
        if (questionTitles) {
          contentValue = `本调查问卷包含以下问题：\n${questionTitles}`
        } else {
          contentValue = '调查问卷'
        }
      } else {
        contentValue = '调查问卷'
      }
    }
    
    // 对于投票类型，如果content为空，自动生成
    if (selectedTemplate.value === 'vote' && (!contentValue || contentValue.trim() === '')) {
      if (templateData.value.options && templateData.value.options.length > 0) {
        const optionTitles = templateData.value.options
          .filter((opt: any) => opt.text && opt.text.trim())
          .map((opt: any, index: number) => `${index + 1}. ${opt.text}`)
          .join('\n')
        
        if (optionTitles) {
          contentValue = `投票选项：\n${optionTitles}`
        } else {
          contentValue = '投票表决'
        }
      } else {
        contentValue = '投票表决'
      }
    }
    
    // 确保content不为空
    if (!contentValue || contentValue.trim() === '') {
      contentValue = templateData.value.title || '内容'
    }
    
    formData.append('content', contentValue)

    if (templateData.value.title) {
      formData.append('title', templateData.value.title)
    }

    if (selectedTemplate.value && selectedTemplate.value !== 'basic') {
      // 模板ID映射
      const templateIdMap: Record<string, string> = {
        'survey': '1',      // 调查问卷
        'vote': '2',        // 投票表决
        'activity': '3',    // 活动发起
        'groupbuy': '4',    // 团购模板
        'announcement': '5' // 公告模板
      }
      
      const templateId = templateIdMap[selectedTemplate.value] || '1'
      formData.append('template_id', templateId)
      console.log(`发布页面 - 设置模板ID: ${selectedTemplate.value} -> ${templateId}`)
    }

    // 添加其他模板数据
    console.log('发布页面 - 模板数据:', templateData.value)
    console.log('发布页面 - 选择的模板:', selectedTemplate.value)
    
    Object.keys(templateData.value).forEach(key => {
      if (key !== 'content' && key !== 'title' && key !== 'fileList' && key !== 'images') {
        const value = templateData.value[key]
        console.log(`发布页面 - 处理字段 ${key}:`, value)
        if (value !== null && value !== undefined && value !== '') {
          const serializedValue = typeof value === 'object' ? JSON.stringify(value) : value.toString()
          formData.append(key, serializedValue)
          console.log(`发布页面 - 添加到FormData: ${key} = ${serializedValue}`)
        }
      }
    })

    // 验证并处理文件上传（智能检测）
    if (templateData.value.fileList && templateData.value.fileList.length > 0) {
      // 首先验证所有文件格式
      for (const fileItem of templateData.value.fileList) {
        if (fileItem.file) {
          const file = fileItem.file
          const fileName = file.name || ''
          const mimeType = file.type || ''
          const fileExtension = fileName.toLowerCase().match(/\.[^.]+$/)?.[0] || ''

          // 支持的格式（与后端保持一致）
          const supportedImageTypes = ['image/jpeg', 'image/jpg', 'image/png']
          const supportedImageExtensions = ['.jpg', '.jpeg', '.png']
          const supportedVideoTypes = ['video/mp4', 'video/avi']
          const supportedVideoExtensions = ['.mp4', '.avi']

          let isValidFormat = false

          // 检查图片格式
          if (mimeType.startsWith('image/') || supportedImageExtensions.includes(fileExtension)) {
            isValidFormat = supportedImageTypes.includes(mimeType) || supportedImageExtensions.includes(fileExtension)
            if (!isValidFormat) {
              uni.showToast({
                title: '图片格式仅支持jpg、jpeg、png',
                icon: 'none',
                duration: 3000
              })
              return
            }
          }
          // 检查视频格式
          else if (mimeType.startsWith('video/') || supportedVideoExtensions.includes(fileExtension)) {
            isValidFormat = supportedVideoTypes.includes(mimeType) || supportedVideoExtensions.includes(fileExtension)
            if (!isValidFormat) {
              uni.showToast({
                title: '视频格式仅支持mp4、avi',
                icon: 'none',
                duration: 3000
              })
              return
            }
          }
          // 不支持的文件类型
          else {
            uni.showToast({
              title: '不支持的文件格式，请选择图片或视频文件',
              icon: 'none',
              duration: 3000
            })
            return
          }
        }
      }

      // 格式验证通过后，添加文件到FormData
      for (let i = 0; i < templateData.value.fileList.length; i++) {
        const fileItem = templateData.value.fileList[i]
        if (fileItem.file) {
          formData.append('files', fileItem.file, fileItem.file.name || fileItem.name)
        }
      }
    }





    // 获取token
    const token = memberStore.profile?.token || uni.getStorageSync('token') || ''

    // 调用智能发布接口
    const response = await fetch('/api/posts/smart-publish', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })

    const result = await response.json()

    if (result.success) {
      uni.showToast({
        title: '发布成功',
        icon: 'success'
      })

      // 设置发布成功标志
      justPublished.value = true

      // 设置全局刷新标记，让首页刷新
      const app = getApp()
      if (!app.globalData) {
        app.globalData = {}
      }
      app.globalData.needRefreshHome = true
      console.log('发布成功，设置首页刷新标记')

      // 清空表单数据
      resetAllForms()

      // 返回首页并刷新
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)
    } else {
      // 显示后端返回的具体错误信息
      const errorMessage = result.message || '发布失败'
      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })
      throw new Error(errorMessage)
    }
  } catch (error: any) {
    console.error('智能发布失败:', error)
    uni.showToast({
      title: error.message || '发布失败',
      icon: 'none',
      duration: 3000
    })
  } finally {
    publishing.value = false
  }
}

// 重置所有表单数据
const resetAllForms = () => {
  // 重置模板数据
  templateData.value = {}

  // 调用当前模板的resetForm方法
  if (templateRef.value && templateRef.value.resetForm) {
    templateRef.value.resetForm()
  }

  // 重置选择状态并重新选择默认分类
  selectedCategoryId.value = 0
  selectedTemplate.value = ''

  // 重新选择默认的"邻友互助"分类
  setTimeout(() => {
    const neighborHelpCategory = categories.value.find(c => c.categoryName === '邻友互助')
    if (neighborHelpCategory) {
      selectCategory(neighborHelpCategory.id)
    }
  }, 100)
}

// 注意：现在使用智能发布接口，文件上传已集成到发布流程中
</script>

<style scoped lang="scss">
.publish-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.content-area {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.category-section, .template-section {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.category-list, .template-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.category-item, .template-item {
  padding:0px 6px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-size: 12px;
  color: #666;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  height: 28px;
  line-height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;

  &:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.active {
    background:rgb(86, 119, 252);
    color: white;
    // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    // box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.template-content {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.publish-footer {
  padding: 12px 20px;
  background-color: white;
  border-top: 1px solid #e9ecef;

  :deep(.publish-btn) {
    // height: 44px !important;
    // border-radius: 12px !important;
    // font-size: 16px !important;
    // font-weight: 600 !important;
    // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    // border: none !important;
    // box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3) !important;
    // transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }

    // &.u-button--disabled {
    //   background: #e9ecef !important;
    //   color: #adb5bd !important;
    //   box-shadow: none !important;
    //   transform: none !important;
    // }
  }
}
</style>
