/*
 * @Author: Rock
 * @Date: 2025-04-02 08:31:52
 * @LastEditors: Rock
 * @LastEditTime: 2025-04-02 09:47:09
 * @Description:
 */
// https://eslint.org/docs/user-guide/configuring

module.exports = {
  root: true,
  parserOptions: {
    parser: "babel-eslint",
  },
  env: {
    browser: true,
  },
  extends: [
    // https://github.com/vuejs/eslint-plugin-vue#priority-a-essential-error-prevention
    // consider switching to `plugin:vue/strongly-recommended` or `plugin:vue/recommended` for stricter rules.
    "plugin:vue/essential",
    // https://github.com/standard/standard/blob/master/docs/RULES-en.md
    "standard",
    "./.eslintrc-auto-import.json", // 添加这行
  ],
  // required to lint *.vue files
  plugins: ["vue"],
  // add your custom rules here
  rules: {
    // allow async-await
    "generator-star-spacing": "off",
    // allow debugger during development
    "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
    "vue/no-parsing-error": [
      2,
      {
        "x-invalid-end-tag": false,
      },
    ],
    "no-undef": "off",
    camelcase: "off",
    quotes: 0,
    semi: 0,
    "eol-last": 0,
    "no-unused-vars": 0,
    "comma-spacing": 0,
    "spaced-comment": 0,
    indent: "off",
    // 强制在function的左括号之前使用一致的空格
    "space-before-function-paren": 0,
  },
};
