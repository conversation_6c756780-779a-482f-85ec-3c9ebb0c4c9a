/**
 * 搜索历史管理工具
 */

const SEARCH_HISTORY_KEY = 'search_history'
const MAX_HISTORY_COUNT = 10

export interface SearchHistoryItem {
  keyword: string
  timestamp: number
  count: number // 搜索次数
}

/**
 * 获取搜索历史
 */
export const getSearchHistory = (): SearchHistoryItem[] => {
  try {
    const history = uni.getStorageSync(SEARCH_HISTORY_KEY)
    return Array.isArray(history) ? history : []
  } catch (error) {
    console.error('获取搜索历史失败:', error)
    return []
  }
}

/**
 * 添加搜索历史
 */
export const addSearchHistory = (keyword: string): void => {
  if (!keyword || keyword.trim() === '') return
  
  try {
    const history = getSearchHistory()
    const trimmedKeyword = keyword.trim()
    
    // 查找是否已存在
    const existingIndex = history.findIndex(item => item.keyword === trimmedKeyword)
    
    if (existingIndex !== -1) {
      // 如果已存在，更新时间戳和搜索次数，并移到最前面
      const existingItem = history[existingIndex]
      history.splice(existingIndex, 1)
      history.unshift({
        ...existingItem,
        timestamp: Date.now(),
        count: existingItem.count + 1
      })
    } else {
      // 如果不存在，添加新记录
      history.unshift({
        keyword: trimmedKeyword,
        timestamp: Date.now(),
        count: 1
      })
    }
    
    // 限制历史记录数量
    if (history.length > MAX_HISTORY_COUNT) {
      history.splice(MAX_HISTORY_COUNT)
    }
    
    uni.setStorageSync(SEARCH_HISTORY_KEY, history)
  } catch (error) {
    console.error('添加搜索历史失败:', error)
  }
}

/**
 * 删除单个搜索历史
 */
export const removeSearchHistory = (keyword: string): void => {
  try {
    const history = getSearchHistory()
    const filteredHistory = history.filter(item => item.keyword !== keyword)
    uni.setStorageSync(SEARCH_HISTORY_KEY, filteredHistory)
  } catch (error) {
    console.error('删除搜索历史失败:', error)
  }
}

/**
 * 清空搜索历史
 */
export const clearSearchHistory = (): void => {
  try {
    uni.removeStorageSync(SEARCH_HISTORY_KEY)
  } catch (error) {
    console.error('清空搜索历史失败:', error)
  }
}

/**
 * 获取热门搜索词
 */
export const getHotSearchKeywords = (): string[] => {
  const history = getSearchHistory()
  
  // 按搜索次数排序，取前5个
  return history
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)
    .map(item => item.keyword)
}

/**
 * 格式化搜索历史显示时间
 */
export const formatSearchTime = (timestamp: number): string => {
  const now = Date.now()
  const diff = now - timestamp
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else {
    const date = new Date(timestamp)
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}