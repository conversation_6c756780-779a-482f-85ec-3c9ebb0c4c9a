package com.haolinkyou.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
public class DatabaseUpdater implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        updateUserTable();
    }

    private void updateUserTable() {
        try {
            // 检查字段是否已存在，如果不存在则添加
            String[] newColumns = {
                "bio TEXT COMMENT '个人简介'",
                "region VARCHAR(100) COMMENT '地区'",
                "profession VARCHAR(100) COMMENT '职业'",
                "school VARCHAR(200) COMMENT '学校'",
                "red_book_id VARCHAR(50) COMMENT '小红书号'",
                "background_image VARCHAR(500) COMMENT '背景图URL'"
            };

            String[] columnNames = {"bio", "region", "profession", "school", "red_book_id", "background_image"};

            for (int i = 0; i < columnNames.length; i++) {
                if (!columnExists("users", columnNames[i])) {
                    String sql = "ALTER TABLE users ADD COLUMN " + newColumns[i];
                    jdbcTemplate.execute(sql);
                    System.out.println("Added column: " + columnNames[i]);
                } else {
                    System.out.println("Column already exists: " + columnNames[i]);
                }
            }

            System.out.println("Database update completed successfully!");

        } catch (Exception e) {
            System.err.println("Error updating database: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private boolean columnExists(String tableName, String columnName) {
        try {
            String sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? AND COLUMN_NAME = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName, columnName);
            return count != null && count > 0;
        } catch (Exception e) {
            return false;
        }
    }
}
