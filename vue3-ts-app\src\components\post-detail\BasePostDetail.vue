<template>
  <view class="base-post-detail">
    <!-- 帖子标题 -->
    <view v-if="post.title" class="post-title">
      {{ post.title }}
    </view>
    
    <!-- 帖子内容 -->
    <view v-if="post.content" class="post-content">
      <ExpandableRichText
        :content="post.content || ''"
        :maxLength="500"
        @linkClick="handleLinkClick"
        @imageClick="handleImageClick"
      />
    </view>
    
    <!-- 图片展示 -->
    <view v-if="post.images && post.images.length > 0" class="post-images">
      <swiper
        :current="currentImageIndex"
        @change="handleSwiperChange"
        indicator-dots
        indicator-color="rgba(255, 255, 255, 0.5)"
        indicator-active-color="#fff"
        class="media-swiper"
      >
        <swiper-item
          v-for="(media, index) in post.images"
          :key="index"
          @click="handleMediaClick(index)"
          class="swiper-item"
        >
          <view class="media-container">
            <!-- 图片 -->
            <image
              v-if="detectMediaType(media) === 'image'"
              :src="media"
              mode="aspectFit"
              class="media-image"
            />
            
            <!-- 视频 -->
            <view v-else-if="detectMediaType(media) === 'video'" class="video-container">
              <video
                :src="media"
                :poster="getVideoPoster(media)"
                :show-center-play-btn="false"
                :show-play-btn="false"
                :show-fullscreen-btn="false"
                :show-progress="false"
                :controls="false"
                :autoplay="false"
                muted
                class="media-video"
                @loadedmetadata="handleVideoLoaded"
                @error="handleVideoError"
              />
              <view class="video-overlay">
                <view class="play-button">
                  <up-icon name="play-circle-fill" size="60" color="rgba(255, 255, 255, 0.9)" />
                </view>
                <view class="video-badge">
                  <up-icon name="video" size="14" color="white" />
                  <text class="video-text">视频</text>
                </view>
                <view class="video-duration" v-if="videoDuration">
                  <text class="duration-text">{{ formatDuration(videoDuration) }}</text>
                </view>
              </view>
            </view>
            
            <!-- 未知类型 -->
            <view v-else class="unknown-media">
              <up-icon name="file" size="60" color="#999" />
              <text class="unknown-text">不支持的文件类型</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
    
    <!-- 联系方式 -->
    <view v-if="post.contact" class="contact-info">
      <view class="contact-label">联系方式：</view>
      <view class="contact-value">{{ post.contact }}</view>
    </view>
    

    
    <!-- 媒体预览组件 -->
    <MediaPreview
      :show="showMediaPreview"
      :mediaList="previewMediaList"
      :initialIndex="previewInitialIndex"
      @close="closeMediaPreview"
      @change="handleMediaPreviewChange"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MediaPreview from '../MediaPreview.vue'
import ExpandableRichText from '../ExpandableRichText.vue'

interface Props {
  post: {
    id?: number
    title?: string
    content?: string // 改为可选，避免undefined错误
    images?: string[]
    contact?: string
    createdTime: string
    updatedTime?: string
    [key: string]: any
  }
}

const props = defineProps<Props>()

// 媒体预览相关
const showMediaPreview = ref(false)
const previewMediaList = ref<string[]>([])
const previewInitialIndex = ref(0)
const currentImageIndex = ref(0)
const videoDuration = ref<number>(0)

// 检测媒体文件类型
const detectMediaType = (url: string): 'image' | 'video' | 'unknown' => {
  if (!url) return 'unknown'
  
  const lowerUrl = url.toLowerCase()
  
  // 检测图片格式
  if (lowerUrl.match(/\.(jpg|jpeg|png|gif|webp|bmp)(\?.*)?$/)) {
    return 'image'
  }
  
  // 检测视频格式
  if (lowerUrl.match(/\.(mp4|mov|avi|wmv|flv|webm|mkv)(\?.*)?$/)) {
    return 'video'
  }
  
  return 'unknown'
}

// 预览媒体文件（支持图片和视频混合）
const previewMedia = (mediaList: string[], current: number) => {
  previewMediaList.value = mediaList
  previewInitialIndex.value = current
  showMediaPreview.value = true
}

// 关闭媒体预览
const closeMediaPreview = () => {
  showMediaPreview.value = false
}

// 媒体预览索引变化
const handleMediaPreviewChange = (index: number) => {
  previewInitialIndex.value = index
}

// 处理swiper变化事件
const handleSwiperChange = (e: any) => {
  if (e.detail && typeof e.detail.current === 'number') {
    currentImageIndex.value = e.detail.current
  }
}

// 处理媒体点击事件
const handleMediaClick = (index: number) => {
  console.log('Media clicked:', index)
  
  if (props.post.images && props.post.images.length > 0) {
    previewMedia(props.post.images, index)
  }
}

// 获取视频缩略图
const getVideoPoster = (videoUrl: string): string => {
  if (!videoUrl) return ''
  
  // 方案1: 如果服务器提供缩略图服务，可以通过URL参数获取
  // 例如: videoUrl + '?thumbnail=true' 或 videoUrl.replace('.mp4', '_thumb.jpg')
  
  // 方案2: 使用默认的视频缩略图占位符
  // 这里可以返回一个默认的视频封面图片URL
  
  // 方案3: 让video组件自动生成第一帧作为缩略图（当前方案）
  // 返回空字符串，video组件会自动显示第一帧
  return ''
  
  // 如果有专门的缩略图服务，可以这样实现：
  // const baseUrl = videoUrl.substring(0, videoUrl.lastIndexOf('.'))
  // return baseUrl + '_thumb.jpg'
}

// 格式化时间
const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < month) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < 3 * month) {
    return time.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  } else if (diff < year) {
    return time.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  } else {
    return time.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' })
  }
}

// 预览图片（兼容旧接口）
const previewImage = (index: number) => {
  if (props.post.images && props.post.images.length > 0) {
    previewMedia(props.post.images, index)
  }
}

// 处理富文本中的链接点击
const handleLinkClick = (url: string) => {
  console.log('链接点击:', url)
  
  // 检查是否是内部链接
  if (url.startsWith('/') || url.includes('your-domain.com')) {
    // 内部链接，直接跳转
    uni.navigateTo({
      url: url
    })
  } else {
    // 外部链接，使用WebView打开
    uni.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(url)}`
    })
  }
}

// 处理富文本中的图片点击
const handleImageClick = (src: string) => {
  console.log('图片点击:', src)
  
  // 预览图片
  uni.previewImage({
    urls: [src],
    current: src,
    success: () => {
      console.log('图片预览成功')
    },
    fail: (err) => {
      console.error('图片预览失败:', err)
      uni.showToast({
        title: '图片加载失败',
        icon: 'none'
      })
    }
  })
}

// 视频加载完成
const handleVideoLoaded = (e: any) => {
  if (e.detail && e.detail.duration) {
    videoDuration.value = e.detail.duration
  }
}

// 视频加载错误
const handleVideoError = (e: any) => {
  console.error('视频加载失败:', e)
}

// 格式化视频时长
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 导出方法供外部使用
defineExpose({
  previewImage,
  handleLinkClick,
  handleImageClick
})
</script>

<style scoped lang="scss">
.base-post-detail {
  padding: 16px;
  background-color: white;
  border-radius: 12px;
  margin-bottom: 16px;
}

.post-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12px;
}

.post-content {
  font-size: 16px;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16px;
  white-space: pre-wrap;
  word-break: break-word;
}

.post-images {
  margin-bottom: 16px;
}

.media-swiper {
  width: 100%;
  height: 300px;
  border-radius: 8px;
  overflow: hidden;
}

.swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.media-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.video-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.media-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}

.play-button {
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 16px;
}

.video-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.video-duration {
  position: absolute;
  bottom: 12px;
  right: 12px;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
}

.duration-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.unknown-media {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #999;
}

.unknown-text {
  font-size: 14px;
}

.contact-info {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.contact-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.contact-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}


</style>