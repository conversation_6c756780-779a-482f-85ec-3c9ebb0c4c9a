package com.haolinkyou.controller;

import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.UserRoles;
import com.haolinkyou.service.UserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/roles")
public class UserRoleController {

    @Autowired
    private UserRoleService userRoleService;

    /**
     * 获取所有角色
     */
    @GetMapping("/list")
    public Result<List<UserRoles>> getAllRoles() {
        try {
            List<UserRoles> roles = userRoleService.getAllActiveRoles();
            return Result.success(roles);
        } catch (Exception e) {
            return Result.error("获取角色列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据角色代码获取角色信息
     */
    @GetMapping("/{roleCode}")
    public Result<UserRoles> getRoleByCode(@PathVariable String roleCode) {
        try {
            UserRoles role = userRoleService.getRoleByCode(roleCode);
            if (role == null) {
                return Result.error("角色不存在");
            }
            return Result.success(role);
        } catch (Exception e) {
            return Result.error("获取角色信息失败：" + e.getMessage());
        }
    }

    /**
     * 检查用户权限
     */
    @GetMapping("/check-permission")
    public Result<Boolean> checkPermission(@RequestParam String userRole, @RequestParam String permission) {
        try {
            boolean hasPermission = userRoleService.hasPermission(userRole, permission);
            return Result.success(hasPermission);
        } catch (Exception e) {
            return Result.error("权限检查失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户权限列表
     */
    @GetMapping("/permissions/{userRole}")
    public Result<List<String>> getUserPermissions(@PathVariable String userRole) {
        try {
            List<String> permissions = userRoleService.getUserPermissions(userRole);
            return Result.success(permissions);
        } catch (Exception e) {
            return Result.error("获取用户权限失败：" + e.getMessage());
        }
    }

    /**
     * 获取角色权限映射
     */
    @GetMapping("/role-permissions")
    public Result<Map<String, Object>> getRolePermissions() {
        try {
            List<UserRoles> roles = userRoleService.getAllActiveRoles();
            Map<String, Object> result = new HashMap<>();
            
            for (UserRoles role : roles) {
                Map<String, Object> roleInfo = new HashMap<>();
                roleInfo.put("name", role.getRoleName());
                roleInfo.put("description", role.getRoleDescription());
                roleInfo.put("permissions", role.getPermissions());
                roleInfo.put("isAdmin", userRoleService.isAdmin(role.getRoleCode()));
                result.put(role.getRoleCode(), roleInfo);
            }
            
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("获取角色权限映射失败：" + e.getMessage());
        }
    }
}
