/*
 * @Author: Rock
 * @Date: 2025-04-09 19:56:09
 * @LastEditors: Rock
 * @LastEditTime: 2025-04-19 16:48:56
 * @Description:
 */
import type { LoginResult } from "@/types/member";
import { defineStore } from "pinia";
import { ref } from "vue";

// 定义 Store
export const useMemberStore = defineStore(
  "member",
  () => {
    // 会员信息
    const profile = ref<LoginResult>();

    // 保存会员信息，登录时使用
    const setProfile = (val: LoginResult) => {
      profile.value = val;
    };

    // 清理会员信息，退出时使用
    const clearProfile = () => {
      profile.value = undefined;
      // 手动清除本地存储，确保token完全删除
      try {
        uni.removeStorageSync('member');
      } catch (error) {
        console.error('清除本地存储失败:', error);
      }
    };

    // 记得 return
    return {
      profile,
      setProfile,
      clearProfile,
    };
  },
  {
    // 网页端配置
    // persist: true,
    // 小程序端配置
    persist: {
      storage: {
        getItem(key) {
          return uni.getStorageSync(key);
        },
        setItem(key, value) {
          uni.setStorageSync(key, value);
        },
      },
    },
  }
);
