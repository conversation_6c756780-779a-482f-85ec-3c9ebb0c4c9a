package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.entity.Posts;
import com.haolinkyou.entity.UserCollects;
import com.haolinkyou.mapper.UserCollectsMapper;
import com.haolinkyou.service.IPostsService;
import com.haolinkyou.service.IUserCollectsService;
import com.haolinkyou.vo.UserCollectVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户收藏服务实现类
 */
@Service
public class UserCollectsServiceImpl extends ServiceImpl<UserCollectsMapper, UserCollects> implements IUserCollectsService {
    
    @Autowired
    private UserCollectsMapper userCollectsMapper;
    
    @Autowired
    private IPostsService postsService;
    
    @Override
    @Transactional
    public boolean toggleCollect(Long postId, Long userId) {
        if (postId == null || userId == null) {
            throw new RuntimeException("帖子ID和用户ID不能为空");
        }

        // 检查当前收藏状态
        int existingCount = userCollectsMapper.checkUserCollected(postId, userId);
        boolean currentlyCollected = existingCount > 0;

        if (currentlyCollected) {
            // 当前已收藏，执行取消收藏
            int affected = userCollectsMapper.removeCollect(postId, userId);
            if (affected > 0) {
                // 更新帖子收藏数
                updatePostCollectCount(postId);
                return false; // 返回false表示已取消收藏
            } else {
                throw new RuntimeException("取消收藏失败");
            }
        } else {
            // 当前未收藏，执行收藏操作
            int affected = userCollectsMapper.addCollect(postId, userId);
            if (affected > 0) {
                // 更新帖子收藏数
                updatePostCollectCount(postId);
                return true; // 返回true表示已收藏
            } else {
                throw new RuntimeException("收藏失败");
            }
        }
    }
    
    @Override
    public boolean isUserCollected(Long postId, Long userId) {
        if (postId == null || userId == null) {
            return false;
        }
        return userCollectsMapper.checkUserCollected(postId, userId) > 0;
    }
    
    @Override
    public int getPostCollectCount(Long postId) {
        if (postId == null) {
            return 0;
        }
        return userCollectsMapper.getPostCollectCount(postId);
    }
    
    @Override
    public IPage<UserCollectVo> getUserCollections(Long userId, Integer page, Integer pageSize, String categoryId, String sortBy) {
        if (userId == null) {
            throw new RuntimeException("用户ID不能为空");
        }

        Page<UserCollectVo> pageQuery = new Page<>(page, pageSize);
        return userCollectsMapper.getUserCollections(pageQuery, userId, categoryId, sortBy);
    }

    @Override
    public Map<String, Object> getUserCollectStats(Long userId) {
        if (userId == null) {
            throw new RuntimeException("用户ID不能为空");
        }

        Map<String, Object> stats = new HashMap<>();

        // 获取今日收藏数
        int todayCount = userCollectsMapper.getUserTodayCollectCount(userId);
        stats.put("todayCount", todayCount);

        // 获取本月收藏数
        int monthCount = userCollectsMapper.getUserMonthCollectCount(userId);
        stats.put("monthCount", monthCount);

        return stats;
    }

    /**
     * 更新帖子的收藏数
     * @param postId 帖子ID
     */
    private void updatePostCollectCount(Long postId) {
        int collectCount = getPostCollectCount(postId);

        UpdateWrapper<Posts> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", postId)
                    .set("collect_count", collectCount);

        postsService.update(updateWrapper);
    }
}
