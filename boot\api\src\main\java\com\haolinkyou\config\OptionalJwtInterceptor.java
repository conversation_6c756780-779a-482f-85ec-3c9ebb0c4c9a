package com.haolinkyou.config;

import com.haolinkyou.common.utils.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 可选JWT认证拦截器
 * 用于处理既允许游客访问，又需要获取登录用户信息的接口
 * 如果token有效，则解析用户信息；如果无效或不存在，则继续执行但不设置用户信息
 */
@Component
public class OptionalJwtInterceptor implements HandlerInterceptor {
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 对于OPTIONS请求直接放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }

        // 获取Authorization头
        String authHeader = request.getHeader("Authorization");

        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            // 提取token（去掉"Bearer "前缀）
            String token = authHeader.substring(7);

            // 验证token
            if (jwtUtil.validateToken(token)) {
                // 从token中解析用户信息并设置到request属性中
                Long userId = jwtUtil.getUserIdFromToken(token);
                String username = jwtUtil.getUsernameFromToken(token);
                String nickname = jwtUtil.getNicknameFromToken(token);

                // 将用户信息存储到request中，供后续使用
                request.setAttribute("userId", userId);
                request.setAttribute("username", username);
                request.setAttribute("nickname", nickname);
            }
        } else if (authHeader != null) {
            // 兼容旧的token格式（直接是token，没有Bearer前缀）
            String token = authHeader;

            // 验证token
            if (jwtUtil.validateToken(token)) {
                // 从token中解析用户信息并设置到request属性中
                Long userId = jwtUtil.getUserIdFromToken(token);
                String username = jwtUtil.getUsernameFromToken(token);
                String nickname = jwtUtil.getNicknameFromToken(token);

                // 将用户信息存储到request中，供后续使用
                request.setAttribute("userId", userId);
                request.setAttribute("username", username);
                request.setAttribute("nickname", nickname);
            }
        }

        // 无论token是否有效，都继续执行
        // 这样既允许游客访问，又能获取登录用户的信息
        return true;
    }
}
