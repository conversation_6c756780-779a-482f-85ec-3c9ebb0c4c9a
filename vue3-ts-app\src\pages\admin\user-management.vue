<template>
  <!-- 权限验证加载中 -->
  <view v-if="permissionLoading" class="permission-loading">
    <view class="loading-content">
      <up-loading-icon mode="circle" size="24" color="#1890ff"></up-loading-icon>
      <text class="loading-text">权限验证中...</text>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="user-management-page" v-else-if="hasPagePermission">
    <!-- 页面头部 -->
    <up-navbar
      title="用户管理"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 搜索和筛选 -->
    <view class="search-section" :style="{ marginTop: mainContentPaddingTop }">
      <up-search
        v-model="searchKeyword"
        placeholder="搜索用户昵称或ID"
        @search="loadUserList"
        @clear="handleClearSearch"
        :showAction="false"
      ></up-search>

      <view class="filter-tabs">
        <view
          class="filter-tab"
          :class="{ active: currentFilter === 'all' }"
          @click="handleFilterChange('all')"
        >
          全部({{ stats.total }})
        </view>
        <view
          class="filter-tab"
          :class="{ active: currentFilter === 'verified' }"
          @click="handleFilterChange('verified')"
        >
          已认证({{ stats.verified }})
        </view>
        <view
          class="filter-tab"
          :class="{ active: currentFilter === 'unverified' }"
          @click="handleFilterChange('unverified')"
        >
          未认证({{ stats.unverified }})
        </view>
        <view
          class="filter-tab"
          :class="{ active: currentFilter === 'banned' }"
          @click="handleFilterChange('banned')"
        >
          已封禁({{ stats.banned }})
        </view>
      </view>
    </view>

    <!-- 用户列表 -->
    <view class="user-list">
      <view
        class="user-item"
        v-for="user in userList"
        :key="user.id"
        @click="handleUserDetail(user)"
      >
        <up-avatar :src="user.avatar" shape="circle" :size="45"></up-avatar>
        <view class="user-info">
          <view class="user-name-row">
            <text class="user-name">{{ user.nickname }}</text>
            <view class="user-badges">
              <view v-if="user.isVerified" class="auth-badge">
                <up-icon name="checkmark-circle-fill" color="#52c41a" size="12"></up-icon>
                <text class="badge-text">已认证</text>
              </view>
              <view v-if="user.userRole === 'admin'" class="admin-badge">
                <text class="badge-text">管理员</text>
              </view>
              <view v-if="user.status === 0" class="banned-badge">
                <text class="badge-text">已封禁</text>
              </view>
            </view>
          </view>
          <view class="user-details">
            <text class="user-id">ID: {{ user.id }}</text>
            <text class="user-phone" v-if="user.mobile">{{ user.mobile }}</text>
          </view>
          <view class="user-stats">
            <text class="stat-item">积分: {{ user.points || 0 }}</text>
            <text class="stat-item">角色: {{ getRoleName(user.userRole) }}</text>
            <text class="stat-item">注册: {{ formatDate(user.createdTime) }}</text>
          </view>
        </view>
        <view class="user-actions">
          <up-button
            :text="user.status === 0 ? '解禁' : '禁用'"
            :type="user.status === 0 ? 'success' : 'error'"
            size="mini"
            plain
            @click.stop="handleBanUser(user)"
          ></up-button>
          <up-button
            :text="user.userRole === 'admin' ? '取消管理员' : '设为管理员'"
            type="primary"
            size="mini"
            @click.stop="handleSetAdmin(user)"
          ></up-button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore">
      <up-loadmore
        :status="loadStatus"
        @loadmore="loadMoreUsers"
      ></up-loadmore>
    </view>

    <!-- 用户详情弹窗 -->
    <up-popup 
      v-model:show="showUserDetailModal" 
      mode="center" 
      :round="10"
      :closeable="true"
      @close="closeUserDetailModal"
    >
      <view class="user-detail-modal" v-if="selectedUser">
        <view class="modal-header">
          <text class="modal-title">用户详情</text>
          <up-icon name="close" @click="closeUserDetailModal" size="20" color="#999"></up-icon>
        </view>
        <view class="modal-content">
          <view class="detail-item">
            <text class="detail-label">用户ID:</text>
            <text class="detail-value">{{ selectedUser.id }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">昵称:</text>
            <text class="detail-value">{{ selectedUser.nickname }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">手机号:</text>
            <text class="detail-value">{{ selectedUser.mobile || '未绑定' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">认证状态:</text>
            <text class="detail-value">{{ selectedUser.isVerified ? '已认证' : '未认证' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">用户角色:</text>
            <text class="detail-value">{{ getRoleName(selectedUser.userRole) }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">账户状态:</text>
            <text class="detail-value">{{ getStatusText(selectedUser.status) }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">注册时间:</text>
            <text class="detail-value">{{ formatDateTime(selectedUser.createdTime) }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">最后登录:</text>
            <text class="detail-value">{{ selectedUser.lastLoginTime ? formatDateTime(selectedUser.lastLoginTime) : '从未登录' }}</text>
          </view>
        </view>
        <view class="modal-footer">
          <up-button
            :text="selectedUser.status === 0 ? '解封用户' : '封禁用户'"
            :type="selectedUser.status === 0 ? 'success' : 'error'"
            size="small"
            plain
            @click="handleToggleBan"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useSafeArea } from '@/utils/safeArea';
import type { LoadStatus } from '@/types/admin';
import type { User } from '@/types/user';
import { checkPagePermission } from '@/utils/pagePermission';
import {
  getUserListAPI,
  getUserStatsAPI,
  getUserDetailAPI,
  updateUserStatusAPI,
  updateUserRoleAPI,
  type UserSearchParams,
  type UserStatsData
} from '@/services/userManagement';

const { mainContentPaddingTop } = useSafeArea();

// 权限验证状态
const permissionLoading = ref(true);
const hasPagePermission = ref(false);

// 搜索关键词
const searchKeyword = ref<string>('');

// 当前筛选条件
const currentFilter = ref<string>('all');

// 统计数据
const stats = reactive<UserStatsData>({
  total: 0,
  verified: 0,
  unverified: 0,
  banned: 0
});

// 用户列表
const userList = ref<User[]>([]);

// 分页相关
const currentPage = ref<number>(1);
const pageSize = ref<number>(10);
const hasMore = ref<boolean>(true);
const loadStatus = ref<LoadStatus>('loadmore');

// 弹窗相关
const showUserDetailModal = ref<boolean>(false);
const selectedUser = ref<User | null>(null);

// 初始化数据
const initData = async () => {
  try {
    // 获取统计数据
    const statsRes = await getUserStatsAPI();
    if (statsRes.success && statsRes.data) {
      Object.assign(stats, statsRes.data);
    }

    // 加载用户列表
    await loadUserList();
  } catch (error) {
    console.error('初始化数据失败:', error);
    uni.showToast({
      title: '初始化失败',
      icon: 'none'
    });
  }
};

// 加载用户列表
const loadUserList = async (isLoadMore = false) => {
  try {
    if (!isLoadMore) {
      loadStatus.value = 'loading';
      currentPage.value = 1;
    }

    const params: UserSearchParams = {
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value.trim() || undefined,
      status: getStatusFilter(),
      isVerified: getVerifiedFilter()
    };

    const res = await getUserListAPI(params);

    if (res.success && res.data) {
      if (isLoadMore) {
        userList.value.push(...res.data.records);
      } else {
        userList.value = res.data.records;
      }

      hasMore.value = res.data.current < res.data.pages;
      loadStatus.value = hasMore.value ? 'loadmore' : 'nomore';
    } else {
      throw new Error(res.message || '获取用户列表失败');
    }
  } catch (error) {
    console.error('加载用户列表失败:', error);
    loadStatus.value = 'loadmore';
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  }
};

// 获取状态筛选条件
const getStatusFilter = (): number | undefined => {
  switch (currentFilter.value) {
    case 'banned':
      return 0; // 封禁状态
    case 'normal':
      return 1; // 正常状态
    default:
      return undefined; // 全部
  }
};

// 获取认证状态筛选条件
const getVerifiedFilter = (): boolean | undefined => {
  switch (currentFilter.value) {
    case 'verified':
      return true; // 已认证
    case 'unverified':
      return false; // 未认证
    default:
      return undefined; // 全部
  }
};

// 页面权限检查和初始化
const initPageWithPermission = async () => {
  try {
    permissionLoading.value = true;

    // 检查页面权限
    const hasPermission = await checkPagePermission('/pages/admin/user-management');
    hasPagePermission.value = hasPermission;

    if (hasPermission) {
      // 权限验证通过，初始化页面数据
      await initData();
    }
  } catch (error) {
    console.error('页面权限检查失败:', error);
    hasPagePermission.value = false;
  } finally {
    permissionLoading.value = false;
  }
};

onMounted(() => {
  initPageWithPermission();
});

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 清除搜索
const handleClearSearch = () => {
  searchKeyword.value = '';
  loadUserList();
};

// 筛选变更
const handleFilterChange = (filter: string) => {
  currentFilter.value = filter;
  loadUserList();
};

// 加载更多用户
const loadMoreUsers = () => {
  if (hasMore.value && loadStatus.value !== 'loading') {
    currentPage.value++;
    loadUserList(true);
  }
};

// 查看用户详情
const handleUserDetail = async (user: User) => {
  try {
    const res = await getUserDetailAPI(user.id);
    if (res.success && res.data) {
      selectedUser.value = res.data;
      showUserDetailModal.value = true;
    } else {
      throw new Error(res.message || '获取用户详情失败');
    }
  } catch (error) {
    console.error('获取用户详情失败:', error);
    uni.showToast({
      title: '获取详情失败',
      icon: 'none'
    });
  }
};

// 关闭用户详情弹窗
const closeUserDetailModal = () => {
  showUserDetailModal.value = false;
  selectedUser.value = null;
};

// 切换封禁状态
const handleToggleBan = async () => {
  if (!selectedUser.value) return;

  const action = selectedUser.value.status === 0 ? '解封' : '封禁';
  const newStatus = selectedUser.value.status === 0 ? 1 : 0;

  uni.showModal({
    title: `确认${action}`,
    content: `确定要${action}用户"${selectedUser.value.nickname}"吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await updateUserStatusAPI(selectedUser.value!.id, newStatus);
          if (result.success) {
            // 更新本地数据
            selectedUser.value!.status = newStatus;
            const userIndex = userList.value.findIndex(u => u.id === selectedUser.value!.id);
            if (userIndex > -1) {
              userList.value[userIndex].status = newStatus;
            }

            uni.showToast({
              title: `${action}成功`,
              icon: 'success'
            });
            closeUserDetailModal();
          } else {
            throw new Error(result.message || `${action}失败`);
          }
        } catch (error) {
          console.error(`${action}用户失败:`, error);
          uni.showToast({
            title: `${action}失败`,
            icon: 'none'
          });
        }
      }
    }
  });
};

// 禁用用户
const handleBanUser = async (user: User) => {
  const action = user.status === 0 ? '解禁' : '禁用';
  const newStatus = user.status === 0 ? 1 : 0;

  uni.showModal({
    title: `确认${action}`,
    content: `确定要${action}用户"${user.nickname}"吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await updateUserStatusAPI(user.id, newStatus);
          if (result.success) {
            // 更新本地数据
            user.status = newStatus;
            uni.showToast({
              title: `${action}成功`,
              icon: 'success'
            });
          } else {
            throw new Error(result.message || `${action}失败`);
          }
        } catch (error) {
          console.error(`${action}用户失败:`, error);
          uni.showToast({
            title: `${action}失败`,
            icon: 'none'
          });
        }
      }
    }
  });
};

// 设为管理员
const handleSetAdmin = async (user: User) => {
  const isAdmin = user.userRole === 'admin';
  const action = isAdmin ? '取消管理员' : '设为管理员';
  const newRole = isAdmin ? 'owner' : 'admin'; // 取消管理员时设为业主角色

  uni.showModal({
    title: `确认${action}`,
    content: `确定要${action}"${user.nickname}"吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await updateUserRoleAPI(user.id, newRole);
          if (result.success) {
            // 更新本地数据
            user.userRole = newRole;
            uni.showToast({
              title: `${action}成功`,
              icon: 'success'
            });
          } else {
            throw new Error(result.message || `${action}失败`);
          }
        } catch (error) {
          console.error(`${action}失败:`, error);
          uni.showToast({
            title: `${action}失败`,
            icon: 'none'
          });
        }
      }
    }
  });
};

// 获取角色名称
const getRoleName = (roleCode: string) => {
  const roleMap: Record<string, string> = {
    guest: '游客',
    owner: '业主',
    tenant: '租户',
    property: '物业',
    committee: '业委会',
    community: '社区管理员',
    admin: '系统管理员'
  };
  return roleMap[roleCode] || roleCode;
};

// 格式化日期
const formatDate = (dateStr: string | Date) => {
  if (!dateStr) return '未知';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN');
};

// 格式化日期时间
const formatDateTime = (dateStr: string | Date) => {
  if (!dateStr) return '未知';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN');
};

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '已封禁',
    1: '正常',
    2: '冻结'
  };
  return statusMap[status] || '未知';
};
</script>

<style lang="scss">
// 权限验证加载状态
.permission-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .loading-text {
      font-size: 14px;
      color: #666;
    }
  }
}

.user-management-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  // 搜索区域
  .search-section {
    background: #fff;
    padding: 15px 20px;
    margin-bottom: 10px;

    .filter-tabs {
      display: flex;
      margin-top: 15px;
      border-radius: 6px;
      overflow: hidden;
      border: 1px solid #d9d9d9;

      .filter-tab {
        flex: 1;
        padding: 8px 12px;
        text-align: center;
        font-size: 14px;
        color: #666;
        background: #fafafa;
        cursor: pointer;

        &.active {
          background: #1890ff;
          color: #fff;
        }

        &:not(:last-child) {
          border-right: 1px solid #d9d9d9;
        }
      }
    }
  }

  // 用户列表
  .user-list {
    background: #fff;

    .user-item {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;

      &:active {
        background: #f5f5f5;
      }

      .user-info {
        flex: 1;
        margin-left: 15px;

        .user-name-row {
          display: flex;
          align-items: center;
          margin-bottom: 6px;

          .user-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-right: 10px;
          }

          .user-badges {
            display: flex;
            gap: 6px;

            .auth-badge, .admin-badge, .banned-badge {
              display: flex;
              align-items: center;
              padding: 2px 6px;
              border-radius: 10px;
              font-size: 10px;

              .badge-text {
                margin-left: 2px;
              }
            }

            .auth-badge {
              background: #f6ffed;
              color: #52c41a;
            }

            .admin-badge {
              background: #fff2e8;
              color: #fa8c16;

              .badge-text {
                margin-left: 0;
              }
            }

            .banned-badge {
              background: #fff1f0;
              color: #ff4d4f;

              .badge-text {
                margin-left: 0;
              }
            }
          }
        }

        .user-details {
          display: flex;
          gap: 15px;
          margin-bottom: 4px;

          .user-id, .user-phone {
            font-size: 12px;
            color: #999;
          }
        }

        .user-stats {
          display: flex;
          gap: 15px;

          .stat-item {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .user-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 10px;
      }
    }
  }

  // 加载更多
  .load-more {
    padding: 20px;
  }

  // 用户详情弹窗
  .user-detail-modal {
    width: 320px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;

      .modal-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }

    .modal-content {
      padding: 20px;

      .detail-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        .detail-label {
          font-size: 14px;
          color: #666;
        }

        .detail-value {
          font-size: 14px;
          color: #333;
        }
      }
    }

    .modal-footer {
      display: flex;
      justify-content: center;
      padding: 0 20px 20px;

      :deep(.u-button) {
        width: 120px;
        height: 36px;
      }
    }
  }
}
</style>
