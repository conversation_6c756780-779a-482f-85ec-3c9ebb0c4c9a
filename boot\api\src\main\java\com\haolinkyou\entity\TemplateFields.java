package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模板字段配置实体类
 */
@Data
@TableName("template_fields")
@EqualsAndHashCode(callSuper = true)
public class TemplateFields extends BaseEntity {

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段标签
     */
    private String fieldLabel;

    /**
     * 字段类型 (text, textarea, number, date, select, radio, checkbox, file, image)
     */
    private String fieldType;

    /**
     * 字段配置JSON
     * 包含选项、验证规则、默认值等
     */
    private String fieldConfig;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 字段排序
     */
    private Integer sortOrder;

    /**
     * 字段状态 (0-禁用 1-启用)
     */
    private Integer status;

    /**
     * 占位符文本
     */
    private String placeholder;

    /**
     * 帮助文本
     */
    private String helpText;
}
