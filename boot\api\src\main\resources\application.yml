server:
  port: 3205

spring:
  main:
    banner-mode: off
  datasource:
    url: **********************************************************************************************************************
    username: demo
    password: R5e7BcMDxp3B8Pxs
    driver-class-name: com.mysql.cj.jdbc.Driver
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  web:
    resources:
#     配置静态资源路径
      static-locations: file:${file.upload.dir}


# MyBatis-Plus配置
mybatis-plus:
  global-config:
    banner: false
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
      table-prefix:
#指定mapper.xml文件的位置
  mapper-locations: classpath*:com/haolinkyou/mapper/xml/*.xml
  type-aliases-package: com.haolinkyou.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # MyBatis-Plus类型处理器配置
  type-handlers-package: com.haolinkyou.config

# 文件上传配置
file:
  upload:
    dir: "D:/files/"

#查看所有注册的路由
management:
  endpoints:
    web:
      exposure:
        include: "*"