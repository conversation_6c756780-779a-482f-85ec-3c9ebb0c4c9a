/**
 * 帖子状态管理工具
 * 用于管理点赞、收藏等状态的跨页面同步
 */

interface PostState {
  isLiked: boolean
  isCollected: boolean
  likeCount: number
  collectCount: number
  commentCount: number
  viewCount: number
  lastUpdated: number
}

class PostStateManager {
  private states: Map<number, PostState> = new Map()
  private readonly STORAGE_KEY = 'post_states'
  private readonly MAX_CACHE_SIZE = 500
  private readonly CACHE_EXPIRE_TIME = 30 * 60 * 1000 // 30分钟

  constructor() {
    this.loadFromStorage()
  }

  /**
   * 从本地存储加载状态
   */
  private loadFromStorage() {
    try {
      const stored = uni.getStorageSync(this.STORAGE_KEY)
      if (stored && typeof stored === 'object') {
        // 清理过期数据
        const now = Date.now()
        Object.entries(stored).forEach(([postId, state]: [string, any]) => {
          if (state.lastUpdated && (now - state.lastUpdated) < this.CACHE_EXPIRE_TIME) {
            this.states.set(parseInt(postId), state)
          }
        })
      }
    } catch (error) {
      console.error('加载帖子状态失败:', error)
    }
  }

  /**
   * 保存状态到本地存储
   */
  private saveToStorage() {
    try {
      // 限制缓存大小
      if (this.states.size > this.MAX_CACHE_SIZE) {
        // 删除最旧的数据
        const entries = Array.from(this.states.entries())
        entries.sort((a, b) => a[1].lastUpdated - b[1].lastUpdated)
        
        const toDelete = entries.slice(0, entries.length - this.MAX_CACHE_SIZE)
        toDelete.forEach(([postId]) => {
          this.states.delete(postId)
        })
      }

      const stateObj = Object.fromEntries(this.states)
      uni.setStorageSync(this.STORAGE_KEY, stateObj)
    } catch (error) {
      console.error('保存帖子状态失败:', error)
    }
  }

  /**
   * 更新帖子状态
   */
  updatePostState(postId: number, updates: Partial<PostState>) {
    const currentState = this.states.get(postId) || {
      isLiked: false,
      isCollected: false,
      likeCount: 0,
      collectCount: 0,
      commentCount: 0,
      viewCount: 0,
      lastUpdated: 0
    }

    const newState = {
      ...currentState,
      ...updates,
      lastUpdated: Date.now()
    }

    this.states.set(postId, newState)
    this.saveToStorage()

    // 通知其他页面状态变更
    this.notifyStateChange(postId, newState)
  }

  /**
   * 获取帖子状态
   */
  getPostState(postId: number): PostState | null {
    const state = this.states.get(postId)
    if (!state) return null

    // 检查是否过期
    const now = Date.now()
    if (now - state.lastUpdated > this.CACHE_EXPIRE_TIME) {
      this.states.delete(postId)
      this.saveToStorage()
      return null
    }

    return state
  }

  /**
   * 批量更新帖子状态
   */
  batchUpdatePosts(posts: Array<{ id: number; [key: string]: any }>) {
    posts.forEach(post => {
      const state = this.getPostState(post.id)
      if (state) {
        // 应用缓存的状态
        post.isLiked = state.isLiked
        post.isCollected = state.isCollected
        post.likeCount = state.likeCount
        post.collectCount = state.collectCount
        post.commentCount = state.commentCount
        post.viewCount = state.viewCount
      }
    })
  }

  /**
   * 清除指定帖子的状态
   */
  clearPostState(postId: number) {
    this.states.delete(postId)
    this.saveToStorage()
  }

  /**
   * 清除所有状态
   */
  clearAllStates() {
    this.states.clear()
    uni.removeStorageSync(this.STORAGE_KEY)
  }

  /**
   * 通知状态变更
   */
  private notifyStateChange(postId: number, state: PostState) {
    // 使用uni-app的事件总线通知状态变更
    uni.$emit('postStateChanged', { postId, state })
  }

  /**
   * 监听状态变更
   */
  onStateChange(callback: (data: { postId: number; state: PostState }) => void) {
    uni.$on('postStateChanged', callback)
  }

  /**
   * 取消监听状态变更
   */
  offStateChange(callback: (data: { postId: number; state: PostState }) => void) {
    uni.$off('postStateChanged', callback)
  }
}

// 创建单例实例
export const postStateManager = new PostStateManager()

// 导出类型
export type { PostState }