<template>
  <view class="groupbuy-post-detail">
    <!-- 团购标题和价格 -->
    <view class="groupbuy-header">
      <view class="product-title">{{ post.title }}</view>
      <view class="price-section">
        <view class="price-row">
          <text class="original-price">原价：¥{{ post.originalPrice }}</text>
          <text class="group-price">团购价：¥{{ post.groupPrice }}</text>
        </view>
        <view class="discount-info">
          <text class="save-amount">立省¥{{ saveAmount }}</text>
          <text class="discount-percent">{{ discountPercent }}折</text>
        </view>
      </view>
    </view>
    
    <!-- 团购进度 -->
    <view class="groupbuy-progress">
      <view class="progress-header">
        <text class="progress-title">团购进度</text>
        <text class="progress-status" :class="statusClass">{{ statusText }}</text>
      </view>
      
      <view class="progress-bar">
        <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
      </view>
      
      <view class="progress-info">
        <text class="current-count">已参团：{{ currentCount }}人</text>
        <text class="min-count">起团：{{ post.minQuantity }}人</text>
      </view>
      
      <!-- 时间信息 -->
      <view class="time-info">
        <view v-if="post.startTime" class="time-item">
          <text class="time-label">开始时间：</text>
          <text class="time-value">{{ formatDateTime(post.startTime) }}</text>
        </view>
        <view class="time-item">
          <text class="time-label">截止时间：</text>
          <text class="time-value">{{ formatDateTime(post.deadline) }}</text>
          <text v-if="isExpired" class="expired-tag">已截止</text>
        </view>
        <view v-if="!isExpired && timeRemaining" class="time-remaining">
          <text class="remaining-text">剩余时间：{{ timeRemaining }}</text>
        </view>
      </view>
    </view>
    
    <!-- 基础内容 -->
    <BasePostDetail :post="post" />
    
    <!-- 联系信息 -->
    <view class="contact-section">
      <view class="section-title">联系信息</view>
      <view class="contact-grid">
        <view class="contact-item">
          <text class="contact-label">联系人：</text>
          <text class="contact-value">{{ post.contactPerson }}</text>
        </view>
        <view class="contact-item">
          <text class="contact-label">联系电话：</text>
          <text class="contact-value">{{ post.contactPhone }}</text>
        </view>
        <view class="contact-item">
          <text class="contact-label">取货地点：</text>
          <text class="contact-value">{{ post.pickupLocation }}</text>
        </view>
        <view class="contact-item">
          <text class="contact-label">付款方式：</text>
          <text class="contact-value">{{ formatPaymentMethod(post.paymentMethod) }}</text>
        </view>
      </view>
    </view>
    
    <!-- 备注说明 -->
    <view v-if="post.notes" class="notes-section">
      <view class="section-title">备注说明</view>
      <view class="notes-content">{{ post.notes }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BasePostDetail from './BasePostDetail.vue'

interface Props {
  post: {
    id?: number
    title: string
    content: string
    images?: string[]
    originalPrice: string
    groupPrice: string
    minQuantity: string
    startTime?: string
    deadline: string
    contactPerson: string
    contactPhone: string
    pickupLocation: string
    paymentMethod: string
    notes?: string
    createdTime: string
    updatedTime?: string
    currentCount?: number // 当前参团人数，需要从后端获取
    [key: string]: any
  }
}

const props = defineProps<Props>()

// 当前参团人数（模拟数据，实际应从后端获取）
const currentCount = computed(() => props.post.currentCount || 0)

// 节省金额
const saveAmount = computed(() => {
  const original = parseFloat(props.post.originalPrice)
  const group = parseFloat(props.post.groupPrice)
  return (original - group).toFixed(2)
})

// 折扣百分比
const discountPercent = computed(() => {
  const original = parseFloat(props.post.originalPrice)
  const group = parseFloat(props.post.groupPrice)
  return ((group / original) * 10).toFixed(1)
})

// 进度百分比
const progressPercent = computed(() => {
  const min = parseInt(props.post.minQuantity)
  const current = currentCount.value
  return Math.min((current / min) * 100, 100)
})

// 是否过期
const isExpired = computed(() => {
  return new Date(props.post.deadline) < new Date()
})

// 状态文本和样式
const statusText = computed(() => {
  if (isExpired.value) return '已截止'
  if (currentCount.value >= parseInt(props.post.minQuantity)) return '成团成功'
  return '进行中'
})

const statusClass = computed(() => {
  if (isExpired.value) return 'expired'
  if (currentCount.value >= parseInt(props.post.minQuantity)) return 'success'
  return 'active'
})

// 剩余时间
const timeRemaining = computed(() => {
  if (isExpired.value) return null
  
  const now = new Date()
  const deadline = new Date(props.post.deadline)
  const diff = deadline.getTime() - now.getTime()
  
  if (diff <= 0) return null
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) return `${days}天${hours}小时`
  if (hours > 0) return `${hours}小时${minutes}分钟`
  return `${minutes}分钟`
})

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化付款方式
const formatPaymentMethod = (method: string) => {
  const methodMap = {
    'wechat': '微信支付',
    'alipay': '支付宝',
    'cash': '现金',
    'bank': '银行转账'
  }
  return methodMap[method as keyof typeof methodMap] || method
}
</script>

<style scoped lang="scss">
.groupbuy-post-detail {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.groupbuy-header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
  color: white;
  padding: 20px 16px;
}

.product-title {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 16px;
}

.price-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.original-price {
  font-size: 14px;
  text-decoration: line-through;
  opacity: 0.8;
}

.group-price {
  font-size: 18px;
  font-weight: bold;
}

.discount-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.save-amount {
  font-size: 14px;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
}

.discount-percent {
  font-size: 14px;
  font-weight: bold;
}

.groupbuy-progress {
  padding: 16px;
  background-color: #f8f9fa;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.progress-status {
  font-size: 14px;
  font-weight: bold;
  
  &.active {
    color: #007bff;
  }
  
  &.success {
    color: #28a745;
  }
  
  &.expired {
    color: #dc3545;
  }
}

.progress-bar {
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.time-info {
  border-top: 1px solid #e9ecef;
  padding-top: 12px;
}

.time-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 14px;
}

.time-label {
  color: #666;
  margin-right: 8px;
}

.time-value {
  color: #333;
  font-weight: 500;
}

.expired-tag {
  margin-left: 8px;
  padding: 2px 6px;
  background-color: #dc3545;
  color: white;
  border-radius: 4px;
  font-size: 12px;
}

.time-remaining {
  margin-top: 8px;
  text-align: center;
}

.remaining-text {
  font-size: 14px;
  color: #ff6b6b;
  font-weight: bold;
}

.contact-section, .notes-section {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.contact-item {
  display: flex;
  flex-direction: column;
}

.contact-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.contact-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.notes-content {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
}
</style>
