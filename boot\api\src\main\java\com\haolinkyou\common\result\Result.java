package com.haolinkyou.common.result;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private boolean success;
    private String message;
    private Integer code; // 可选：自定义错误码
    private T data;

    // 成功无数据
    public static <T> Result<T> success() {
        return build(true, null, null, null);
    }

    // 成功带数据
    public static <T> Result<T> success(T data) {
        return build(true, null, null, data);
    }

    // 成功带消息和数据
    public static <T> Result<T> success(T data, String message) {
        return build(true, message, null, data);
    }

    // 失败无数据，带消息
    public static <T> Result<T> error(String message) {
        return build(false, message, null, null);
    }

    // 失败带消息和数据
    public static <T> Result<T> error(String message, T data) {
        return build(false, message, null, data);
    }

    // 失败带消息和错误码
    public static <T> Result<T> error(Integer code, String message) {
        return build(false, message, code, null);
    }

    // 失败带消息、错误码和数据
    public static <T> Result<T> error(Integer code, String message, T data) {
        return build(false, message, code, data);
    }

    // 构建方法（内部使用）
    private static <T> Result<T> build(boolean success, String message, Integer code, T data) {
        Result<T> result = new Result<>();
        result.setSuccess(success);
        result.setMessage(message);
        result.setCode(code);
        result.setData(data);
        return result;
    }
}
