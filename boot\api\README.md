# 评论系统接口实现

## 概述

本项目实现了完整的评论系统接口，包括添加评论、修改评论、删除评论、获取评论列表等功能。

## 主要功能

### 1. 添加评论 (`//comments/addComment`)
- **方法**: POST
- **功能**: 添加新评论，支持回复功能
- **特点**: 
  - 参数验证（帖子ID、用户ID、评论内容必填）
  - 自动设置默认值（状态、点赞数等）
  - 支持嵌套回复（通过parentCommentId）
  - 自动更新帖子评论数

### 2. 获取评论列表 (`/comments/getCommentsByPostId`)
- **方法**: GET
- **功能**: 获取指定帖子的评论列表
- **特点**: 支持嵌套回复结构

### 3. 修改评论 (`/comments/editComment`)
- **方法**: POST
- **功能**: 修改评论内容

### 4. 删除评论 (`/comments/deleteComment`)
- **方法**: POST
- **功能**: 删除指定评论
- **特点**: 自动更新帖子评论数

### 5. 获取所有评论 (`/comments/listAll`)
- **方法**: GET
- **功能**: 获取所有评论列表

## 技术特点

### 1. 参数验证
- 在Controller中进行手动参数验证
- 完整的参数检查（空值、格式等）
- 统一的错误响应格式

### 2. 统一响应格式
- 使用 `Result<T>` 包装所有响应
- 包含成功状态、消息、错误码、数据
- 支持带消息的成功响应

### 3. 业务逻辑
- 完整的参数验证
- 自动设置默认值
- 关联数据更新（帖子评论数）
- 支持评论嵌套回复

### 4. 异常处理
- 全局异常处理器
- 参数错误处理
- 业务异常处理
- 系统异常处理

## 文件结构

```
boot/src/main/java/com/haolinkyou/
├── controller/
│   └── CommentsController.java          # 评论控制器
├── service/
│   ├── ICommentsService.java           # 评论服务接口
│   ├── impl/
│   │   └── CommentsServiceImpl.java    # 评论服务实现
│   ├── IPostsService.java              # 帖子服务接口
│   └── impl/
│       └── PostsServiceImpl.java       # 帖子服务实现
├── entity/
│   └── Comments.java                   # 评论实体类
├── api/
│   └── dto/
│       └── AddCommentDto.java          # 添加评论DTO
├── common/
│   └── result/
│       └── Result.java                 # 统一响应结果类
└── handler/
    └── GlobalExceptionHandler.java     # 全局异常处理器
```

## 数据库设计

评论表 (`comments`) 包含以下字段：
- `comment_id`: 评论ID（主键）
- `post_id`: 帖子ID（外键）
- `user_id`: 用户ID（外键）
- `content`: 评论内容
- `status`: 状态（0-待审核 1-已审核 2-被驳回）
- `reject_reason`: 驳回原因
- `parent_comment_id`: 父评论ID（用于回复功能）
- `like_count`: 点赞数
- `created_time`: 创建时间
- `updated_time`: 更新时间
- `is_deleted`: 逻辑删除标记

## 使用示例

### 添加评论
```bash
curl -X POST http://localhost:8080//comments/addComment \
  -H "Content-Type: application/json" \
  -d '{
    "postId": 1,
    "userId": 1,
    "content": "这是一条测试评论"
  }'
```

### 获取评论列表
```bash
curl -X GET "http://localhost:8080/comments/getCommentsByPostId?postId=1"
```

## 测试

项目包含完整的单元测试：
- `CommentsControllerTest.java`: 控制器测试
- 测试覆盖参数验证、业务逻辑、异常处理等场景

## 注意事项

1. 确保数据库表结构正确创建
2. 配置正确的数据库连接信息
3. 启动前检查依赖是否完整
4. 建议在生产环境中添加更多的安全验证

## 扩展功能

可以考虑添加的功能：
1. 评论点赞功能
2. 评论举报功能
3. 评论审核流程
4. 评论通知功能
5. 评论敏感词过滤 