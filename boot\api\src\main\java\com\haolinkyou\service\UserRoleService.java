package com.haolinkyou.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haolinkyou.entity.UserRoles;

import java.util.List;

public interface UserRoleService extends IService<UserRoles> {

    /**
     * 根据角色代码获取角色信息
     * @param roleCode 角色代码
     * @return 角色信息
     */
    UserRoles getRoleByCode(String roleCode);

    /**
     * 获取所有启用的角色
     * @return 角色列表
     */
    List<UserRoles> getAllActiveRoles();

    /**
     * 检查用户是否有指定权限
     * @param userRole 用户角色
     * @param permission 权限代码
     * @return 是否有权限
     */
    boolean hasPermission(String userRole, String permission);

    /**
     * 获取用户的所有权限
     * @param userRole 用户角色
     * @return 权限列表
     */
    List<String> getUserPermissions(String userRole);

    /**
     * 检查是否为管理员角色
     * @param userRole 用户角色
     * @return 是否为管理员
     */
    boolean isAdmin(String userRole);
}
