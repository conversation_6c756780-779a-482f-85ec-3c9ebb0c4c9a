package com.haolinkyou.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.Votes;
import com.haolinkyou.entity.VoteRecords;
import com.haolinkyou.entity.Users;
import com.haolinkyou.entity.Posts;
import com.haolinkyou.mapper.VotesMapper;
import com.haolinkyou.mapper.VoteRecordsMapper;
import com.haolinkyou.mapper.PostsMapper;
import com.haolinkyou.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

@RestController
@RequestMapping("/api/vote")
public class VoteController {

    @Autowired
    private VotesMapper votesMapper;
    
    @Autowired
    private VoteRecordsMapper voteRecordsMapper;
    
    @Autowired
    private PostsMapper postsMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 提交投票
     */
    @PostMapping("/submit")
    public Result<String> submitVote(@RequestBody Map<String, Object> requestData, HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 获取提交数据
            Long postId = Long.valueOf(requestData.get("postId").toString());
            Long voteId = Long.valueOf(requestData.get("voteId").toString());
            List<Object> selectedOptions = (List<Object>) requestData.get("selectedOptions");

            // 检查投票是否存在
            Votes vote = votesMapper.selectById(voteId);
            if (vote == null) {
                return Result.error("投票不存在");
            }

            // 检查投票是否已截止
            if (vote.getEndTime() != null && vote.getEndTime().isBefore(LocalDateTime.now())) {
                return Result.error("投票已截止");
            }

            // 检查用户是否已经投票过
            QueryWrapper<VoteRecords> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vote_id", voteId)
                       .eq("user_id", userId);
            VoteRecords existingRecord = voteRecordsMapper.selectOne(queryWrapper);
            
            if (existingRecord != null) {
                return Result.error("您已经投票过了");
            }

            // 保存投票记录到数据库
            VoteRecords voteRecord = new VoteRecords();
            voteRecord.setVoteId(voteId);
            voteRecord.setUserId(userId);
            voteRecord.setSelectedOptions(objectMapper.writeValueAsString(selectedOptions));
            voteRecord.setIsAnonymous(vote.getIsAnonymous());

            int result = voteRecordsMapper.insert(voteRecord);
            if (result > 0) {
                return Result.success("投票成功");
            } else {
                return Result.error("投票失败");
            }

        } catch (Exception e) {
            System.err.println("投票提交失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("投票失败: " + e.getMessage());
        }
    }

    /**
     * 获取投票结果
     */
    @GetMapping("/results")
    public Result<Map<String, Object>> getVoteResults(@RequestParam Long postId, HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 根据帖子ID查找投票
            QueryWrapper<Votes> voteQuery = new QueryWrapper<>();
            voteQuery.eq("post_id", postId);
            Votes vote = votesMapper.selectOne(voteQuery);
            
            if (vote == null) {
                return Result.error("投票不存在");
            }

            // 获取所有投票记录
            QueryWrapper<VoteRecords> recordQuery = new QueryWrapper<>();
            recordQuery.eq("vote_id", vote.getId());
            List<VoteRecords> allRecords = voteRecordsMapper.selectList(recordQuery);

            Map<String, Object> results = new HashMap<>();
            
            // 统计选项投票数
            Map<String, Integer> optionVotes = new HashMap<>();
            List<Map<String, Object>> participants = new ArrayList<>();
            
            for (VoteRecords record : allRecords) {
                try {
                    List<Object> selectedOptions = objectMapper.readValue(record.getSelectedOptions(), List.class);
                    
                    // 统计选项
                    for (Object option : selectedOptions) {
                        String optionStr = option.toString();
                        optionVotes.put(optionStr, optionVotes.getOrDefault(optionStr, 0) + 1);
                    }
                    
                    // 如果不是匿名投票，添加参与者信息
                    if (!record.getIsAnonymous()) {
                        Map<String, Object> participant = new HashMap<>();
                        participant.put("id", record.getUserId());
                        
                        Users user = userMapper.selectById(record.getUserId());
                        participant.put("username", user != null ? user.getNickname() : "未知用户");
                        participant.put("selectedOptions", selectedOptions);
                        participant.put("voteTime", record.getCreatedTime().toString());
                        participants.add(participant);
                    }
                } catch (JsonProcessingException e) {
                    System.err.println("解析投票选项JSON失败: " + e.getMessage());
                }
            }
            
            results.put("optionVotes", optionVotes);
            results.put("totalVotes", allRecords.size());
            results.put("participants", participants);
            results.put("trend", new ArrayList<>()); // 趋势数据

            return Result.success(results);

        } catch (Exception e) {
            System.err.println("获取投票结果失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("获取结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取投票详情和用户参与状态
     */
    @GetMapping("/detail")
    public Result<Map<String, Object>> getVoteDetail(@RequestParam Long postId, HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            
            // 首先根据帖子ID查找投票
            Votes vote = null;
            
            // 方法1：通过post_id直接查找
            QueryWrapper<Votes> voteQuery = new QueryWrapper<>();
            voteQuery.eq("post_id", postId);
            vote = votesMapper.selectOne(voteQuery);
            
            // 方法2：如果没找到，通过帖子的template_data查找
            if (vote == null) {
                Posts post = postsMapper.selectById(postId);
                if (post != null && "vote".equals(post.getTemplateType())) {
                    // 从模板数据中提取投票信息，创建临时投票对象
                    try {
                        Map<String, Object> templateData = objectMapper.readValue(post.getTemplateData(), Map.class);
                        vote = new Votes();
                        vote.setId(postId); // 使用帖子ID作为投票ID
                        vote.setPostId(postId);
                        vote.setTitle(post.getTitle());
                        // 注释：Votes实体类中没有creatorId字段，已移除该方法调用
                        vote.setEndTime(templateData.get("endTime") != null ? 
                            LocalDateTime.parse(templateData.get("endTime").toString()) : null);
                        vote.setIsAnonymous(Boolean.TRUE.equals(templateData.get("isAnonymous")));
                        vote.setVoteType("multiple".equals(templateData.get("voteType")) ? "multiple" : "single");
                        vote.setOptions(objectMapper.writeValueAsString(templateData.get("options")));
                    } catch (Exception e) {
                        System.err.println("解析帖子模板数据失败: " + e.getMessage());
                        return Result.error("投票数据格式错误");
                    }
                }
            }
            
            if (vote == null) {
                return Result.error("投票不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("vote", vote);
            
            // 检查用户是否已投票
            boolean hasVoted = false;
            List<Object> userSelectedOptions = null;
            
            if (userId != null) {
                QueryWrapper<VoteRecords> recordQuery = new QueryWrapper<>();
                recordQuery.eq("vote_id", vote.getId())
                          .eq("user_id", userId);
                VoteRecords existingRecord = voteRecordsMapper.selectOne(recordQuery);
                
                if (existingRecord != null) {
                    hasVoted = true;
                    try {
                        userSelectedOptions = objectMapper.readValue(existingRecord.getSelectedOptions(), List.class);
                    } catch (JsonProcessingException e) {
                        System.err.println("解析用户投票选项失败: " + e.getMessage());
                    }
                }
            }
            
            result.put("hasVoted", hasVoted);
            result.put("userSelectedOptions", userSelectedOptions);
            
            // 获取参与人数统计
            QueryWrapper<VoteRecords> countQuery = new QueryWrapper<>();
            countQuery.eq("vote_id", vote.getId());
            long participantCount = voteRecordsMapper.selectCount(countQuery);
            result.put("participantCount", participantCount);

            return Result.success(result);

        } catch (Exception e) {
            System.err.println("获取投票详情失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("获取投票详情失败: " + e.getMessage());
        }
    }
}