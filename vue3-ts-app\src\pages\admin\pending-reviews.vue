<template>
  <!-- 权限验证加载中 -->
  <view v-if="permissionLoading" class="permission-loading">
    <view class="loading-content">
      <up-loading-icon mode="circle" size="24" color="#1890ff"></up-loading-icon>
      <text class="loading-text">权限验证中...</text>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="pending-reviews-page" v-else-if="hasPagePermission">
    <!-- 页面头部 -->
    <up-navbar
      title="待审核管理"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 统计概览 -->
    <view class="stats-section" :style="{ marginTop: mainContentPaddingTop }">
      <view class="stats-grid">
        <view class="stats-card">
          <view class="stats-number">{{ stats.totalPending }}</view>
          <view class="stats-label">待审核总数</view>
          <up-icon name="clock-fill" color="#faad14" size="20"></up-icon>
        </view>
        <view class="stats-card">
          <view class="stats-number">{{ stats.pendingPosts }}</view>
          <view class="stats-label">待审核帖子</view>
          <up-icon name="chat-fill" color="#1890ff" size="20"></up-icon>
        </view>
        <view class="stats-card">
          <view class="stats-number">{{ stats.pendingAuth }}</view>
          <view class="stats-label">待审核认证</view>
          <up-icon name="checkmark-circle-fill" color="#52c41a" size="20"></up-icon>
        </view>
        <view class="stats-card">
          <view class="stats-number">{{ stats.pendingFeedback }}</view>
          <view class="stats-label">待处理反馈</view>
          <up-icon name="edit-pen-fill" color="#722ed1" size="20"></up-icon>
        </view>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view
          class="filter-tab"
          :class="{ active: currentFilter === 'all' }"
          @click="handleFilterChange('all')"
        >
          全部({{ stats.totalPending }})
        </view>
        <view
          class="filter-tab"
          :class="{ active: currentFilter === 'pending' }"
          @click="handleFilterChange('pending')"
        >
          待审核({{ stats.pendingPosts }})
        </view>
        <view
          class="filter-tab"
          :class="{ active: currentFilter === 'approved' }"
          @click="handleFilterChange('approved')"
        >
          已通过
        </view>
        <view
          class="filter-tab"
          :class="{ active: currentFilter === 'rejected' }"
          @click="handleFilterChange('rejected')"
        >
          已拒绝
        </view>
      </view>
    </view>

    <!-- 搜索区域 -->
    <view class="search-section">
      <up-search
        v-model="searchKeyword"
        placeholder="搜索帖子标题或内容"
        @search="loadPendingList"
        @clear="handleClearSearch"
        :showAction="false"
      ></up-search>
    </view>

    <!-- 待审核列表 -->
    <view class="pending-list">
      <!-- 帖子审核项 -->
      <view 
        class="pending-item post-item" 
        v-for="item in pendingList" 
        :key="`${currentTab}-${item.id}`"
        @click="handleItemDetail(item)"
      >
        <view class="item-header">
          <view class="item-type">
            <up-icon 
              :name="getItemIcon(item)" 
              :color="getItemColor(item)" 
              size="16"
            ></up-icon>
            <text class="type-text">{{ getItemTypeText(item) }}</text>
          </view>
          <view class="item-status">
            <text class="status-text" :style="{ color: getStatusColor(item) }">
              {{ getStatusText(item) }}
            </text>
          </view>
        </view>
        
        <view class="item-content">
          <text class="content-text">{{ getItemContent(item) }}</text>
        </view>
        
        <view class="item-meta">
          <text class="meta-text">{{ getItemAuthor(item) }}</text>
          <text class="meta-text">{{ formatDate(getItemTime(item)) }}</text>
        </view>
        
        <view class="item-actions" @click.stop>
          <up-button
            text="通过"
            type="success"
            size="mini"
            plain
            @click="handleApprove(item)"
          ></up-button>
          <up-button
            text="拒绝"
            type="error"
            size="mini"
            plain
            @click="handleReject(item)"
          ></up-button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore">
      <up-loadmore 
        :status="loadStatus"
        @loadmore="loadMoreItems"
      ></up-loadmore>
    </view>

    <!-- 拒绝原因弹窗 -->
    <up-popup 
      v-model:show="showRejectModal" 
      mode="center" 
      :round="10"
      :closeable="true"
      @close="closeRejectModal"
    >
      <view class="reject-modal">
        <view class="modal-header">
          <text class="modal-title">拒绝原因</text>
        </view>
        <view class="modal-content">
          <up-textarea
            v-model="rejectReason"
            placeholder="请输入拒绝原因..."
            :maxlength="200"
            count
            :height="120"
          ></up-textarea>
        </view>
        <view class="modal-footer">
          <up-button 
            text="取消" 
            size="small"
            plain
            @click="closeRejectModal"
          ></up-button>
          <up-button
            text="确认拒绝"
            type="error"
            size="small"
            @click="confirmReject"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useSafeArea } from '@/utils/safeArea';
import type { LoadStatus } from '@/types/admin';
import type { PostManagement } from '@/types/admin';
import { checkPagePermission } from '@/utils/pagePermission';
import {
  getPendingReviewsStatsAPI,
  getPendingPostsAPI,
  reviewPostAPI,
  type PendingReviewsStats,
  type PendingReviewsSearchParams
} from '@/services/pendingReviews';

const { mainContentPaddingTop } = useSafeArea();

// 权限验证状态
const permissionLoading = ref(true);
const hasPagePermission = ref(false);

// 当前筛选条件
const currentFilter = ref<string>('pending');

// 搜索关键词
const searchKeyword = ref<string>('');

// 统计数据
const stats = reactive<PendingReviewsStats>({
  totalPending: 0,
  pendingPosts: 0,
  pendingAuth: 0,
  pendingFeedback: 0,
  todayPending: 0,
  weeklyPending: 0
});

// 帖子列表
const pendingList = ref<PostManagement[]>([]);

// 分页相关
const currentPage = ref<number>(1);
const pageSize = ref<number>(10);
const hasMore = ref<boolean>(true);
const loadStatus = ref<LoadStatus>('loadmore');

// 拒绝弹窗相关
const showRejectModal = ref<boolean>(false);
const rejectReason = ref<string>('');
const currentRejectItem = ref<any>(null);

// 初始化数据
const initData = async () => {
  try {
    // 获取统计数据
    const statsRes = await getPendingReviewsStatsAPI();
    if (statsRes.success && statsRes.data) {
      Object.assign(stats, statsRes.data);
    }

    // 加载待审核列表
    await loadPendingList();
  } catch (error) {
    console.error('初始化数据失败:', error);
    uni.showToast({
      title: '初始化失败',
      icon: 'none'
    });
  }
};

// 页面权限检查和初始化
const initPageWithPermission = async () => {
  try {
    permissionLoading.value = true;

    // 检查页面权限
    const hasPermission = await checkPagePermission('/pages/admin/pending-reviews');
    hasPagePermission.value = hasPermission;

    if (hasPermission) {
      // 权限验证通过，初始化页面数据
      await initData();
    }
  } catch (error) {
    console.error('页面权限检查失败:', error);
    hasPagePermission.value = false;
  } finally {
    permissionLoading.value = false;
  }
};

onMounted(() => {
  initPageWithPermission();
});

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 加载帖子列表
const loadPendingList = async (isLoadMore = false) => {
  try {
    if (!isLoadMore) {
      loadStatus.value = 'loading';
      currentPage.value = 1;
    }

    const params: PendingReviewsSearchParams = {
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value.trim() || undefined,
      status: getStatusFilter()
    };

    const res = await getPendingPostsAPI(params);

    if (res.success && res.data) {
      if (isLoadMore) {
        pendingList.value.push(...res.data.records);
      } else {
        pendingList.value = res.data.records;
      }

      hasMore.value = res.data.current < res.data.pages;
      loadStatus.value = hasMore.value ? 'loadmore' : 'nomore';
    } else {
      throw new Error(res.message || '获取帖子列表失败');
    }
  } catch (error) {
    console.error('加载帖子列表失败:', error);
    loadStatus.value = 'loadmore';
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  }
};

// 获取状态筛选条件
const getStatusFilter = (): number | undefined => {
  switch (currentFilter.value) {
    case 'pending':
      return 0; // 待审核
    case 'approved':
      return 1; // 已通过
    case 'rejected':
      return 2; // 已拒绝
    default:
      return undefined; // 全部
  }
};

// 筛选切换
const handleFilterChange = (filter: string) => {
  currentFilter.value = filter;
  loadPendingList();
};

// 清除搜索
const handleClearSearch = () => {
  searchKeyword.value = '';
  loadPendingList();
};

// 加载更多
const loadMoreItems = () => {
  if (hasMore.value && loadStatus.value !== 'loading') {
    currentPage.value++;
    loadPendingList(true);
  }
};

// 获取项目图标
const getItemIcon = (item: PostManagement) => {
  return 'chat-fill'; // 帖子
};

// 获取项目颜色
const getItemColor = (item: PostManagement) => {
  return '#1890ff'; // 帖子
};

// 获取项目类型文本
const getItemTypeText = (item: PostManagement) => {
  return '帖子审核';
};

// 获取状态颜色
const getStatusColor = (item: any) => {
  const status = item.status;
  if (status === 0) return '#faad14'; // 待审核
  if (status === 1) return '#52c41a'; // 已通过
  if (status === 2) return '#ff4d4f'; // 已拒绝
  return '#999'; // 其他
};

// 获取状态文本
const getStatusText = (item: any) => {
  const status = item.status;
  if (status === 0) return '待审核';
  if (status === 1) return '已通过';
  if (status === 2) return '已拒绝';
  if (status === 3) return '已处理';
  return '未知';
};

// 获取项目内容
const getItemContent = (item: PostManagement) => {
  return item.title || (item.content && item.content.length > 50 ? item.content.substring(0, 50) + '...' : item.content) || '无内容';
};

// 获取项目作者
const getItemAuthor = (item: PostManagement) => {
  return item.user?.nickname || '未知用户';
};

// 获取项目时间
const getItemTime = (item: PostManagement) => {
  return item.createdTime || '';
};



// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '未知时间';
  const date = new Date(dateStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
  if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`;

  return date.toLocaleDateString('zh-CN');
};

// 查看帖子详情
const handleItemDetail = (item: PostManagement) => {
  uni.navigateTo({
    url: `/pages/postDetail/index?id=${item.id}`
  });
};

// 通过审核
const handleApprove = async (item: PostManagement) => {
  uni.showModal({
    title: '确认通过',
    content: '确定要通过这个帖子的审核吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await reviewPostAPI({ id: item.id, action: 'approve' });

          if (result.success) {
            uni.showToast({
              title: '审核通过',
              icon: 'success'
            });

            // 从列表中移除该项目（如果当前是待审核列表）
            if (currentFilter.value === 'pending') {
              const index = pendingList.value.findIndex(i => i.id === item.id);
              if (index > -1) {
                pendingList.value.splice(index, 1);
              }
            }

            // 更新统计数据
            await refreshStats();
          } else {
            throw new Error(result.message || '审核失败');
          }
        } catch (error) {
          console.error('审核通过失败:', error);
          uni.showToast({
            title: '审核失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 拒绝审核
const handleReject = (item: PostManagement) => {
  currentRejectItem.value = item;
  rejectReason.value = '';
  showRejectModal.value = true;
};

// 关闭拒绝弹窗
const closeRejectModal = () => {
  showRejectModal.value = false;
  currentRejectItem.value = null;
  rejectReason.value = '';
};

// 确认拒绝
const confirmReject = async () => {
  if (!rejectReason.value.trim()) {
    uni.showToast({
      title: '请输入拒绝原因',
      icon: 'none'
    });
    return;
  }

  try {
    const item = currentRejectItem.value;

    const result = await reviewPostAPI({
      id: item.id,
      action: 'reject',
      reason: rejectReason.value.trim()
    });

    if (result.success) {
      uni.showToast({
        title: '已拒绝',
        icon: 'success'
      });

      // 从列表中移除该项目（如果当前是待审核列表）
      if (currentFilter.value === 'pending') {
        const index = pendingList.value.findIndex(i => i.id === item.id);
        if (index > -1) {
          pendingList.value.splice(index, 1);
        }
      }

      // 更新统计数据
      await refreshStats();

      closeRejectModal();
    } else {
      throw new Error(result.message || '拒绝失败');
    }
  } catch (error) {
    console.error('拒绝审核失败:', error);
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    });
  }
};

// 刷新统计数据
const refreshStats = async () => {
  try {
    const statsRes = await getPendingReviewsStatsAPI();
    if (statsRes.success && statsRes.data) {
      Object.assign(stats, statsRes.data);
    }
  } catch (error) {
    console.error('刷新统计数据失败:', error);
  }
};
</script>

<style lang="scss">
// 权限验证加载状态
.permission-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .loading-text {
      font-size: 14px;
      color: #666;
    }
  }
}

.pending-reviews-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  // 统计概览
  .stats-section {
    background: #fff;
    padding: 20px;
    margin-bottom: 10px;

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;

      .stats-card {
        background: #fafafa;
        padding: 15px;
        border-radius: 8px;
        position: relative;

        .stats-number {
          font-size: 24px;
          font-weight: bold;
          color: #333;
          display: block;
          margin-bottom: 4px;
        }

        .stats-label {
          font-size: 12px;
          color: #666;
        }

        .u-icon {
          position: absolute;
          top: 15px;
          right: 15px;
        }
      }
    }
  }

  // 筛选标签
  .filter-section {
    background: #fff;
    padding: 0 20px 15px;
    margin-bottom: 10px;

    .filter-tabs {
      display: flex;
      border-radius: 6px;
      overflow: hidden;
      border: 1px solid #d9d9d9;

      .filter-tab {
        flex: 1;
        padding: 8px 12px;
        text-align: center;
        font-size: 14px;
        color: #666;
        background: #fafafa;
        cursor: pointer;

        &.active {
          background: #1890ff;
          color: #fff;
        }

        &:not(:last-child) {
          border-right: 1px solid #d9d9d9;
        }
      }
    }
  }

  // 搜索区域
  .search-section {
    background: #fff;
    padding: 15px 20px;
    margin-bottom: 10px;
  }

  // 待审核列表
  .pending-list {
    background: #fff;

    .pending-item {
      padding: 15px 20px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;

      &:active {
        background: #f5f5f5;
      }

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .item-type {
          display: flex;
          align-items: center;
          gap: 6px;

          .type-text {
            font-size: 14px;
            font-weight: bold;
            color: #333;
          }
        }

        .item-status {
          .status-text {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            background: #f0f0f0;
          }
        }
      }

      .item-content {
        margin-bottom: 8px;

        .content-text {
          font-size: 14px;
          color: #333;
          line-height: 1.4;
        }
      }

      .item-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        .meta-text {
          font-size: 12px;
          color: #999;
        }
      }

      .item-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
      }
    }
  }

  // 加载更多
  .load-more {
    padding: 20px;
  }

  // 拒绝原因弹窗
  .reject-modal {
    width: 320px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .modal-header {
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;

      .modal-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }

    .modal-content {
      padding: 20px;
    }

    .modal-footer {
      display: flex;
      gap: 10px;
      padding: 0 20px 20px;

      :deep(.u-button) {
        flex: 1;
        height: 36px;
      }
    }
  }
}
</style>
