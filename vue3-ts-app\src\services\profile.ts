import { http } from '@/utils/http'
import type { User } from '@/types/user'
import type { Result } from '@/types/ApiResponse'

/**
 * 用户资料更新参数
 */
export interface ProfileUpdateParams {
  nickname?: string
  gender?: number
  birthday?: string
  bio?: string
  region?: string
  profession?: string
  school?: string
  redBookId?: string
  backgroundImage?: string
}

/**
 * 文件上传响应
 */
export interface FileUploadResponse {
  url: string
  filename: string
}

/**
 * 获取当前用户信息
 */
export const getCurrentUserAPI = () => {
  return http<Result<User>>({
    method: 'GET',
    url: '/user/profile'
  })
}

/**
 * 更新用户资料
 */
export const updateProfileAPI = (data: ProfileUpdateParams) => {
  return http<Result<User>>({
    method: 'PUT',
    url: '/user/profile',
    data
  })
}

/**
 * 上传头像
 */
export const uploadAvatarAPI = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return http<Result<FileUploadResponse>>({
    method: 'POST',
    url: '/user/avatar/upload',
    data: formData,
    header: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 上传背景图
 */
export const uploadBackgroundAPI = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return http<Result<FileUploadResponse>>({
    method: 'POST',
    url: '/user/background/upload',
    data: formData,
    header: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * uni-app 文件上传封装 - 使用单文件上传接口
 */
export const uploadFileUni = (filePath: string, type: 'avatar' | 'background') => {
  return new Promise<FileUploadResponse>((resolve, reject) => {
    // 使用用户文件上传接口，完全模仿发帖页面
    const url = '/files/userUpload'

    // 获取token
    const token = uni.getStorageSync('token') || ''
    console.log('上传文件，token:', token)
    console.log('上传URL:', `http://localhost:3205${url}`)
    console.log('文件路径:', filePath)
    console.log('文件类型:', type)

    uni.uploadFile({
      url: `http://localhost:3205${url}`,
      filePath,
      name: 'file', // 单文件上传使用 'file' 参数名
      formData: {
        fileType: type // 传递文件类型信息
      },
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
      },
      success: (res) => {
        console.log('上传响应:', res)

        // 检查HTTP状态码
        if (res.statusCode === 200) {
          // 成功响应，res.data直接是文件路径字符串
          const filePath = res.data
          console.log('上传成功，文件路径:', filePath)

          resolve({
            url: filePath,
            filename: filePath.split('/').pop() || ''
          })
        } else {
          // 错误响应
          console.error('上传失败，状态码:', res.statusCode)
          console.error('错误信息:', res.data)
          reject(new Error(res.data || '上传失败'))
        }
      },
      fail: (error) => {
        console.error('上传失败:', error)
        reject(error)
      }
    })
  })
}
