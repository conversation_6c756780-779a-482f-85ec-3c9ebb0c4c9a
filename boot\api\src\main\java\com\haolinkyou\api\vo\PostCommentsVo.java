package com.haolinkyou.api.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PostCommentsVo {
    private Long commentId;
    private String username;
    private Long userId;
    private String content;
    private String createdTime;
    private Integer likeCount;
    private Long parentCommentId;
    private List<PostCommentsVo> replies = new ArrayList<>() ;
}
