package com.haolinkyou.service;

import com.haolinkyou.entity.Posts;

import java.util.List;

/**
 * 帖子置顶服务接口
 */
public interface IPostTopService {
    
    /**
     * 置顶帖子
     * @param postId 帖子ID
     * @param userId 用户ID
     * @param hours 置顶时长（小时）
     * @return 置顶结果
     */
    TopResult topPost(Long postId, Long userId, Integer hours);
    
    /**
     * 取消置顶
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean cancelTop(Long postId, Long userId);
    
    /**
     * 检查帖子是否可以置顶
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 检查结果
     */
    TopCheckResult checkCanTop(Long postId, Long userId);
    
    /**
     * 获取置顶费用
     * @param hours 置顶时长（小时）
     * @return 所需积分
     */
    Integer getTopCost(Integer hours);
    
    /**
     * 获取置顶配置
     * @return 置顶配置
     */
    TopConfig getTopConfig();
    
    /**
     * 处理过期的置顶帖子
     * @return 处理的帖子数量
     */
    int processExpiredTopPosts();
    
    /**
     * 获取用户的置顶帖子列表
     * @param userId 用户ID
     * @return 置顶帖子列表
     */
    List<Posts> getUserTopPosts(Long userId);
    
    /**
     * 置顶结果
     */
    class TopResult {
        private boolean success;
        private String message;
        private Integer pointsUsed;
        private Long expireTime;
        
        public TopResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        public TopResult(boolean success, String message, Integer pointsUsed, Long expireTime) {
            this.success = success;
            this.message = message;
            this.pointsUsed = pointsUsed;
            this.expireTime = expireTime;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public Integer getPointsUsed() { return pointsUsed; }
        public void setPointsUsed(Integer pointsUsed) { this.pointsUsed = pointsUsed; }
        
        public Long getExpireTime() { return expireTime; }
        public void setExpireTime(Long expireTime) { this.expireTime = expireTime; }
    }
    
    /**
     * 置顶检查结果
     */
    class TopCheckResult {
        private boolean canTop;
        private String reason;
        private Integer requiredPoints;
        private Integer userPoints;
        
        public TopCheckResult(boolean canTop, String reason) {
            this.canTop = canTop;
            this.reason = reason;
        }
        
        public TopCheckResult(boolean canTop, String reason, Integer requiredPoints, Integer userPoints) {
            this.canTop = canTop;
            this.reason = reason;
            this.requiredPoints = requiredPoints;
            this.userPoints = userPoints;
        }
        
        // Getters and Setters
        public boolean isCanTop() { return canTop; }
        public void setCanTop(boolean canTop) { this.canTop = canTop; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        
        public Integer getRequiredPoints() { return requiredPoints; }
        public void setRequiredPoints(Integer requiredPoints) { this.requiredPoints = requiredPoints; }
        
        public Integer getUserPoints() { return userPoints; }
        public void setUserPoints(Integer userPoints) { this.userPoints = userPoints; }
    }
    
    /**
     * 置顶配置
     */
    class TopConfig {
        private Integer costPerHour;
        private Integer maxHours;
        private Integer minHours;
        private Integer defaultHours;
        
        // Getters and Setters
        public Integer getCostPerHour() { return costPerHour; }
        public void setCostPerHour(Integer costPerHour) { this.costPerHour = costPerHour; }
        
        public Integer getMaxHours() { return maxHours; }
        public void setMaxHours(Integer maxHours) { this.maxHours = maxHours; }
        
        public Integer getMinHours() { return minHours; }
        public void setMinHours(Integer minHours) { this.minHours = minHours; }
        
        public Integer getDefaultHours() { return defaultHours; }
        public void setDefaultHours(Integer defaultHours) { this.defaultHours = defaultHours; }
    }
}
