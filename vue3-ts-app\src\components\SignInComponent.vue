<template>
  <view class="sign-in-component">
    <!-- 签到按钮 -->
    <view class="sign-button-container">
      <up-button v-if="!signStatus.hasSignedToday" type="primary" size="normal" :loading="isSigningIn"
        @click="handleSignIn" :customStyle="signButtonStyle">
        <up-icon name="calendar" size="22" style="margin-right: 4px;" color="#fff"></up-icon>
        立即签到
      </up-button>

      <view v-else class="signed-status">
        <up-icon name="checkmark-circle-fill" color="#52c41a" size="20"></up-icon>
        <text class="signed-text">今日已签到</text>
      </view>
    </view>

    <!-- 签到信息展示 -->
    <view class="sign-info">
      <view class="info-item">
        <text class="info-label">连续签到</text>
        <text class="info-value">{{ signStatus.continuousDays || 0 }}天</text>
      </view>

      <view class="info-item" v-if="!signStatus.hasSignedToday && signStatus.todayPoints">
        <text class="info-label">今日可得</text>
        <text class="info-value points">{{ signStatus.todayPoints }}积分</text>
      </view>

      <view class="info-item" v-if="signStatus.nextDayBonus">
        <text class="info-label">明日奖励</text>
        <text class="info-value bonus">{{ signStatus.nextDayBonus }}积分</text>
      </view>

      <view class="info-item records-link" @click="goToSignRecords">
        <text class="info-label">签到记录</text>
        <view class="link-content">
          <up-icon name="arrow-right" size="12" color="#999"></up-icon>
        </view>
      </view>
    </view>

    <!-- 签到成功动画弹窗 -->
    <up-popup v-model:show="showSuccessModal" mode="center" :round="10" :closeable="false">
      <view class="success-modal">
        <view class="success-animation">
          <up-icon name="checkmark-circle-fill" color="#52c41a" size="60"></up-icon>
        </view>
        <view class="success-title">签到成功！</view>
        <view class="success-content">
          <text class="points-earned">获得 {{ lastSignResult?.pointsEarned || 0 }} 积分</text>
          <text class="continuous-days">连续签到 {{ lastSignResult?.continuousDays || 0 }} 天</text>
        </view>
        <up-button text="确定" type="primary" size="normal" @click="closeSuccessModal"
          :customStyle="{ marginTop: '20px', width: '120px' }"></up-button>
      </view>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { get, post } from '@/utils/http'
import { useMemberStore } from '@/stores'

// 接口类型定义
interface SignStatus {
  hasSignedToday: boolean
  continuousDays: number
  lastSignDate?: string
  todayPoints?: number
  nextDayBonus?: number
}

interface SignResult {
  success: boolean
  message: string
  pointsEarned?: number
  continuousDays?: number
}

// 响应式数据
const memberStore = useMemberStore()
const signStatus = ref<SignStatus>({
  hasSignedToday: false,
  continuousDays: 0
})
const isSigningIn = ref(false)
const showSuccessModal = ref(false)
const lastSignResult = ref<SignResult | null>(null)

// 样式配置
const signButtonStyle = {
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  border: 'none',
  borderRadius: '20px',
  padding: '12px 24px'
}

// 获取签到状态
const fetchSignStatus = async () => {
  try {
    const res = await get<ApiResponse<SignStatus>>('/sign/status')
    if (res.success) {
      signStatus.value = res.data || {
        hasSignedToday: false,
        continuousDays: 0
      }
      console.log('获取签到状态成功:', signStatus.value)
    } else {
      console.error('获取签到状态失败:', res.message)
    }
  } catch (error) {
    console.error('获取签到状态异常:', error)
  }
}

// 处理签到
const handleSignIn = async () => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  if (isSigningIn.value) {
    return
  }

  isSigningIn.value = true

  try {
    const res = await post<ApiResponse<SignResult>>('/sign/daily', {})

    if (res.success) {
      lastSignResult.value = res.data
      signStatus.value.hasSignedToday = true
      signStatus.value.continuousDays = res.data?.continuousDays || 0

      // 显示成功动画
      showSuccessModal.value = true

      // 发送全局事件通知其他组件刷新
      uni.$emit('userSignSuccess', {
        pointsEarned: res.data?.pointsEarned,
        continuousDays: res.data?.continuousDays
      })

      console.log('签到成功:', res.data)
    } else {
      uni.showToast({
        title: res.message || '签到失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('签到异常:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    isSigningIn.value = false
  }
}

// 关闭成功弹窗
const closeSuccessModal = () => {
  showSuccessModal.value = false
  // 重新获取签到状态
  fetchSignStatus()
}

// 跳转到签到记录页面
const goToSignRecords = () => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  uni.navigateTo({
    url: '/pages/my/sign-records'
  })
}

// 监听用户登录状态变化
watch(() => memberStore.profile?.id, (newUserId) => {
  if (newUserId) {
    fetchSignStatus()
  } else {
    // 用户退出登录，重置状态
    signStatus.value = {
      hasSignedToday: false,
      continuousDays: 0
    }
  }
})

// 组件挂载时获取签到状态
onMounted(() => {
  if (memberStore.profile?.id) {
    fetchSignStatus()
  }
})

// 暴露方法供父组件调用
defineExpose({
  fetchSignStatus,
  handleSignIn
})
</script>

<style scoped lang="scss">
.sign-in-component {
  .sign-button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16px;

    .signed-status {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 24px;
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 20px;

      .signed-text {
        color: #52c41a;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  .sign-info {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 16px;

    .info-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;

      .info-label {
        font-size: 12px;
        color: #666;
      }

      .info-value {
        font-size: 16px;
        font-weight: bold;
        color: #333;

        &.points {
          color: #ff6b35;
        }

        &.bonus {
          color: #52c41a;
        }
      }

      &.records-link {
        cursor: pointer;

        .link-content {
          display: flex;
          align-items: center;
        }

        .info-label {
          color: #5677fc;
        }
      }
    }
  }

  .success-modal {
    width: 280px;
    padding: 30px 20px;
    text-align: center;
    background: white;
    border-radius: 10px;

    .success-animation {
      margin-bottom: 20px;
      animation: bounce 0.6s ease-in-out;
    }

    .success-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
    }

    .success-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 20px;

      .points-earned {
        font-size: 16px;
        color: #ff6b35;
        font-weight: bold;
      }

      .continuous-days {
        font-size: 14px;
        color: #666;
      }
    }
  }
}

@keyframes bounce {

  0%,
  20%,
  60%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  80% {
    transform: translateY(-5px);
  }
}
</style>