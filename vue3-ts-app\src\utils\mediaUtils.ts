/**
 * 媒体文件处理工具
 */

export interface MediaFile {
  url: string;
  type: 'image' | 'video' | 'unknown';
  extension: string;
  poster?: string;
}

/**
 * 检查服务器是否可访问
 */
export const checkServerStatus = async (baseUrl: string = 'http://localhost:3205'): Promise<boolean> => {
  try {
    const response = await fetch(`${baseUrl}/actuator/health`, {
      method: 'GET',
      mode: 'cors',
      timeout: 5000
    });
    return response.ok;
  } catch (error) {
    console.warn('服务器连接检查失败:', error);
    return false;
  }
};

/**
 * 检查媒体文件是否可访问
 */
export const checkMediaAccess = async (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    const video = document.createElement('video');
    
    // 先尝试作为图片加载
    img.onload = () => resolve(true);
    img.onerror = () => {
      // 图片加载失败，尝试作为视频加载
      video.onloadedmetadata = () => resolve(true);
      video.onerror = () => resolve(false);
      video.src = url;
      video.load();
    };
    img.src = url;
    
    // 设置超时
    setTimeout(() => resolve(false), 5000);
  });
};

/**
 * 处理媒体文件列表，增加错误处理
 */
export const processMediaFiles = (fileList: string, fileType: number, baseUrl: string = 'http://localhost:3205'): MediaFile[] => {
  if (!fileList) return [];

  const files = fileList.split(',').filter(file => file.trim());
  return files.map(file => {
    const relativePath = file.trim();
    const url = `${baseUrl}/${relativePath}`;
    const extension = relativePath.toLowerCase().split('.').pop() || '';

    // 判断文件类型
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'];

    let type: 'image' | 'video' | 'unknown' = 'unknown';
    if (imageExtensions.includes(extension)) {
      type = 'image';
    } else if (videoExtensions.includes(extension)) {
      type = 'video';
    }

    return {
      url,
      type,
      extension,
      poster: undefined
    };
  });
};

/**
 * 安全播放视频
 */
export const safePlayVideo = async (videoUrl: string): Promise<void> => {
  try {
    // 检查视频是否可访问
    const isAccessible = await checkMediaAccess(videoUrl);
    
    if (!isAccessible) {
      uni.showToast({
        title: '视频文件无法访问，请检查网络连接',
        icon: 'none',
        duration: 3000
      });
      return;
    }

    // 使用uni.previewMedia播放视频（如果支持）
    if (uni.previewMedia) {
      uni.previewMedia({
        sources: [{
          url: videoUrl,
          type: 'video'
        }],
        fail: (error) => {
          console.error('视频播放失败:', error);
          // 降级方案
          // #ifdef H5
          window.open(videoUrl, '_blank');
          // #endif
        }
      });
    } else {
      // 降级方案
      // #ifdef H5
      window.open(videoUrl, '_blank');
      // #endif
      
      // #ifndef H5
      uni.showToast({
        title: '当前平台不支持视频预览',
        icon: 'none'
      });
      // #endif
    }
  } catch (error) {
    console.error('视频播放异常:', error);
    uni.showToast({
      title: '视频播放失败',
      icon: 'none'
    });
  }
};

/**
 * 创建错误占位图
 */
export const createErrorPlaceholder = (type: 'image' | 'video' = 'image'): string => {
  const canvas = document.createElement('canvas');
  canvas.width = 200;
  canvas.height = 150;
  const ctx = canvas.getContext('2d');
  
  if (ctx) {
    // 绘制灰色背景
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 绘制错误图标
    ctx.fillStyle = '#999';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(type === 'video' ? '视频加载失败' : '图片加载失败', canvas.width / 2, canvas.height / 2);
  }
  
  return canvas.toDataURL();
};