<template>
  <view class="feedback-management-page">
    <!-- 页面头部 -->
    <up-navbar
      title="用户反馈"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 反馈列表 -->
    <view class="feedback-list" :style="{ marginTop: mainContentPaddingTop }">
      <view
        class="feedback-item" 
        v-for="feedback in feedbackList" 
        :key="feedback.id"
        @click="handleFeedbackDetail(feedback)"
      >
        <!-- 用户信息 -->
        <view class="feedback-header">
          <up-avatar :src="feedback.user.avatar" shape="circle" :size="40"></up-avatar>
          <view class="user-info">
            <text class="user-name">{{ feedback.user.nickname }}</text>
            <text class="feedback-time">{{ formatTime(feedback.createdTime) }}</text>
          </view>
          <view class="status-container">
            <view 
              class="status-badge"
              :class="getStatusClass(feedback.status)"
            >
              {{ getStatusText(feedback.status) }}
            </view>
          </view>
        </view>

        <!-- 反馈内容 -->
        <view class="feedback-content">
          <text class="content-text">{{ feedback.content }}</text>
        </view>

        <!-- 处理状态和操作 -->
        <view class="feedback-actions">
          <view class="status-info">
            <text class="status-label">状态: {{ getStatusText(feedback.status) }}</text>
          </view>
          <view class="action-buttons">
            <up-button 
              v-if="feedback.status === 'pending'"
              text="不采纳" 
              type="error" 
              size="mini"
              plain
              @click.stop="handleReject(feedback)"
            ></up-button>
            <up-button 
              v-if="feedback.status === 'pending'"
              text="采纳" 
              type="success" 
              size="mini"
              @click.stop="handleAccept(feedback)"
            ></up-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore">
      <up-loadmore 
        :status="loadStatus"
        @loadmore="loadMoreFeedback"
      ></up-loadmore>
    </view>

    <!-- 反馈详情弹窗 -->
    <up-popup 
      v-model:show="showFeedbackDetailModal" 
      mode="center" 
      :round="10"
      :closeable="true"
      @close="closeFeedbackDetailModal"
    >
      <view class="feedback-detail-modal" v-if="selectedFeedback">
        <view class="modal-header">
          <text class="modal-title">反馈详情</text>
          <up-icon name="close" @click="closeFeedbackDetailModal" size="20" color="#999"></up-icon>
        </view>
        <view class="modal-content">
          <view class="detail-item">
            <text class="detail-label">反馈用户:</text>
            <text class="detail-value">{{ selectedFeedback.user.nickname }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">反馈时间:</text>
            <text class="detail-value">{{ formatDateTime(selectedFeedback.createdTime) }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">处理状态:</text>
            <text class="detail-value">{{ getStatusText(selectedFeedback.status) }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">反馈内容:</text>
            <text class="detail-value content-full">{{ selectedFeedback.content }}</text>
          </view>
          <view class="detail-item" v-if="selectedFeedback.processTime">
            <text class="detail-label">处理时间:</text>
            <text class="detail-value">{{ formatDateTime(selectedFeedback.processTime) }}</text>
          </view>
          <view class="detail-item" v-if="selectedFeedback.processNote">
            <text class="detail-label">处理备注:</text>
            <text class="detail-value">{{ selectedFeedback.processNote }}</text>
          </view>
        </view>
        <view class="modal-footer" v-if="selectedFeedback.status === 'pending'">
          <up-button 
            text="不采纳" 
            type="error" 
            size="small"
            plain
            @click="handleReject(selectedFeedback)"
          ></up-button>
          <up-button 
            text="采纳" 
            type="success" 
            size="small"
            @click="handleAccept(selectedFeedback)"
          ></up-button>
        </view>
      </view>
    </up-popup>

    <!-- 处理备注输入弹窗 -->
    <up-popup 
      v-model:show="showProcessModal" 
      mode="center" 
      :round="10"
      :closeable="true"
      @close="closeProcessModal"
    >
      <view class="process-modal">
        <view class="modal-header">
          <text class="modal-title">{{ processType === 'accept' ? '采纳反馈' : '不采纳反馈' }}</text>
          <up-icon name="close" @click="closeProcessModal" size="20" color="#999"></up-icon>
        </view>
        <view class="modal-content">
          <up-textarea 
            v-model="processNote"
            :placeholder="processType === 'accept' ? '请输入采纳说明（选填）...' : '请输入不采纳原因...'"
            :maxlength="200"
            count
            :autoHeight="true"
            :height="100"
          ></up-textarea>
        </view>
        <view class="modal-footer">
          <up-button 
            :text="processType === 'accept' ? '确认采纳' : '确认不采纳'" 
            :type="processType === 'accept' ? 'success' : 'error'"
            @click="confirmProcess"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useSafeArea } from '@/utils/safeArea';
import type { FeedbackItem, LoadStatus, ProcessType } from '@/types/admin';

const { mainContentPaddingTop } = useSafeArea();

// 反馈列表
const feedbackList = ref<FeedbackItem[]>([]);

// 分页相关
const hasMore = ref<boolean>(true);
const loadStatus = ref<LoadStatus>('loadmore');

// 弹窗相关
const showFeedbackDetailModal = ref<boolean>(false);
const showProcessModal = ref<boolean>(false);
const selectedFeedback = ref<FeedbackItem | null>(null);
const processType = ref<ProcessType | ''>(''); // 'accept' 或 'reject'
const processNote = ref<string>('');

// 初始化数据
const initData = async () => {
  loadFeedbackList();
};

// 加载反馈列表
const loadFeedbackList = async () => {
  try {
    // TODO: 调用真实API获取反馈列表
    // const res = await getFeedbackList({
    //   page: currentPage.value,
    //   pageSize: pageSize.value
    // });
    // feedbackList.value = res.data.records;

    // 暂时清空列表，等待真实API接入
    feedbackList.value = [];
  } catch (error) {
    console.error('加载反馈列表失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  }
};

onMounted(() => {
  initData();
});

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 加载更多反馈
const loadMoreFeedback = () => {
  loadStatus.value = 'loading';
  // 模拟加载更多
  setTimeout(() => {
    loadStatus.value = 'loadmore';
    hasMore.value = false; // 模拟没有更多数据
  }, 1000);
};

// 查看反馈详情
const handleFeedbackDetail = (feedback: FeedbackItem) => {
  selectedFeedback.value = feedback;
  showFeedbackDetailModal.value = true;
};

// 关闭反馈详情弹窗
const closeFeedbackDetailModal = () => {
  showFeedbackDetailModal.value = false;
  selectedFeedback.value = null;
};

// 采纳反馈
const handleAccept = (feedback: FeedbackItem) => {
  selectedFeedback.value = feedback;
  processType.value = 'accept';
  showProcessModal.value = true;
  closeFeedbackDetailModal();
};

// 不采纳反馈
const handleReject = (feedback: FeedbackItem) => {
  selectedFeedback.value = feedback;
  processType.value = 'reject';
  showProcessModal.value = true;
  closeFeedbackDetailModal();
};

// 关闭处理弹窗
const closeProcessModal = () => {
  showProcessModal.value = false;
  processNote.value = '';
  processType.value = '';
};

// 确认处理
const confirmProcess = () => {
  if (processType.value === 'reject' && !processNote.value.trim()) {
    uni.showToast({
      title: '请输入不采纳原因',
      icon: 'none'
    });
    return;
  }

  if (!selectedFeedback.value) {
    return;
  }

  // 这里应该调用实际的API
  selectedFeedback.value.status = processType.value === 'accept' ? 'accepted' : 'rejected';
  selectedFeedback.value.processTime = new Date().toLocaleString();
  selectedFeedback.value.processNote = processNote.value || (processType.value === 'accept' ? '已采纳' : '已拒绝');

  uni.showToast({
    title: processType.value === 'accept' ? '已采纳反馈' : '已拒绝反馈',
    icon: 'success'
  });

  closeProcessModal();
};

// 获取状态样式类
const getStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    pending: 'status-pending',
    accepted: 'status-accepted',
    rejected: 'status-rejected',
    processed: 'status-processed'
  };
  return classMap[status] || '';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    accepted: '已采纳',
    rejected: '已拒绝',
    processed: '已处理'
  };
  return statusMap[status] || '未知';
};

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  return timeStr;
};

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '未知';
  return dateStr;
};
</script>

<style lang="scss">
.feedback-management-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  // 反馈列表
  .feedback-list {
    padding: 10px 20px;

    .feedback-item {
      background: #fff;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }

      .feedback-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .user-info {
          flex: 1;
          margin-left: 12px;

          .user-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            display: block;
            margin-bottom: 4px;
          }

          .feedback-time {
            font-size: 12px;
            color: #999;
          }
        }

        .status-container {
          .status-badge {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;

            &.status-pending {
              background: #fff7e6;
              color: #fa8c16;
            }

            &.status-accepted {
              background: #f6ffed;
              color: #52c41a;
            }

            &.status-rejected {
              background: #fff1f0;
              color: #ff4d4f;
            }

            &.status-processed {
              background: #f0f0f0;
              color: #666;
            }
          }
        }
      }

      .feedback-content {
        margin-bottom: 12px;

        .content-text {
          font-size: 14px;
          color: #333;
          line-height: 1.4;
        }
      }

      .feedback-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .status-info {
          .status-label {
            font-size: 12px;
            color: #666;
          }
        }

        .action-buttons {
          display: flex;
          gap: 8px;

          :deep(.u-button) {
            height: 28px;
            padding: 0 12px;
          }
        }
      }
    }
  }

  // 加载更多
  .load-more {
    padding: 20px;
  }

  // 反馈详情弹窗
  .feedback-detail-modal {
    width: 350px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;

      .modal-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }

    .modal-content {
      padding: 20px;
      max-height: 400px;
      overflow-y: auto;

      .detail-item {
        display: flex;
        margin-bottom: 12px;

        .detail-label {
          font-size: 14px;
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .detail-value {
          font-size: 14px;
          color: #333;
          flex: 1;

          &.content-full {
            line-height: 1.4;
          }
        }
      }
    }

    .modal-footer {
      display: flex;
      gap: 10px;
      padding: 0 20px 20px;

      :deep(.u-button) {
        flex: 1;
        height: 36px;
      }
    }
  }

  // 处理备注弹窗
  .process-modal {
    width: 300px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;

      .modal-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }

    .modal-content {
      padding: 20px;

      :deep(.u-textarea) {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        padding: 10px;
      }
    }

    .modal-footer {
      padding: 0 20px 20px;

      :deep(.u-button) {
        width: 100%;
        height: 44px;
        border-radius: 22px;
      }
    }
  }
}
</style>
