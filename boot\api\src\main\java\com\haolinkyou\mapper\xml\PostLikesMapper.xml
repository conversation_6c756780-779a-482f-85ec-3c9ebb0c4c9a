<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haolinkyou.mapper.PostLikesMapper">

    <!-- 检查用户是否已点赞该帖子 -->
    <select id="checkUserLiked" resultType="int">
        SELECT COUNT(*)
        FROM post_likes
        WHERE post_id = #{postId}
          AND user_id = #{userId}
          AND del_flag = 0
    </select>

    <!-- 获取帖子的点赞数 -->
    <select id="getPostLikeCount" resultType="int">
        SELECT COUNT(*)
        FROM post_likes
        WHERE post_id = #{postId}
          AND del_flag = 0
    </select>

    <!-- 添加点赞记录 -->
    <insert id="addLike">
        INSERT INTO post_likes (post_id, user_id, created_time, del_flag)
        VALUES (#{postId}, #{userId}, NOW(), 0)
        ON DUPLICATE KEY UPDATE
            del_flag = 0,
            created_time = NOW()
    </insert>

    <!-- 取消点赞（逻辑删除） -->
    <update id="removeLike">
        UPDATE post_likes
        SET del_flag = 1
        WHERE post_id = #{postId}
          AND user_id = #{userId}
          AND del_flag = 0
    </update>

</mapper>
