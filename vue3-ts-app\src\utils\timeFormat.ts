/**
 * 时间格式化工具函数
 * 提供各种时间格式化功能，包括相对时间、标准时间格式等
 */

// 时间单位常量
const TIME_UNITS = {
  SECOND: 1000,
  MINUTE: 1000 * 60,
  HOUR: 1000 * 60 * 60,
  DAY: 1000 * 60 * 60 * 24,
  MONTH: 1000 * 60 * 60 * 24 * 30,
  YEAR: 1000 * 60 * 60 * 24 * 365
} as const;

// 时间格式化选项类型
export interface TimeFormatOptions {
  showSeconds?: boolean;
  use24Hour?: boolean;
  showYear?: boolean;
  locale?: 'zh-CN' | 'en-US';
}

/**
 * 安全地解析时间字符串
 * @param timeStr 时间字符串
 * @returns Date对象或null
 */
const parseTime = (timeStr: string | undefined | null): Date | null => {
  if (!timeStr) return null;

  try {
    const date = new Date(timeStr);
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return null;
    }
    return date;
  } catch {
    return null;
  }
};

/**
 * 格式化相对时间
 * @param timeStr 时间字符串
 * @returns 相对时间字符串（如：刚刚、5分钟前、2小时前等）
 */
export const formatRelativeTime = (timeStr: string | undefined): string => {
  const targetTime = parseTime(timeStr);
  if (!targetTime) return timeStr || '';

  const now = new Date();
  const diffMs = now.getTime() - targetTime.getTime();

  // 如果是未来时间，返回具体日期
  if (diffMs < 0) {
    return formatDate(timeStr);
  }

  // 转换为各种时间单位
  const diffMinutes = Math.floor(diffMs / TIME_UNITS.MINUTE);
  const diffHours = Math.floor(diffMs / TIME_UNITS.HOUR);
  const diffDays = Math.floor(diffMs / TIME_UNITS.DAY);
  const diffMonths = Math.floor(diffDays / 30);
  const diffYears = Math.floor(diffDays / 365);

  // 根据时间差返回相应格式
  if (diffMs < TIME_UNITS.MINUTE) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 30) {
    return `${diffDays}天前`;
  } else if (diffMonths < 3) {
    return `${diffMonths}个月前`;
  } else if (diffYears < 1) {
    // 超过3个月但不到1年，显示 MM-DD 格式
    const month = String(targetTime.getMonth() + 1).padStart(2, '0');
    const day = String(targetTime.getDate()).padStart(2, '0');
    return `${month}-${day}`;
  } else {
    // 超过1年，显示 YYYY-MM-DD 格式
    return formatDate(timeStr);
  }
};

/**
 * 格式化帖子时间（带前缀）
 * @param post 帖子对象，包含 createdTime 和 updatedTime
 * @returns 格式化后的时间字符串（如：发布于 2小时前、编辑于 1天前）
 */
export const formatPostTime = (post: { createdTime: string; updatedTime?: string | null }): string => {
  const hasUpdated = post.updatedTime && post.updatedTime !== post.createdTime;
  const timeToShow = hasUpdated ? post.updatedTime || post.createdTime : post.createdTime;
  const prefix = hasUpdated ? '编辑于' : '发布于';
  const relativeTime = formatRelativeTime(timeToShow);
  
  return `${prefix} ${relativeTime}`;
};

/**
 * 格式化标准时间
 * @param timeStr 时间字符串
 * @param options 格式化选项
 * @returns 标准格式时间字符串（YYYY-MM-DD HH:mm:ss）
 */
export const formatStandardTime = (
  timeStr: string | undefined,
  options: TimeFormatOptions = {}
): string => {
  const time = parseTime(timeStr);
  if (!time) return timeStr || '';

  const { showSeconds = true, use24Hour = true } = options;

  const year = time.getFullYear();
  const month = String(time.getMonth() + 1).padStart(2, '0');
  const day = String(time.getDate()).padStart(2, '0');

  let hours = time.getHours();
  const minutes = String(time.getMinutes()).padStart(2, '0');
  const seconds = String(time.getSeconds()).padStart(2, '0');

  // 处理12小时制
  let ampm = '';
  if (!use24Hour) {
    ampm = hours >= 12 ? ' PM' : ' AM';
    hours = hours % 12;
    if (hours === 0) hours = 12;
  }

  const hoursStr = String(hours).padStart(2, '0');
  const timeFormat = showSeconds ?
    `${hoursStr}:${minutes}:${seconds}${ampm}` :
    `${hoursStr}:${minutes}${ampm}`;

  return `${year}-${month}-${day} ${timeFormat}`;
};

/**
 * 格式化日期
 * @param timeStr 时间字符串
 * @returns 日期字符串（YYYY-MM-DD）
 */
export const formatDate = (timeStr: string | undefined): string => {
  if (!timeStr) return '';

  try {
    const time = new Date(timeStr);
    const year = time.getFullYear();
    const month = String(time.getMonth() + 1).padStart(2, '0');
    const day = String(time.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('时间格式化错误:', error);
    return timeStr;
  }
};

/**
 * 格式化日期时间（用于显示完整的日期时间）
 * @param timeStr 时间字符串
 * @returns 格式化后的日期时间字符串（YYYY年MM月DD日 HH:mm）
 */
export const formatDateTime = (timeStr: string | undefined): string => {
  if (!timeStr) return '';

  try {
    const time = new Date(timeStr);
    const year = time.getFullYear();
    const month = String(time.getMonth() + 1).padStart(2, '0');
    const day = String(time.getDate()).padStart(2, '0');
    const hours = String(time.getHours()).padStart(2, '0');
    const minutes = String(time.getMinutes()).padStart(2, '0');
    
    return `${year}年${month}月${day}日 ${hours}:${minutes}`;
  } catch (error) {
    console.error('时间格式化错误:', error);
    return timeStr;
  }
};