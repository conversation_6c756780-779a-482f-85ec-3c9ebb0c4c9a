package com.haolinkyou.controller;

import com.haolinkyou.common.result.Result;
import com.haolinkyou.service.IPostLikesService;
import com.haolinkyou.service.IPointsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 帖子点赞控制器
 */
@RestController
@RequestMapping("/api/post-likes")
public class PostLikesController {
    
    @Autowired
    private IPostLikesService postLikesService;

    @Autowired
    private IPointsService pointsService;
    
    /**
     * 点赞或取消点赞
     * @param postId 帖子ID
     * @param request HTTP请求（从中获取用户ID）
     * @return 操作结果
     */
    @PostMapping("/toggle")
    public Result<Map<String, Object>> toggleLike(@RequestParam Long postId, javax.servlet.http.HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 执行切换操作
            boolean isLiked = postLikesService.toggleLike(postId, userId);

            // 如果是点赞操作，给用户增加积分
            if (isLiked) {
                try {
                    IPointsService.PointsRules rules = pointsService.getPointsRules();
                    boolean pointsAdded = pointsService.addPoints(userId, rules.getLikeGive(), "like", "点赞帖子", postId);
                    if (!pointsAdded) {
                        System.out.println("点赞积分奖励发放失败（可能已达每日上限）");
                    }
                } catch (Exception e) {
                    System.out.println("点赞积分奖励发放异常: " + e.getMessage());
                    // 积分发放失败不影响点赞流程
                }
            }

            // 获取最新点赞数
            int likeCount = postLikesService.getPostLikeCount(postId);

            Map<String, Object> result = new HashMap<>();
            result.put("isLiked", isLiked);
            result.put("likeCount", likeCount);
            result.put("message", isLiked ? "点赞成功" : "取消点赞成功");

            return Result.success(result);
        } catch (Exception e) {
            return Result.error("操作失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查用户是否已点赞该帖子
     * @param postId 帖子ID
     * @param request HTTP请求（从中获取用户ID）
     * @return 点赞状态
     */
    @GetMapping("/check")
    public Result<Map<String, Object>> checkUserLiked(@RequestParam Long postId, javax.servlet.http.HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            boolean isLiked = postLikesService.isUserLiked(postId, userId);
            int likeCount = postLikesService.getPostLikeCount(postId);

            Map<String, Object> result = new HashMap<>();
            result.put("isLiked", isLiked);
            result.put("likeCount", likeCount);

            return Result.success(result);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取帖子的点赞数
     * @param postId 帖子ID
     * @return 点赞数
     */
    @GetMapping("/count")
    public Result<Integer> getPostLikeCount(@RequestParam Long postId) {
        try {
            int likeCount = postLikesService.getPostLikeCount(postId);
            return Result.success(likeCount);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
