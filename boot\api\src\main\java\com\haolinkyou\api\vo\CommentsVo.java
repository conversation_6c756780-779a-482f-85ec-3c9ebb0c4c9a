package com.haolinkyou.api.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CommentsVo {
    private Long commentId;
    private String username;
    private Long userId;
    private String content;
    private String createdTime;
    private Integer likeCount;
    private Long parentCommentId;
    private String parentUsername; // 被回复用户的用户名
    private List<CommentsVo> replies = new ArrayList<>() ;
}
