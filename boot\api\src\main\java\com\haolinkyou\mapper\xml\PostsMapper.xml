<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.haolinkyou.mapper.PostsMapper">
    <select id="selectByPage" resultType="com.haolinkyou.api.vo.PostsListVo">
        SELECT
            p.id,
            p.user_id,
            p.title,
            p.content,
            CASE WHEN p.file_list IS NOT NULL AND p.file_list != '' THEN 0 ELSE NULL END AS file_type,
            u.gender AS user_gender,
            u.nickname,
            CASE WHEN auth.status = 1 THEN TRUE ELSE FALSE END AS user_is_verified,
            COALESCE(auth.identity_type, u.user_role, 'guest') AS user_role,
            p.file_list,
            p.category_id,
            c.category_name,
            p.view_count,
            p.like_count,
            p.comment_count,
            p.collect_count,
            p.`status`,
            p.is_top AS top_status,
            p.template_id,
            p.template_type,
            p.template_data,
            p.created_time,
            p.updated_time
        FROM
            posts p
                LEFT JOIN users u ON p.user_id = u.id
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN auth_applications auth ON u.id = auth.user_id AND auth.del_flag = 0
        WHERE
            p.del_flag = 0
        ORDER BY
            p.created_time DESC
    </select>

    <select id="listAllPosts" resultType="com.haolinkyou.api.vo.PostsListVo">
    SELECT
        p.id,
        p.user_id,
        p.title,
        p.content,
        u.nickname,
        u.gender AS user_gender,
        CASE WHEN auth.status = 1 THEN TRUE ELSE FALSE END AS user_is_verified,
        COALESCE(auth.identity_type, u.user_role, 'guest') AS user_role,
        CASE WHEN p.file_list IS NOT NULL AND p.file_list != '' THEN 0 ELSE NULL END AS file_type,
        p.file_list,
        p.category_id,
        c.category_name,
        p.view_count,
        p.like_count,
        p.comment_count,
        p.collect_count,
        p.status,
        p.is_top AS top_status,
        p.template_id,
        p.template_type,
        p.template_data,
        p.created_time,
        p.updated_time
    FROM
        posts p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN auth_applications auth ON u.id = auth.user_id AND auth.del_flag = 0
    <where>
        ${ew.sqlSegment}
    </where>
</select>

    <select id="listAllPostsWithUserStatus" resultType="com.haolinkyou.api.vo.PostsListVo">
    SELECT
        p.id,
        p.user_id,
        p.title,
        p.content,
        u.nickname,
        u.gender AS user_gender,
        CASE WHEN auth.status = 1 THEN TRUE ELSE FALSE END AS user_is_verified,
        COALESCE(auth.identity_type, u.user_role, 'guest') AS user_role,
        CASE WHEN p.file_list IS NOT NULL AND p.file_list != '' THEN 0 ELSE NULL END AS file_type,
        p.file_list,
        p.category_id,
        c.category_name,
        p.view_count,
        p.like_count,
        p.comment_count,
        p.collect_count,
        p.status,
        p.is_top AS top_status,
        p.template_id,
        p.template_type,
        p.template_data,
        p.created_time,
        p.updated_time,
        CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked,
        CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected
    FROM
        posts p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN auth_applications auth ON u.id = auth.user_id AND auth.del_flag = 0
        LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = #{userId} AND pl.del_flag = 0
        LEFT JOIN user_collects uc ON p.id = uc.post_id AND uc.user_id = #{userId} AND uc.del_flag = 0
    <where>
        ${ew.sqlSegment}
    </where>
</select>

   <select id="selectPostDetailById" resultType="com.haolinkyou.api.vo.PostDetailVo">
       SELECT
           p.id,
           p.user_id,
           u.nickname,
           u.gender,
           CASE WHEN auth.status = 1 THEN TRUE ELSE FALSE END AS user_is_verified,
           COALESCE(auth.identity_type, u.user_role, 'guest') AS user_role,
           p.title,
           p.content,
           CASE WHEN p.file_list IS NOT NULL AND p.file_list != '' THEN 0 ELSE NULL END AS file_type,
           p.file_list,
           p.category_id,
           c.category_name,
           p.view_count,
           p.like_count,
           p.comment_count,
           p.collect_count,
           p.status,
           p.template_id,
           p.template_type,
           p.template_data,
           p.created_time,
           p.updated_time
       FROM
           posts p
               LEFT JOIN
           users u ON
               p.user_id = u.id
               LEFT JOIN
           categories c ON
               p.category_id = c.id
               LEFT JOIN
           auth_applications auth ON
               u.id = auth.user_id AND auth.del_flag = 0
       WHERE
           p.id = #{postId}
         AND p.del_flag = 0;
   </select>
</mapper>
