<template>
  <view class="search-page">
    <!-- 搜索栏 -->
    <view class="search-header" :style="{ paddingTop: safeAreaTop + 'px' }">
      <view class="search-input-container">
        <up-search
          v-model="searchKeyword"
          placeholder="搜索帖子标题、内容..."
          :showAction="true"
          actionText="搜索"
          bgColor="#f5f5f5"
          @search="handleSearch"
          @clear="handleClearSearch"
          @action="handleSearchAction"
          @input="handleSearchInput"
          @confirm="handleSearch"
          :focus="true"
        />
      </view>
      <view class="cancel-btn" @click="handleCancel">
        <text>取消</text>
      </view>
    </view>
    </view>

    <!-- 搜索内容区域 -->
    <view class="search-content">
      <!-- 搜索建议/历史 -->
      <view v-if="!hasSearched" class="search-suggestions">
        <!-- 搜索历史 -->
        <view v-if="searchHistory.length > 0" class="history-section">
          <view class="section-header">
            <text class="section-title">搜索历史</text>
            <up-icon 
              name="trash" 
              size="16" 
              color="#999" 
              @click="handleClearHistory"
            />
          </view>
          <view class="history-list">
            <view
              v-for="item in searchHistory"
              :key="item.keyword"
              class="history-item"
              @click="handleHistoryClick(item.keyword)"
            >
              <up-icon name="clock" size="14" color="#999" />
              <text class="history-keyword">{{ item.keyword }}</text>
              <text class="history-time">{{ formatSearchTime(item.timestamp) }}</text>
              <up-icon 
                name="close" 
                size="14" 
                color="#ccc" 
                @click.stop="handleRemoveHistory(item.keyword)"
              />
            </view>
          </view>
        </view>

        <!-- 热门搜索 -->
        <view v-if="hotKeywords.length > 0" class="hot-section">
          <view class="section-header">
            <text class="section-title">热门搜索</text>
          </view>
          <view class="hot-keywords">
            <view
              v-for="keyword in hotKeywords"
              :key="keyword"
              class="hot-keyword"
              @click="handleHotKeywordClick(keyword)"
            >
              {{ keyword }}
            </view>
          </view>
        </view>

        <!-- 搜索提示 -->
        <view v-if="searchSuggestions.length > 0" class="suggestions-section">
          <view class="section-header">
            <text class="section-title">搜索建议</text>
          </view>
          <view class="suggestions-list">
            <view
              v-for="suggestion in searchSuggestions"
              :key="suggestion"
              class="suggestion-item"
              @click="handleSuggestionClick(suggestion)"
            >
              <up-icon name="search" size="14" color="#999" />
              <text class="suggestion-text">{{ suggestion }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 搜索结果 -->
      <view v-if="hasSearched" class="search-results">
        <view class="results-header">
          <text class="results-count">找到 {{ totalCount }} 条相关内容</text>
          <view class="sort-filter">
            <view class="sort-selector" @click="showSortOptions = !showSortOptions">
              <text class="sort-text">{{ getSortLabel(sortType) }}</text>
              <up-icon name="arrow-down" size="12" color="#666" />
            </view>

            <!-- 自定义排序选项弹窗 -->
            <up-popup
              v-model="showSortOptions"
              mode="bottom"
              :border-radius="12"
              :safe-area-inset-bottom="true"
            >
              <view class="sort-options-popup">
                <view class="popup-header">
                  <text class="popup-title">选择排序方式</text>
                  <up-icon name="close" size="18" color="#999" @click="showSortOptions = false" />
                </view>
                <view class="options-list">
                  <view
                    v-for="option in sortOptions"
                    :key="option.value"
                    class="option-item"
                    :class="{ active: sortType === option.value }"
                    @click="handleSortSelect(option.value)"
                  >
                    <text class="option-text">{{ option.label }}</text>
                    <up-icon
                      v-if="sortType === option.value"
                      name="checkmark"
                      size="16"
                      color="#007aff"
                    />
                  </view>
                </view>
              </view>
            </up-popup>
          </view>
        </view>

        <!-- 结果列表 -->
        <up-list
          @scrolltolower="debouncedLoadMore"
          :show-scrollbar="false"
          :refresher-enabled="true"
          :refresher-triggered="refreshing"
          @refresherrefresh="handleRefresh"
          :lower-threshold="100"
        >
          <view v-if="searchResults.length === 0 && !loading" class="empty-results">
            <up-empty
              mode="search"
              text="没有找到相关内容"
              textColor="#999"
              textSize="16"
            />
            <view class="search-tips">
              <text class="tips-title">搜索建议：</text>
              <text class="tips-item">• 检查关键词拼写</text>
              <text class="tips-item">• 尝试更简短的关键词</text>
              <text class="tips-item">• 使用不同的关键词</text>
            </view>
          </view>
          
          <view v-for="(post, index) in searchResults" :key="post.id || index" class="result-item">
            <PostListItem
              :post="post"
              :highlight-keyword="currentSearchKeyword"
              @click="handlePostClick"
              @like="handleLike"
              @collect="handleCollect"
              @delete="handleDelete"
              @share="handleShare"
            />
          </view>
          
          <!-- 加载更多 -->
          <view v-if="loading" class="loading-more">
            <up-loading-icon mode="spinner" />
            <text class="loading-text">搜索中...</text>
          </view>
          
          <view v-if="!hasMore && searchResults.length > 0" class="no-more">
            <up-divider text="没有更多了" textSize="12px" />
          </view>
        </up-list>
      </view>
    </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useSafeArea } from '@/utils/safeArea'
import { get } from '@/utils/http'
import { useMemberStore } from '@/stores'
import PostListItem from '@/components/PostListItem.vue'
import { toggleLike, toggleCollect, deletePost } from '@/services/likeCollectService'
import { 
  getSearchHistory, 
  addSearchHistory, 
  removeSearchHistory, 
  clearSearchHistory,
  getHotSearchKeywords,
  formatSearchTime,
  type SearchHistoryItem 
} from '@/utils/searchHistory'
import type { PostsList, Result } from '@/types'

const { safeAreaTop } = useSafeArea()
const memberStore = useMemberStore()

// 搜索状态
const searchKeyword = ref('')
const currentSearchKeyword = ref('')
const hasSearched = ref(false)
const searchResults = ref<PostsList[]>([])
const totalCount = ref(0)
const loading = ref(false)
const refreshing = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = 10
const sortType = ref('relevance')
const showSortOptions = ref(false)

// 搜索历史和建议
const searchHistory = ref<SearchHistoryItem[]>([])
const searchSuggestions = ref<string[]>([])
const hotKeywords = ref<string[]>([])

// 排序选项
const sortOptions = [
  { label: '相关度', value: 'relevance' },
  { label: '最新发布', value: 'latest' },
  { label: '最多点赞', value: 'likes' },
  { label: '最多评论', value: 'comments' }
]



// 页面加载
onMounted(() => {
  loadSearchHistory()
  loadHotKeywords()
})

// 监听搜索关键词变化，提供搜索建议
watch(searchKeyword, (newKeyword) => {
  if (newKeyword && newKeyword.length > 0 && !hasSearched.value) {
    debouncedGetSuggestions(newKeyword)
  } else {
    searchSuggestions.value = []
  }
})

// 加载搜索历史
const loadSearchHistory = () => {
  searchHistory.value = getSearchHistory()
}

// 加载热门搜索词
const loadHotKeywords = () => {
  hotKeywords.value = getHotSearchKeywords()
}

// 搜索输入处理
const handleSearchInput = (value: string) => {
  console.log('搜索输入变化:', value)
  if (!value) {
    hasSearched.value = false
    searchResults.value = []
  }
}

// 处理搜索按钮点击（action事件）
const handleSearchAction = () => {
  console.log('=== 搜索按钮被点击（action事件）===')
  handleSearch()
}

// 执行搜索
const handleSearch = async () => {
  console.log('=== 搜索函数被调用 ===')
  console.log('搜索关键词:', searchKeyword.value)

  const keyword = searchKeyword.value.trim()
  if (!keyword) {
    console.log('搜索关键词为空，返回')
    uni.showToast({
      title: '请输入搜索关键词',
      icon: 'none'
    })
    return
  }

  console.log('开始执行搜索，关键词:', keyword)
  currentSearchKeyword.value = keyword
  hasSearched.value = true
  currentPage.value = 1
  hasMore.value = true

  // 添加到搜索历史
  addSearchHistory(keyword)
  loadSearchHistory()

  await performSearch(true)
}

// 执行搜索请求
const performSearch = async (reset = false) => {
  console.log('执行搜索请求，reset:', reset, 'loading:', loading.value)
  if (loading.value && !reset) return

  loading.value = true

  try {
    const params = {
      page: reset ? 1 : currentPage.value,
      pageSize,
      title: currentSearchKeyword.value, // 使用title参数进行搜索
      status: 1 // 只搜索已审核通过的帖子
    }

    console.log('搜索请求参数:', params)
    const res = await get<Result<{ records: PostsList[], total: number }>>('/posts/listAll', params)
    console.log('搜索API响应:', res)
    
    if (res.success && res.data) {
      const newPosts = res.data.records || []
      
      // 处理搜索结果
      const processedPosts = newPosts.map(post => {
        // 处理媒体文件列表
        let fileListData: string[] = []
        let mediaFiles: any[] = []

        if (post.fileList && post.fileList.trim()) {
          fileListData = post.fileList.split(',').filter(f => f.trim())
          // 处理媒体文件（参考首页的processMediaFiles函数）
          mediaFiles = fileListData.map(file => {
            const relativePath = file.trim()
            const url = `http://localhost:3205/${relativePath}`
            const extension = relativePath.toLowerCase().split('.').pop() || ''

            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp']
            const videoExtensions = ['mp4', 'avi', 'mov', 'wmv']

            let type: 'image' | 'video' | 'unknown' = 'unknown'
            if (imageExtensions.includes(extension)) {
              type = 'image'
            } else if (videoExtensions.includes(extension)) {
              type = 'video'
            }

            return {
              url,
              type,
              extension
            }
          })
        }

        return {
          ...post,
          likeCount: post.likeCount || 0,
          commentCount: post.commentCount || 0,
          collectCount: post.collectCount || 0,
          viewCount: post.viewCount || 0,
          isLiked: post.isLiked || false,
          isCollected: post.isCollected || false,
          fileListData,
          mediaFiles,
          isProcessing: false
        }
      })

      console.log('处理后的搜索结果:', processedPosts)
      
      if (reset) {
        searchResults.value = processedPosts
        currentPage.value = 2
        totalCount.value = res.data.total || 0
      } else {
        searchResults.value.push(...processedPosts)
        currentPage.value++
      }

      hasMore.value = newPosts.length === pageSize

      console.log(`搜索完成: ${newPosts.length}条结果, 总计: ${searchResults.value.length}条`)
      console.log('当前状态:', {
        hasSearched: hasSearched.value,
        searchResults: searchResults.value.length,
        totalCount: totalCount.value,
        loading: loading.value
      })
    } else {
      console.error('搜索失败:', res.message)
      uni.showToast({
        title: res.message || '搜索失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('搜索异常:', error)
    uni.showToast({
      title: '搜索失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 获取搜索建议 (暂时禁用，因为后端没有对应接口)
const getSuggestions = async (keyword: string) => {
  // 暂时不实现搜索建议功能
  console.log('搜索建议功能暂未实现:', keyword)
  searchSuggestions.value = []
}

// 防抖获取搜索建议
let suggestionsTimer: number | null = null
const debouncedGetSuggestions = (keyword: string) => {
  if (suggestionsTimer) {
    clearTimeout(suggestionsTimer)
  }
  suggestionsTimer = setTimeout(() => {
    getSuggestions(keyword)
  }, 300)
}

// 清除搜索
const handleClearSearch = () => {
  searchKeyword.value = ''
  currentSearchKeyword.value = ''
  hasSearched.value = false
  searchResults.value = []
  searchSuggestions.value = []
}

// 取消搜索
const handleCancel = () => {
  uni.navigateBack()
}

// 历史记录点击
const handleHistoryClick = (keyword: string) => {
  searchKeyword.value = keyword
  handleSearch()
}

// 热门关键词点击
const handleHotKeywordClick = (keyword: string) => {
  searchKeyword.value = keyword
  handleSearch()
}

// 搜索建议点击
const handleSuggestionClick = (suggestion: string) => {
  searchKeyword.value = suggestion
  handleSearch()
}

// 删除单个历史记录
const handleRemoveHistory = (keyword: string) => {
  removeSearchHistory(keyword)
  loadSearchHistory()
}

// 清空历史记录
const handleClearHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有搜索历史吗？',
    success: (res) => {
      if (res.confirm) {
        clearSearchHistory()
        loadSearchHistory()
        uni.showToast({
          title: '已清空',
          icon: 'success'
        })
      }
    }
  })
}

// 获取排序标签
const getSortLabel = (value: string) => {
  const option = sortOptions.find(opt => opt.value === value)
  return option ? option.label : '排序'
}

// 选择排序方式
const handleSortSelect = (value: string) => {
  sortType.value = value
  showSortOptions.value = false
  if (hasSearched.value) {
    currentPage.value = 1
    hasMore.value = true
    performSearch(true)
  }
}

// 排序变更（保留兼容性）
const handleSortChange = () => {
  if (hasSearched.value) {
    currentPage.value = 1
    hasMore.value = true
    performSearch(true)
  }
}

// 下拉刷新
const handleRefresh = async () => {
  if (!hasSearched.value) return
  
  refreshing.value = true
  currentPage.value = 1
  hasMore.value = true
  await performSearch(true)
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value && searchResults.value.length > 0) {
    performSearch()
  }
}

// 防抖加载更多
let loadMoreTimer: number | null = null
const debouncedLoadMore = () => {
  if (loadMoreTimer) {
    clearTimeout(loadMoreTimer)
  }
  loadMoreTimer = setTimeout(() => {
    loadMore()
  }, 300)
}

// 帖子相关事件处理（复用列表页面的逻辑）
const handlePostClick = (post: PostsList) => {
  uni.navigateTo({
    url: `/pages/post/detail?id=${post.id}`
  })
}

const handleLike = async (post: PostsList) => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  if (post.isProcessing) return
  
  try {
    const postIndex = searchResults.value.findIndex(p => p.id === post.id)
    if (postIndex !== -1) {
      searchResults.value[postIndex].isProcessing = true
    }
    
    const result = await toggleLike(post.id)
    
    if (result.success && result.data) {
      if (postIndex !== -1) {
        searchResults.value[postIndex].isLiked = result.data.isLiked
        searchResults.value[postIndex].likeCount = result.data.likeCount
      }
      
      uni.showToast({
        title: result.data.isLiked ? '点赞成功' : '取消点赞',
        icon: 'success',
        duration: 1500
      })
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  } finally {
    const postIndex = searchResults.value.findIndex(p => p.id === post.id)
    if (postIndex !== -1) {
      searchResults.value[postIndex].isProcessing = false
    }
  }
}

const handleCollect = async (post: PostsList) => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  if (post.isProcessing) return
  
  try {
    const postIndex = searchResults.value.findIndex(p => p.id === post.id)
    if (postIndex !== -1) {
      searchResults.value[postIndex].isProcessing = true
    }
    
    const result = await toggleCollect(post.id)
    
    if (result.success && result.data) {
      if (postIndex !== -1) {
        searchResults.value[postIndex].isCollected = result.data.isCollected
        searchResults.value[postIndex].collectCount = result.data.collectCount
      }
      
      uni.showToast({
        title: result.data.isCollected ? '收藏成功' : '取消收藏',
        icon: 'success',
        duration: 1500
      })
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  } finally {
    const postIndex = searchResults.value.findIndex(p => p.id === post.id)
    if (postIndex !== -1) {
      searchResults.value[postIndex].isProcessing = false
    }
  }
}

const handleDelete = async (post: PostsList) => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  const canDelete = post.userId === memberStore.profile.id || memberStore.profile.isAdmin
  if (!canDelete) {
    uni.showToast({
      title: '无权限删除此帖子',
      icon: 'none'
    })
    return
  }
  
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条帖子吗？删除后无法恢复。',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await deletePost(post.id)
          
          if (result.success) {
            const postIndex = searchResults.value.findIndex(p => p.id === post.id)
            if (postIndex !== -1) {
              searchResults.value.splice(postIndex, 1)
              totalCount.value = Math.max(totalCount.value - 1, 0)
            }
            
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
        } catch (error) {
          console.error('删除操作失败:', error)
          uni.showToast({
            title: '删除失败，请重试',
            icon: 'none'
          })
        }
      }
    }
  })
}

const handleShare = (post: PostsList) => {
  const shareTitle = post.title || '查看这个有趣的内容'
  const shareContent = post.content ? post.content.substring(0, 100) + '...' : '来看看这个内容吧'
  
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    title: shareTitle,
    summary: shareContent,
    href: `https://your-domain.com/post/${post.id}`,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.setClipboardData({
        data: `${shareTitle}\n${shareContent}\n查看详情: https://your-domain.com/post/${post.id}`,
        success: () => {
          uni.showToast({
            title: '链接已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    }
  })
}
</script>

<style scoped lang="scss">
.search-page {
  background-color: #f5f5f5;

  /* 强制重置文字方向为水平 */
  * {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
  }
}

.search-header {
  background-color: white;
  padding: 16px 16px 12px 16px; /* 减少底部间距 */
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-input-container {
  flex: 1;
}

.cancel-btn {
  color: #007bff;
  font-size: 16px;
  padding: 8px;
}

.search-content {
  padding: 8px 16px 16px 16px; /* 减少顶部间距 */
}

.search-suggestions {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  margin-top: 0; /* 确保没有额外的顶部间距 */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.history-list {
  padding: 8px 0;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  
  &:active {
    background-color: #f8f9fa;
  }
}

.history-keyword {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.history-time {
  font-size: 12px;
  color: #999;
}

.hot-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px;
}

.hot-keyword {
  padding: 6px 12px;
  background-color: #f8f9fa;
  border-radius: 16px;
  font-size: 14px;
  color: #666;
  
  &:active {
    background-color: #e9ecef;
  }
}

.suggestions-list {
  padding: 8px 0;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  
  &:active {
    background-color: #f8f9fa;
  }
}

.suggestion-text {
  font-size: 14px;
  color: #333;
}

.search-results {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.results-count {
  font-size: 14px;
  color: #666;
}

.sort-filter {
  position: relative;
}

.sort-selector {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background-color: #f8f9fa;
  border-radius: 16px;
  cursor: pointer;

  &:active {
    background-color: #e9ecef;
  }
}

.sort-text {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}

.sort-options-popup {
  background-color: white;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.options-list {
  padding: 8px 0;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;

  &:active {
    background-color: #f8f9fa;
  }

  &.active {
    background-color: #f0f8ff;
  }
}

.option-text {
  font-size: 16px;
  color: #333;
  white-space: nowrap;

  .option-item.active & {
    color: #007aff;
    font-weight: 500;
  }
}

.result-item {
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.empty-results {
  padding: 60px 20px;
  text-align: center;
}

.search-tips {
  margin-top: 20px;
  text-align: left;
}

.tips-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  display: block;
  margin-bottom: 8px;
}

.tips-item {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 4px;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 8px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.no-more {
  padding: 20px;
}
</style>