# 社区管理系统设计文档

**最新更新时间**: 2025-01-30
**当前版本**: v2.2 - 错误修复与稳定性增强版

## 系统架构设计

### 前端架构
```
vue3-ts-app/
├── src/
│   ├── components/          # 组件库
│   │   └── templates/       # 发布模板组件
│   │       ├── BaseTemplate.vue           # 基础模板
│   │       ├── AnnouncementTemplate.vue   # 公告模板
│   │       ├── GroupBuyTemplate.vue       # 团购模板
│   │       ├── ActivityTemplate.vue       # 活动发起模板
│   │       ├── SurveyFormTemplate.vue     # 调查问卷模板
│   │       └── VoteTemplate.vue           # 投票表决模板
│   ├── pages/               # 页面
│   │   ├── publish/         # 发布页面
│   │   ├── index/           # 首页
│   │   └── my/              # 个人中心
│   ├── services/            # API服务
│   ├── stores/              # 状态管理
│   ├── types/               # TypeScript类型定义
│   └── utils/               # 工具函数
│       ├── mediaUtils.ts    # 媒体文件处理工具
│       └── validation.ts    # 表单验证工具
```

### 后端架构
```
boot/api/src/main/java/com/haolinkyou/
├── controller/          # 控制器层
│   ├── PostsController.java           # 帖子管理
│   ├── TemplateController.java        # 模板管理
│   ├── PointsController.java          # 积分管理
│   ├── SignController.java            # 签到功能
│   ├── ProductsController.java        # 积分商城
│   ├── CommentsController.java        # 评论管理
│   ├── PostLikesController.java       # 点赞功能
│   └── UserCollectsController.java    # 收藏功能
├── service/             # 服务层
├── entity/              # 实体类
├── mapper/              # 数据访问层
└── util/                # 工具类
```

- **端口**：3205
- **框架**：Spring Boot + MyBatis-Plus
- **认证**：JWT Token
- **数据库**：MySQL（26个表，支持JSON字段）

### 技术栈
- **前端**：uni-app + Vue 3 + TypeScript + uview-plus
- **后端**：Spring Boot + MyBatis-Plus
- **数据库**：MySQL
- **状态管理**：Pinia
- **HTTP请求**：axios（自动添加/api前缀）

## 模板系统设计

### 模板分类策略
1. **分类专用模板**：特定分类使用的专门模板
2. **通用模板**：所有分类都可使用的通用模板
3. **特殊处理**：组团邀约分类提供多模板选择

### 模板组件设计原则
1. **组合式设计**：所有模板基于BaseTemplate构建
2. **数据驱动**：通过props和events进行数据交互
3. **验证统一**：统一的表单验证机制
4. **响应式布局**：适配不同屏幕尺寸

### 已实现模板组件

#### 1. BaseTemplate（基础模板）
```vue
<template>
  <view class="base-template">
    <!-- 标题输入 -->
    <up-input
      v-model="formData.title"
      :placeholder="titlePlaceholder"
      :maxlength="100"
      @input="handleTitleChange"
      @blur="validateTitle"
    />

    <!-- 内容输入 -->
    <up-textarea
      v-model="formData.content"
      :placeholder="contentPlaceholder"
      :maxlength="2000"
      :autoHeight="true"
      @input="handleContentChange"
      @blur="validateContent"
    />

    <!-- 媒体上传 -->
    <view v-if="showMediaUpload" class="upload-section">
      <!-- 自定义文件上传和预览 -->
      <view class="custom-upload-container">
        <!-- 文件预览列表 -->
        <view class="file-preview-list">
          <!-- 文件预览项 -->
        </view>
      </view>
    </view>

    <!-- 联系方式 -->
    <up-input
      v-if="showContact"
      v-model="formData.contact"
      placeholder="请输入联系方式"
      @blur="validateContact"
    />
  </view>
</template>
```

**功能特性**：
- 标题输入（支持必填/可选，长度限制）
- 内容输入（自动高度，字数限制）
- 媒体上传（图片/视频，自定义预览）
- 联系方式（手机号格式验证）
- 统一的表单验证机制（validation.ts）
- 实时错误提示和状态反馈

#### 2. ActivityTemplate（活动发起模板）
**核心功能**：
- **活动类型选择**：使用up-action-sheet底部弹出选择
- **时间地点设置**：
  - 活动日期：up-datetime-picker（date模式，hasInput）
  - 开始时间：up-datetime-picker（time模式，hasInput）
  - 结束时间：up-datetime-picker（time模式，hasInput）
- **费用管理**：up-action-sheet选择（免费/AA制/固定费用）
- **参与人数控制**：最少、最多人数设置
- **报名设置**：截止时间、联系方式、特殊要求

**技术实现**：
```vue
<!-- 活动类型选择 -->
<view class="input-wrapper" @click="showActivityTypeSheet = true">
  <up-input v-model="activityTypeText" disabled readonly />
</view>
<up-action-sheet
  :show="showActivityTypeSheet"
  :actions="activityTypeActions"
  @select="handleActivityTypeSelect"
/>

<!-- 时间选择 -->
<up-datetime-picker
  v-model="dateValue"
  mode="date"
  hasInput
  placeholder="选择活动日期"
/>
```

#### 3. GroupBuyTemplate（团购模板）
**核心功能**：
- 商品信息管理（标题必填、描述、图片）
- 价格设置（原价、团购价）
- 团购条件（数量、时间）
- 联系信息（联系人、电话、取货地点）
- **付款方式**：
  - 微信支付和支付宝支付
  - 收款码上传功能（每种限1张）
  - 50%宽度美化布局
  - 默认显示两种方式

**付款方式设计**：
```vue
<view class="payment-methods">
  <view class="payment-method-item">
    <view class="payment-header">
      <up-icon name="weixin-fill" color="#07C160"></up-icon>
      <text>微信支付</text>
    </view>
    <up-upload :fileList="formData.wechatQrCode" :maxCount="1" />
  </view>

  <view class="payment-method-item">
    <view class="payment-header">
      <up-icon name="zhifubao" color="#1677FF"></up-icon>
      <text>支付宝支付</text>
    </view>
    <up-upload :fileList="formData.alipayQrCode" :maxCount="1" />
  </view>
</view>
#### 4. SurveyFormTemplate（调查问卷模板）
**核心功能**：
- 问卷标题和背景说明（标题必填）
- 多问题支持（最多20个问题）
- 三种问题类型：单选题、多选题、文本输入题
- 每个问题可设置必答/非必答
- 动态添加/删除问题和选项
- 匿名设置和截止时间
- 目标对象选择

#### 5. VoteTemplate（投票表决模板）
**核心功能**：
- 投票标题和背景说明（标题必填）
- 投票选项管理（最多10个选项）
- 投票方式（单选/多选）
- 匿名/实名投票设置
- 截止时间设置
- 目标对象选择

#### 6. AnnouncementTemplate（公告模板）
**核心功能**：
- 标题必填，内容描述
- 图片上传（最多6张）
- 联系方式（可选，手机号验证）
- 目标对象选择（非必填，支持"不限"选项）

## 积分系统设计

### 积分获取机制
```typescript
// 积分类型定义
enum PointsType {
  SIGN = 'sign',        // 签到
  POST = 'post',        // 发帖
  COMMENT = 'comment',  // 评论
  LIKE = 'like',        // 点赞
  COLLECT = 'collect',  // 收藏
  REDEEM = 'redeem'     // 兑换（负分）
}
```

### 积分规则配置
- **每日签到**：基础积分5分，连续签到额外奖励
- **发帖奖励**：每篇帖子10分，每日上限50分
- **评论奖励**：每条评论2分，每日上限20分
- **点赞奖励**：每次点赞1分，每日上限10分
- **收藏奖励**：每次收藏2分，每日上限10分

### 防刷机制
- 每日积分获取上限
- 操作频率限制
- 重复操作检测
- 异常行为监控

### 积分商城设计
```sql
-- 商品表结构
CREATE TABLE products (
  id BIGINT PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  points INT NOT NULL,
  stock INT DEFAULT 0,
  status TINYINT DEFAULT 1
);
```

### 兑换流程设计
1. **积分检查**：验证用户积分是否足够
2. **库存检查**：验证商品库存是否充足
3. **事务处理**：扣除积分、减少库存、创建订单
4. **状态跟踪**：订单状态管理和更新

## 用户界面设计

### 设计系统
- **主色调**：蓝色系（#007bff）
- **辅助色**：绿色（认证用户）、红色（错误提示）、黄色（收藏激活）
- **字体大小**：12px-20px 层次化设计
- **圆角**：8px-12px 统一圆角设计
- **间距**：8px、12px、16px、20px 规范化间距

### 组件设计规范

#### 表单组件样式
```scss
.form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff4757;
  margin-left: 4px;
}
```

#### 付款方式设计
```scss
.payment-methods {
  display: flex;
  gap: 16px;
}

.payment-method-item {
  flex: 1;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  background-color: #fafafa;
}
```

#### 输入框包装器
```scss
.input-wrapper {
  cursor: pointer;
}

.input-wrapper .up-input {
  pointer-events: none;
}
```

### 交互设计

#### 时间选择器
- **设计原则**：使用hasInput模式的datetime-picker
- **用户体验**：点击输入框触发选择器，选择后显示在输入框中
- **格式化**：统一的日期时间格式显示

#### 底部弹出选择器
- **设计原则**：使用up-action-sheet进行选择
- **交互方式**：点击输入框弹出底部选择菜单
- **状态反馈**：选择后在输入框显示选中的内容

#### 文件上传
- **视觉设计**：虚线边框 + 图标 + 文字提示
- **交互反馈**：上传进度、预览功能、删除功能
- **限制提示**：文件数量、大小限制的友好提示

#### 表单验证
- **实时验证**：输入时和失焦时触发验证
- **错误提示**：红色文字，位于表单项下方
- **成功状态**：清除错误提示，允许提交

## 数据流设计

### 组件数据流
```
Parent Component
    ↓ props
Template Component
    ↓ events
Parent Component
    ↓ API call
Backend Service
```

### 状态管理
- **用户状态**：Pinia store管理用户信息和认证状态
- **表单状态**：组件内部ref管理，通过events向上传递
- **全局状态**：分类信息、目标对象等通过API动态获取

## API设计规范

### 请求格式
```typescript
// 发布内容
POST /api/posts
{
  categoryId: number,
  templateType: string,
  title: string,
  content: string,
  // ... 其他模板特定字段
}
```

### 响应格式
```typescript
{
  success: boolean,
  data: any,
  message?: string
}
```

### 文件上传
- **策略**：单次FormData请求包含所有文件
- **处理**：后端提取并处理文件，返回文件路径列表
- **存储**：仅在用户实际发布时上传，避免浪费存储

## 安全设计

### 前端安全
- **输入验证**：所有用户输入进行客户端验证
- **XSS防护**：内容显示时进行HTML转义
- **文件上传**：限制文件类型和大小
- **错误处理**：友好的错误提示，避免敏感信息泄露

### 后端安全
- **JWT认证**：敏感操作验证用户身份
- **权限控制**：基于角色的访问控制
- **数据验证**：服务端再次验证所有输入
- **CORS配置**：正确配置跨域访问策略

### 系统稳定性
- **组件错误处理**：修复第三方组件潜在错误
- **媒体文件访问**：增强媒体文件访问的错误处理
- **服务器连接检测**：自动检测服务器状态

## 性能优化设计

### 前端优化
- **组件懒加载**：模板组件按需加载
- **图片优化**：压缩和格式优化
- **缓存策略**：合理使用浏览器缓存

### 后端优化
- **数据库索引**：关键字段建立索引
- **查询优化**：避免N+1查询问题
- **缓存机制**：热点数据缓存

## 响应式设计

### 断点设计
- **手机**：< 768px
- **平板**：768px - 1024px
- **桌面**：> 1024px

### 布局适配
- **弹性布局**：使用flex布局适配不同屏幕
- **网格系统**：复杂布局使用CSS Grid
- **字体缩放**：根据屏幕尺寸调整字体大小

## 数据库设计详情

### 核心表结构分析（基于demoDDL0726.sql）

#### 用户系统表
```sql
-- 用户基础信息表
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL,
  role VARCHAR(20) DEFAULT 'guest',
  is_authenticated TINYINT DEFAULT 0,
  points INT DEFAULT 0,  -- 当前积分余额
  total_points INT DEFAULT 0,  -- 累计获得积分
  -- 其他字段...
);

-- 用户积分记录表
CREATE TABLE user_points (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  points INT NOT NULL,  -- 积分变动（正负数）
  type VARCHAR(50) NOT NULL,  -- 积分类型
  description VARCHAR(200),
  related_id BIGINT,  -- 关联ID
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 内容系统表
```sql
-- 帖子表（核心表）
CREATE TABLE posts (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  category_id BIGINT NOT NULL,
  title VARCHAR(200),
  content TEXT NOT NULL,
  template_data JSON,  -- 模板特定数据
  template_id BIGINT,  -- 模板ID
  post_type VARCHAR(20) DEFAULT 'normal',
  status TINYINT DEFAULT 0,  -- 审核状态
  is_top TINYINT DEFAULT 0,  -- 是否置顶
  top_expire_time DATETIME,  -- 置顶过期时间
  like_count INT DEFAULT 0,
  comment_count INT DEFAULT 0,
  collect_count INT DEFAULT 0,
  view_count INT DEFAULT 0,
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 模板配置表
CREATE TABLE category_templates (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  category_id BIGINT NOT NULL,
  template_name VARCHAR(100) NOT NULL,
  template_config JSON,  -- 模板配置
  is_default TINYINT DEFAULT 0,
  status TINYINT DEFAULT 1
);
```

#### 调查投票表
```sql
-- 调查问卷表
CREATE TABLE surveys (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  creator_id BIGINT NOT NULL,
  start_time DATETIME,
  end_time DATETIME,
  status TINYINT DEFAULT 1,
  participant_count INT DEFAULT 0
);

-- 问卷题目表
CREATE TABLE survey_questions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  survey_id BIGINT NOT NULL,
  question_text TEXT NOT NULL,
  question_type VARCHAR(20) NOT NULL,  -- single/multiple/text
  options JSON,  -- 选项配置
  is_required TINYINT DEFAULT 1,
  sort_order INT DEFAULT 0
);
```

## 后端API设计

### RESTful API规范

#### 帖子管理API
```java
@RestController
@RequestMapping("/api/posts")
public class PostsController {
    
    // 创建帖子
    @PostMapping
    public Result<Long> createPost(@RequestBody PostCreateDto dto, HttpServletRequest request);
    
    // 获取帖子列表
    @GetMapping
    public Result<IPage<PostsListVo>> getPostsList(@RequestParam PageDto pageDto);
    
    // 获取帖子详情
    @GetMapping("/{id}")
    public Result<PostDetailVo> getPostDetail(@PathVariable Long id);
    
    // 更新帖子
    @PutMapping("/{id}")
    public Result<Boolean> updatePost(@PathVariable Long id, @RequestBody PostUpdateDto dto);
    
    // 删除帖子
    @DeleteMapping("/{id}")
    public Result<Boolean> deletePost(@PathVariable Long id);
}
```

#### 积分管理API
```java
@RestController
@RequestMapping("/api/points")
public class PointsController {
    
    // 获取用户积分信息
    @GetMapping("/user/{userId}")
    public Result<UserPointsVo> getUserPoints(@PathVariable Long userId);
    
    // 获取积分记录
    @GetMapping("/records")
    public Result<IPage<UserPointsRecordVo>> getPointsRecords(@RequestParam PageDto pageDto);
    
    // 获取积分统计
    @GetMapping("/statistics")
    public Result<PointsStatisticsVo> getPointsStatistics();
}
```

#### 模板管理API
```java
@RestController
@RequestMapping("/api/templates")
public class TemplateController {
    
    // 根据分类获取模板列表
    @GetMapping("/category/{categoryId}")
    public Result<List<CategoryTemplates>> getTemplatesByCategory(@PathVariable Long categoryId);
    
    // 获取默认模板
    @GetMapping("/category/{categoryId}/default")
    public Result<CategoryTemplates> getDefaultTemplate(@PathVariable Long categoryId);
}
```

## 前端状态管理

### Pinia Store设计

#### 用户状态管理
```typescript
// stores/user.ts
export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null as UserInfo | null,
    isAuthenticated: false,
    currentRole: 'guest' as UserRole,
    points: 0,
    totalPoints: 0
  }),
  
  actions: {
    async login(credentials: LoginCredentials) {
      // 登录逻辑
    },
    
    async updatePoints(points: number) {
      // 更新积分
    },
    
    async checkAuthStatus() {
      // 检查认证状态
    }
  }
})
```

#### 帖子状态管理
```typescript
// stores/posts.ts
export const usePostsStore = defineStore('posts', {
  state: () => ({
    postsList: [] as PostItem[],
    currentPost: null as PostDetail | null,
    categories: [] as Category[],
    templates: [] as Template[]
  }),
  
  actions: {
    async fetchPostsList(params: PostsQueryParams) {
      // 获取帖子列表
    },
    
    async createPost(postData: PostCreateData) {
      // 创建帖子
    },
    
    async likePost(postId: number) {
      // 点赞帖子
    }
  }
})
```

## 可访问性设计

### 语义化HTML
- **标签语义**：使用语义化HTML标签
- **ARIA属性**：为复杂组件添加ARIA属性
- **键盘导航**：支持键盘操作

### 视觉设计
- **对比度**：确保足够的颜色对比度
- **字体大小**：最小字体不小于12px
- **触摸目标**：按钮最小44px×44px

## 部署架构

### 生产环境部署
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Nginx)   │    │  后端 (Tomcat)   │    │  数据库 (MySQL)  │
│   Port: 80/443  │────│   Port: 3205    │────│   Port: 3306    │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 开发环境配置
- **前端开发服务器**: Vite Dev Server (Port: 5173)
- **后端开发服务器**: Spring Boot (Port: 3205)
- **数据库**: MySQL (Port: 3306)
- **代理配置**: 前端请求自动添加/api前缀并代理到后端