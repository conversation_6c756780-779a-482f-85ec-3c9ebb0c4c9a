<!--
 * @Author: Rock
 * @Date: 2025-07-12 19:39:01
 * @LastEditors: Rock
 * @LastEditTime: 2025-07-26 10:40:09
 * @Description: 
-->
<template>
  <!-- 权限验证加载中 -->
  <view v-if="permissionLoading" class="permission-loading">
    <view class="loading-content">
      <up-loading-icon mode="circle" size="24" color="#1890ff"></up-loading-icon>
      <text class="loading-text">权限验证中...</text>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="my-posts-page" v-else-if="hasPagePermission">
    <!-- 顶部统计 -->
    <view class="stats-header">
      <view class="stats-item">
        <text class="stats-number">{{ totalPosts }}</text>
        <text class="stats-label">总动态</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{ totalLikes }}</text>
        <text class="stats-label">获赞</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{ totalViews }}</text>
        <text class="stats-label">浏览</text>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <up-tabs
        :list="filterTabs"
        :current="currentFilter"
        @change="onFilterChange"
        :scrollable="false"
      ></up-tabs>
    </view>

    <!-- 动态列表 -->
    <view class="posts-list">
      <view v-if="postList.length === 0 && !loading" class="empty-state">
        <up-empty
          mode="data"
          text="暂无动态"
          textSize="14"
        ></up-empty>
      </view>

      <view v-for="post in postList" :key="post.id" class="post-item" @click="goToDetail(post.id)">
        <view class="post-header">
          <view class="post-meta">
            <text class="post-time">{{ formatPostTime(post) }}</text>
            <view class="post-status" :class="getStatusClass(post.status)">
              <text class="status-text">{{ getStatusText(post.status) }}</text>
            </view>
          </view>
          <view class="post-actions" @click.stop="showPostActions(post)">
            <up-icon name="more-dot-fill" size="16" color="#999"></up-icon>
          </view>
        </view>

        <view class="post-content">
          <up-parse :content="post.content" class="content-text"></up-parse>
          
          <!-- 图片展示 -->
          <view v-if="post.fileListData && post.fileListData.length > 0" class="post-images">
            <view class="image-grid" :class="getGridClass(post.fileListData.length)">
              <view
                v-for="(image, index) in post.fileListData.slice(0, 9)"
                :key="index"
                class="grid-item"
              >
                <up-image
                  :src="image"
                  width="100%"
                  height="100%"
                  radius="6"
                  mode="aspectFill"
                  :lazy-load="true"
                ></up-image>
              </view>
            </view>
          </view>
        </view>

        <view class="post-footer">
          <view class="post-stats">
            <view class="stat-item">
              <up-icon name="eye" size="14" color="#999"></up-icon>
              <text class="stat-text">{{ post.viewCount || 0 }}</text>
            </view>
            <view class="stat-item">
              <up-icon name="thumb-up" size="14" color="#999"></up-icon>
              <text class="stat-text">{{ post.likeCount || 0 }}</text>
            </view>
            <view class="stat-item">
              <up-icon name="chat" size="14" color="#999"></up-icon>
              <text class="stat-text">{{ post.commentCount || 0 }}</text>
            </view>
          </view>
          <view class="post-category">
            <up-tag
              :text="post.categoryName"
              size="mini"
              type="primary"
              plain plainFill
            borderColor="transparent" :autoBgColor="95"
            ></up-tag>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <up-loadmore
      :status="loadStatus"
      :loading-text="loadingText"
      :loadmore-text="loadmoreText"
      :nomore-text="nomoreText"
      @loadmore="loadMore"
    />

    <!-- 操作菜单 -->
    <up-action-sheet
      :show="showActions"
      :actions="actionList"
      @close="showActions = false"
      @select="onActionSelect"
    ></up-action-sheet>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMemberStore } from '@/stores'
import { get } from '@/utils/http'
import { setFileList } from '@/utils/util'
import { formatPostTime } from '@/utils/timeFormat'
import type { PostsList, Result, PageResult } from '@/types'
import { checkPagePermission } from '@/utils/pagePermission'

const memberStore = useMemberStore()

// 权限验证状态
const permissionLoading = ref(true)
const hasPagePermission = ref(false)

// 筛选选项类型
interface FilterTab {
  name: string
}

// 操作菜单项类型
interface ActionItem {
  name: string
  value: string
  color?: string
}

// 数据
const postList = ref<PostsList[]>([])
const totalPosts = ref(0)
const totalLikes = ref(0)
const totalViews = ref(0)
const loading = ref(false)
const currentFilter = ref(0)
const selectedPost = ref<PostsList | null>(null)
const showActions = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const loadStatus = ref<'loadmore' | 'loading' | 'nomore'>('loadmore')
const loadingText = ref('正在加载...')
const loadmoreText = ref('上拉加载更多')
const nomoreText = ref('已经到底了')

// 筛选选项
const filterTabs = ref<FilterTab[]>([
  { name: '全部' },
  { name: '已发布' },
  { name: '审核中' },
  { name: '已拒绝' }
])

// 操作菜单
const actionList = ref<ActionItem[]>([
  { name: '编辑', value: 'edit' },
  { name: '删除', value: 'delete', color: '#ff4757' }
])

// 获取统计数据
const fetchStats = async () => {
  if (!memberStore.profile?.id) return

  try {
    // 获取所有统计数据（不受筛选影响）- 使用不带筛选条件的请求
    const statsRes = await get('/posts/my', {
      userId: memberStore.profile.id,
      page: 1,
      pageSize: 1, // 只需要获取总数，不需要具体数据
      status: '' // 不筛选状态，获取所有数据的统计
    }) as Result<PageResult<PostsList>>

    if (statsRes.success && statsRes.data) {
      totalPosts.value = statsRes.data.total || 0
      // 如果接口返回了统计信息，使用它们；否则保持默认值
      if (statsRes.data.totalLikes !== undefined) {
        totalLikes.value = statsRes.data.totalLikes || 0
      }
      if (statsRes.data.totalViews !== undefined) {
        totalViews.value = statsRes.data.totalViews || 0
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取我的动态列表
const fetchMyPosts = async (isRefresh = false) => {
  if (!memberStore.profile?.id) return

  if (isRefresh) {
    currentPage.value = 1
    hasMore.value = true
    loadStatus.value = 'loading'
  }

  loading.value = true

  try {
    const params = {
      userId: memberStore.profile.id,
      page: currentPage.value,
      pageSize: pageSize.value,
      status: currentFilter.value === 0 ? '' : currentFilter.value
    }

    // 调用获取用户动态的真实API
    const res = await get('/posts/my', params) as Result<PageResult<PostsList>>

    if (res.success && res.data) {
      const newPosts = res.data.records || []

      // 处理文件列表
      newPosts.forEach((post: PostsList) => {
        if (post.fileList) {
          post.fileListData = setFileList(post.fileList)
        }
      })

      if (isRefresh) {
        postList.value = newPosts
      } else {
        postList.value.push(...newPosts)
      }

      // 注意：这里不再更新 totalPosts，因为它应该显示所有动态的总数

      // 判断是否还有更多数据
      if (newPosts.length < pageSize.value) {
        hasMore.value = false
        loadStatus.value = 'nomore'
      } else {
        loadStatus.value = 'loadmore'
      }
    }
  } catch (error) {
    console.error('获取动态失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 筛选切换
const onFilterChange = (item: { index: number }) => {
  currentFilter.value = item.index
  fetchMyPosts(true)
}

// 加载更多
const loadMore = () => {
  if (!hasMore.value || loading.value) return
  
  currentPage.value++
  fetchMyPosts()
}



// 获取状态样式
const getStatusClass = (status: number) => {
  const statusMap = {
    0: 'pending',
    1: 'published',
    2: 'rejected'
  }
  return statusMap[status as keyof typeof statusMap] || 'pending'
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap = {
    0: '审核中',
    1: '已发布',
    2: '已拒绝'
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

// 获取图片网格样式
const getGridClass = (count: number) => {
  if (count === 1) return 'grid-1'
  if (count === 2) return 'grid-2'
  if (count === 3) return 'grid-3'
  if (count === 4) return 'grid-4'
  return 'grid-9'
}

// 显示操作菜单
const showPostActions = (post: PostsList) => {
  selectedPost.value = post
  showActions.value = true
}

// 操作选择
const onActionSelect = (item: ActionItem) => {
  showActions.value = false

  if (item.value === 'edit' && selectedPost.value) {
    // 编辑动态
    uni.navigateTo({
      url: `/pages/publish/index?id=${selectedPost.value.id}`
    })
  } else if (item.value === 'delete' && selectedPost.value) {
    // 删除动态
    uni.showModal({
      title: '确认删除',
      content: '确定要删除这条动态吗？',
      success: (res) => {
        if (res.confirm && selectedPost.value) {
          deletePost(selectedPost.value.id)
        }
      }
    })
  }
}

// 删除动态
const deletePost = async (postId: number) => {
  try {
    // 调用删除API
    // await deletePostAPI(postId)
    
    // 从列表中移除
    const index = postList.value.findIndex(p => p.id === postId)
    if (index > -1) {
      postList.value.splice(index, 1)
      // 重新获取统计数据以保持准确性
      await fetchStats()
    }
    
    uni.showToast({
      title: '删除成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('删除失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    })
  }
}

// 跳转到详情
const goToDetail = (postId: number) => {
  uni.navigateTo({
    url: `/pages/post/detail?id=${postId}`
  })
}

// 页面权限检查和初始化
const initPageWithPermission = async () => {
  try {
    permissionLoading.value = true

    // 检查页面权限
    const hasPermission = await checkPagePermission('/pages/my/posts')
    hasPagePermission.value = hasPermission

    if (hasPermission) {
      // 权限验证通过，初始化页面数据
      await fetchStats() // 先获取统计数据
      fetchMyPosts(true)
    }
  } catch (error) {
    console.error('页面权限检查失败:', error)
    hasPagePermission.value = false
  } finally {
    permissionLoading.value = false
  }
}

onMounted(() => {
  initPageWithPermission()
})
</script>

<style scoped lang="scss">
// 权限验证加载状态
.permission-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .loading-text {
      font-size: 14px;
      color: #666;
    }
  }
}

.my-posts-page {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.stats-header {
  background-color: white;
  display: flex;
  padding: 20px;
  margin-bottom: 10px;
}

.stats-item {
  flex: 1;
  text-align: center;
  
  .stats-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
  }
  
  .stats-label {
    font-size: 12px;
    color: #999;
  }
}

.filter-tabs {
  background-color: white;
  margin-bottom: 10px;
}

.posts-list {
  padding: 0 10px;
}

.post-item {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 10px;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.post-time {
  font-size: 12px;
  color: #999;
}

.post-status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  
  &.published {
    background-color: #f6ffed;
    color: #52c41a;
  }
  
  &.pending {
    background-color: #fff7e6;
    color: #fa8c16;
  }
  
  &.rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
  }
}

.post-content {
  margin-bottom: 12px;
}

.content-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 8px;
}

.post-images {
  .image-grid {
    display: grid;
    gap: 4px;
    border-radius: 8px;
    overflow: hidden;
    
    &.grid-1 {
      grid-template-columns: 1fr;
      max-width: 200px;
    }
    
    &.grid-2 {
      grid-template-columns: 1fr 1fr;
    }
    
    &.grid-3 {
      grid-template-columns: repeat(3, 1fr);
    }
    
    &.grid-4 {
      grid-template-columns: repeat(2, 1fr);
    }
    
    &.grid-9 {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  .grid-item {
    aspect-ratio: 1;
    overflow: hidden;
  }
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.post-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-text {
  font-size: 12px;
  color: #999;
}

.empty-state {
  padding: 60px 20px;
}
</style>
