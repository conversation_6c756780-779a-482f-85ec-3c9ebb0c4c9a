/*
 * @Author: Rock
 * @Date: 2025-06-10 20:36:44
 * @LastEditors: Rock
 * @LastEditTime: 2025-06-29 09:36:31
 * @Description: 
 */
package com.haolinkyou.controller;

import com.haolinkyou.api.dto.AddCommentDto;
import com.haolinkyou.api.vo.CommentsVo;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.Comments;
import com.haolinkyou.service.ICommentsService;
import com.haolinkyou.service.IPointsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/comments")
public class CommentsController {
    @Autowired
    private ICommentsService commentsService;

    @Autowired
    private IPointsService pointsService;

    @GetMapping("/getCommentsByPostId")
    public Result<List<CommentsVo>> getCommentsByPostId(@RequestParam Integer postId) {
        if (postId == null) {
            throw new IllegalArgumentException("ID cannot be null");
        }
        return Result.success(commentsService.getCommentsByPostId(postId));
    }
    
    @GetMapping("/listAll")
    public Result<List<Comments>> listAll() {
        return Result.success(commentsService.listAll());
    }
    
    @PostMapping("/addComment")
    public Result<Boolean> addComment(@RequestBody AddCommentDto addCommentDto) {
        try {
            // 手动参数验证
            if (addCommentDto == null) {
                return Result.error("请求参数不能为空");
            }
            if (addCommentDto.getPostId() == null) {
                return Result.error("帖子ID不能为空");
            }
            if (addCommentDto.getUserId() == null) {
                return Result.error("用户ID不能为空");
            }
            if (!StringUtils.hasText(addCommentDto.getContent())) {
                return Result.error("评论内容不能为空");
            }
            
            // 将DTO转换为实体
            Comments comment = new Comments();
            BeanUtils.copyProperties(addCommentDto, comment);
            
            boolean result = commentsService.addComment(comment);
            if (result) {
                // 评论成功后给用户增加积分
                try {
                    IPointsService.PointsRules rules = pointsService.getPointsRules();
                    boolean pointsAdded = pointsService.addPoints(addCommentDto.getUserId(), rules.getCommentCreate(), "comment", "发表评论", addCommentDto.getPostId());
                    if (!pointsAdded) {
                        System.out.println("评论积分奖励发放失败（可能已达每日上限）");
                    }
                } catch (Exception e) {
                    System.out.println("评论积分奖励发放异常: " + e.getMessage());
                    // 积分发放失败不影响评论流程
                }
                return Result.success(true, "评论添加成功");
            } else {
                return Result.error("评论添加失败");
            }
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("系统错误，请稍后重试");
        }
    }
    
    @PostMapping("/editComment")
    public Result<Boolean> editComment(@RequestBody Comments comment) {
        try {
            boolean result = commentsService.editComment(comment);
            if (result) {
                return Result.success(true, "评论修改成功");
            } else {
                return Result.error("评论修改失败");
            }
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("系统错误，请稍后重试");
        }
    }
    
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteComment(@PathVariable Long id) {
        try {
            if (id == null) {
                return Result.error("评论ID不能为空");
            }

            boolean result = commentsService.deleteComment(id);
            if (result) {
                return Result.success(true, "评论删除成功");
            } else {
                return Result.error("评论删除失败");
            }
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("系统错误，请稍后重试");
        }
    }
}
