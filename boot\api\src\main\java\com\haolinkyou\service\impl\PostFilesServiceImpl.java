/*
 * @Author: Rock
 * @Date: 2025-05-01 01:24:45
 * @LastEditors: Rock
 * @LastEditTime: 2025-05-06 14:27:30
 * @Description: 
 */
package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.haolinkyou.entity.PostFiles;
import com.haolinkyou.mapper.PostFilesMapper;
import com.haolinkyou.service.IPostFilesService;
import com.haolinkyou.common.utils.Md5Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class PostFilesServiceImpl implements IPostFilesService {

    @Autowired
    private PostFilesMapper postFilesMapper;

    @Value("${file.upload.dir}")
    private String uploadDir;

    @Override
    @Transactional
    public List<String> uploadPostFiles(Long postId, Long userId, Integer type, MultipartFile[] files) throws Exception {
        if (files == null || files.length == 0) {
            throw new IllegalArgumentException("请选择要上传的文件");
        }

        // 智能类型检测：如果type为null，则自动检测
        if (type == null) {
            type = detectDominantFileType(files);
            System.out.println("智能检测到的文件类型: " + (type == 0 ? "图片" : "视频"));
        }

        // 校验媒体类型（支持混合类型）
        if (type != 0 && type != 1 && type != 2) {
            throw new IllegalArgumentException("媒体类型不合法");
        }

        List<String> relativePaths = new ArrayList<>();
        long totalSize = 0;

        // 校验所有文件（支持混合类型）
        for (MultipartFile file : files) {
            // 累计文件大小
            totalSize += file.getSize();

            // 校验文件格式
            String originalFilename = file.getOriginalFilename();
            String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();

            // 检查是否为支持的格式（图片或视频）
            boolean isValidImage = "jpg".equals(suffix) || "jpeg".equals(suffix) || "png".equals(suffix);
            boolean isValidVideo = "mp4".equals(suffix) || "avi".equals(suffix);

            if (!isValidImage && !isValidVideo) {
                throw new IllegalArgumentException("文件格式不支持: " + suffix + "，仅支持jpg、jpeg、png图片和mp4、avi视频");
            }

            // 如果是纯图片类型，只允许图片
            if (type == 0 && !isValidImage) {
                throw new IllegalArgumentException("图片格式仅支持jpg、jpeg、png");
            }
            // 如果是纯视频类型，只允许视频
            if (type == 1 && !isValidVideo) {
                throw new IllegalArgumentException("视频格式仅支持mp4、avi");
            }
            // 混合类型(type == 2)允许图片和视频混合
        }

        // 校验总文件大小（支持混合类型）
        long maxTotalSize;
        if (type == 0) {
            maxTotalSize = 20 * 1024 * 1024; // 图片总限制20MB
        } else if (type == 1) {
            maxTotalSize = 200 * 1024 * 1024; // 视频200MB
        } else {
            maxTotalSize = 220 * 1024 * 1024; // 混合类型220MB
        }

        if (totalSize > maxTotalSize) {
            throw new IllegalArgumentException("文件总大小超出限制");
        }

        // 生成保存路径
        String dateFolder = LocalDate.now().toString();
        String saveDir =  uploadDir + dateFolder;
        File dir = new File(saveDir);
        if (!dir.exists()) dir.mkdirs();

        // 保存所有文件
        for (int i = 0; i < files.length; i++) {
            MultipartFile file = files[i];
            String originalFilename = file.getOriginalFilename();
            String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();

            // 生成唯一文件名：原文件名 + 时间戳 + 纳秒 + 文件索引 + 随机数
            // 这样即使文件内容相同，也会生成不同的文件名
            String raw = originalFilename + "_" + System.currentTimeMillis() + "_" + System.nanoTime() + "_" + i + "_" + (int)(Math.random() * 10000);
            String newFileName = Md5Util.md5(raw, 16) + "." + suffix;

            // 确保文件名唯一，如果存在则重新生成
            File dest = new File(dir, newFileName);
            int attempt = 0;
            while (dest.exists() && attempt < 10) {
                raw = originalFilename + "_" + System.currentTimeMillis() + "_" + System.nanoTime() + "_" + i + "_" + attempt + "_" + (int)(Math.random() * 10000);
                newFileName = Md5Util.md5(raw, 16) + "." + suffix;
                dest = new File(dir, newFileName);
                attempt++;
            }

            System.out.println("保存文件 " + (i + 1) + "/" + files.length + ": " + originalFilename + " -> " + newFileName);
            file.transferTo(dest);

            // 添加到相对路径列表
            relativePaths.add(dateFolder + "/" + newFileName);
        }

        // 只保存文件到磁盘，不处理数据库合并逻辑
        // 数据库的合并逻辑由Controller层统一处理，避免重复合并
        System.out.println("uploadPostFiles方法只负责文件保存，返回新文件路径: " + relativePaths);

        return relativePaths;
    }

    @Override
    public Long getLastInsertedFileId() {
        return postFilesMapper.getLastInsertedFileId();
    }

    @Override
    public Long saveOrUpdatePostFiles(Long postId, Long userId, Integer type, List<String> allFilePaths, long totalSize) {
        // 删除该帖子的所有现有文件记录
        postFilesMapper.delete(new QueryWrapper<PostFiles>().eq("post_id", postId));

        // 为每个文件创建新记录
        Long lastInsertedId = null;
        for (int i = 0; i < allFilePaths.size(); i++) {
            String filePath = allFilePaths.get(i);
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);

            // 根据文件扩展名确定文件类型和MIME类型
            String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            String fileType = getFileType(fileExtension);
            String mimeType = getMimeType(fileExtension);

            PostFiles media = new PostFiles();
            media.setPostId(postId);
            media.setUserId(userId);
            media.setFileName(fileName);
            media.setFilePath(filePath);
            media.setFileSize(totalSize / allFilePaths.size()); // 平均分配文件大小
            media.setFileType(fileType);
            media.setMimeType(mimeType);
            media.setSortOrder(i + 1);

            System.out.println("保存文件记录到数据库，postId: " + postId + ", 文件: " + fileName);
            postFilesMapper.insert(media);
            lastInsertedId = media.getId();
        }

        System.out.println("文件记录保存成功，最后插入ID: " + lastInsertedId);
        return lastInsertedId;
    }

    /**
     * 根据文件扩展名获取文件类型
     */
    private String getFileType(String extension) {
        switch (extension) {
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
            case "webp":
                return "image";
            case "mp4":
            case "avi":
            case "mov":
            case "wmv":
            case "flv":
                return "video";
            case "mp3":
            case "wav":
            case "flac":
                return "audio";
            case "pdf":
            case "doc":
            case "docx":
            case "txt":
                return "document";
            default:
                return "other";
        }
    }

    /**
     * 智能检测文件类型（支持混合类型）
     */
    private Integer detectDominantFileType(MultipartFile[] files) {
        int imageCount = 0;
        int videoCount = 0;

        for (MultipartFile file : files) {
            String filename = file.getOriginalFilename();
            if (filename == null) continue;

            String extension = "";
            int lastDotIndex = filename.lastIndexOf(".");
            if (lastDotIndex > 0) {
                extension = filename.substring(lastDotIndex + 1).toLowerCase();
            }

            String fileType = getFileType(extension);
            if ("image".equals(fileType)) {
                imageCount++;
            } else if ("video".equals(fileType)) {
                videoCount++;
            }
        }

        // 返回混合类型标识
        if (imageCount > 0 && videoCount > 0) {
            return 2; // 混合类型
        } else if (videoCount > 0) {
            return 1; // 视频类型
        } else {
            return 0; // 图片类型（默认）
        }
    }

    /**
     * 根据文件扩展名获取MIME类型
     */
    private String getMimeType(String extension) {
        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "webp":
                return "image/webp";
            case "mp4":
                return "video/mp4";
            case "avi":
                return "video/avi";
            case "mov":
                return "video/quicktime";
            case "mp3":
                return "audio/mpeg";
            case "wav":
                return "audio/wav";
            case "pdf":
                return "application/pdf";
            case "txt":
                return "text/plain";
            default:
                return "application/octet-stream";
        }
    }

}
