<template>
  <view class="system-config-page">
    <!-- 页面头部 -->
    <up-navbar
      title="系统配置管理"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    >
      <template #right>
        <up-button
          text="新增配置"
          type="primary"
          size="mini"
          @click="handleAddConfig"
        ></up-button>
      </template>
    </up-navbar>

    <!-- 搜索和筛选 -->
    <view class="search-section" :style="{ marginTop: mainContentPaddingTop }">
      <up-search
        v-model="searchKeyword"
        placeholder="搜索配置键或描述"
        @search="handleSearch"
        @clear="handleClearSearch"
        :showAction="false"
      ></up-search>
      
      <view class="filter-section">
        <view class="filter-actions">
          <!-- 分组筛选 -->
          <view class="dropdown-container">
            <view class="custom-dropdown" @click="toggleGroupDropdown">
              <text class="dropdown-text">{{ getCurrentGroupLabel() }}</text>
              <up-icon
                :name="groupDropdownVisible ? 'arrow-up' : 'arrow-down'"
                size="14"
                color="#999"
                class="dropdown-arrow"
              ></up-icon>
            </view>
            <view v-if="groupDropdownVisible" class="dropdown-menu">
              <view
                v-for="option in groupOptions"
                :key="option.value"
                class="dropdown-item"
                :class="{ 'dropdown-item-active': selectedGroup === option.value }"
                @click="selectGroup(option.value)"
              >
                <text class="dropdown-item-text">{{ option.label }}</text>
                <text v-if="selectedGroup === option.value" class="dropdown-check">✓</text>
              </view>
            </view>
          </view>

          <!-- 类型筛选 -->
          <view class="dropdown-container">
            <view class="custom-dropdown" @click="toggleTypeDropdown">
              <text class="dropdown-text">{{ getCurrentTypeLabel() }}</text>
              <up-icon
                :name="typeDropdownVisible ? 'arrow-up' : 'arrow-down'"
                size="14"
                color="#999"
                class="dropdown-arrow"
              ></up-icon>
            </view>
            <view v-if="typeDropdownVisible" class="dropdown-menu">
              <view
                v-for="option in typeOptions"
                :key="option.value"
                class="dropdown-item"
                :class="{ 'dropdown-item-active': selectedType === option.value }"
                @click="selectType(option.value)"
              >
                <text class="dropdown-item-text">{{ option.label }}</text>
                <text v-if="selectedType === option.value" class="dropdown-check">✓</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 配置列表 -->
    <view class="config-list">
      <view 
        class="config-item" 
        v-for="config in configList" 
        :key="config.id"
        @click="handleEditConfig(config)"
      >
        <view class="config-header">
          <view class="config-key">{{ config.configKey }}</view>
          <view class="config-badges">
            <u-tag v-if="config.isSystem" text="系统" type="success" plain size="mini"></u-tag>
            <up-tag
              :text="getTypeLabel(config.configType)"
              :type="getTagType(config.configType)"
              size="mini"
            ></up-tag>
          </view>
        </view>
        
        <view class="config-value">{{ formatConfigValue(config) }}</view>
        
        <view class="config-info">
          <text class="config-description" v-if="config.description">{{ config.description }}</text>
          <view class="config-meta">
            <text class="meta-item">分组: {{ config.groupName }}</text>
            <text class="meta-item">排序: {{ config.sortOrder }}</text>
          </view>
        </view>
        
        <view class="config-actions" @click.stop>
          <up-button
            text="编辑"
            type="primary"
            size="mini"
            plain
            @click="handleEditConfig(config)"
          ></up-button>
          <up-button
            v-if="!config.isSystem"
            text="删除"
            type="error"
            size="mini"
            plain
            @click="handleDeleteConfig(config)"
          ></up-button>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="configList.length === 0 && !loading" class="empty-state">
        <up-empty mode="data" text="暂无配置数据"></up-empty>
      </view>
    </view>

    <!-- 加载更多 -->
    <up-loadmore
      v-if="configList.length > 0"
      :status="loadmoreStatus"
      @loadmore="handleLoadMore"
    ></up-loadmore>

    <!-- 配置编辑弹窗 -->
    <up-popup
      v-model:show="showEditModal"
      mode="center"
      :round="10"
      :closeable="true"
      :safeAreaInsetBottom="false"
    >
      <view class="edit-modal">
        <view class="modal-header">
          <text class="modal-title">{{ editingConfig.id ? '编辑配置' : '新增配置' }}</text>
        </view>
        
        <view class="modal-content">
          <up-form ref="formRef" :model="editingConfig" :rules="formRules">
            <up-form-item label="配置键" prop="configKey" required>
              <up-input
                v-model="editingConfig.configKey"
                placeholder="请输入配置键"
                :disabled="!!editingConfig.id"
              ></up-input>
            </up-form-item>
            
            <up-form-item label="配置值" prop="configValue" required>
              <up-textarea
                v-model="editingConfig.configValue"
                placeholder="请输入配置值"
                :maxlength="1000"
                count
              ></up-textarea>
            </up-form-item>
            
            <up-form-item label="配置类型" prop="configType" required>
              <up-radio-group v-model="editingConfig.configType" placement="row">
                <up-radio
                  v-for="type in configTypeOptions"
                  :key="type.value"
                  :label="type.value"
                  :name="type.value"
                >
                  {{ type.label }}
                </up-radio>
              </up-radio-group>
            </up-form-item>
            
            <up-form-item label="配置分组" prop="groupName" required>
              <up-input
                v-model="editingConfig.groupName"
                placeholder="请输入配置分组"
              ></up-input>
            </up-form-item>
            
            <up-form-item label="配置描述" prop="description">
              <up-textarea
                v-model="editingConfig.description"
                placeholder="请输入配置描述"
                :maxlength="255"
                count
              ></up-textarea>
            </up-form-item>
            
            <up-form-item label="排序" prop="sortOrder">
              <up-number-box
                v-model="editingConfig.sortOrder"
                :min="0"
                :max="9999"
              ></up-number-box>
            </up-form-item>
            
            <up-form-item label="系统配置" prop="isSystem" v-if="!editingConfig.id">
              <up-switch v-model="editingConfig.isSystem"></up-switch>
            </up-form-item>
          </up-form>
        </view>
        
        <view class="modal-actions">
          <up-button
            text="取消"
            @click="handleCancelEdit"
          ></up-button>
          <up-button
            text="保存"
            type="primary"
            @click="handleSaveConfig"
            :loading="saving"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { useSafeArea } from '@/utils/safeArea'
import {
  getSystemConfigPageAPI,
  getAllGroupsAPI,
  addSystemConfigAPI,
  updateSystemConfigAPI,
  deleteSystemConfigAPI,
  CONFIG_TYPE_OPTIONS,
  type SystemConfigPageParams
} from '@/services/systemConfig'
import type { SystemConfig } from '@/types/admin'

const { mainContentPaddingTop } = useSafeArea()

// 搜索和筛选
const searchKeyword = ref('')
const selectedGroup = ref('')
const selectedType = ref('')
const groupDropdownVisible = ref(false)
const typeDropdownVisible = ref(false)
const groupOptions = ref<Array<{ label: string; value: string }>>([
  { label: '全部分组', value: '' }
])
const typeOptions = ref<Array<{ label: string; value: string }>>([
  { label: '全部类型', value: '' },
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '布尔值', value: 'boolean' },
  { label: 'JSON', value: 'json' }
])

// 列表数据
const configList = ref<SystemConfig[]>([])
const loading = ref(false)
const loadmoreStatus = ref<'loadmore' | 'loading' | 'nomore'>('loadmore')

// 分页参数
const pageParams = reactive<SystemConfigPageParams & { configType?: string }>({
  page: 1,
  pageSize: 10,
  groupName: '',
  keyword: '',
  configType: ''
})

// 编辑相关
const showEditModal = ref(false)
const saving = ref(false)
const editingConfig = ref<Partial<SystemConfig>>({})
const formRef = ref()

// 配置类型选项
const configTypeOptions = CONFIG_TYPE_OPTIONS

// 表单验证规则
const formRules = {
  configKey: [
    { required: true, message: '请输入配置键', trigger: 'blur' }
  ],
  configValue: [
    { required: true, message: '请输入配置值', trigger: 'blur' }
  ],
  configType: [
    { required: true, message: '请选择配置类型', trigger: 'change' }
  ],
  groupName: [
    { required: true, message: '请输入配置分组', trigger: 'blur' }
  ]
}

// 初始化数据
const initData = async () => {
  await Promise.all([
    loadConfigList(true),
    loadGroups()
  ])
}

// 加载配置列表
const loadConfigList = async (reset = false) => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    if (reset) {
      pageParams.page = 1
      configList.value = []
    }
    
    const res = await getSystemConfigPageAPI(pageParams)
    if (res.success && res.data) {
      if (reset) {
        configList.value = res.data.records
      } else {
        configList.value.push(...res.data.records)
      }
      
      // 更新加载状态
      if (res.data.records.length < pageParams.pageSize) {
        loadmoreStatus.value = 'nomore'
      } else {
        loadmoreStatus.value = 'loadmore'
        pageParams.page++
      }
    }
  } catch (error) {
    console.error('加载配置列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载分组列表
const loadGroups = async () => {
  try {
    const res = await getAllGroupsAPI()
    if (res.success && res.data) {
      groupOptions.value = [
        { label: '全部分组', value: '' },
        ...res.data.map(group => ({ label: group, value: group }))
      ]
    }
  } catch (error) {
    console.error('加载分组列表失败:', error)
  }
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  const dropdowns = document.querySelectorAll('.dropdown-container')
  let clickedInside = false

  dropdowns.forEach(dropdown => {
    if (dropdown.contains(target)) {
      clickedInside = true
    }
  })

  if (!clickedInside) {
    groupDropdownVisible.value = false
    typeDropdownVisible.value = false
  }
}

onMounted(() => {
  initData()
  // 添加点击外部关闭下拉菜单的事件监听
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 搜索
const handleSearch = () => {
  pageParams.keyword = searchKeyword.value
  loadConfigList(true)
}

// 清除搜索
const handleClearSearch = () => {
  searchKeyword.value = ''
  pageParams.keyword = ''
  loadConfigList(true)
}

// 获取当前分组标签
const getCurrentGroupLabel = () => {
  const option = groupOptions.value.find(opt => opt.value === selectedGroup.value)
  return option ? option.label : '全部分组'
}

// 获取当前类型标签
const getCurrentTypeLabel = () => {
  const option = typeOptions.value.find(opt => opt.value === selectedType.value)
  return option ? option.label : '全部类型'
}

// 切换分组下拉菜单
const toggleGroupDropdown = () => {
  groupDropdownVisible.value = !groupDropdownVisible.value
  if (groupDropdownVisible.value) {
    typeDropdownVisible.value = false
  }
}

// 切换类型下拉菜单
const toggleTypeDropdown = () => {
  typeDropdownVisible.value = !typeDropdownVisible.value
  if (typeDropdownVisible.value) {
    groupDropdownVisible.value = false
  }
}

// 选择分组
const selectGroup = (value: string) => {
  selectedGroup.value = value
  pageParams.groupName = value
  groupDropdownVisible.value = false
  loadConfigList(true)
}

// 选择类型
const selectType = (value: string) => {
  selectedType.value = value
  pageParams.configType = value
  typeDropdownVisible.value = false
  loadConfigList(true)
}



// 加载更多
const handleLoadMore = () => {
  if (loadmoreStatus.value === 'loadmore') {
    loadmoreStatus.value = 'loading'
    loadConfigList()
  }
}

// 获取类型标签
const getTypeLabel = (type: string) => {
  const option = configTypeOptions.find(opt => opt.value === type)
  return option?.label || type
}

// 获取标签类型
const getTagType = (configType: string) => {
  switch (configType) {
    case 'string':
      return 'primary'
    case 'number':
      return 'warning'
    case 'boolean':
      return 'success'
    case 'json':
      return 'info'
    default:
      return 'primary'
  }
}

// 格式化配置值显示
const formatConfigValue = (config: SystemConfig) => {
  if (!config.configValue) return '(空)'
  
  if (config.configType === 'boolean') {
    return config.configValue === 'true' ? '是' : '否'
  }
  
  if (config.configValue.length > 50) {
    return config.configValue.substring(0, 50) + '...'
  }
  
  return config.configValue
}

// 新增配置
const handleAddConfig = () => {
  editingConfig.value = {
    configKey: '',
    configValue: '',
    configType: 'string',
    groupName: 'basic',
    description: '',
    sortOrder: 0,
    isSystem: false
  }
  showEditModal.value = true
}

// 编辑配置
const handleEditConfig = (config: SystemConfig) => {
  editingConfig.value = { ...config }
  showEditModal.value = true
}

// 取消编辑
const handleCancelEdit = () => {
  showEditModal.value = false
  editingConfig.value = {}
}

// 保存配置
const handleSaveConfig = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch (error) {
    return
  }
  
  saving.value = true
  
  try {
    let success = false
    
    if (editingConfig.value.id) {
      // 更新配置
      const res = await updateSystemConfigAPI(editingConfig.value.id, editingConfig.value)
      success = res.success
    } else {
      // 新增配置
      const res = await addSystemConfigAPI(editingConfig.value as Omit<SystemConfig, 'id'>)
      success = res.success
    }
    
    if (success) {
      uni.showToast({
        title: editingConfig.value.id ? '更新成功' : '添加成功',
        icon: 'success'
      })
      showEditModal.value = false
      loadConfigList(true)
      loadGroups() // 重新加载分组列表
    } else {
      uni.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  } catch (error: any) {
    console.error('保存配置失败:', error)
    uni.showToast({
      title: error.message || '保存失败',
      icon: 'none'
    })
  } finally {
    saving.value = false
  }
}

// 删除配置
const handleDeleteConfig = (config: SystemConfig) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除配置"${config.configKey}"吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await deleteSystemConfigAPI(config.id)
          if (result.success) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
            loadConfigList(true)
          } else {
            uni.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }
        } catch (error: any) {
          console.error('删除配置失败:', error)
          uni.showToast({
            title: error.message || '删除失败',
            icon: 'none'
          })
        }
      }
    }
  })
}
</script>

<style lang="scss">
.system-config-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  // 搜索和筛选区域
  .search-section {
    background: #fff;
    padding: 15px;
    margin-bottom: 10px;

    .filter-section {
      margin-top: 10px;

      .filter-actions {
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
      }
    }
  }

  // 下拉菜单样式
  .dropdown-container {
    min-width: 0;
    position: relative;
  }

  .custom-dropdown {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 4px;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 32px;
  }

  .dropdown-text {
    font-size: 14px;
    color: #666;
    font-weight: normal;
  }

  .dropdown-arrow {
    margin-left: 6px;
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: -4px;
    right: -4px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    z-index: 1000;
    margin-top: 4px;
    overflow: hidden;
    min-width: 120px;
  }

  .dropdown-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    cursor: pointer;
    transition: background-color 0.15s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    &.dropdown-item-active {
      background-color: #f0f7ff;

      &:hover {
        background-color: #e6f3ff;
      }
    }
  }

  .dropdown-item-text {
    font-size: 14px;
    color: #333;
  }

  .dropdown-item-active .dropdown-item-text {
    color: #1890ff;
    font-weight: 500;
  }

  .dropdown-check {
    font-size: 12px;
    color: #1890ff;
    font-weight: bold;
  }

  // 配置列表
  .config-list {
    .config-item {
      background: #fff;
      margin-bottom: 10px;
      padding: 15px;
      border-radius: 8px;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }

      .config-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .config-key {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          flex: 1;
        }
        .config-badges{
          display: flex;
          gap:5px;
        }
      }

      .config-value {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        word-break: break-all;
        line-height: 1.4;
      }

      .config-info {
        .config-description {
          font-size: 13px;
          color: #999;
          display: block;
          margin-bottom: 6px;
          line-height: 1.3;
        }

        .config-meta {
          display: flex;
          gap: 15px;

          .meta-item {
            font-size: 12px;
            color: #ccc;
          }
        }
      }

      .config-actions {
        margin-top: 10px;
        display: flex;
        gap: 8px;
        justify-content: flex-end;
      }
    }

    .empty-state {
      padding: 40px 20px;
      text-align: center;
    }
  }

  // 编辑弹窗
  .edit-modal {
    width: 90vw;
    max-width: 500px;
    max-height: 80vh;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .modal-header {
      padding: 20px 20px 0;
      text-align: center;

      .modal-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }

    .modal-content {
      padding: 20px;
      max-height: 60vh;
      overflow-y: auto;

      :deep(.u-form-item) {
        margin-bottom: 20px;

        .u-form-item__label {
          font-size: 14px;
          color: #333;
          margin-bottom: 8px;
        }
      }

      :deep(.u-radio-group) {
        .u-radio {
          margin-right: 15px;
          margin-bottom: 8px;
        }
      }
    }

    .modal-actions {
      padding: 0 20px 20px;
      display: flex;
      gap: 10px;

      .u-button {
        flex: 1;
      }
    }
  }

  // 响应式优化
  @media (max-width: 375px) {
    .filter-actions {
      padding: 8px 12px;
      gap: 8px;
    }

    .custom-dropdown {
      padding: 6px 2px;
      min-height: 30px;
    }

    .dropdown-text {
      font-size: 13px;
    }

    .dropdown-arrow {
      margin-left: 4px;
    }

    .dropdown-item {
      padding: 8px 10px;
    }

    .dropdown-item-text {
      font-size: 13px;
    }
  }

  // 确保下拉菜单在小屏幕上也能正常显示
  @media (max-width: 320px) {
    .filter-actions {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
    }

    .dropdown-container {
      width: 100%;
    }

    .dropdown-menu {
      left: 0;
      right: 0;
    }
  }
}
</style>
