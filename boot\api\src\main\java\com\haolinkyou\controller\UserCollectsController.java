package com.haolinkyou.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.service.IUserCollectsService;
import com.haolinkyou.service.IPointsService;
import com.haolinkyou.vo.UserCollectVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户收藏控制器
 */
@RestController
@RequestMapping("/api/user-collects")
public class UserCollectsController {
    
    @Autowired
    private IUserCollectsService userCollectsService;

    @Autowired
    private IPointsService pointsService;
    
    /**
     * 收藏或取消收藏
     * @param postId 帖子ID
     * @param request HTTP请求（从中获取用户ID）
     * @return 操作结果
     */
    @PostMapping("/toggle")
    public Result<Map<String, Object>> toggleCollect(@RequestParam Long postId, javax.servlet.http.HttpServletRequest request) {
        try {
            System.out.println("收藏/取消收藏请求 - 帖子ID: " + postId);

            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                System.out.println("用户未登录，无法操作");
                return Result.error("用户未登录");
            }

            System.out.println("用户ID: " + userId + " 正在操作帖子ID: " + postId);

            // 获取操作前的状态
            boolean beforeStatus = userCollectsService.isUserCollected(postId, userId);
            System.out.println("操作前收藏状态: " + beforeStatus);

            // 执行切换操作
            boolean isCollected = userCollectsService.toggleCollect(postId, userId);
            System.out.println("操作后收藏状态: " + isCollected);

            // 如果是收藏操作，给用户增加积分
            if (isCollected) {
                try {
                    IPointsService.PointsRules rules = pointsService.getPointsRules();
                    // 收藏积分奖励（从配置中获取）
                    boolean pointsAdded = pointsService.addPoints(userId, rules.getCollectGive(), "collect", "收藏帖子", postId);
                    if (!pointsAdded) {
                        System.out.println("收藏积分奖励发放失败（可能已达每日上限）");
                    }
                } catch (Exception e) {
                    System.out.println("收藏积分奖励发放异常: " + e.getMessage());
                    // 积分发放失败不影响收藏流程
                }
            }

            // 获取最新收藏数
            int collectCount = userCollectsService.getPostCollectCount(postId);
            System.out.println("最新收藏数: " + collectCount);

            // 再次检查状态，确保一致性
            boolean afterStatus = userCollectsService.isUserCollected(postId, userId);
            System.out.println("再次检查收藏状态: " + afterStatus);

            // 如果状态不一致，记录警告
            if (isCollected != afterStatus) {
                System.out.println("警告：状态不一致，返回值: " + isCollected + ", 实际状态: " + afterStatus);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("isCollected", isCollected);
            result.put("collectCount", collectCount);
            result.put("message", isCollected ? "收藏成功" : "取消收藏成功");

            System.out.println("返回结果: " + result);
            return Result.success(result);
        } catch (Exception e) {
            System.out.println("收藏/取消收藏操作失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * 检查用户是否已收藏该帖子
     * @param postId 帖子ID
     * @param request HTTP请求（从中获取用户ID）
     * @return 收藏状态
     */
    @GetMapping("/check")
    public Result<Map<String, Object>> checkUserCollected(@RequestParam Long postId, javax.servlet.http.HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            boolean isCollected = userCollectsService.isUserCollected(postId, userId);
            int collectCount = userCollectsService.getPostCollectCount(postId);

            Map<String, Object> result = new HashMap<>();
            result.put("isCollected", isCollected);
            result.put("collectCount", collectCount);

            return Result.success(result);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取帖子的收藏数
     * @param postId 帖子ID
     * @return 收藏数
     */
    @GetMapping("/count")
    public Result<Integer> getPostCollectCount(@RequestParam Long postId) {
        try {
            int collectCount = userCollectsService.getPostCollectCount(postId);
            return Result.success(collectCount);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户收藏列表
     * @param page 页码
     * @param pageSize 每页大小
     * @param categoryId 分类ID（可选）
     * @param sortBy 排序方式（time-收藏时间，create-发布时间，hot-热度）
     * @param request HTTP请求（从中获取用户ID）
     * @return 收藏列表
     */
    @GetMapping("/my")
    public Result<Map<String, Object>> getMyCollections(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String categoryId,
            @RequestParam(defaultValue = "time") String sortBy,
            HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            System.out.println("获取用户收藏列表 - 用户ID: " + userId + ", 页码: " + page + ", 每页: " + pageSize + ", 分类: " + categoryId + ", 排序: " + sortBy);

            // 调用服务获取收藏列表
            IPage<UserCollectVo> collectPage = userCollectsService.getUserCollections(userId, page, pageSize, categoryId, sortBy);

            // 获取统计数据
            Map<String, Object> stats = userCollectsService.getUserCollectStats(userId);

            Map<String, Object> result = new HashMap<>();
            result.put("records", collectPage.getRecords());
            result.put("total", collectPage.getTotal());
            result.put("current", collectPage.getCurrent());
            result.put("pages", collectPage.getPages());
            result.put("size", collectPage.getSize());
            result.put("todayCount", stats.get("todayCount"));
            result.put("monthCount", stats.get("monthCount"));

            return Result.success(result);
        } catch (Exception e) {
            System.err.println("获取收藏列表失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("获取收藏列表失败：" + e.getMessage());
        }
    }
}
