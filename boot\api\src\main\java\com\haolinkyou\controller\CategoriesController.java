package com.haolinkyou.controller;

import com.haolinkyou.api.vo.PostsListVo;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.Categories;
import com.haolinkyou.service.ICategoriesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
@RestController
@RequestMapping("/api/categories")
public class CategoriesController {

    @Autowired
    private ICategoriesService categoriesService;

    /**
     * 获取分类列表 - 新接口
     */
    @GetMapping
    public Result getCategories() {
        List<Categories> list = categoriesService.selectList();
        return Result.success(list);
    }

    /**
     * 获取分类列表 - 兼容旧接口
     */
    @RequestMapping("/list")
    public Result selectList() {
        List<Categories> list = categoriesService.selectList();
        return Result.success(list);
    }
}
