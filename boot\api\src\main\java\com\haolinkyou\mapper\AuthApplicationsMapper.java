package com.haolinkyou.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haolinkyou.entity.AuthApplications;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface AuthApplicationsMapper extends BaseMapper<AuthApplications> {

    /**
     * 根据用户ID获取认证申请记录
     * @param userId 用户ID
     * @return 认证申请记录
     */
    @Select("SELECT * FROM auth_applications WHERE user_id = #{userId} AND del_flag = 0 ORDER BY created_time DESC LIMIT 1")
    AuthApplications getByUserId(Long userId);

    /**
     * 检查用户是否已有认证申请记录
     * @param userId 用户ID
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM auth_applications WHERE user_id = #{userId} AND del_flag = 0")
    int countByUserId(Long userId);

    /**
     * 获取原始的 documents 字段数据（用于调试）
     * @param userId 用户ID
     * @return documents 字段的原始字符串
     */
    @Select("SELECT documents FROM auth_applications WHERE user_id = #{userId} AND del_flag = 0 ORDER BY created_time DESC LIMIT 1")
    String getRawDocumentsByUserId(Long userId);
}
