package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("survey_answers")
public class SurveyAnswers {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long surveyId;
    
    private Long userId;
    
    private String answers; // JSON格式存储答案数据
    
    private Boolean isAnonymous;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
    
    @TableLogic
    private Integer delFlag;
}