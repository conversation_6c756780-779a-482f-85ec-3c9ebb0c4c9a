package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@TableName("auth_applications")
@EqualsAndHashCode(callSuper = true)
public class AuthApplications extends BaseEntity {

    @TableField("user_id")
    private Long userId;

    @TableField("real_name")
    private String realName;

    private String phone;

    @TableField("identity_type")
    private String identityType;

    @TableField("house_number")
    private String houseNumber;

    @TableField(value = "documents", typeHandler = com.haolinkyou.config.ListStringTypeHandler.class)
    private List<String> documents;

    private String remark;

    private Integer status; // 0-待审核 1-已通过 2-已拒绝

    @TableField("review_time")
    private Date reviewTime;

    @TableField("review_user_id")
    private Long reviewUserId;

    @TableField("review_note")
    private String reviewNote;
}
