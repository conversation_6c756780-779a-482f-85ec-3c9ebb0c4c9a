<template>
  <view class="mall-page">
    <!-- 页面头部 -->
    <view class="page-header" :style="{ paddingTop: pageHeaderPaddingTop }">
      <view class="header-content">
        <up-icon name="arrow-left" size="20" color="#333" @click="goBack"></up-icon>
        <text class="page-title">积分商城</text>
        <view class="header-actions">
          <up-icon name="list" size="18" color="#666" @click="goToRedemptions"></up-icon>
        </view>
      </view>
    </view>

    <!-- 用户积分信息 -->
    <view class="points-info">
      <view class="points-card">
        <view class="points-content">
          <text class="points-label">我的积分</text>
          <text class="points-value">{{ userPoints }}</text>
        </view>
        <view class="points-icon">
          <up-icon name="gift-fill" color="#ff6b35" size="32"></up-icon>
        </view>
      </view>
    </view>

    <!-- 商品分类 -->
    <view class="category-tabs">
      <scroll-view scroll-x class="category-scroll">
        <view class="category-list">
          <view 
            v-for="(category, index) in categories" 
            :key="index"
            class="category-item"
            :class="{ active: currentCategory === index }"
            @click="switchCategory(index)"
          >
            <text class="category-text">{{ category }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 商品列表 -->
    <view class="products-section">
      <view v-if="productList.length === 0 && !loading" class="empty-state">
        <up-empty
          mode="data"
          text="暂无商品"
          textSize="14"
        ></up-empty>
      </view>

      <view class="products-grid">
        <view 
          v-for="product in productList" 
          :key="product.id" 
          class="product-card"
          @click="goToProductDetail(product)"
        >
          <view class="product-image">
            <up-image
              :src="product.imageUrl || '/static/images/default-product.png'"
              width="100%"
              height="120px"
              mode="aspectFill"
              :fade="true"
              :loading="true"
            ></up-image>
            <view v-if="product.stock <= 0" class="sold-out-mask">
              <text class="sold-out-text">已售罄</text>
            </view>
          </view>
          
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-desc">{{ product.description }}</text>
            
            <view class="product-footer">
              <view class="product-price">
                <up-icon name="gift" color="#ff6b35" size="14"></up-icon>
                <text class="price-text">{{ product.points }}积分</text>
              </view>
              
              <view class="product-stock">
                <text class="stock-text">库存{{ product.stock }}</text>
              </view>
            </view>
            
            <up-button
              :text="product.stock > 0 ? '立即兑换' : '已售罄'"
              type="primary"
              size="mini"
              :disabled="product.stock <= 0 || userPoints < product.points"
              :customStyle="getButtonStyle(product)"
              @click.stop="handleRedeem(product)"
            ></up-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <up-loadmore
      :status="loadStatus"
      :loading-text="loadingText"
      :loadmore-text="loadmoreText"
      :nomore-text="nomoreText"
      @loadmore="loadMore"
    />

    <!-- 兑换确认弹窗 -->
    <up-popup
      v-model:show="showRedeemModal"
      mode="center"
      :round="10"
      :closeable="true"
    >
      <view class="redeem-modal">
        <view class="modal-header">
          <text class="modal-title">确认兑换</text>
        </view>
        
        <view class="modal-content" v-if="selectedProduct">
          <view class="product-preview">
            <up-image
              :src="selectedProduct.imageUrl || '/static/images/default-product.png'"
              width="60px"
              height="60px"
              mode="aspectFill"
              :fade="true"
            ></up-image>
            <view class="preview-info">
              <text class="preview-name">{{ selectedProduct.name }}</text>
              <text class="preview-points">{{ selectedProduct.points }}积分</text>
            </view>
          </view>
          
          <view class="form-section">
            <view class="form-item">
              <text class="form-label">兑换数量</text>
              <view class="quantity-control">
                <up-icon 
                  name="minus-circle" 
                  size="24" 
                  color="#ddd"
                  :class="{ disabled: redeemQuantity <= 1 }"
                  @click="decreaseQuantity"
                ></up-icon>
                <text class="quantity-text">{{ redeemQuantity }}</text>
                <up-icon 
                  name="plus-circle" 
                  size="24" 
                  color="#5677fc"
                  :class="{ disabled: redeemQuantity >= selectedProduct.stock }"
                  @click="increaseQuantity"
                ></up-icon>
              </view>
            </view>
            
            <view class="form-item">
              <text class="form-label">收货地址</text>
              <up-textarea
                v-model="redeemForm.shippingAddress"
                placeholder="请输入收货地址"
                :maxlength="200"
                height="60px"
              ></up-textarea>
            </view>
            
            <view class="form-item">
              <text class="form-label">联系电话</text>
              <up-input
                v-model="redeemForm.contactPhone"
                placeholder="请输入联系电话"
                :maxlength="20"
              ></up-input>
            </view>
            
            <view class="form-item">
              <text class="form-label">备注</text>
              <up-textarea
                v-model="redeemForm.remark"
                placeholder="选填"
                :maxlength="100"
                height="40px"
              ></up-textarea>
            </view>
          </view>
          
          <view class="total-info">
            <text class="total-text">总计：{{ selectedProduct.points * redeemQuantity }}积分</text>
          </view>
        </view>
        
        <view class="modal-actions">
          <up-button
            text="取消"
            type="info"
            size="normal"
            :customStyle="{ marginRight: '12px', flex: 1 }"
            @click="closeRedeemModal"
          ></up-button>
          <up-button
            text="确认兑换"
            type="primary"
            size="normal"
            :loading="redeeming"
            :customStyle="{ flex: 1 }"
            @click="confirmRedeem"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useMemberStore } from '@/stores'
import { get, post } from '@/utils/http'
import { useSafeArea } from '@/utils/safeArea'

const { pageHeaderPaddingTop } = useSafeArea()
const memberStore = useMemberStore()

// 用户积分
const userPoints = ref(0)

// 商品分类
const categories = ref(['全部', '生活用品', '电子产品', '食品饮料', '其他'])
const currentCategory = ref(0)

// 商品列表
const productList = ref<any[]>([])
const loading = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)
const loadStatus = ref('loadmore')
const loadingText = ref('正在加载...')
const loadmoreText = ref('上拉加载更多')
const nomoreText = ref('已经到底了')

// 兑换相关
const showRedeemModal = ref(false)
const selectedProduct = ref<any>(null)
const redeemQuantity = ref(1)
const redeeming = ref(false)
const redeemForm = reactive({
  shippingAddress: '',
  contactPhone: '',
  remark: ''
})

// 获取用户积分
const fetchUserPoints = async () => {
  try {
    const res = await get('/points/balance')
    if (res.success) {
      userPoints.value = res.data || 0
    }
  } catch (error) {
    console.error('获取用户积分失败:', error)
  }
}

// 获取商品列表
const fetchProductList = async (isRefresh = false) => {
  if (isRefresh) {
    currentPage.value = 1
    hasMore.value = true
    loadStatus.value = 'loading'
  }

  loading.value = true

  try {
    const res = await get('/products/list')

    if (res.success) {
      const newProducts = res.data || []

      if (isRefresh) {
        productList.value = newProducts
      } else {
        productList.value.push(...newProducts)
      }

      // 判断是否还有更多数据
      if (newProducts.length < pageSize.value) {
        hasMore.value = false
        loadStatus.value = 'nomore'
      } else {
        loadStatus.value = 'loadmore'
      }
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 切换分类
const switchCategory = (index: number) => {
  currentCategory.value = index
  fetchProductList(true)
}

// 加载更多
const loadMore = () => {
  if (!hasMore.value || loading.value) return
  
  currentPage.value++
  fetchProductList()
}

// 获取按钮样式
const getButtonStyle = (product: any) => {
  if (product.stock <= 0) {
    return { backgroundColor: '#ccc', borderColor: '#ccc' }
  }
  if (userPoints.value < product.points) {
    return { backgroundColor: '#ddd', borderColor: '#ddd' }
  }
  return { backgroundColor: '#5677fc', borderColor: '#5677fc' }
}

// 处理兑换
const handleRedeem = (product: any) => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  if (product.stock <= 0) {
    uni.showToast({
      title: '商品已售罄',
      icon: 'none'
    })
    return
  }

  if (userPoints.value < product.points) {
    uni.showToast({
      title: '积分不足',
      icon: 'none'
    })
    return
  }

  selectedProduct.value = product
  redeemQuantity.value = 1
  redeemForm.shippingAddress = ''
  redeemForm.contactPhone = ''
  redeemForm.remark = ''
  showRedeemModal.value = true
}

// 增加数量
const increaseQuantity = () => {
  if (selectedProduct.value && redeemQuantity.value < selectedProduct.value.stock) {
    redeemQuantity.value++
  }
}

// 减少数量
const decreaseQuantity = () => {
  if (redeemQuantity.value > 1) {
    redeemQuantity.value--
  }
}

// 确认兑换
const confirmRedeem = async () => {
  if (!redeemForm.shippingAddress.trim()) {
    uni.showToast({
      title: '请输入收货地址',
      icon: 'none'
    })
    return
  }

  if (!redeemForm.contactPhone.trim()) {
    uni.showToast({
      title: '请输入联系电话',
      icon: 'none'
    })
    return
  }

  redeeming.value = true

  try {
    const res = await post('/products/redeem', {
      productId: selectedProduct.value.id,
      quantity: redeemQuantity.value,
      shippingAddress: redeemForm.shippingAddress,
      contactPhone: redeemForm.contactPhone,
      remark: redeemForm.remark
    })

    if (res.success) {
      uni.showToast({
        title: '兑换成功',
        icon: 'success'
      })
      
      closeRedeemModal()
      fetchUserPoints()
      fetchProductList(true)
    } else {
      uni.showToast({
        title: res.message || '兑换失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('兑换失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    redeeming.value = false
  }
}

// 关闭兑换弹窗
const closeRedeemModal = () => {
  showRedeemModal.value = false
  selectedProduct.value = null
}

// 跳转到商品详情
const goToProductDetail = (product: any) => {
  // 这里可以实现商品详情页面
  console.log('查看商品详情:', product)
}

// 跳转到兑换记录
const goToRedemptions = () => {
  uni.navigateTo({
    url: '/pages/my/redemptions'
  })
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

onMounted(() => {
  fetchUserPoints()
  fetchProductList(true)
})
</script>

<style scoped lang="scss">
.mall-page {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  border-bottom: 1px solid #f0f0f0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  width: 20px;
  display: flex;
  justify-content: center;
}

.points-info {
  padding: 16px;
}

.points-card {
  background: linear-gradient(135deg, #5677fc 0%, #7c4dff 100%);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.points-content {
  color: white;
}

.points-label {
  font-size: 14px;
  opacity: 0.9;
  display: block;
  margin-bottom: 8px;
}

.points-value {
  font-size: 28px;
  font-weight: bold;
}

.category-tabs {
  background-color: white;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 0 16px;
  gap: 20px;
}

.category-item {
  flex-shrink: 0;
  padding: 8px 16px;
  border-radius: 20px;
  background-color: #f5f5f5;
  
  &.active {
    background-color: #5677fc;
    
    .category-text {
      color: white;
    }
  }
}

.category-text {
  font-size: 14px;
  color: #666;
}

.products-section {
  padding: 16px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.product-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-image {
  position: relative;
  width: 100%;
  height: 120px;
}

.sold-out-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sold-out-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.product-info {
  padding: 12px;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 4px;
}

.price-text {
  font-size: 14px;
  color: #ff6b35;
  font-weight: 600;
}

.product-stock {
  .stock-text {
    font-size: 10px;
    color: #999;
  }
}

.empty-state {
  padding: 40px 20px;
}

.redeem-modal {
  width: 320px;
  background: white;
  border-radius: 10px;
  overflow: hidden;
}

.modal-header {
  padding: 20px 20px 0;
  text-align: center;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.modal-content {
  padding: 20px;
}

.product-preview {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.preview-info {
  flex: 1;
}

.preview-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4px;
}

.preview-points {
  font-size: 12px;
  color: #ff6b35;
  font-weight: 600;
}

.form-section {
  margin-bottom: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 16px;
}

.quantity-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  min-width: 30px;
  text-align: center;
}

.total-info {
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

.total-text {
  font-size: 16px;
  color: #ff6b35;
  font-weight: 600;
}

.modal-actions {
  display: flex;
  padding: 0 20px 20px;
  gap: 12px;
}

.disabled {
  opacity: 0.3;
}
</style>
