<template>
  <!-- 权限验证加载中 -->
  <view v-if="permissionLoading" class="permission-loading">
    <view class="loading-content">
      <up-loading-icon mode="circle" size="24" color="#1890ff"></up-loading-icon>
      <text class="loading-text">权限验证中...</text>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="admin-page" v-else-if="hasPagePermission">
    <!-- 页面头部 -->
    <up-navbar
      title="管理后台"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 管理员信息 -->
    <view class="admin-info" :style="{ marginTop: mainContentPaddingTop }">
      <up-avatar :src="adminInfo.avatar" shape="circle" :size="50"></up-avatar>
      <view class="admin-details">
        <text class="admin-name">{{ adminInfo.name }}</text>
        <text class="admin-role">系统管理员</text>
      </view>
      <view class="admin-badge">
        <up-icon name="checkmark-circle-fill" color="#52c41a" size="16"></up-icon>
        <text class="badge-text">管理员</text>
      </view>
    </view>

    <!-- 统计概览 -->
    <view class="stats-overview">
      <view class="stats-title">数据概览</view>
      <view class="stats-grid">
        <view class="stats-card" @click="handleUserManagement">
          <view class="stats-number">{{ stats.totalUsers }}</view>
          <view class="stats-label">用户总数</view>
          <up-icon name="account-fill" color="#1890ff" size="20"></up-icon>
        </view>
        <view class="stats-card" @click="handlePostManagement">
          <view class="stats-number">{{ stats.totalPosts }}</view>
          <view class="stats-label">动态总数</view>
          <up-icon name="chat-fill" color="#52c41a" size="20"></up-icon>
        </view>
        <view class="stats-card" @click="handlePendingReviews">
          <view class="stats-number">{{ stats.pendingReviews }}</view>
          <view class="stats-label">待审核</view>
          <up-icon name="clock-fill" color="#faad14" size="20"></up-icon>
        </view>
        <view class="stats-card">
          <view class="stats-number">{{ stats.todayActive }}</view>
          <view class="stats-label">今日活跃</view>
          <up-icon name="integral" color="#ff4d4f" size="20"></up-icon>
        </view>
      </view>
    </view>

    <!-- 管理功能 -->
    <view class="management-section">
      <up-cell-group :border="false">
        <up-cell
          title="用户管理"
          isLink
          @click="handleUserManagement"
          :border="false"
        >
          <template #icon>
            <up-icon name="account" color="#1890ff" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="动态管理"
          isLink
          @click="handlePostManagement"
          :border="false"
        >
          <template #icon>
            <up-icon name="chat" color="#52c41a" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="认证管理"
          isLink
          @click="handleAuthManagement"
          :border="false"
        >
          <template #icon>
            <up-icon name="checkmark-circle" color="#faad14" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="建议反馈"
          isLink
          @click="handleFeedbackManagement"
          :border="false"
        >
          <template #icon>
            <up-icon name="edit-pen" color="#722ed1" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="举报处理"
          isLink
          @click="handleReportManagement"
          :border="false"
        >
          <template #icon>
            <up-icon name="thumb-down" color="#ff4d4f" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="初始化积分"
          isLink
          @click="handlePointsInit"
          :border="false"
        >
          <template #icon>
            <up-icon name="gift" color="#ff6b35" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="商品管理"
          isLink
          @click="handleProductManagement"
          :border="false"
        >
          <template #icon>
            <up-icon name="shopping-cart" color="#f5222d" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="分享图配置"
          isLink
          @click="handleShareConfig"
          :border="false"
        >
          <template #icon>
            <up-icon name="share" color="#13c2c2" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="群组管理"
          isLink
          @click="handleGroupManagement"
          :border="false"
        >
          <template #icon>
            <up-icon name="account-fill" color="#722ed1" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="店铺管理"
          isLink
          @click="handleStoreManagement"
          :border="false"
        >
          <template #icon>
            <up-icon name="home" color="#fa8c16" size="20"></up-icon>
          </template>
        </up-cell>

        <up-cell
          title="系统配置"
          isLink
          @click="handleSystemConfig"
          :border="false"
        >
          <template #icon>
            <up-icon name="setting" color="#1890ff" size="20"></up-icon>
          </template>
        </up-cell>
      </up-cell-group>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="section-title">快捷操作</view>
      <view class="action-grid">
        <view class="action-item" @click="handleDataExport">
          <up-icon name="download" color="#1890ff" size="24"></up-icon>
          <text class="action-text">数据导出</text>
        </view>
        <view class="action-item" @click="handleSystemLog">
          <up-icon name="list" color="#52c41a" size="24"></up-icon>
          <text class="action-text">系统日志</text>
        </view>
        <view class="action-item" @click="handleBackup">
          <up-icon name="folder" color="#faad14" size="24"></up-icon>
          <text class="action-text">数据备份</text>
        </view>
        <view class="action-item" @click="handleNotification">
          <up-icon name="bell" color="#ff4d4f" size="24"></up-icon>
          <text class="action-text">系统通知</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useMemberStore } from '@/stores';
import { useSafeArea } from '@/utils/safeArea';
import { getAdminStatsAPI, type AdminStatsData } from '@/services/userManagement';
import { checkPagePermission } from '@/utils/pagePermission';

const { mainContentPaddingTop } = useSafeArea();

// 权限验证状态
const permissionLoading = ref(true);
const hasPagePermission = ref(false);

// 管理员信息
const adminInfo = reactive({
  name: '系统管理员',
  avatar: '',
  role: 'admin'
});

// 统计数据
const stats = reactive<AdminStatsData>({
  totalUsers: 0,
  totalPosts: 0,
  pendingReviews: 0,
  todayActive: 0,
  pendingAuth: 0,
  feedbackCount: 0
});

const memberStore = useMemberStore();

// 初始化数据
const initData = async () => {
  const profile = memberStore.profile;
  if (profile) {
    adminInfo.name = profile.nickname || '系统管理员';
    adminInfo.avatar = profile.avatar || '';
  }

  try {
    // 获取管理员统计数据
    const res = await getAdminStatsAPI();
    if (res.success && res.data) {
      Object.assign(stats, res.data);
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    uni.showToast({
      title: '获取统计数据失败',
      icon: 'none'
    });
  }
};

// 页面权限检查和初始化
const initPageWithPermission = async () => {
  try {
    permissionLoading.value = true;

    // 检查页面权限
    const hasPermission = await checkPagePermission('/pages/admin/index');
    hasPagePermission.value = hasPermission;

    if (hasPermission) {
      // 权限验证通过，初始化页面数据
      await initData();
    }
  } catch (error) {
    console.error('页面权限检查失败:', error);
    hasPagePermission.value = false;
  } finally {
    permissionLoading.value = false;
  }
};

onMounted(() => {
  initPageWithPermission();
});

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 用户管理
const handleUserManagement = () => {
  uni.navigateTo({
    url: '/pages/admin/user-management'
  });
};

// 动态管理
const handlePostManagement = () => {
  uni.navigateTo({
    url: '/pages/admin/post-management'
  });
};

// 待审核管理
const handlePendingReviews = () => {
  uni.navigateTo({
    url: '/pages/admin/pending-reviews'
  });
};

// 认证管理
const handleAuthManagement = () => {
  uni.navigateTo({
    url: '/pages/admin/auth-management'
  });
};

// 建议反馈管理
const handleFeedbackManagement = () => {
  uni.navigateTo({
    url: '/pages/admin/feedback-management'
  });
};

// 举报处理
const handleReportManagement = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  });
};

// 初始化积分
const handlePointsInit = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  });
};

// 商品管理
const handleProductManagement = () => {
  uni.navigateTo({
    url: '/pages/admin/product-management'
  });
};

// 分享图配置
const handleShareConfig = () => {
  uni.navigateTo({
    url: '/pages/admin/share-config'
  });
};

// 群组管理
const handleGroupManagement = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  });
};

// 店铺管理
const handleStoreManagement = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  });
};

// 数据导出
const handleDataExport = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  });
};

// 系统日志
const handleSystemLog = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  });
};

// 数据备份
const handleBackup = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  });
};

// 系统通知
const handleNotification = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  });
};

// 系统配置管理
const handleSystemConfig = () => {
  uni.navigateTo({
    url: '/pages/admin/system-config'
  });
};
</script>

<style lang="scss">
// 权限验证加载状态
.permission-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .loading-text {
      font-size: 14px;
      color: #666;
    }
  }
}

.admin-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  // 管理员信息
  .admin-info {
    background: #fff;
    padding: 20px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .admin-details {
      flex: 1;
      margin-left: 15px;

      .admin-name {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 4px;
      }

      .admin-role {
        font-size: 14px;
        color: #666;
      }
    }

    .admin-badge {
      display: flex;
      align-items: center;
      background: #f6ffed;
      padding: 4px 8px;
      border-radius: 12px;

      .badge-text {
        font-size: 12px;
        color: #52c41a;
        margin-left: 4px;
      }
    }
  }

  // 统计概览
  .stats-overview {
    background: #fff;
    padding: 20px;
    margin-bottom: 10px;

    .stats-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 15px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;

      .stats-card {
        background: #fafafa;
        padding: 15px;
        border-radius: 8px;
        position: relative;
        cursor: pointer;

        .stats-number {
          font-size: 24px;
          font-weight: bold;
          color: #333;
          display: block;
          margin-bottom: 4px;
        }

        .stats-label {
          font-size: 12px;
          color: #666;
        }

        .u-icon {
          position: absolute;
          top: 15px;
          right: 15px;
        }

        &:active {
          opacity: 0.7;
        }
      }
    }
  }

  // 管理功能
  .management-section {
    background: #fff;
    margin-bottom: 10px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      padding: 20px 20px 10px;
    }

    :deep(.u-cell) {
      padding: 15px 20px;

      .u-cell__title {
        font-size: 16px;
        color: #333;
      }

      .u-cell__value {
        font-size: 14px;
        color: #999;
      }
    }
  }

  // 快捷操作
  .quick-actions {
    background: #fff;
    padding: 20px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 15px;
    }

    .action-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: 15px;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px 10px;
        background: #fafafa;
        border-radius: 8px;
        cursor: pointer;

        .action-text {
          font-size: 12px;
          color: #666;
          margin-top: 8px;
        }

        &:active {
          opacity: 0.7;
        }
      }
    }
  }
}
</style>
