<template>
  <view class="webview-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: safeAreaTop + 'px' }">
      <view class="navbar-content">
        <view class="navbar-left" @click="handleBack">
          <up-icon name="arrow-left" size="20" color="#333" />
        </view>
        <view class="navbar-title">
          <text class="title-text">{{ pageTitle || '网页浏览' }}</text>
        </view>
        <view class="navbar-right">
          <up-icon name="more-dot-fill" size="20" color="#333" @click="showActionSheet = true" />
        </view>
      </view>
    </view>

    <!-- WebView容器 -->
    <view class="webview-container">
      <web-view
        :src="webviewUrl"
        @message="handleMessage"
        @load="handleLoad"
        @error="handleError"
        class="webview"
      />
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-content">
        <up-loading-icon mode="spinner" size="24" color="#007bff" />
        <text class="loading-text">页面加载中...</text>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-if="error" class="error-overlay">
      <view class="error-content">
        <up-icon name="wifi-off" size="60" color="#999" />
        <text class="error-title">页面加载失败</text>
        <text class="error-message">{{ errorMessage }}</text>
        <up-button
          text="重新加载"
          type="primary"
          size="small"
          @click="reload"
        />
      </view>
    </view>

    <!-- 操作菜单 -->
    <up-action-sheet
      :show="showActionSheet"
      :actions="actionSheetActions"
      @close="showActionSheet = false"
      @select="handleActionSelect"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useSafeArea } from '@/utils/safeArea'

const { safeAreaTop } = useSafeArea()

// 页面参数
const webviewUrl = ref('')
const pageTitle = ref('')
const loading = ref(true)
const error = ref(false)
const errorMessage = ref('')
const showActionSheet = ref(false)

// 操作菜单
const actionSheetActions = [
  { name: '刷新页面' },
  { name: '复制链接' },
  { name: '在浏览器中打开' }
]

// 页面加载
onLoad((options: any) => {
  if (options.url) {
    webviewUrl.value = decodeURIComponent(options.url)
  }
  
  if (options.title) {
    pageTitle.value = decodeURIComponent(options.title)
  }
  
  // 验证URL
  if (!isValidUrl(webviewUrl.value)) {
    error.value = true
    errorMessage.value = '无效的网页地址'
    loading.value = false
  }
})

// 验证URL格式
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url)
    return url.startsWith('http://') || url.startsWith('https://')
  } catch {
    return false
  }
}

// 处理返回
const handleBack = () => {
  uni.navigateBack()
}

// 处理WebView消息
const handleMessage = (event: any) => {
  console.log('WebView消息:', event.detail)
  
  // 可以处理来自网页的消息
  const { data } = event.detail
  if (data && data.title) {
    pageTitle.value = data.title
  }
}

// 处理页面加载完成
const handleLoad = (event: any) => {
  console.log('WebView加载完成:', event.detail)
  loading.value = false
  error.value = false
  
  // 尝试获取页面标题
  if (!pageTitle.value) {
    // 从URL中提取域名作为标题
    try {
      const url = new URL(webviewUrl.value)
      pageTitle.value = url.hostname
    } catch {
      pageTitle.value = '网页浏览'
    }
  }
}

// 处理加载错误
const handleError = (event: any) => {
  console.error('WebView加载错误:', event.detail)
  loading.value = false
  error.value = true
  errorMessage.value = '网络连接失败或页面不存在'
}

// 重新加载
const reload = () => {
  loading.value = true
  error.value = false
  errorMessage.value = ''
  
  // 重新设置URL触发重新加载
  const currentUrl = webviewUrl.value
  webviewUrl.value = ''
  setTimeout(() => {
    webviewUrl.value = currentUrl
  }, 100)
}

// 处理操作菜单选择
const handleActionSelect = (action: { name: string }) => {
  showActionSheet.value = false
  
  switch (action.name) {
    case '刷新页面':
      reload()
      break
    case '复制链接':
      uni.setClipboardData({
        data: webviewUrl.value,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          })
        }
      })
      break
    case '在浏览器中打开':
      // 在小程序中无法直接打开浏览器，提示用户复制链接
      uni.showModal({
        title: '提示',
        content: '请复制链接后在浏览器中打开',
        showCancel: false,
        confirmText: '复制链接',
        success: () => {
          uni.setClipboardData({
            data: webviewUrl.value,
            success: () => {
              uni.showToast({
                title: '链接已复制',
                icon: 'success'
              })
            }
          })
        }
      })
      break
  }
}
</script>

<style scoped lang="scss">
.webview-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.custom-navbar {
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
  z-index: 1000;
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
}

.navbar-left, .navbar-right {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  
  &:active {
    opacity: 0.6;
  }
}

.navbar-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.webview-container {
  flex: 1;
  position: relative;
}

.webview {
  width: 100%;
  height: 100%;
}

.loading-overlay, .error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  z-index: 100;
}

.loading-content, .error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px 20px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.error-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.error-message {
  font-size: 14px;
  color: #666;
  text-align: center;
  line-height: 1.5;
}
</style>