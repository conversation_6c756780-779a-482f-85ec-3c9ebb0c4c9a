-- 1. 创建表结构
-- 用户等级表
CREATE TABLE user_levels (
    level_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '等级ID，主键自增',
    level_name VARCHAR(50) NOT NULL COMMENT '等级名称',
    level_desc VARCHAR(255) COMMENT '等级描述',
    post_limit INT DEFAULT 10 COMMENT '每日发帖限制',
    comment_limit INT DEFAULT 50 COMMENT '每日评论限制',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除(0-未删除 1-已删除)'
) COMMENT='用户等级表';

-- 用户表
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID，主键自增',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名(登录用)',
    password VARCHAR(255) NOT NULL COMMENT '加密后的密码',
    nickname VARCHAR(50) NOT NULL COMMENT '用户昵称(显示用)',
    avatar_url VARCHAR(255) COMMENT '用户头像URL',
    mobile VARCHAR(20) COMMENT '手机号码',
    email VARCHAR(100) COMMENT '电子邮箱',
    gender TINYINT COMMENT '性别(0-未知 1-男 2-女)',
    birthday DATE COMMENT '出生日期',
    level_id INT NOT NULL DEFAULT 1 COMMENT '用户等级ID',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否认证用户',
    status TINYINT DEFAULT 1 COMMENT '状态(0-禁用 1-正常)',
    creator_id INT COMMENT '创建人ID(管理员添加时使用)',
    last_login_time DATETIME COMMENT '最后登录时间',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除(0-未删除 1-已删除)',
    FOREIGN KEY (level_id) REFERENCES user_levels(level_id)
) COMMENT='用户信息表';

-- 分类表
CREATE TABLE categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID，主键自增',
    category_name VARCHAR(50) NOT NULL UNIQUE COMMENT '分类名称',
    icon VARCHAR(100) COMMENT '分类图标',
    description VARCHAR(255) COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    status TINYINT DEFAULT 1 COMMENT '状态(0-禁用 1-启用)',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除(0-未删除 1-已删除)'
) COMMENT='帖子分类表';

-- 分类权限表
CREATE TABLE category_permissions (
    permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID，主键自增',
    category_id INT NOT NULL COMMENT '分类ID',
    level_id INT NOT NULL COMMENT '用户等级ID',
    can_post BOOLEAN DEFAULT TRUE COMMENT '是否允许发帖',
    can_comment BOOLEAN DEFAULT TRUE COMMENT '是否允许评论',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除(0-未删除 1-已删除)',
    FOREIGN KEY (category_id) REFERENCES categories(category_id),
    FOREIGN KEY (level_id) REFERENCES user_levels(level_id),
    UNIQUE KEY (category_id, level_id)
) COMMENT='分类权限表';

-- 帖子媒体表
CREATE TABLE post_media (
    media_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '媒体ID，主键自增',
    post_id INT COMMENT '所属帖子ID',
    user_id INT NOT NULL COMMENT '上传用户ID',
    media_url VARCHAR(255) NOT NULL COMMENT '媒体文件URL',
    media_type ENUM('image', 'video') NOT NULL COMMENT '媒体类型(图片或视频)',
    file_size INT COMMENT '文件大小(KB)',
    duration INT COMMENT '视频时长(秒)',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除(0-未删除 1-已删除)',
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) COMMENT='帖子媒体文件表';

-- 帖子表
CREATE TABLE posts (
    post_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '帖子ID，主键自增',
    user_id INT NOT NULL COMMENT '发帖用户ID',
    category_id INT NOT NULL COMMENT '帖子分类ID',
    title VARCHAR(100) COMMENT '帖子标题',
    content TEXT NOT NULL COMMENT '帖子正文内容',
    media_type TINYINT COMMENT '附件类型(0-图片 1-视频)',
    media_id INT COMMENT '附件ID(关联post_media表)',
    view_count INT DEFAULT 0 COMMENT '浏览数',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    status TINYINT DEFAULT 0 COMMENT '状态(0-待审核 1-已审核 2-被驳回)',
    reject_reason VARCHAR(255) COMMENT '驳回原因',
    top_status TINYINT DEFAULT 0 COMMENT '置顶状态(0-普通 1-板块置顶 2-全局置顶)',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除(0-未删除 1-已删除)',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (category_id) REFERENCES categories(category_id),
    FOREIGN KEY (media_id) REFERENCES post_media(media_id)
) COMMENT='帖子信息表';

-- 评论表
CREATE TABLE comments (
    comment_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '评论ID，主键自增',
    post_id INT NOT NULL COMMENT '所属帖子ID',
    user_id INT NOT NULL COMMENT '评论用户ID',
    content TEXT NOT NULL COMMENT '评论内容',
    status TINYINT DEFAULT 1 COMMENT '状态(0-待审核 1-已审核 2-被驳回)',
    reject_reason VARCHAR(255) COMMENT '驳回原因',
    parent_comment_id INT COMMENT '父评论ID(用于回复功能)',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评论时间',
    updated_time DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除(0-未删除 1-已删除)',
    FOREIGN KEY (post_id) REFERENCES posts(post_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (parent_comment_id) REFERENCES comments(comment_id) ON DELETE SET NULL
) COMMENT='帖子评论表';

-- 2. 插入模拟数据
-- 插入用户等级数据
INSERT INTO user_levels (level_name, level_desc, post_limit, comment_limit) VALUES 
('普通会员', '基础用户等级，享有基本权限', 5, 20),
('VIP会员', '高级用户等级，享有更多权限', 20, 100),
('版主', '板块管理员，拥有管理权限', 50, 500),
('管理员', '系统管理员，拥有全部权限', 100, 1000);

-- 插入用户数据
INSERT INTO users (username, password, nickname, avatar_url, mobile, email, gender, birthday, level_id, is_verified, last_login_time) VALUES 
('happy_user', '$2a$10$xJwL5v5zJz6Z6Z6Z6Z6Z6e', '幸福派乐然', 'https://example.com/avatars/1.jpg', '13800138001', '<EMAIL>', 1, '1990-05-15', 2, TRUE, '2023-06-15 10:30:00'),
('neighbor', '$2a$10$xJwL5v5zJz6Z6Z6Z6Z6Z6e', '热心邻居', 'https://example.com/avatars/2.jpg', '13800138002', '<EMAIL>', 0, '1985-08-20', 1, FALSE, '2023-06-15 11:20:00'),
('angel', '$2a$10$xJwL5v5zJz6Z6Z6Z6Z6Z6e', '快乐小天使', 'https://example.com/avatars/3.jpg', '13800138003', '<EMAIL>', 2, '1995-03-10', 1, FALSE, '2023-06-15 09:15:00'),
('admin', '$2a$10$xJwL5v5zJz6Z6Z6Z6Z6Z6e', '系统管理员', 'https://example.com/avatars/4.jpg', '13800138000', '<EMAIL>', 1, '1980-12-25', 4, TRUE, '2023-06-15 08:00:00');

-- 插入分类数据
INSERT INTO categories (category_name, icon, description, sort_order) VALUES 
('三期公告', 'announcement', '社区三期相关公告信息', 1),
('邻友互助', 'help', '邻里之间互相帮助', 2),
('房东直租', 'rent', '房东直接发布的租房信息', 3),
('二手闲置', 'secondhand', '二手物品交易信息', 4);

-- 插入分类权限数据
INSERT INTO category_permissions (category_id, level_id, can_post, can_comment) VALUES 
(1, 1, FALSE, TRUE), -- 普通会员不能在公告区发帖
(2, 1, TRUE, TRUE),
(3, 1, TRUE, TRUE),
(4, 1, TRUE, TRUE),
(1, 2, FALSE, TRUE), -- VIP会员不能在公告区发帖
(2, 2, TRUE, TRUE),
(3, 2, TRUE, TRUE),
(4, 2, TRUE, TRUE),
(1, 3, TRUE, TRUE),  -- 版主可以在所有板块发帖
(2, 3, TRUE, TRUE),
(3, 3, TRUE, TRUE),
(4, 3, TRUE, TRUE),
(1, 4, TRUE, TRUE),  -- 管理员可以在所有板块发帖
(2, 4, TRUE, TRUE),
(3, 4, TRUE, TRUE),
(4, 4, TRUE, TRUE);

-- 插入媒体数据(先插入，因为帖子会引用)
INSERT INTO post_media (user_id, media_url, media_type, file_size, duration) VALUES 
(1, 'https://example.com/media/post1.jpg', 'image', 1024, NULL),
(1, 'https://example.com/media/post1_video.mp4', 'video', 5120, 120);

-- 插入帖子数据
INSERT INTO posts (user_id, category_id, title, content, media_type, media_id, view_count, like_count, comment_count, status, top_status) VALUES 
(1, 3, '房东直租', '幸福小区3号楼2单元精装两居室出租，南北通透，家电齐全，拎包入住。', 0, 1, 156, 24, 3, 1, 0),
(2, 2, '寻找遛狗伙伴', '有没有早上7点左右在小区花园遛狗的朋友？可以一起遛狗聊天~', NULL, NULL, 89, 12, 5, 1, 0),
(3, 4, '转让二手自行车', '九成新山地自行车转让，价格面议，适合身高170-180cm。', NULL, NULL, 45, 8, 2, 1, 0);

-- 更新媒体数据的post_id
UPDATE post_media SET post_id = 1 WHERE media_id IN (1, 2);

-- 插入评论数据
INSERT INTO comments (post_id, user_id, content, status, like_count) VALUES 
(1, 2, '说得好！', 1, 3),
(1, 1, '谢谢支持！', 1, 1),
(1, 3, '我也这么觉得', 1, 0),
(1, 3, '非常赞同这个观点', 1, 2),
(1, 2, '确实很有道理', 1, 1),
(2, 1, '我有只金毛，可以一起遛', 1, 2),
(2, 3, '我早上7:15可以，时间合适吗？', 1, 1),
(3, 2, '车子还在吗？多少钱？', 1, 0);

-- 更新帖子的评论计数
UPDATE posts SET comment_count = (SELECT COUNT(*) FROM comments WHERE post_id = posts.post_id);

-- 3. 创建视图(可选)
CREATE VIEW post_detail_view AS
SELECT 
    p.post_id, p.title, p.content, p.view_count, p.like_count, p.comment_count,
    p.status, p.created_time AS post_created,
    u.user_id, u.nickname AS author_name, u.avatar_url AS author_avatar,
    c.category_id, c.category_name,
    pm.media_url, pm.media_type
FROM 
    posts p
JOIN users u ON p.user_id = u.user_id
JOIN categories c ON p.category_id = c.category_id
LEFT JOIN post_media pm ON p.media_id = pm.media_id
WHERE p.is_deleted = 0;

-- 4. 创建索引(可选)
CREATE INDEX idx_posts_user ON posts(user_id);
CREATE INDEX idx_posts_category ON posts(category_id);
CREATE INDEX idx_comments_post ON comments(post_id);
CREATE INDEX idx_comments_user ON comments(user_id);






执行说明
这个SQL脚本会完整创建所有表结构

插入模拟数据包括：

4个用户等级

4个用户(包含原型中出现的3个用户)

4个分类(与原型一致)

分类权限配置

2个媒体文件(图片和视频各1个)

3篇帖子(包含原型中的"房东直租"帖子)

8条评论(包含原型中的5条评论)

数据完全互相关联，符合业务逻辑：

用户有对应的等级

帖子有对应的用户和分类

评论有对应的帖子和用户

媒体文件有对应的帖子和上传用户

脚本还包含可选视图和索引创建，可根据实际需要执行

执行此脚本后，您将获得一个完整的、包含关联数据的社区平台数据库。

