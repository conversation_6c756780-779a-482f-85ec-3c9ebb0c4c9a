package com.haolinkyou.task;

import com.haolinkyou.service.IPostTopService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 帖子置顶相关定时任务
 */
@Component
public class PostTopTask {
    
    @Autowired
    private IPostTopService postTopService;
    
    /**
     * 每10分钟检查一次过期的置顶帖子
     */
    @Scheduled(fixedRate = 10 * 60 * 1000) // 10分钟
    public void processExpiredTopPosts() {
        try {
            int processedCount = postTopService.processExpiredTopPosts();
            if (processedCount > 0) {
                System.out.println("定时任务处理过期置顶帖子: " + processedCount + " 个");
            }
        } catch (Exception e) {
            System.out.println("定时任务处理过期置顶帖子异常: " + e.getMessage());
        }
    }
}
