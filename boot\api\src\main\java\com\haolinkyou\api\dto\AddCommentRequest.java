package com.haolinkyou.api.dto;

/**
 * 添加评论请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
public class AddCommentRequest {
    
    private Long postId;
    private Long userId;
    private String content;
    private Long parentCommentId;
    private Long replyToUserId;

    // Constructors
    public AddCommentRequest() {}

    // Getters and Setters
    public Long getPostId() { return postId; }
    public void setPostId(Long postId) { this.postId = postId; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }

    public Long getParentCommentId() { return parentCommentId; }
    public void setParentCommentId(Long parentCommentId) { this.parentCommentId = parentCommentId; }

    public Long getReplyToUserId() { return replyToUserId; }
    public void setReplyToUserId(Long replyToUserId) { this.replyToUserId = replyToUserId; }

    @Override
    public String toString() {
        return "AddCommentRequest{" +
                "postId=" + postId +
                ", userId=" + userId +
                ", content='" + content + '\'' +
                ", parentCommentId=" + parentCommentId +
                ", replyToUserId=" + replyToUserId +
                '}';
    }
}