/*
 * @Author: Rock
 * @Date: 2025-05-06 08:47:49
 * @LastEditors: Rock
 * @LastEditTime: 2025-05-06 14:30:19
 * @Description: 
 */
package com.haolinkyou.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.haolinkyou.entity.PostFiles;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PostFilesMapper extends BaseMapper<PostFiles> {
    default Long getLastInsertedFileId() {
        return selectList(null).stream()
            .mapToLong(PostFiles::getId)
            .max()
            .orElse(0L);
    }

    default void updatePostFilesWithPostId(Long filesId, Long postId) {
        UpdateWrapper<PostFiles> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", filesId)
                    .set("post_id", postId);
        update(null, updateWrapper);
    }
}
