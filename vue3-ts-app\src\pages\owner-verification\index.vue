<template>
  <view class="verification-page">
    <!-- 页面头部 -->
    <up-navbar
      title="业主认证"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 提示信息 -->
    <view class="tip-section" :style="{ marginTop: mainContentPaddingTop }">
      <up-icon name="info-circle" color="#1890ff" size="16"></up-icon>
      <text class="tip-text">保护您的个人隐私安全，请如实上传身份证、房产证等真实证件的完整照片</text>
    </view>

    <!-- 表单内容 -->
    <view class="form-section">
      <up-form :model="formData" ref="formRef" :rules="rules" labelWidth="80px">
        <!-- 姓名 -->
        <up-form-item label="姓名" prop="name" required :border-bottom="true">
          <up-input 
            required
            v-model="formData.name" 
            placeholder="请输入姓名"
            :clearable="true"
            border="none"
          ></up-input>
        </up-form-item>

        <!-- 手机号 -->
        <up-form-item label="手机号" prop="phone" required :border-bottom="true">
          <up-input
            required
            v-model="formData.phone" 
            placeholder="请输入手机号"
            type="number"
            :clearable="true"
            border="none"
          ></up-input>
        </up-form-item>

        <!-- 房屋号码 -->
        <up-form-item label="房屋号码" prop="houseNumber" required :border-bottom="true">
          <up-input 
            v-model="formData.houseNumber" 
            placeholder="例如：1梯1001室(或1T1001)"
            :clearable="true"
            border="none"
          ></up-input>
        </up-form-item>

        <!-- 身份选择 -->
        <up-form-item label="身份" prop="identity" required :border-bottom="true">
          <view class="identity-options">
            <up-radio-group v-model="formData.identity" placement="column" v-if="!loading">
              <view
                v-for="option in identityTypeOptions"
                :key="option.code"
                class="radio-item"
              >
                <up-radio
                  :label="option.name"
                  :name="option.code"
                  activeColor="#52c41a"
                ></up-radio>
                <text class="radio-desc" v-if="option.description">{{ option.description }}</text>
              </view>
            </up-radio-group>

            <!-- 加载状态 -->
            <view class="loading-identity" v-if="loading">
              <up-loading-icon mode="circle" size="20"></up-loading-icon>
              <text class="loading-text">加载身份选项中...</text>
            </view>
          </view>
        </up-form-item>

        <!-- 证明材料 -->
        <up-form-item label="佐证材料" prop="documents" :border-bottom="false">
          <template #right>
            <text class="optional-text">选填</text>
          </template>
        </up-form-item>
      
        <!-- 文件上传 -->
        <view class="upload-section">
          <view class="upload-hint">
            <text>📷 图片模式 - 只能上传图片文件，最多3张</text>
          </view>
          <up-upload
            :fileList="fileList"
            @afterRead="afterRead"
            @delete="deleteFile"
            @oversize="onOversize"
            name="documents"
            multiple
            :maxCount="3"
            :maxSize="10 * 1024 * 1024"
            accept="image"
            :previewFullImage="true"
            width="120"
            height="120"
            uploadIcon="camera-fill"
            uploadIconColor="#c0c4cc"
            uploadText="选择图片"
          >
            <template #default>
              <view class="upload-placeholder">
                <up-icon name="camera-fill" size="32" color="#c0c4cc"></up-icon>
                <text class="upload-text">选择图片</text>
              </view>
            </template>
          </up-upload>
        </view>

        <view class="upload-tip">
          <text>请上传清晰照片，注意保护隐私信息</text>
        </view>

        <!-- 备注说明 -->
        <up-form-item label="备注说明" prop="remark" :border-bottom="false">
          <up-textarea 
            v-model="formData.remark"
            placeholder="请输入补充说明（选填）"
            :maxlength="200"
            count
            :autoHeight="true"
            :height="80"
          ></up-textarea>
        </up-form-item>
      </up-form>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <up-button 
        text="提交认证" 
        type="primary" 
        :loading="submitting"
        @click="handleSubmit"
        :customStyle="{ 
          width: '100%', 
          height: '50px',
          borderRadius: '25px',
          fontSize: '16px'
        }"
      ></up-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useSafeArea } from '@/utils/safeArea';
import { useMemberStore } from '@/stores';
import { submitVerificationAPI, getIdentityTypesAPI, getVerificationStatusAPI } from '@/services/verification';
import type { VerificationApplicationParams, IdentityTypeOption } from '@/services/verification';
import type { AuthApplication } from '@/types/user';

const { mainContentPaddingTop } = useSafeArea();
const memberStore = useMemberStore();

// 表单数据
const formData = reactive({
  name: '',
  phone: '',
  houseNumber: '',
  identity: '', // 初始为空，等待API获取后设置默认值
  documents: [],
  remark: ''
});

// 文件列表
const fileList = ref<any[]>([]);

// 身份类型选项列表
const identityTypeOptions = ref<IdentityTypeOption[]>([]);

// 表单引用
const formRef = ref();

// 提交状态
const submitting = ref(false);

// 加载状态
const loading = ref(false);

// 认证申请记录
const existingApplication = ref<AuthApplication | null>(null);

// 表单验证规则
const rules = {
  name: [
    {
      required: true,
      message: '请输入姓名',
      trigger: ['blur', 'change']
    }
  ]
};

// 获取身份类型选项列表
const fetchIdentityTypes = async () => {
  try {
    loading.value = true
    const response = await getIdentityTypesAPI()

    if (response.success && response.data) {
      identityTypeOptions.value = response.data
      console.log('身份类型选项获取成功:', response.data)

      // 如果有选项且当前没有选中值，设置默认值为第一个选项
      if (response.data.length > 0 && !formData.identity) {
        formData.identity = response.data[0].code
      }
    } else {
      console.error('身份类型选项获取失败:', response.message)
      uni.showToast({
        title: '获取身份选项失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('身份类型选项获取异常:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 获取认证申请状态
const fetchVerificationStatus = async () => {
  try {
    console.log('=== 获取认证申请状态 ===')
    const response = await getVerificationStatusAPI()

    if (response.success && response.data) {
      existingApplication.value = response.data
      console.log('找到现有认证申请:', response.data)

      // 回显数据到表单
      formData.name = response.data.realName || ''
      formData.phone = response.data.phone || ''
      formData.houseNumber = response.data.houseNumber || ''
      formData.identity = response.data.identityType || ''
      formData.remark = response.data.remark || ''

      // 回显文件列表（如果有的话）
      if (response.data.documents && response.data.documents.length > 0) {
        console.log('回显文件列表，原始documents:', response.data.documents)

        // 将文件路径转换为文件列表格式，添加服务器地址前缀
        fileList.value = response.data.documents.map((filePath, index) => {
          // 如果文件路径不是完整URL，则添加服务器地址前缀
          const fullUrl = filePath.startsWith('http') ? filePath : `http://localhost:3205/${filePath}`
          console.log(`文件${index + 1}: ${filePath} -> ${fullUrl}`)

          return {
            url: fullUrl,
            name: `证明文件${index + 1}`,
            status: 'success',
            message: '已上传'
          }
        })

        console.log('回显文件列表处理完成:', fileList.value)
      }

      console.log('认证申请数据已回显到表单')
    } else {
      console.log('未找到认证申请记录')
      existingApplication.value = null
    }
  } catch (error) {
    console.error('获取认证申请状态失败:', error)
    // 不显示错误提示，静默处理
  }
}

// 页面初始化
onMounted(async () => {
  // 先获取身份类型选项
  await fetchIdentityTypes()

  // 再获取认证申请状态（需要在身份类型加载完成后）
  await fetchVerificationStatus()
})

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// uview-plus上传组件：读取文件后的处理
const afterRead = (event: any) => {
  console.log('afterRead事件:', event)

  // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
  let lists = [].concat(event.file)

  lists.forEach((item: any) => {
    // 为每个文件添加状态信息
    const fileObj = {
      ...item,
      status: 'ready', // 准备状态，提交时上传
      message: '准备上传'
    }

    fileList.value.push(fileObj)
  })

  console.log('当前文件列表:', fileList.value)
  uni.showToast({
    title: `已选择${lists.length}个文件`,
    icon: 'success'
  })
}

// uview-plus上传组件：删除文件
const deleteFile = (event: any) => {
  console.log('deleteFile事件:', event)
  fileList.value.splice(event.index, 1)
  uni.showToast({
    title: '文件已删除',
    icon: 'success'
  })
}

// uview-plus上传组件：文件大小超出限制
const onOversize = (event: any) => {
  console.log('文件大小超出限制:', event)
  uni.showToast({
    title: '文件大小不能超过10MB',
    icon: 'none'
  })
}

// 优化的文件上传函数 - 区分已上传文件和新文件
const uploadVerificationFiles = async (files: any[]) => {
  console.log('开始处理认证文件，数量:', files.length)
  let successCount = 0
  const finalFiles: string[] = []

  for (let i = 0; i < files.length; i++) {
    const fileItem = files[i]
    console.log(`处理第${i + 1}个认证文件:`, fileItem)

    // 更新处理进度
    uni.showLoading({
      title: `正在处理文件 (${i + 1}/${files.length})`,
      mask: true
    })

    try {
      let filePath = ''

      // 判断文件类型：已上传的文件 vs 新添加的文件
      if (fileItem.url && fileItem.url.startsWith('http://localhost:3205/')) {
        // 这是已经上传到服务器的文件（回显的文件），直接提取相对路径
        filePath = fileItem.url.replace('http://localhost:3205/', '')
        console.log(`第${i + 1}个文件是已上传文件，相对路径:`, filePath)
        finalFiles.push(filePath)
        successCount++
        continue
      }

      // 以下是新添加的文件，需要上传
      console.log(`第${i + 1}个文件是新文件，需要上传`)

      // 优先使用原始File对象上传（H5环境）
      if (fileItem.file && typeof FormData !== 'undefined') {
        console.log(`第${i + 1}个文件使用FormData上传:`, {
          name: fileItem.file.name || fileItem.name,
          size: fileItem.file.size || fileItem.size,
          type: fileItem.file.type || fileItem.type
        })

        const formData = new FormData()
        formData.append('file', fileItem.file, fileItem.file.name || fileItem.name)
        formData.append('fileType', 'verification')

        const uploadResponse = await fetch('/files/userUpload', {
          method: 'POST',
          body: formData,
          headers: {
            'Authorization': `Bearer ${memberStore.profile?.token}`
          }
        })

        if (uploadResponse.ok) {
          filePath = await uploadResponse.text()
        } else {
          throw new Error(`上传失败: ${uploadResponse.statusText}`)
        }
      }
      // 如果是blob URL，尝试使用uni.uploadFile（小程序环境或其他情况）
      else if (fileItem.url && fileItem.url.startsWith('blob:')) {
        console.log(`第${i + 1}个文件是blob URL，使用uni.uploadFile上传:`, fileItem.url)

        // 使用uni.uploadFile上传（小程序环境）
        const uploadResult = await new Promise<string>((resolve, reject) => {
          uni.uploadFile({
            url: 'http://localhost:3205/files/userUpload',
            filePath: fileItem.url,
            name: 'file',
            formData: {
              fileType: 'verification'
            },
            header: {
              'Authorization': `Bearer ${memberStore.profile?.token}`
            },
            success: (res) => {
              console.log(`第${i + 1}个文件上传成功:`, res)
              if (res.statusCode === 200) {
                resolve(res.data)
              } else {
                reject(new Error(`上传失败，状态码: ${res.statusCode}`))
              }
            },
            fail: (err) => {
              console.error(`第${i + 1}个文件上传失败:`, err)
              reject(err)
            }
          })
        })

        filePath = uploadResult
      }

      if (filePath) {
        finalFiles.push(filePath)
        successCount++
        console.log(`第${i + 1}个文件处理成功，路径:`, filePath)
      } else {
        console.warn(`第${i + 1}个文件无法获取有效路径`)
      }

    } catch (error) {
      console.error(`第${i + 1}个文件处理失败:`, error)
      // 继续处理其他文件，不中断整个流程
    }
  }

  uni.hideLoading()
  console.log(`认证文件处理完成，成功: ${successCount}/${files.length}`)
  console.log('最终文件路径列表:', finalFiles)
  return finalFiles
}

// 提交认证申请数据到服务器
const submitApplicationWithFiles = async (uploadedDocuments: string[]) => {
  console.log('=== 提交认证申请数据 ===')
  console.log('上传的文件路径列表:', uploadedDocuments)

  const applicationData: VerificationApplicationParams = {
    realName: formData.name,
    phone: formData.phone,
    houseNumber: formData.houseNumber,
    identityType: formData.identity,
    documents: uploadedDocuments,
    remark: formData.remark
  }

  console.log('提交认证申请完整数据:', applicationData)
  console.log('documents字段内容:', applicationData.documents)
  console.log('documents字段类型:', typeof applicationData.documents)
  console.log('documents字段长度:', applicationData.documents?.length)

  try {
    // 调用实际的认证申请API
    const response = await submitVerificationAPI(applicationData)

    if (response.success && response.data) {
      console.log('认证申请提交成功:', response.data)

      uni.showToast({
        title: '认证申请已提交',
        icon: 'success'
      });

      // 清空表单
      formData.name = ''
      formData.phone = ''
      formData.houseNumber = ''
      formData.identity = 'owner'
      formData.remark = ''
      fileList.value = []

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);

    } else {
      throw new Error(response.message || '提交失败')
    }
  } catch (apiError) {
    console.error('认证申请API调用失败:', apiError)
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none'
    })
  }
}

// 提交认证申请
const handleSubmit = async () => {
  try {
    // 检查登录状态
    if (!memberStore.profile?.token) {
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 表单验证
    await formRef.value.validate();

    submitting.value = true;

    // 第一步：上传文件（如果有的话）
    let uploadedDocuments: string[] = []
    if (fileList.value.length > 0) {
      console.log('开始上传认证文件，数量:', fileList.value.length)

      // 显示上传进度提示
      uni.showLoading({
        title: `正在上传文件 (0/${fileList.value.length})`,
        mask: true
      })

      try {
        uploadedDocuments = await uploadVerificationFiles(fileList.value)
        console.log('文件上传结果:', uploadedDocuments)

        // 检查是否所有文件都上传成功
        if (uploadedDocuments.length < fileList.value.length) {
          uni.hideLoading()
          uni.showModal({
            title: '部分文件上传失败',
            content: `${fileList.value.length - uploadedDocuments.length}个文件上传失败，是否继续提交申请？`,
            success: (res) => {
              if (!res.confirm) {
                return
              }
              // 用户选择继续，继续提交流程
              submitApplicationWithFiles(uploadedDocuments)
            }
          })
          return
        }

        uni.hideLoading()
      } catch (error) {
        uni.hideLoading()
        console.error('文件上传失败:', error)
        uni.showToast({
          title: '文件上传失败，请重试',
          icon: 'none'
        })
        return
      }
    }

    // 第二步：提交认证申请
    await submitApplicationWithFiles(uploadedDocuments)

  } catch (error) {
    console.error('认证申请提交失败:', error);
    uni.showToast({
      title: '请完善必填信息',
      icon: 'none'
    });
  } finally {
    submitting.value = false;
  }
};
</script>

<style lang="scss">
.verification-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  // 提示信息
  .tip-section {
    background: #fff3cd;
    padding: 12px 20px;
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;

    .tip-text {
      font-size: 14px;
      color: #856404;
      margin-left: 8px;
      line-height: 1.4;
    }
  }

  // 表单区域
  .form-section {
    background: #fff;
    padding: 0 20px;

    .example-text {
      padding: 10px 0;
      margin-left: 80px;
      
      text {
        font-size: 12px;
        color: #999;
      }
    }

    .optional-text {
      font-size: 12px;
      color: #999;
    }

    .identity-options {
      flex: 1;

      .radio-item {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .radio-desc {
          font-size: 12px;
          color: #999;
          margin-left: 30px;
          margin-top: 4px;
          display: block;
        }
      }

      .loading-identity {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px 0;

        .loading-text {
          font-size: 14px;
          color: #999;
          margin-left: 8px;
        }
      }
    }

    .upload-tip {
      padding: 10px 0;
      margin-left: 80px;
      
      text {
        font-size: 12px;
        color: #1890ff;
      }
    }

    .upload-section {
      margin-left: 80px;
      margin-bottom: 20px;

      .upload-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 120px;
        border: 1px dashed #d9d9d9;
        border-radius: 8px;
        background: #fafafa;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          background: #f0f8ff;
        }

        .upload-text {
          font-size: 12px;
          color: #666;
          margin-top: 8px;
          font-weight: 500;
        }
      }
    }

    .privacy-tip {
      padding: 15px 0;
      text-align: center;
      
      text {
        font-size: 12px;
        color: #1890ff;
      }
    }
  }

  // 提交按钮区域
  .submit-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 20px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
