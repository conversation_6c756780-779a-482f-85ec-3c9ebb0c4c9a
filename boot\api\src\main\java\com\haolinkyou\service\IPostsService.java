/*
 * @Author: Rock
 * @Date: 2025-05-01 00:46:08
 * @LastEditors: Rock
 * @LastEditTime: 2025-05-01 00:46:20
 * @Description: 
 */
package com.haolinkyou.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.haolinkyou.api.dto.PageDto;
import com.haolinkyou.api.dto.PostListAllDto;
import com.haolinkyou.api.vo.PostCommentsVo;
import com.haolinkyou.api.vo.PostDetailVo;
import com.haolinkyou.api.vo.PostsListVo;
import com.haolinkyou.entity.Posts;

import java.util.List;

public interface IPostsService extends IService<Posts> {

    boolean add(Posts post);

    Posts  selectById(Long id);

    boolean updateById(Posts post);

    boolean removeById(Long id);

    IPage<PostsListVo> selectByPage(PageDto dto);

    IPage<PostsListVo> listAllPosts(PostListAllDto dto);

    IPage<PostsListVo> listAllPostsWithUserStatus(PostListAllDto dto, Long userId);

    /**
     * 获取帖子列表（带权限控制）
     * @param dto 查询条件
     * @param userId 当前用户ID
     * @param isAdmin 是否为管理员
     * @return 帖子列表
     */
    IPage<PostsListVo> listAllPostsWithPermission(PostListAllDto dto, Long userId, boolean isAdmin);

    /**
     * 获取用户个人发布的动态列表
     * @param dto 查询条件
     * @param status 动态状态过滤（可选）
     * @return 动态列表
     */
    IPage<PostsListVo> getUserPosts(PostListAllDto dto, Integer status);

    /**
     * 管理员获取帖子列表（支持状态筛选）
     * @param dto 查询条件
     * @param status 状态筛选（可选）
     * @return 帖子列表
     */
    IPage<PostsListVo> listAllPostsForAdmin(PostListAllDto dto, Integer status);

    PostDetailVo selectPostDetailById(Long id);
    
    /**
     * 更新帖子的评论数
     * @param postId 帖子ID
     * @return 是否更新成功
     */
    boolean updateCommentCount(Long postId);
}