package com.haolinkyou.config;

import com.haolinkyou.common.utils.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * JWT认证拦截器
 * 用于验证JWT token并解析用户信息
 */
@Component
public class JwtInterceptor implements HandlerInterceptor {
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 对于OPTIONS请求直接放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }

        // 获取Authorization头
        String authHeader = request.getHeader("Authorization");
        System.out.println("JWT拦截器 - 请求路径: " + request.getRequestURI());
        System.out.println("JWT拦截器 - 请求方法: " + request.getMethod());
        System.out.println("JWT拦截器 - Content-Type: " + request.getContentType());
        System.out.println("JWT拦截器 - Authorization头: " + (authHeader != null ? "存在" : "不存在"));

        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            // 提取token（去掉"Bearer "前缀）
            String token = authHeader.substring(7);
            
            // 验证token
            if (jwtUtil.validateToken(token)) {
                // 从token中解析用户信息并设置到request属性中
                Long userId = jwtUtil.getUserIdFromToken(token);
                String username = jwtUtil.getUsernameFromToken(token);
                String nickname = jwtUtil.getNicknameFromToken(token);

                System.out.println("JWT拦截器 - token验证成功，用户ID: " + userId);

                // 将用户信息存储到request中，供后续使用
                request.setAttribute("userId", userId);
                request.setAttribute("username", username);
                request.setAttribute("nickname", nickname);

                return true;
            } else {
                System.out.println("JWT拦截器 - token验证失败");
            }
        } else if (authHeader != null) {
            // 兼容旧的token格式（直接是token，没有Bearer前缀）
            String token = authHeader;
            
            // 验证token
            if (jwtUtil.validateToken(token)) {
                // 从token中解析用户信息并设置到request属性中
                Long userId = jwtUtil.getUserIdFromToken(token);
                String username = jwtUtil.getUsernameFromToken(token);
                String nickname = jwtUtil.getNicknameFromToken(token);
                
                // 将用户信息存储到request中，供后续使用
                request.setAttribute("userId", userId);
                request.setAttribute("username", username);
                request.setAttribute("nickname", nickname);
                
                return true;
            }
        }
        
        // token无效或不存在，返回401未授权
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"success\":false,\"message\":\"未授权访问，请先登录\",\"code\":401}");
        return false;
    }
}
