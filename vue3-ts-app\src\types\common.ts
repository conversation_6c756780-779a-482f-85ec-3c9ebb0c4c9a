// 通用类型定义文件
import type { ModalConfig, ToastConfig, FormRule } from './ui';

// 通用响应类型
export interface BaseResponse<T = any> {
  success: boolean;
  message?: string;
  code?: number | string;
  data?: T;
}

// 分页响应类型
export interface PaginationResponse<T = any> extends BaseResponse<T> {
  current?: number;
  pages?: number;
  size?: number;
  total?: number;
  records?: T[];
}

// 通用字典类型
export interface DictItem {
  id: string | number;
  name: string;
  value?: any;
}

// 文件上传相关类型
export interface UploadFile {
  name: string;
  url: string;
  size?: number;
  type?: string;
}

// FormRule 类型已从 ui.ts 导入

// 表单验证规则集合类型
export type FormRules = Record<string, FormRule[]>;

// 操作按钮类型
export interface ActionButton {
  text: string;
  type?: 'primary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'large' | 'normal' | 'small' | 'mini';
  plain?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
}

// ModalConfig 类型已从 ui.ts 导入

// ToastConfig 类型已从 ui.ts 导入

// 导航配置类型
export interface NavigationConfig {
  url: string;
  animationType?: string;
  animationDuration?: number;
  success?: () => void;
  fail?: () => void;
  complete?: () => void;
}

// 事件处理器类型
export type EventHandler<T = any> = (event: T) => void;

// 异步事件处理器类型
export type AsyncEventHandler<T = any> = (event: T) => Promise<void>;

// 状态类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// 通用状态管理类型
export interface StateManager<T = any> {
  data: T;
  loading: boolean;
  error: string | null;
  lastUpdated?: Date;
}

// 列表状态管理类型
export interface ListState<T = any> extends StateManager<T[]> {
  hasMore: boolean;
  currentPage: number;
  pageSize: number;
  total: number;
}

// 搜索参数类型
export interface SearchParams {
  keyword?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

// 用户基本信息类型
export interface BaseUser {
  id: number;
  username?: string;
  nickname: string;
  avatar?: string;
  email?: string;
  mobile?: string;
}

// 时间戳类型
export type Timestamp = string | number | Date;

// 颜色类型
export type Color = string;

// 尺寸类型
export type Size = 'small' | 'medium' | 'large';

// 位置类型
export interface Position {
  x: number;
  y: number;
}

// 尺寸信息类型
export interface Dimensions {
  width: number;
  height: number;
}

// 矩形区域类型
export interface Rectangle extends Position, Dimensions {}

// 选项类型
export interface Option<T = any> {
  label: string;
  value: T;
  disabled?: boolean;
  children?: Option<T>[];
}

// 树形数据类型
export interface TreeNode<T = any> {
  id: string | number;
  label: string;
  value?: T;
  children?: TreeNode<T>[];
  parent?: TreeNode<T>;
  level?: number;
  expanded?: boolean;
  selected?: boolean;
  disabled?: boolean;
}

// 表格列定义类型
export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex?: string;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: T, index: number) => any;
}

// 表格数据类型
export interface TableData<T = any> {
  columns: TableColumn<T>[];
  dataSource: T[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
  };
}

// 媒体文件类型
export interface MediaFile {
  id?: string | number;
  name: string;
  url: string;
  type: 'image' | 'video' | 'audio' | 'document';
  size?: number;
  duration?: number; // 视频/音频时长（秒）
  thumbnail?: string; // 缩略图
  createdAt?: Timestamp;
}

// 地理位置类型
export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  province?: string;
  country?: string;
}

// 设备信息类型
export interface DeviceInfo {
  platform: string;
  system: string;
  version: string;
  model: string;
  brand: string;
  screenWidth: number;
  screenHeight: number;
  pixelRatio: number;
}

// 网络状态类型
export type NetworkType = 'wifi' | '2g' | '3g' | '4g' | '5g' | 'unknown' | 'none';

// 权限状态类型
export type PermissionStatus = 'authorized' | 'denied' | 'not_determined';

// 主题类型
export type Theme = 'light' | 'dark' | 'auto';

// 语言类型
export type Language = 'zh-CN' | 'zh-TW' | 'en-US' | 'ja-JP' | 'ko-KR';

// 环境类型
export type Environment = 'development' | 'testing' | 'staging' | 'production';

// 日志级别类型
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// 缓存策略类型
export type CacheStrategy = 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB';

// 请求方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

// 内容类型
export type ContentType = 
  | 'application/json'
  | 'application/x-www-form-urlencoded'
  | 'multipart/form-data'
  | 'text/plain'
  | 'text/html'
  | 'application/xml';
