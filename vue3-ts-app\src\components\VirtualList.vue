<template>
  <view class="virtual-list" :style="{ height: containerHeight + 'px' }">
    <scroll-view
      :scroll-y="true"
      :style="{ height: containerHeight + 'px' }"
      @scroll="handleScroll"
      :scroll-top="scrollTop"
      :lower-threshold="lowerThreshold"
      @scrolltolower="handleScrollToLower"
      :refresher-enabled="refresherEnabled"
      :refresher-triggered="refresherTriggered"
      @refresherrefresh="handleRefresh"
    >
      <!-- 占位容器，用于撑开滚动高度 -->
      <view :style="{ height: totalHeight + 'px', position: 'relative' }">
        <!-- 可视区域内的项目 -->
        <view
          v-for="item in visibleItems"
          :key="getItemKey(item.data)"
          :style="{
            position: 'absolute',
            top: item.top + 'px',
            left: '0',
            right: '0',
            height: itemHeight + 'px'
          }"
          class="virtual-item"
        >
          <slot :item="item.data" :index="item.index"></slot>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'

interface Props {
  items: any[] // 数据列表
  itemHeight: number // 每项的高度
  containerHeight: number // 容器高度
  bufferSize?: number // 缓冲区大小
  lowerThreshold?: number // 触底阈值
  refresherEnabled?: boolean // 是否启用下拉刷新
  refresherTriggered?: boolean // 下拉刷新状态
  getItemKey?: (item: any) => string | number // 获取项目key的函数
}

const props = withDefaults(defineProps<Props>(), {
  bufferSize: 5,
  lowerThreshold: 100,
  refresherEnabled: false,
  refresherTriggered: false,
  getItemKey: (item: any) => item.id || Math.random()
})

const emit = defineEmits<{
  scrolltolower: []
  refresherrefresh: []
  scroll: [event: any]
}>()

// 滚动状态
const scrollTop = ref(0)
const currentScrollTop = ref(0)

// 计算总高度
const totalHeight = computed(() => props.items.length * props.itemHeight)

// 计算可视区域内的项目
const visibleItems = computed(() => {
  const containerHeight = props.containerHeight
  const itemHeight = props.itemHeight
  const bufferSize = props.bufferSize
  
  // 计算可视区域的开始和结束索引
  const startIndex = Math.max(0, Math.floor(currentScrollTop.value / itemHeight) - bufferSize)
  const endIndex = Math.min(
    props.items.length - 1,
    Math.ceil((currentScrollTop.value + containerHeight) / itemHeight) + bufferSize
  )
  
  // 生成可视项目列表
  const visibleItems = []
  for (let i = startIndex; i <= endIndex; i++) {
    if (props.items[i]) {
      visibleItems.push({
        index: i,
        data: props.items[i],
        top: i * itemHeight
      })
    }
  }
  
  return visibleItems
})

// 处理滚动事件
const handleScroll = (event: any) => {
  currentScrollTop.value = event.detail.scrollTop
  emit('scroll', event)
}

// 处理滚动到底部
const handleScrollToLower = () => {
  emit('scrolltolower')
}

// 处理下拉刷新
const handleRefresh = () => {
  emit('refresherrefresh')
}

// 滚动到顶部
const scrollToTop = () => {
  scrollTop.value = 0
  currentScrollTop.value = 0
}

// 滚动到指定位置
const scrollToIndex = (index: number) => {
  const targetScrollTop = index * props.itemHeight
  scrollTop.value = targetScrollTop
  currentScrollTop.value = targetScrollTop
}

// 暴露方法给父组件
defineExpose({
  scrollToTop,
  scrollToIndex
})
</script>

<style scoped lang="scss">
.virtual-list {
  position: relative;
  overflow: hidden;
}

.virtual-item {
  box-sizing: border-box;
}
</style>