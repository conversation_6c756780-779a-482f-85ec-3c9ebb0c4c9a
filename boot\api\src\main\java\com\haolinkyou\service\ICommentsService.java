package com.haolinkyou.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haolinkyou.api.vo.CommentsVo;
import com.haolinkyou.entity.Comments;

import java.util.List;

public interface ICommentsService extends IService<Comments> {
    List<CommentsVo> getCommentsByPostId(Integer postId);
    List<Comments> listAll();
    boolean addComment(Comments comment);
    boolean editComment(Comments comment);
    boolean deleteComment(Long id);
}
