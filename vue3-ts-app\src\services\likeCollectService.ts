import { get, post, del } from '@/utils/http'
import type { ApiResponse, Result } from '@/types/ApiResponse'

/**
 * 点赞和收藏相关的API服务
 */

// 点赞相关接口
export interface LikeResponse {
  isLiked: boolean
  likeCount: number
  message?: string
}

export interface CollectResponse {
  isCollected: boolean
  collectCount: number
  message?: string
}

export interface PostStatusResponse {
  isLiked: boolean
  isCollected: boolean
  likeCount: number
  collectCount: number
}

/**
 * 点赞或取消点赞
 * @param postId 帖子ID
 */
export const toggleLike = (postId: number) => {
  return post<Result<LikeResponse>>('/post-likes/toggle', {
    postId
  })
}

/**
 * 检查用户是否已点赞该帖子
 * @param postId 帖子ID
 */
export const checkUserLiked = (postId: number) => {
  return get<ApiResponse<LikeResponse>>('/post-likes/check', {
    postId
  })
}

/**
 * 获取帖子的点赞数
 * @param postId 帖子ID
 */
export const getPostLikeCount = (postId: number) => {
  return get<ApiResponse<number>>('/post-likes/count', {
    postId
  })
}

/**
 * 收藏或取消收藏
 * @param postId 帖子ID
 */
export const toggleCollect = (postId: number) => {
  return post<Result<CollectResponse>>('/user-collects/toggle', {
    postId
  })
}

/**
 * 检查用户是否已收藏该帖子
 * @param postId 帖子ID
 */
export const checkUserCollected = (postId: number) => {
  return get<ApiResponse<CollectResponse>>('/user-collects/check', {
    postId
  })
}

/**
 * 获取帖子的收藏数
 * @param postId 帖子ID
 */
export const getPostCollectCount = (postId: number) => {
  return get<ApiResponse<number>>('/user-collects/count', {
    postId
  })
}

/**
 * 获取帖子的点赞和收藏状态
 * @param postId 帖子ID
 */
export const getPostStatus = (postId: number) => {
  return get<ApiResponse<PostStatusResponse>>('/posts/status', {
    postId
  })
}

/**
 * 删除帖子
 * @param postId 帖子ID
 */
export const deletePost = (postId: number) => {
  return del<ApiResponse<boolean>>(`/posts/delete?id=${postId}`)
}

/**
 * 编辑帖子
 * @param editData 编辑数据
 */
export const editPost = (editData: {
  id: number
  content: string
  categoryId: number
  fileList: string
  fileType: number
  existingFiles?: string
}) => {
  // 使用FormData格式提交，类似发布帖子的方式
  const formData = new FormData()
  formData.append('id', editData.id.toString())
  formData.append('content', editData.content)
  formData.append('category_id', editData.categoryId.toString())
  formData.append('currentFileType', editData.fileType.toString())

  if (editData.existingFiles) {
    formData.append('existingFiles', editData.existingFiles)
  }

  // 获取token
  const token = uni.getStorageSync('token') || ''

  return fetch('/posts/edit', {
    method: 'POST',
    body: formData,
    headers: {
      'Authorization': token ? `Bearer ${token}` : ''
    }
  }).then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  })
}
