/*
 * @Author: Rock
 * @Date: 2025-04-30 22:58:38
 * @LastEditors: Rock
 * @LastEditTime: 2025-05-05 14:26:14
 * @Description: 
 */
package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@TableName("users")
@EqualsAndHashCode(callSuper = true)
public class Users extends BaseEntity {

    private String username;

    @TableField(select = false)
    private String password;

    private String nickname;

    @TableField("avatar")
    private String avatarUrl;

    private String mobile;

    private String email;

    private Integer gender;

    private Date birthday;

    private Integer points;

    @TableField("auth_type")
    private Integer authType;

    @TableField("verification_type")
    private String verificationType;

    @TableField("last_login_time")
    private Date lastLoginTime;

    @TableField("last_login_ip")
    private String lastLoginIp;

    @TableField("is_verified")
    private Boolean isVerified;

    @TableField("user_role")
    private String userRole;

    private String permissions;

    @TableField("house_number")
    private String houseNumber;

    @TableField("real_name")
    private String realName;

    private Integer status;

    // 新增字段
    private String bio; // 个人简介

    private String region; // 地区

    private String profession; // 职业

    // 签到相关字段
    @TableField("last_sign_date")
    private java.time.LocalDate lastSignDate; // 最后签到日期

    @TableField("continuous_sign_days")
    private Integer continuousSignDays; // 连续签到天数

    private String school; // 学校

    @TableField("red_book_id")
    private String redBookId; // 小红书号

    @TableField("background_image")
    private String backgroundImage; // 背景图
}
