package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.entity.Products;
import com.haolinkyou.entity.UserRedemptions;
import com.haolinkyou.mapper.ProductsMapper;
import com.haolinkyou.mapper.UserRedemptionsMapper;
import com.haolinkyou.service.IProductsService;
import com.haolinkyou.service.IPointsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 积分商品服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Service
public class ProductsServiceImpl extends ServiceImpl<ProductsMapper, Products> implements IProductsService {

    @Autowired
    private UserRedemptionsMapper userRedemptionsMapper;

    @Autowired
    private IPointsService pointsService;

    @Override
    public List<Products> getAvailableProducts() {
        QueryWrapper<Products> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1) // 上架状态
                   .gt("stock", 0) // 有库存
                   .orderByAsc("sort_order")
                   .orderByDesc("created_time");
        
        return list(queryWrapper);
    }

    @Override
    public Page<Products> getProductsPage(Integer page, Integer size, Integer status) {
        Page<Products> pageInfo = new Page<>(page, size);
        QueryWrapper<Products> queryWrapper = new QueryWrapper<>();
        
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByAsc("sort_order")
                   .orderByDesc("created_time");
        
        return page(pageInfo, queryWrapper);
    }

    @Override
    @Transactional
    public RedeemResult redeemProduct(Long userId, Long productId, Integer quantity, 
                                     String shippingAddress, String contactPhone, String remark) {
        try {
            // 参数验证
            if (userId == null || productId == null || quantity == null || quantity <= 0) {
                return new RedeemResult(false, "参数错误");
            }

            // 检查商品是否存在且可兑换
            Products product = getById(productId);
            if (product == null) {
                return new RedeemResult(false, "商品不存在");
            }

            if (product.getStatus() != 1) {
                return new RedeemResult(false, "商品已下架");
            }

            if (product.getStock() < quantity) {
                return new RedeemResult(false, "库存不足");
            }

            // 计算所需积分
            Integer totalPoints = product.getPoints() * quantity;

            // 检查用户积分余额
            Integer userPoints = pointsService.getUserPointsBalance(userId);
            if (userPoints < totalPoints) {
                return new RedeemResult(false, "积分不足");
            }

            // 扣除用户积分
            boolean pointsDeducted = pointsService.deductPoints(userId, totalPoints, "redeem", 
                "兑换商品：" + product.getName(), productId);
            if (!pointsDeducted) {
                return new RedeemResult(false, "积分扣除失败");
            }

            // 减少商品库存
            product.setStock(product.getStock() - quantity);
            product.setSalesCount((product.getSalesCount() != null ? product.getSalesCount() : 0) + quantity);
            updateById(product);

            // 创建兑换记录
            UserRedemptions redemption = new UserRedemptions();
            redemption.setUserId(userId);
            redemption.setProductId(productId);
            redemption.setProductName(product.getName());
            redemption.setPointsUsed(totalPoints);
            redemption.setQuantity(quantity);
            redemption.setStatus(0); // 待发货
            redemption.setShippingAddress(shippingAddress);
            redemption.setContactPhone(contactPhone);
            redemption.setRemark(remark);
            redemption.setCreatedTime(new Date());

            userRedemptionsMapper.insert(redemption);

            // 检查并更新商品状态
            checkAndUpdateProductStatus(productId);

            return new RedeemResult(true, "兑换成功", redemption.getId());

        } catch (Exception e) {
            log.error("商品兑换失败: userId=" + userId + ", productId=" + productId + ", quantity=" + quantity + ", error=" + e.getMessage());
            return new RedeemResult(false, "兑换失败，请重试");
        }
    }

    @Override
    public Page<UserRedemptions> getUserRedemptions(Long userId, Integer page, Integer size) {
        Page<UserRedemptions> pageInfo = new Page<>(page, size);
        QueryWrapper<UserRedemptions> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("created_time");
        
        return userRedemptionsMapper.selectPage(pageInfo, queryWrapper);
    }

    @Override
    public Page<UserRedemptions> getAllRedemptions(Integer page, Integer size, Integer status) {
        Page<UserRedemptions> pageInfo = new Page<>(page, size);
        QueryWrapper<UserRedemptions> queryWrapper = new QueryWrapper<>();
        
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByDesc("created_time");
        
        return userRedemptionsMapper.selectPage(pageInfo, queryWrapper);
    }

    @Override
    @Transactional
    public boolean updateRedemptionStatus(Long redemptionId, Integer status, String remark) {
        try {
            UserRedemptions redemption = userRedemptionsMapper.selectById(redemptionId);
            if (redemption == null) {
                return false;
            }

            redemption.setStatus(status);
            if (remark != null) {
                redemption.setRemark(remark);
            }
            redemption.setUpdatedTime(new Date());

            return userRedemptionsMapper.updateById(redemption) > 0;
        } catch (Exception e) {
            log.error("更新兑换订单状态失败: redemptionId=" + redemptionId + ", status=" + status + ", error=" + e.getMessage());
            return false;
        }
    }

    @Override
    public void checkAndUpdateProductStatus(Long productId) {
        try {
            Products product = getById(productId);
            if (product != null && product.getStock() <= 0 && product.getStatus() == 1) {
                // 库存为0时自动下架
                product.setStatus(0);
                updateById(product);
                System.out.println("商品库存不足自动下架: productId=" + productId + ", name=" + product.getName());
            }
        } catch (Exception e) {
            log.error("检查商品状态失败: productId=" + productId + ", error=" + e.getMessage());
        }
    }
}