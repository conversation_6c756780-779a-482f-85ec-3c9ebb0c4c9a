<template>
  <view class="like-collect-actions">
    <!-- 点赞按钮 -->
    <view class="action-item" @click="handleLike">
      <up-icon
        :name="isLiked ? 'thumb-up-fill' : 'thumb-up'"
        :size="iconSize"
        :color="isLiked ? '#ff6b6b' : iconColor"
      />
      <text class="action-text" :class="{ active: isLiked }">
        {{ likeCount || 0 }}
      </text>
      <text v-if="showText" class="action-label">点赞</text>
    </view>
    
    <!-- 收藏按钮 -->
    <view class="action-item" @click="handleCollect">
      <up-icon
        :name="isCollected ? 'star-fill' : 'star'"
        :size="iconSize"
        :color="isCollected ? '#ffc107' : iconColor"
      />
      <text class="action-text" :class="{ active: isCollected }">
        {{ collectCount || 0 }}
      </text>
      <text v-if="showText" class="action-label">收藏</text>
    </view>
    
    <!-- 评论按钮（可选） -->
    <view v-if="showComment" class="action-item" @click="handleComment">
      <up-icon name="chat" :size="iconSize" :color="iconColor" />
      <text class="action-text">{{ commentCount || 0 }}</text>
      <text v-if="showText" class="action-label">评论</text>
    </view>
    
    <!-- 分享按钮（可选） -->
    <view v-if="showShare" class="action-item" @click="handleShare">
      <up-icon name="share" :size="iconSize" :color="iconColor" />
      <text v-if="showText" class="action-label">分享</text>
    </view>
    
    <!-- 积分奖励提示 -->
    <up-toast ref="toastRef" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { toggleLike, toggleCollect } from '@/services/likeCollectService'
import { useMemberStore } from '@/stores'

interface Props {
  postId: number
  initialLiked?: boolean
  initialCollected?: boolean
  initialLikeCount?: number
  initialCollectCount?: number
  commentCount?: number
  iconSize?: number
  iconColor?: string
  showText?: boolean
  showComment?: boolean
  showShare?: boolean
  layout?: 'horizontal' | 'vertical'
}

const props = withDefaults(defineProps<Props>(), {
  initialLiked: false,
  initialCollected: false,
  initialLikeCount: 0,
  initialCollectCount: 0,
  commentCount: 0,
  iconSize: 20,
  iconColor: '#999',
  showText: false,
  showComment: false,
  showShare: false,
  layout: 'horizontal'
})

const memberStore = useMemberStore()
const toastRef = ref()

// 状态管理
const isLiked = ref(props.initialLiked)
const isCollected = ref(props.initialCollected)
const likeCount = ref(props.initialLikeCount)
const collectCount = ref(props.initialCollectCount)
const isProcessing = ref(false)

// 事件定义
const emit = defineEmits<{
  like: [{ isLiked: boolean; likeCount: number }]
  collect: [{ isCollected: boolean; collectCount: number }]
  comment: []
  share: []
}>()

// 处理点赞
const handleLike = async () => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  if (isProcessing.value) return
  isProcessing.value = true
  
  try {
    const result = await toggleLike(props.postId)
    
    if (result.success && result.data) {
      const newIsLiked = Boolean(result.data.isLiked)
      const newLikeCount = Number(result.data.likeCount) || 0
      
      isLiked.value = newIsLiked
      likeCount.value = newLikeCount
      
      // 显示积分奖励提示
      if (newIsLiked && result.data.pointsAwarded) {
        showPointsToast(`点赞成功，获得${result.data.pointsAwarded}积分！`)
      } else {
        uni.showToast({
          title: result.data.message || (newIsLiked ? '点赞成功' : '取消点赞'),
          icon: 'success',
          duration: 1500
        })
      }
      
      // 通知父组件
      emit('like', { isLiked: newIsLiked, likeCount: newLikeCount })
    } else {
      uni.showToast({
        title: result.message || '操作失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    isProcessing.value = false
  }
}

// 处理收藏
const handleCollect = async () => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  if (isProcessing.value) return
  isProcessing.value = true
  
  try {
    const result = await toggleCollect(props.postId)
    
    if (result.success && result.data) {
      const newIsCollected = Boolean(result.data.isCollected)
      const newCollectCount = Number(result.data.collectCount) || 0
      
      isCollected.value = newIsCollected
      collectCount.value = newCollectCount
      
      // 显示积分奖励提示
      if (newIsCollected && result.data.pointsAwarded) {
        showPointsToast(`收藏成功，获得${result.data.pointsAwarded}积分！`)
      } else {
        uni.showToast({
          title: result.data.message || (newIsCollected ? '收藏成功' : '取消收藏'),
          icon: 'success',
          duration: 1500
        })
      }
      
      // 通知父组件
      emit('collect', { isCollected: newIsCollected, collectCount: newCollectCount })
    } else {
      uni.showToast({
        title: result.message || '操作失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    isProcessing.value = false
  }
}

// 处理评论
const handleComment = () => {
  emit('comment')
}

// 处理分享
const handleShare = () => {
  emit('share')
}

// 显示积分奖励提示
const showPointsToast = (message: string) => {
  if (toastRef.value) {
    toastRef.value.show({
      title: message,
      type: 'success',
      icon: 'checkmark-circle-fill',
      duration: 2000
    })
  } else {
    uni.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    })
  }
}

// 暴露方法给父组件
defineExpose({
  updateLikeStatus: (liked: boolean, count: number) => {
    isLiked.value = liked
    likeCount.value = count
  },
  updateCollectStatus: (collected: boolean, count: number) => {
    isCollected.value = collected
    collectCount.value = count
  }
})
</script>

<style scoped lang="scss">
.like-collect-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  
  &.vertical {
    flex-direction: column;
    gap: 12px;
  }
}

.action-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:active {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(0.95);
  }
}

.action-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  min-width: 16px;
  text-align: center;
  
  &.active {
    color: #333;
    font-weight: 600;
  }
}

.action-label {
  font-size: 12px;
  color: #666;
  margin-left: 2px;
}

// 垂直布局样式
.like-collect-actions.vertical {
  .action-item {
    flex-direction: column;
    gap: 2px;
    padding: 8px 6px;
    min-width: 48px;
    text-align: center;
  }
  
  .action-text {
    font-size: 11px;
  }
  
  .action-label {
    font-size: 10px;
    margin-left: 0;
  }
}

// 大尺寸样式（用于详情页等）
.like-collect-actions.large {
  gap: 20px;
  
  .action-item {
    padding: 8px 12px;
    border-radius: 8px;
  }
  
  .action-text {
    font-size: 14px;
  }
  
  .action-label {
    font-size: 13px;
  }
}

// 紧凑样式（用于列表项等）
.like-collect-actions.compact {
  gap: 12px;
  
  .action-item {
    padding: 4px 6px;
    gap: 3px;
  }
  
  .action-text {
    font-size: 11px;
  }
  
  .action-label {
    font-size: 10px;
  }
}
</style>