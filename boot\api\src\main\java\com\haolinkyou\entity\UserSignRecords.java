package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;

/**
 * 用户签到记录实体类
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@TableName("user_sign_records")
public class UserSignRecords extends BaseEntity {

    private Long userId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate signDate;

    private Integer pointsEarned;

    private Integer continuousDays;

    // Constructors
    public UserSignRecords() {}

    // Getters and Setters
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public LocalDate getSignDate() { return signDate; }
    public void setSignDate(LocalDate signDate) { this.signDate = signDate; }

    public Integer getPointsEarned() { return pointsEarned; }
    public void setPointsEarned(Integer pointsEarned) { this.pointsEarned = pointsEarned; }

    public Integer getContinuousDays() { return continuousDays; }
    public void setContinuousDays(Integer continuousDays) { this.continuousDays = continuousDays; }

    @Override
    public String toString() {
        return "UserSignRecords{" +
                "id=" + getId() +
                ", userId=" + userId +
                ", signDate=" + signDate +
                ", pointsEarned=" + pointsEarned +
                ", continuousDays=" + continuousDays +
                ", createdTime=" + getCreatedTime() +
                ", updatedTime=" + getUpdatedTime() +
                ", delFlag=" + getDelFlag() +
                '}';
    }
}