import type { Directive, DirectiveBinding } from 'vue'
import { checkPermission, checkBackendAccess } from '@/services/permissionService'
import { useMemberStore } from '@/stores'

interface PermissionBinding {
  // 权限代码
  permission?: string
  // 权限代码数组
  permissions?: string[]
  // 是否需要管理后台权限
  requireBackendAccess?: boolean
  // 权限逻辑
  logic?: 'and' | 'or'
  // 权限不足时的处理方式
  onDenied?: 'hide' | 'disable' | 'custom'
  // 自定义处理函数
  customHandler?: (el: HTMLElement, hasPermission: boolean) => void
}

// 权限检查缓存
const permissionCache = new Map<string, { result: boolean; timestamp: number }>()
const CACHE_DURATION = 2 * 60 * 1000 // 2分钟缓存

/**
 * 权限指令
 * 用法：
 * v-permission="'admin_access'"
 * v-permission="{ permission: 'admin_access' }"
 * v-permission="{ permissions: ['admin_access', 'manage_users'], logic: 'or' }"
 * v-permission="{ requireBackendAccess: true }"
 */
export const vPermission: Directive<HTMLElement, string | PermissionBinding> = {
  async mounted(el: HTMLElement, binding: DirectiveBinding<string | PermissionBinding>) {
    await handlePermission(el, binding)
  },
  
  async updated(el: HTMLElement, binding: DirectiveBinding<string | PermissionBinding>) {
    await handlePermission(el, binding)
  }
}

async function handlePermission(el: HTMLElement, binding: DirectiveBinding<string | PermissionBinding>) {
  const memberStore = useMemberStore()
  
  // 如果用户未登录，直接隐藏元素
  if (!memberStore.profile?.id) {
    hideElement(el)
    return
  }
  
  let config: PermissionBinding
  
  // 处理不同的绑定值格式
  if (typeof binding.value === 'string') {
    config = { permission: binding.value }
  } else {
    config = binding.value || {}
  }
  
  const {
    permission,
    permissions = [],
    requireBackendAccess = false,
    logic = 'or',
    onDenied = 'hide',
    customHandler
  } = config
  
  try {
    let hasPermission = true
    
    // 检查管理后台权限
    if (requireBackendAccess) {
      const cacheKey = `backend_access_${memberStore.profile.userRole}`
      const cached = permissionCache.get(cacheKey)
      
      let backendAccess: boolean
      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        backendAccess = cached.result
      } else {
        backendAccess = await checkBackendAccess()
        permissionCache.set(cacheKey, {
          result: backendAccess,
          timestamp: Date.now()
        })
      }
      
      if (!backendAccess) {
        hasPermission = false
      }
    }
    
    // 检查单个权限
    if (hasPermission && permission) {
      const cacheKey = `${memberStore.profile.userRole}_${permission}`
      const cached = permissionCache.get(cacheKey)
      
      let singlePermission: boolean
      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        singlePermission = cached.result
      } else {
        singlePermission = await checkPermission(permission)
        permissionCache.set(cacheKey, {
          result: singlePermission,
          timestamp: Date.now()
        })
      }
      
      hasPermission = hasPermission && singlePermission
    }
    
    // 检查多个权限
    if (hasPermission && permissions.length > 0) {
      const permissionPromises = permissions.map(async (perm) => {
        const cacheKey = `${memberStore.profile.userRole}_${perm}`
        const cached = permissionCache.get(cacheKey)
        
        if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
          return cached.result
        } else {
          const result = await checkPermission(perm)
          permissionCache.set(cacheKey, {
            result,
            timestamp: Date.now()
          })
          return result
        }
      })
      
      const permissionResults = await Promise.all(permissionPromises)
      
      if (logic === 'and') {
        // 需要所有权限
        hasPermission = hasPermission && permissionResults.every(p => p)
      } else {
        // 满足任一权限即可
        hasPermission = hasPermission && permissionResults.some(p => p)
      }
    }
    
    // 处理权限结果
    if (customHandler) {
      customHandler(el, hasPermission)
    } else {
      switch (onDenied) {
        case 'hide':
          if (hasPermission) {
            showElement(el)
          } else {
            hideElement(el)
          }
          break
        case 'disable':
          if (hasPermission) {
            enableElement(el)
          } else {
            disableElement(el)
          }
          break
        default:
          if (hasPermission) {
            showElement(el)
          } else {
            hideElement(el)
          }
      }
    }
  } catch (error) {
    console.error('权限指令执行失败:', error)
    // 出错时默认隐藏元素
    hideElement(el)
  }
}

function hideElement(el: HTMLElement) {
  el.style.display = 'none'
}

function showElement(el: HTMLElement) {
  el.style.display = ''
}

function disableElement(el: HTMLElement) {
  el.style.opacity = '0.5'
  el.style.pointerEvents = 'none'
  
  // 如果是表单元素，设置disabled属性
  if (el.tagName === 'INPUT' || el.tagName === 'BUTTON' || el.tagName === 'SELECT' || el.tagName === 'TEXTAREA') {
    (el as any).disabled = true
  }
}

function enableElement(el: HTMLElement) {
  el.style.opacity = ''
  el.style.pointerEvents = ''
  
  // 如果是表单元素，移除disabled属性
  if (el.tagName === 'INPUT' || el.tagName === 'BUTTON' || el.tagName === 'SELECT' || el.tagName === 'TEXTAREA') {
    (el as any).disabled = false
  }
}

// 清除权限缓存
export function clearPermissionDirectiveCache() {
  permissionCache.clear()
}

export default vPermission
