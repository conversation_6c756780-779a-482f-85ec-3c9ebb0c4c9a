import { ref } from 'vue'
import { checkPermission, checkBackendAccess, validatePagePermissions } from '@/services/permissionService'
import { useMemberStore } from '@/stores'
import { PERMISSIONS } from '@/utils/rolePermissions'

/**
 * 页面权限配置
 */
export interface PagePermissionConfig {
  // 页面路径
  path: string
  // 需要的权限列表
  permissions?: string[]
  // 是否需要管理后台权限
  requireBackendAccess?: boolean
  // 是否需要登录
  requireLogin?: boolean
  // 权限不足时的处理方式
  onDenied?: 'redirect' | 'toast' | 'custom'
  // 重定向路径（当onDenied为redirect时使用）
  redirectTo?: string
  // 自定义处理函数
  customHandler?: () => void
}

/**
 * 页面权限配置映射
 */
export const PAGE_PERMISSIONS: Record<string, PagePermissionConfig> = {
  // 管理后台相关页面
  '/pages/admin/index': {
    path: '/pages/admin/index',
    requireBackendAccess: true,
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/my/index'
  },
  '/pages/admin/user-management': {
    path: '/pages/admin/user-management',
    permissions: [PERMISSIONS.MANAGE_USERS],
    requireBackendAccess: true,
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/admin/index'
  },
  '/pages/admin/post-management': {
    path: '/pages/admin/post-management',
    permissions: [PERMISSIONS.MANAGE_POSTS],
    requireBackendAccess: true,
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/admin/index'
  },
  '/pages/admin/auth-management': {
    path: '/pages/admin/auth-management',
    permissions: [PERMISSIONS.MANAGE_USERS],
    requireBackendAccess: true,
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/admin/index'
  },
  '/pages/admin/feedback-management': {
    path: '/pages/admin/feedback-management',
    requireBackendAccess: true,
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/admin/index'
  },
  '/pages/admin/product-management': {
    path: '/pages/admin/product-management',
    requireBackendAccess: true,
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/admin/index'
  },
  '/pages/admin/product-add': {
    path: '/pages/admin/product-add',
    requireBackendAccess: true,
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/admin/product-management'
  },
  '/pages/admin/share-config': {
    path: '/pages/admin/share-config',
    requireBackendAccess: true,
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/admin/index'
  },
  '/pages/admin/pending-reviews': {
    path: '/pages/admin/pending-reviews',
    permissions: [PERMISSIONS.MANAGE_POSTS, PERMISSIONS.MANAGE_USERS],
    requireBackendAccess: true,
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/admin/index'
  },
  
  // 个人中心相关页面
  '/pages/my/posts': {
    path: '/pages/my/posts',
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/login/index'
  },
  '/pages/my/collections': {
    path: '/pages/my/collections',
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/login/index'
  },
  '/pages/my/points': {
    path: '/pages/my/points',
    requireLogin: true,
    onDenied: 'redirect',
    redirectTo: '/pages/login/index'
  },
  
  // 发布相关页面
  '/pages/publish/index': {
    path: '/pages/publish/index',
    permissions: [PERMISSIONS.CREATE_POSTS],
    requireLogin: true,
    onDenied: 'toast'
  }
}

/**
 * 页面权限中间件
 * 在页面onLoad或onShow时调用
 * @param currentPath 当前页面路径
 * @returns Promise<boolean> 是否有权限访问
 */
export const checkPagePermission = async (currentPath: string): Promise<boolean> => {
  const config = PAGE_PERMISSIONS[currentPath]
  
  // 如果没有配置权限要求，直接允许访问
  if (!config) {
    return true
  }
  
  const memberStore = useMemberStore()
  
  // 检查登录要求
  if (config.requireLogin && !memberStore.profile?.id) {
    handlePermissionDenied(config, '请先登录')
    return false
  }
  
  // 检查管理后台权限
  if (config.requireBackendAccess) {
    try {
      const hasBackendAccess = await checkBackendAccess()
      if (!hasBackendAccess) {
        handlePermissionDenied(config, '权限不足，无法访问管理后台')
        return false
      }
    } catch (error) {
      console.error('检查管理后台权限失败:', error)
      handlePermissionDenied(config, '权限验证失败')
      return false
    }
  }
  
  // 检查具体权限
  if (config.permissions && config.permissions.length > 0) {
    try {
      const hasPermission = await validatePagePermissions(config.permissions)
      if (!hasPermission) {
        return false // validatePagePermissions内部已处理错误提示
      }
    } catch (error) {
      console.error('检查页面权限失败:', error)
      handlePermissionDenied(config, '权限验证失败')
      return false
    }
  }
  
  return true
}

/**
 * 处理权限不足的情况
 */
function handlePermissionDenied(config: PagePermissionConfig, message: string) {
  switch (config.onDenied) {
    case 'redirect':
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 1500
      })
      
      setTimeout(() => {
        if (config.redirectTo) {
          uni.redirectTo({
            url: config.redirectTo,
            fail: () => {
              // 如果重定向失败，尝试切换到首页
              uni.switchTab({
                url: '/pages/index/index'
              })
            }
          })
        } else {
          uni.navigateBack({
            fail: () => {
              uni.switchTab({
                url: '/pages/index/index'
              })
            }
          })
        }
      }, 1500)
      break
      
    case 'toast':
      uni.showToast({
        title: message,
        icon: 'none'
      })
      break
      
    case 'custom':
      if (config.customHandler) {
        config.customHandler()
      }
      break
      
    default:
      uni.showToast({
        title: message,
        icon: 'none'
      })
  }
}

/**
 * 页面权限装饰器
 * 用于Vue组件的onMounted钩子
 * @param pagePath 页面路径
 * @returns 装饰器函数
 */
export const withPagePermission = (pagePath: string) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const hasPermission = await checkPagePermission(pagePath)
      if (hasPermission) {
        return originalMethod.apply(this, args)
      }
    }
    
    return descriptor
  }
}

/**
 * 创建页面权限守卫组合式函数
 * @param pagePath 页面路径
 * @returns 权限状态和检查函数
 */
export const usePagePermission = (pagePath: string) => {
  const loading = ref(true)
  const hasPermission = ref(false)
  
  const checkPermission = async () => {
    loading.value = true
    try {
      hasPermission.value = await checkPagePermission(pagePath)
    } catch (error) {
      console.error('页面权限检查失败:', error)
      hasPermission.value = false
    } finally {
      loading.value = false
    }
  }
  
  return {
    loading,
    hasPermission,
    checkPermission
  }
}

/**
 * 批量检查多个页面权限
 * @param pagePaths 页面路径数组
 * @returns Promise<Record<string, boolean>> 权限检查结果
 */
export const checkMultiplePagePermissions = async (pagePaths: string[]): Promise<Record<string, boolean>> => {
  const results: Record<string, boolean> = {}
  
  const promises = pagePaths.map(async (path) => {
    const hasPermission = await checkPagePermission(path)
    results[path] = hasPermission
  })
  
  await Promise.all(promises)
  return results
}
