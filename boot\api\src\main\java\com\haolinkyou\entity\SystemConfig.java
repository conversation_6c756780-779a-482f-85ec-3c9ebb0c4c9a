package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 系统配置实体类
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@TableName("system_config")
public class SystemConfig extends BaseEntity {

    private String configKey;

    private String configValue;

    private String configType;

    private String description;

    private String groupName;

    private Integer sortOrder;

    private Integer isSystem;

    // Constructors
    public SystemConfig() {}

    // Getters and Setters
    public String getConfigKey() { return configKey; }
    public void setConfigKey(String configKey) { this.configKey = configKey; }

    public String getConfigValue() { return configValue; }
    public void setConfigValue(String configValue) { this.configValue = configValue; }

    public String getConfigType() { return configType; }
    public void setConfigType(String configType) { this.configType = configType; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getGroupName() { return groupName; }
    public void setGroupName(String groupName) { this.groupName = groupName; }

    public Integer getSortOrder() { return sortOrder; }
    public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }

    public Integer getIsSystem() { return isSystem; }
    public void setIsSystem(Integer isSystem) { this.isSystem = isSystem; }

    @Override
    public String toString() {
        return "SystemConfig{" +
                "id=" + getId() +
                ", configKey='" + configKey + '\'' +
                ", configValue='" + configValue + '\'' +
                ", configType='" + configType + '\'' +
                ", description='" + description + '\'' +
                ", groupName='" + groupName + '\'' +
                ", sortOrder=" + sortOrder +
                ", isSystem=" + isSystem +
                ", createdTime=" + getCreatedTime() +
                ", updatedTime=" + getUpdatedTime() +
                ", delFlag=" + getDelFlag() +
                '}';
    }
}