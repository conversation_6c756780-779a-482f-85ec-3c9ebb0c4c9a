package com.haolinkyou.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haolinkyou.entity.UserCollects;
import com.haolinkyou.vo.UserCollectVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户收藏Mapper接口
 */
@Mapper
public interface UserCollectsMapper extends BaseMapper<UserCollects> {

    /**
     * 检查用户是否已收藏该帖子
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 收藏记录数量
     */
    int checkUserCollected(@Param("postId") Long postId, @Param("userId") Long userId);

    /**
     * 获取帖子的收藏数
     * @param postId 帖子ID
     * @return 收藏数
     */
    int getPostCollectCount(@Param("postId") Long postId);

    /**
     * 添加收藏记录
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 影响的行数
     */
    int addCollect(@Param("postId") Long postId, @Param("userId") Long userId);

    /**
     * 取消收藏（物理删除）
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 影响的行数
     */
    int removeCollect(@Param("postId") Long postId, @Param("userId") Long userId);

    /**
     * 获取用户收藏列表
     * @param page 分页对象
     * @param userId 用户ID
     * @param categoryId 分类ID（可选）
     * @param sortBy 排序方式
     * @return 收藏列表分页数据
     */
    IPage<UserCollectVo> getUserCollections(Page<UserCollectVo> page,
                                           @Param("userId") Long userId,
                                           @Param("categoryId") String categoryId,
                                           @Param("sortBy") String sortBy);

    /**
     * 获取用户今日收藏数
     * @param userId 用户ID
     * @return 今日收藏数
     */
    int getUserTodayCollectCount(@Param("userId") Long userId);

    /**
     * 获取用户本月收藏数
     * @param userId 用户ID
     * @return 本月收藏数
     */
    int getUserMonthCollectCount(@Param("userId") Long userId);
}
