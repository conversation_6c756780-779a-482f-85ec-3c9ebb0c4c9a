package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.entity.TemplateFields;
import com.haolinkyou.mapper.TemplateFieldsMapper;
import com.haolinkyou.service.ITemplateFieldsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 模板字段服务实现类
 */
@Service
public class TemplateFieldsServiceImpl extends ServiceImpl<TemplateFieldsMapper, TemplateFields> implements ITemplateFieldsService {

    private static final Logger log = LoggerFactory.getLogger(TemplateFieldsServiceImpl.class);

    @Override
    public List<TemplateFields> getFieldsByTemplateId(Long templateId) {
        try {
            QueryWrapper<TemplateFields> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("template_id", templateId)
                       .eq("status", 1)
                       .orderByAsc("sort_order");
            
            return list(queryWrapper);
        } catch (Exception e) {
            log.error("获取模板字段失败，模板ID: {}", templateId, e);
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional
    public boolean saveTemplateFields(Long templateId, List<TemplateFields> fields) {
        try {
            // 先删除原有字段
            deleteFieldsByTemplateId(templateId);

            // 保存新字段
            if (fields != null && !fields.isEmpty()) {
                for (TemplateFields field : fields) {
                    field.setTemplateId(templateId);
                    field.setId(null); // 确保是新增
                    if (field.getStatus() == null) {
                        field.setStatus(1);
                    }
                }
                return saveBatch(fields);
            }

            return true;
        } catch (Exception e) {
            log.error("保存模板字段失败，模板ID: {}", templateId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteFieldsByTemplateId(Long templateId) {
        try {
            QueryWrapper<TemplateFields> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("template_id", templateId);
            
            return remove(queryWrapper);
        } catch (Exception e) {
            log.error("删除模板字段失败，模板ID: {}", templateId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean createDefaultFields(Long templateId, Long categoryId) {
        try {
            List<TemplateFields> defaultFields = new ArrayList<>();

            // 标题字段（所有分类都有）
            TemplateFields titleField = new TemplateFields();
            titleField.setTemplateId(templateId);
            titleField.setFieldName("title");
            titleField.setFieldLabel("标题");
            titleField.setFieldType("text");
            titleField.setRequired(true);
            titleField.setSortOrder(1);
            titleField.setStatus(1);
            titleField.setPlaceholder("请输入帖子标题");
            titleField.setHelpText("标题长度不超过100个字符");
            defaultFields.add(titleField);

            // 内容字段（所有分类都有）
            TemplateFields contentField = new TemplateFields();
            contentField.setTemplateId(templateId);
            contentField.setFieldName("content");
            contentField.setFieldLabel("内容");
            contentField.setFieldType("textarea");
            contentField.setRequired(true);
            contentField.setSortOrder(2);
            contentField.setStatus(1);
            contentField.setPlaceholder("请输入帖子内容");
            contentField.setHelpText("详细描述您要分享的内容");
            defaultFields.add(contentField);

            // 图片字段（所有分类都有）
            TemplateFields imagesField = new TemplateFields();
            imagesField.setTemplateId(templateId);
            imagesField.setFieldName("images");
            imagesField.setFieldLabel("图片");
            imagesField.setFieldType("image");
            imagesField.setRequired(false);
            imagesField.setSortOrder(3);
            imagesField.setStatus(1);
            imagesField.setPlaceholder("选择图片");
            imagesField.setHelpText("最多可上传9张图片");
            defaultFields.add(imagesField);

            // 根据分类添加特殊字段
            addCategorySpecificFields(defaultFields, templateId, categoryId);

            return saveBatch(defaultFields);
        } catch (Exception e) {
            log.error("创建默认字段失败，模板ID: {}, 分类ID: {}", templateId, categoryId, e);
            return false;
        }
    }

    /**
     * 根据分类添加特殊字段
     */
    private void addCategorySpecificFields(List<TemplateFields> fields, Long templateId, Long categoryId) {
        // 这里可以根据分类ID添加特殊字段
        // 例如：公告分类可能需要有效期字段，团购分类可能需要价格字段等
        
        // 示例：如果是公告分类（假设分类ID为1）
        if (categoryId != null && categoryId.equals(1L)) {
            // 有效期字段
            TemplateFields validDateField = new TemplateFields();
            validDateField.setTemplateId(templateId);
            validDateField.setFieldName("validDate");
            validDateField.setFieldLabel("有效期");
            validDateField.setFieldType("date");
            validDateField.setRequired(false);
            validDateField.setSortOrder(4);
            validDateField.setStatus(1);
            validDateField.setPlaceholder("选择有效期");
            validDateField.setHelpText("公告的有效期限");
            fields.add(validDateField);

            // 重要程度字段
            TemplateFields priorityField = new TemplateFields();
            priorityField.setTemplateId(templateId);
            priorityField.setFieldName("priority");
            priorityField.setFieldLabel("重要程度");
            priorityField.setFieldType("select");
            priorityField.setFieldConfig("{\"options\":[{\"label\":\"普通\",\"value\":\"normal\"},{\"label\":\"重要\",\"value\":\"important\"},{\"label\":\"紧急\",\"value\":\"urgent\"}]}");
            priorityField.setRequired(false);
            priorityField.setSortOrder(5);
            priorityField.setStatus(1);
            priorityField.setHelpText("选择公告的重要程度");
            fields.add(priorityField);
        }

        // 示例：如果是团购分类（假设分类ID为2）
        if (categoryId != null && categoryId.equals(2L)) {
            // 价格字段
            TemplateFields priceField = new TemplateFields();
            priceField.setTemplateId(templateId);
            priceField.setFieldName("price");
            priceField.setFieldLabel("价格");
            priceField.setFieldType("number");
            priceField.setRequired(true);
            priceField.setSortOrder(4);
            priceField.setStatus(1);
            priceField.setPlaceholder("请输入价格");
            priceField.setHelpText("商品的团购价格");
            fields.add(priceField);

            // 截止时间字段
            TemplateFields deadlineField = new TemplateFields();
            deadlineField.setTemplateId(templateId);
            deadlineField.setFieldName("deadline");
            deadlineField.setFieldLabel("截止时间");
            deadlineField.setFieldType("date");
            deadlineField.setRequired(true);
            deadlineField.setSortOrder(5);
            deadlineField.setStatus(1);
            deadlineField.setPlaceholder("选择截止时间");
            deadlineField.setHelpText("团购的截止时间");
            fields.add(deadlineField);

            // 联系方式字段
            TemplateFields contactField = new TemplateFields();
            contactField.setTemplateId(templateId);
            contactField.setFieldName("contact");
            contactField.setFieldLabel("联系方式");
            contactField.setFieldType("text");
            contactField.setRequired(true);
            contactField.setSortOrder(6);
            contactField.setStatus(1);
            contactField.setPlaceholder("请输入联系方式");
            contactField.setHelpText("方便其他用户联系您");
            fields.add(contactField);
        }
    }
}
