import { get, post, del } from '@/utils/http'
import type { Result } from '@/types/ApiResponse'
import type { PostManagement } from '@/types/admin'

/**
 * 文章管理相关的API服务
 */

// 分页查询参数
export interface PostManagementQuery {
  page?: number
  pageSize?: number
  categoryId?: number | string
  title?: string
  status?: number
}

// 分页响应数据
export interface PageResponse<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 统计数据响应
export interface PostStatsResponse {
  total: number
  pending: number
  approved: number
  rejected: number
}

/**
 * 获取管理员文章列表
 * @param params 查询参数
 */
export const getAdminPostListAPI = (params: PostManagementQuery) => {
  return get<Result<PageResponse<PostManagement>>>('/posts/admin/list', params)
}

/**
 * 获取文章统计数据
 */
export const getPostStatsAPI = () => {
  return get<Result<PostStatsResponse>>('/posts/admin/stats')
}

/**
 * 审核通过文章
 * @param postId 文章ID
 */
export const approvePostAPI = (postId: number) => {
  return post<Result<string>>(`/posts/admin/approve/${postId}`)
}

/**
 * 审核拒绝文章
 * @param postId 文章ID
 * @param reason 拒绝原因
 */
export const rejectPostAPI = (postId: number, reason: string) => {
  return post<Result<string>>(`/posts/admin/reject/${postId}`, { reason })
}

/**
 * 删除文章
 * @param postId 文章ID
 */
export const deletePostAPI = (postId: number) => {
  return del<Result<boolean>>(`/posts/delete?id=${postId}`)
}

/**
 * 搜索文章
 * @param params 搜索参数
 */
export const searchPostsAPI = (params: PostManagementQuery) => {
  return get<Result<PageResponse<PostManagement>>>('/posts/admin/list', params)
}
