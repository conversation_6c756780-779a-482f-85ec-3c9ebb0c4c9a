<!--
 * @Author: Rock
 * @Date: 2025-04-12 21:29:18
 * @LastEditors: Rock
 * @LastEditTime: 2025-07-29 16:21:17
 * @Description: 
-->
<template>
  <view class="post-detail-page">
    <!-- 帖子内容部分 -->
    <view class="post-content-section">
      <view class="post-header">
        <up-avatar :text="postData.nickname" fontSize="18" randomBgColor></up-avatar>
        <view class="info">
          <view class="user-info">
            <view class="name-row">
              <view class="name">{{ postData.nickname }}</view>
              <view class="sex">
                <up-icon v-if="postData.userGender !== 0" :name="postData.userGender === 1 ? 'man' : 'woman'" size="18"
                  :color="postData.userGender === 1 ? '#4396F7' : '#FF2442'"></up-icon>
              </view>
              <!-- 用户认证状态标签 -->
              <up-tag :text="getRoleStatusTag(postData.userIsVerified, postData.userRole).text" size="mini"
                :color="getRoleStatusTag(postData.userIsVerified, postData.userRole).color"
                :bgColor="getRoleStatusTag(postData.userIsVerified, postData.userRole).bgColor"
                borderColor="transparent" shape="circle" plain plainFill />
            </view>
          </view>
          <view class="post-time">{{ formatPostTime(postData) }}</view>
          <!-- 置顶标识 -->
          <PostTopBadge :isTopped="postData.isTop" :topExpireTime="postData.topExpireTime" :showExpireTime="true" />
        </view>
        <!-- 操作按钮区域 -->
        <view class="post-actions" v-if="canEditPost">
          <!-- 置顶组件 -->
          <PostTopComponent :postId="postData.id" :isTopped="postData.isTop" @topSuccess="handleTopSuccess"
            @cancelSuccess="handleCancelSuccess" />
            <up-button text="编辑" type="info" size="mini"
              @click="handleEditPost"
            >
            <template #icon>
              <up-icon name="edit-pen" size="14"></up-icon>
            </template>
          </up-button>
        </view>
      </view>
      <!-- 使用新的模板详情组件 -->
      <PostDetailContent :post="postData" @participate="handleParticipate" @vote="handleVote"
        @viewResults="handleViewResults" />
      <view class="post-footer">
        <view class="post-footer-left">
          <up-tag :text="postData.categoryName" color="#007AFF" size="mini" plain plainFill borderColor="transparent"
            :autoBgColor="95"></up-tag>
        </view>
        <view class="post-footer-right">
          <view class="action-btn" @click="handleQuickAction('share')">
            <up-icon name="share" size="16" color="#666"></up-icon>
            <text>分享</text>
          </view>
          <view class="action-btn" @click="handleQuickAction('report')">
            <up-icon name="warning" size="16" color="#666"></up-icon>
            <text>举报</text>
          </view>
          <!-- 删除按钮：只有帖子作者或管理员可见 -->
          <view v-if="canDeletePost()" class="action-btn" @click="handleDeletePost()">
            <up-icon name="trash" size="16" color="#FF2442"></up-icon>
            <text style="color: #FF2442;">删除</text>
          </view>
        </view>
      </view>
    </view>
    <up-divider style="margin: 0;" />

    <!-- 留言部分 -->
    <view class="comment-section">
      <view class="comment-header">
        <view class="comment-count">评论 {{ postData.commentCount || 0 }}</view>
        <view class="comment-stats">
          <text class="like-collect-text">点赞 {{ likeCount || postData.likeCount || 0 }} · 收藏 {{ collectCount ||
            postData.collectCount || 0 }}</text>
        </view>
      </view>

      <!-- 快速评论输入框 -->
      <view class="quick-comment-input">
        <up-avatar :text="currentUser.avatarText" :size="32" randomBgColor></up-avatar>
        <view class="input-placeholder" @click="handleShowComment(null)">
          <text>{{ buttonTip }}</text>
        </view>
        <!-- <view class="input-actions">
          <up-icon name="photo" size="20" color="#999"></up-icon>
          <up-icon name="smile" size="20" color="#999"></up-icon>
          <up-icon name="photo" size="20" color="#999"></up-icon>
        </view> -->
      </view>

      <!-- 评论列表 -->
      <view class="comment-list">
        <view v-for="(comment, index) in commentList" :key="comment.commentId || index" class="comment-item-container">
          <!-- 主评论 -->
          <view class="main-comment" @click="handleShowComment(comment)">
            <view class="comment-avatar">
              <up-avatar :text="comment.avatarText" :size="36" randomBgColor></up-avatar>
            </view>
            <view class="comment-main">
              <view class="comment-header-info">
                <view class="user-info">
                  <text class="username">{{ comment.username }}</text>
                  <up-tag text="作者" color="#FF2442" size="mini" plain plainFill borderColor="transparent"
                    :autoBgColor="95" v-if="postData.userId == comment.userId"></up-tag>
                </view>
                <view class="comment-actions">
                  <!-- 评论点赞功能暂未实现 -->
                </view>
              </view>
              <view class="comment-content">
                <text class="content-text">{{ comment.content }}
                  <text class="comment-time">{{ formatRelativeTime(comment.createdTime || comment.time) }}</text>
                  <text class="reply-text" @click.stop="handleShowComment(comment)">回复</text>
                  <text class="delete-text" @click.stop="deleteComment(comment.commentId)"
                    v-if="canDeleteComment(comment)">删除</text>
                </text>
              </view>
            </view>
          </view>

          <!-- 回复列表 - 默认显示第一条 -->
          <view v-if="comment.replies && comment.replies.length > 0" class="reply-list">
            <!-- 第一条回复始终显示 -->
            <view class="reply-item" @click.stop="handleShowComment(comment.replies[0])">
              <view class="reply-avatar">
                <up-avatar :text="comment.replies[0].avatarText" :size="28" randomBgColor></up-avatar>
              </view>
              <view class="reply-main">
                <view class="reply-header">
                  <text class="reply-username">{{ comment.replies[0].username }}</text>
                  <up-tag text="作者" color="#FF2442" size="mini" plain plainFill borderColor="transparent"
                    :autoBgColor="95" v-if="postData.userId == comment.replies[0].userId"></up-tag>
                </view>
                <view class="reply-content">
                  <text class="reply-text-content">{{ formatReplyContent(comment.replies[0]) }}
                    <text class="reply-time">{{ formatRelativeTime(comment.replies[0].createdTime ||
                      comment.replies[0].time) }}</text>
                    <text class="reply-action" @click.stop="handleShowComment(comment.replies[0])">回复</text>
                    <text class="delete-text" @click.stop="deleteComment(comment.replies[0].commentId)"
                      v-if="canDeleteComment(comment.replies[0])">删除</text>
                  </text>
                </view>
              </view>
              <view class="reply-actions">
                <!-- 回复点赞功能暂未实现 -->
              </view>
            </view>

            <!-- 其余回复在展开时显示 -->
            <view v-if="isCommentExpanded(comment.commentId) && comment.replies.length > 1">
              <view v-for="(reply, replyIndex) in comment.replies.slice(1)" :key="reply.commentId || replyIndex"
                class="reply-item" @click.stop="handleShowComment(reply)">
                <view class="reply-avatar">
                  <up-avatar :text="reply.avatarText" :size="28" randomBgColor></up-avatar>
                </view>
                <view class="reply-main">
                  <view class="reply-header">
                    <text class="reply-username">{{ reply.username }}</text>
                    <up-tag text="作者" color="#FF2442" size="mini" plain plainFill borderColor="transparent"
                      :autoBgColor="95" v-if="postData.userId == reply.userId"></up-tag>
                  </view>
                  <view class="reply-content">
                    <text class="reply-text-content">{{ formatReplyContent(reply) }}
                      <text class="reply-time">{{ formatRelativeTime(reply.createdTime || reply.time) }}</text>
                      <text class="reply-action" @click.stop="handleShowComment(reply)">回复</text>
                      <text class="delete-text" @click.stop="deleteComment(reply.commentId)"
                        v-if="canDeleteComment(reply)">删除</text>
                    </text>
                  </view>
                </view>
                <view class="reply-actions">
                  <!-- 回复点赞功能暂未实现 -->
                </view>
              </view>
            </view>

            <!-- 展开/折叠按钮 - 放在子评论下面，只有多于1条回复时才显示 -->
            <view v-if="comment.replies.length > 1" class="reply-toggle-section">
              <view class="reply-toggle-btn" @click.stop="toggleCommentExpanded(comment.commentId)">
                <up-icon :name="isCommentExpanded(comment.commentId) ? 'arrow-up' : 'arrow-down'" size="10"
                  color="#007AFF"></up-icon>
                <text class="toggle-text">
                  {{ isCommentExpanded(comment.commentId) ? '收起回复' : getExpandButtonText(comment.replies) }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="post-detail-footer">
      <up-divider :text="(postData.commentCount || 0) > 0 ? '到底了' : '抢首评'" textSize="12px"></up-divider>
    </view>
    <!-- 底部操作栏 -->
    <view class="post-footer-wrap" :style="{ paddingBottom: safeAreaBottom }">
      <up-button :text="buttonTip" icon="edit-pen" plain shape="circle"
        style="width:calc(100% - 150px) !important;justify-content: left;" @click="handleShowComment(null)"></up-button>
      <view class="comment-tools-wrapper">
        <view class="comment-tool">
          <up-icon stop :name="isLiked ? 'thumb-up-fill' : 'thumb-up'" @click="handleLike" size="28"
            :color="isLiked ? '#FF2442' : '#9fa0a3'"></up-icon>
          <view class="comment-like-count">{{ likeCount || postData.likeCount || 0 }}</view>
        </view>
        <view class="comment-tool">
          <up-icon stop :name="isCollected ? 'star-fill' : 'star'" @click="handleCollect" size="28"
            :color="isCollected ? '#EBAF58' : '#9fa0a3'"></up-icon>
          <view class="comment-like-count">{{ collectCount || postData.collectCount || 0 }}</view>
        </view>
        <view class="comment-tool">
          <up-icon stop name="chat" @click="handleClearComment" size="28" color="#9fa0a3"></up-icon>
          <view class="comment-like-count">{{ postData.commentCount || 0 }}</view>
        </view>
      </view>
    </view>

    <!-- 回复弹窗 -->
    <up-popup :show="show" @close="close" @open="open" round="10px">
      <view class="comment-popup-wrap">
        <up-textarea v-model="commentContent" :maxlength="1000" :placeholder="commentTip" autoHeight></up-textarea>
        <view class="foot-wrap">
          <view class="tools">
            <!-- <up-icon stop name="photo"  size="28" color="#9fa0a3"></up-icon>
              <up-icon stop name="camera"  size="28" color="#9fa0a3"></up-icon> -->
          </view>
          <view class="submit-btn">
            <u-button type="primary" hairline :text="activeReplyId ? '回复' : '评论'" size="small" @click="submitComment"
              :disabled="!commentContent"></u-button>
          </view>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { useSafeArea } from '@/utils/safeArea';
import { get, post } from '@/utils/http';
import { setFileList } from '@/utils/util';
import { toggleLike, toggleCollect, getPostStatus, deletePost } from '@/services/likeCollectService';
import { postStateManager } from '@/utils/postStateManager';
import { useMemberStore } from '@/stores';
import { getRoleStatusTag } from '@/utils/rolePermissions';
import { formatRelativeTime, formatPostTime } from '@/utils/timeFormat';
import PostTopComponent from '@/components/PostTopComponent.vue';
import PostTopBadge from '@/components/PostTopBadge.vue';
import PostDetailContent from '@/components/post-detail/PostDetailContent.vue';
import type { PostsList, Comments, Result, SwiperChangeHandler } from '@/types';

const { safeAreaBottom } = useSafeArea()
const postId = ref(0);
const postData = ref<PostsList>({} as PostsList);
const commentList = ref<Comments[]>([]);
const commentContent = ref('');
const commentTip = ref('善言结善缘，恶语伤人心.');
const buttonTip = ref('善言结善缘，恶语伤人心.');
const activeReplyId = ref<string | number | null>(null);

// 评论折叠状态管理
const expandedComments = ref<Set<number>>(new Set());

// 获取用户信息
const memberStore = useMemberStore();

// 编辑权限检查
const canEditPost = computed(() => {
  // 只有帖子作者可以编辑
  return memberStore.profile?.id && postData.value.userId === memberStore.profile.id
})

// 处理编辑帖子
const handleEditPost = () => {
  if (!canEditPost.value) {
    uni.showToast({
      title: '无权限编辑此帖子',
      icon: 'none'
    })
    return
  }

  // 跳转到编辑页面
  uni.navigateTo({
    url: `/pages/post/edit?id=${postData.value.id}`
  })
}

// 当前用户信息 - 从store中获取或使用默认值
const currentUser = computed(() => {
  if (memberStore.profile) {
    return {
      id: memberStore.profile.id,
      avatarText: memberStore.profile.nickname?.charAt(0) || '游',
      nickName: memberStore.profile.nickname || '游客',
      status: '在线'
    };
  }
  return {
    id: 0,
    avatarText: '游',
    nickName: '游客',
    status: '离线'
  };
});

// 格式化回复内容，添加"回复 用户名:"前缀
const formatReplyContent = (reply: Comments) => {
  // 如果有被回复用户名，且被回复的不是自己，则添加前缀
  if (reply.parentUsername && reply.parentUsername !== currentUser.value?.nickName) {
    return `回复 ${reply.parentUsername}: ${reply.content}`;
  }
  return reply.content;
};

// 获取帖子详情
const fetchPostDetail = async (id: number) => {
  postData.value = {} as PostsList
  try {
    const res = await get<Result<PostsList>>(`/posts/detail?id=${id}`);

    if (res.success && res.data) {
      let resData: PostsList = res.data as PostsList
      console.log("原始帖子数据:", resData)
      console.log("fileType:", resData.fileType, "fileList:", resData.fileList)

      // 处理文件列表 - 兼容不同的fileType类型
      if ((resData.fileType === 0 || resData.fileType === '0') && resData.fileList && resData.fileList !== '') {
        resData.fileListData = setFileList(resData.fileList)
        console.log("处理后的fileListData:", resData.fileListData)
      } else {
        resData.fileListData = []
        console.log("没有文件或文件类型不匹配，设置为空数组")
      }
      postData.value = resData;
      console.log("获取帖子详情成功:", postData.value.fileListData)
    } else {
      console.error('Failed to fetch post detail:', res.message);
    }
  } catch (err) {
    console.error('Request failed:', err);
  }
};

// 是否多张图片（数组长度 > 1）
const isMultiMedia = computed(() => {
  return Array.isArray(postData.value.fileListData) && postData.value.fileListData.length > 1;
});

// 是否单个文件（数组长度为 1）
const isSingleMedia = computed(() => {
  return Array.isArray(postData.value.fileListData) && postData.value.fileListData.length === 1;
});

// 获取单个媒体地址
const singleMediaUrl = computed(() => {
  return Array.isArray(postData.value.fileListData) && postData.value.fileListData[0]
    ? postData.value.fileListData[0]
    : '';
});

// 为 up-swiper 格式化图片列表 - 直接使用字符串数组
const swiperList = computed(() => {
  if (!Array.isArray(postData.value.fileListData)) {
    return [];
  }
  return postData.value.fileListData;
});

// 计算实际评论总数（用于调试验证）
const calculateActualCommentCount = (comments: Comments[]): number => {
  let total = 0;
  comments.forEach(comment => {
    total += 1; // 主评论
    if (comment.replies && Array.isArray(comment.replies)) {
      total += comment.replies.length; // 回复数
    }
  });
  return total;
};

// 获取留言列表
const fetchComments = async (id: number) => {
  commentList.value = []
  try {
    const res = await get<Result<Comments[]>>(`/comments/getCommentsByPostId?postId=${id}`);
    if (res.success && res.data) {
      let comments: Comments[] = res.data;

      // 临时添加模拟的parentUsername数据用于测试
      if (Array.isArray(comments)) {
        comments = comments.map((comment: Comments) => {
          if (comment.replies && Array.isArray(comment.replies)) {
            comment.replies = comment.replies.map((reply: Comments) => {
              // 为回复添加模拟的parentUsername
              if (reply.commentId === 2) {
                reply.parentUsername = "王五"; // 模拟commentId=2的用户名
              } else if (reply.commentId === 1) {
                reply.parentUsername = "李四"; // 模拟commentId=1的用户名
              } else if (reply.commentId === 10) {
                reply.parentUsername = "管理员"; // 模拟commentId=10的用户名
              }
              return reply;
            });
          }
          return comment;
        });
      }

      commentList.value = comments;

      // 调试：比较实际评论数和数据库中的评论数
      const actualCount = calculateActualCommentCount(comments);
      const dbCount = postData.value.commentCount || 0;
      console.log("评论数对比:", {
        实际评论数: actualCount,
        数据库评论数: dbCount,
        是否一致: actualCount === dbCount
      });

      if (actualCount !== dbCount) {
        console.warn("评论数不一致！需要更新数据库中的评论数");
        // 自动触发评论数更新
        await updatePostCommentCount(id);
      }
    } else {
      console.error('Failed to fetch comments:', res.message);
    }
  } catch (err) {
    console.error('Request failed:', err);
  }
};

// 更新帖子评论数
const updatePostCommentCount = async (postId: number) => {
  try {
    const res = await post<Result<string>>('/posts/updateCommentCount', { postId });
    if (res.success) {
      console.log('评论数更新成功');
      // 重新获取帖子详情以获取最新的评论数
      await fetchPostDetail(postId);
    }
  } catch (err) {
    console.warn('评论数更新失败:', err);
  }
};



// 已点赞
const isLiked = ref(false);
const likeCount = ref(0);
const isProcessingLike = ref(false);

const handleLike = async () => {
  console.log('详情页点赞按钮被点击');

  // 检查登录状态
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    return;
  }

  // 防止重复点击
  if (isProcessingLike.value) {
    return;
  }
  isProcessingLike.value = true;

  try {
    const result = await toggleLike(Number(postId.value));
    console.log('详情页点赞API响应:', result);

    if (result.success) {
      const data = result.data;
      if (data) {
        // 确保数据类型正确
        const newIsLiked = Boolean(data.isLiked);
        const newLikeCount = Number(data.likeCount) || 0;

        isLiked.value = newIsLiked;
        likeCount.value = newLikeCount;

        // 同步更新帖子数据中的点赞状态和数量
        if (postData.value) {
          postData.value.isLiked = newIsLiked;
          postData.value.likeCount = newLikeCount;
        }

        // 更新状态管理器，确保跨页面状态同步
        postStateManager.updatePostState(Number(postId.value), {
          isLiked: newIsLiked,
          likeCount: newLikeCount
        });

        console.log('详情页点赞状态更新:', {
          isLiked: newIsLiked,
          likeCount: newLikeCount
        });

        uni.showToast({
          title: data.message || (newIsLiked ? '点赞成功' : '取消点赞'),
          icon: 'success'
        });
      }
    } else {
      console.error('详情页点赞操作失败:', result);
      uni.showToast({
        title: result.message || '操作失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('详情页点赞操作异常:', error);
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    });
  } finally {
    // 重置处理状态
    isProcessingLike.value = false;
  }
}

// 在收藏夹中
const isCollected = ref(false);
const collectCount = ref(0);
const isProcessingCollect = ref(false);

const handleCollect = async () => {
  console.log('详情页收藏按钮被点击');

  // 检查登录状态
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    return;
  }

  // 防止重复点击
  if (isProcessingCollect.value) {
    return;
  }
  isProcessingCollect.value = true;

  try {
    const result = await toggleCollect(Number(postId.value));
    console.log('详情页收藏API响应:', result);

    if (result.success) {
      const data = result.data;
      if (data) {
        // 确保数据类型正确
        const newIsCollected = Boolean(data.isCollected);
        const newCollectCount = Number(data.collectCount) || 0;

        isCollected.value = newIsCollected;
        collectCount.value = newCollectCount;

        // 同步更新帖子数据中的收藏状态和数量
        if (postData.value) {
          postData.value.isCollected = newIsCollected;
          postData.value.collectCount = newCollectCount;
        }

        console.log('详情页收藏状态更新:', {
          isCollected: newIsCollected,
          collectCount: newCollectCount
        });

        uni.showToast({
          title: data.message || (newIsCollected ? '收藏成功' : '取消收藏'),
          icon: 'success'
        });
      }
    } else {
      console.error('详情页收藏操作失败:', result);
      uni.showToast({
        title: result.message || '操作失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('详情页收藏操作异常:', error);
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    });
  } finally {
    // 重置处理状态
    isProcessingCollect.value = false;
  }
}
const show = ref(false);
const close = () => {
  console.log('close');
  show.value = false;
}
const open = () => {
  console.log('open');
  show.value = true;
}

// 显示回复输入框
const handleShowComment = (item: Comments | null) => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    return;
  }

  if (!item) {
    commentTip.value = buttonTip.value;
    activeReplyId.value = null;
    commentContent.value = '';
  } else if (item.commentId) {
    commentTip.value = `回复 @${item?.username}`;
    activeReplyId.value = item.commentId;
    commentContent.value = '';
  }
  show.value = true;
};

// 清空回复输入框
const handleClearComment = () => {
  commentTip.value = buttonTip.value;
  activeReplyId.value = null;
  commentContent.value = '';
};

// 提交评论
const submitComment = async () => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    return;
  }

  const content = commentContent.value.trim();
  if (!content) {
    uni.showToast({
      title: '请输入评论内容',
      icon: 'none'
    });
    return;
  }

  const params = {
    postId: postData.value.id, // 帖子ID
    userId: memberStore.profile.id, // 使用登录用户ID
    content: content,
    parentCommentId: activeReplyId.value || null // 如果是回复，则有值
  };

  try {
    const res = await post<Result<any>>('/comments/addComment', params);

    if (res.success) {
      uni.showToast({
        title: activeReplyId.value ? '回复成功' : '评论成功',
        icon: 'success'
      });

      // 关闭弹窗并清空内容
      show.value = false;

      // 如果是回复评论，自动展开该评论的回复列表
      if (activeReplyId.value) {
        expandedComments.value.add(Number(activeReplyId.value));
      }

      handleClearComment();

      // 刷新评论列表
      await fetchComments(postData.value.id);

      // 更新帖子评论数
      await updatePostCommentCount(postData.value.id);
    } else {
      uni.showToast({
        title: res.message || '发布失败',
        icon: 'none'
      });
    }
  } catch (err) {
    console.error('请求失败:', err);
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    });
  }
};

// 删除回复
const deleteComment = async (commentId: number) => {
  if (confirm('确定删除吗？')) {
    try {
      const res = await post<Result<any>>('/comments/deleteComment', { id: commentId });

      if (res.success) {
        console.log("评论删除成功", res.data);
        await fetchComments(postData.value.id); // 刷新评论列表

        // 更新帖子评论数
        await updatePostCommentCount(postData.value.id);
      } else {
        uni.showToast({ title: '评论失败', icon: 'none' });
      }
    } catch (err) {
      console.error('请求失败:', err);
      uni.showToast({ title: '网络错误', icon: 'none' });
    }
  }
};

// 判断是否可以删除评论
const canDeleteComment = (comment: Comments) => {
  // 只有评论作者或帖子作者可以删除评论
  return comment.userId === currentUser.value.id || postData.value.userId === currentUser.value.id;
};

// 判断是否可以删除帖子
const canDeletePost = () => {
  if (!memberStore.profile?.id || !postData.value.userId) return false;

  // 帖子作者可以删除
  const isAuthor = postData.value.userId === memberStore.profile.id;

  // 管理员可以删除任意帖子
  const isAdmin = memberStore.profile.isAdmin === true;

  return isAuthor || isAdmin;
};

// 处理参与调查
const handleParticipate = (postId: number) => {
  uni.navigateTo({
    url: `/pages/survey/participate?id=${postId}`
  })
}

// 处理投票
const handleVote = (postId: number) => {
  uni.navigateTo({
    url: `/pages/vote/participate?id=${postId}`
  })
}

// 查看结果
const handleViewResults = (postId: number) => {
  // 根据帖子类型跳转到不同的结果页面
  const templateData = postData.value.templateData
  let parsedData = {}

  if (typeof templateData === 'string') {
    try {
      parsedData = JSON.parse(templateData)
    } catch (error) {
      console.error('解析模板数据失败:', error)
    }
  } else if (templateData) {
    parsedData = templateData
  }

  // 判断是调查还是投票
  if (parsedData.questions && Array.isArray(parsedData.questions)) {
    uni.navigateTo({
      url: `/pages/survey/results?id=${postId}`
    })
  } else if (parsedData.options && Array.isArray(parsedData.options)) {
    uni.navigateTo({
      url: `/pages/vote/results?id=${postId}`
    })
  }
}

// 处理删除帖子
const handleDeletePost = async () => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    return;
  }

  // 确认删除
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条帖子吗？删除后无法恢复。',
    success: async (res) => {
      if (res.confirm) {
        try {
          console.log('发送删除请求，帖子ID:', postData.value.id);
          const result = await deletePost(Number(postData.value.id));
          console.log('删除API响应:', result);

          if (result.success) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });

            // 设置全局刷新标记
            const app = getApp()
            if (!app.globalData) {
              app.globalData = {}
            }
            app.globalData.needRefreshHome = true
            console.log('详情页删除成功，设置首页刷新标记')

            // 删除成功后返回上一页
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            console.error('删除失败:', result);
            uni.showToast({
              title: result.message || '删除失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('删除异常:', error);
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 评论点赞功能暂未实现，等待后端接口支持

// 评论折叠相关方法
// 切换评论展开/折叠状态
const toggleCommentExpanded = (commentId: number) => {
  const expanded = expandedComments.value;
  if (expanded.has(commentId)) {
    expanded.delete(commentId);
  } else {
    expanded.add(commentId);
  }
  // 触发响应式更新
  expandedComments.value = new Set(expanded);
};

// 检查评论是否已展开
const isCommentExpanded = (commentId: number) => {
  return expandedComments.value.has(commentId);
};

// 获取展开按钮文本（针对默认显示第一条的情况）
const getExpandButtonText = (replies: Comments[]) => {
  const count = replies?.length || 0;
  if (count <= 1) return '';
  const remainingCount = count - 1; // 减去已显示的第一条
  if (remainingCount === 1) return '展开1条回复';
  return `展开${remainingCount}条回复`;
};

// 滚动到评论区域
const scrollToCommentSection = () => {
  console.log('滚动到评论区域');
  uni.pageScrollTo({
    selector: '.comment-section',
    duration: 500,
    success: () => {
      console.log('滚动到评论区域成功');
    },
    fail: (err) => {
      console.log('滚动到评论区域失败:', err);
      // 备用方案：滚动到指定位置
      uni.pageScrollTo({
        scrollTop: 600,
        duration: 500
      });
    }
  });
};



// 快捷操作处理
const handleQuickAction = (action: string) => {
  switch (action) {
    case 'share':
      uni.showToast({ title: '分享功能', icon: 'none' });
      break;
    case 'report':
      uni.showToast({ title: '举报功能', icon: 'none' });
      break;
    case 'more':
      uni.showToast({ title: '更多功能', icon: 'none' });
      break;
    default:
      break;
  }
};

// 置顶成功处理
const handleTopSuccess = () => {
  // 重新获取帖子详情以更新置顶状态
  fetchPostDetail(postId.value);

  // 设置全局刷新标记，让首页也刷新
  const app = getApp();
  if (!app.globalData) {
    app.globalData = {};
  }
  app.globalData.needRefreshHome = true;
};

// 取消置顶成功处理
const handleCancelSuccess = () => {
  // 重新获取帖子详情以更新置顶状态
  fetchPostDetail(postId.value);

  // 设置全局刷新标记，让首页也刷新
  const app = getApp();
  if (!app.globalData) {
    app.globalData = {};
  }
  app.globalData.needRefreshHome = true;
};

// 获取帖子状态
const fetchPostStatus = async (postId: number) => {
  console.log('获取帖子状态，用户登录状态:', memberStore.profile?.id ? '已登录' : '未登录');

  if (!memberStore.profile?.id) {
    // 未登录用户：重置为默认状态
    isLiked.value = false;
    isCollected.value = false;
    console.log('未登录用户，重置点赞收藏状态为false');
    return;
  }

  try {
    const result = await getPostStatus(postId);
    console.log('获取帖子状态API响应:', result);

    if (result.success && result.data) {
      // 处理标准响应格式
      const data = result.data;
      isLiked.value = Boolean(data.isLiked);
      isCollected.value = Boolean(data.isCollected);
      likeCount.value = Number(data.likeCount) || 0;
      collectCount.value = Number(data.collectCount) || 0;

      console.log('详情页状态更新:', {
        isLiked: isLiked.value,
        isCollected: isCollected.value,
        likeCount: likeCount.value,
        collectCount: collectCount.value
      });
    } else if (result.code === '200' && result.result) {
      // 兼容旧的响应格式
      const data = result.result;
      isLiked.value = Boolean(data.isLiked);
      isCollected.value = Boolean(data.isCollected);
      likeCount.value = Number(data.likeCount) || 0;
      collectCount.value = Number(data.collectCount) || 0;
    }
  } catch (error) {
    console.error('获取帖子状态失败:', error);
    // 发生错误时重置状态
    isLiked.value = false;
    isCollected.value = false;
  }
};

onLoad((options: Record<string, any>) => {
  console.log('详情页面接收到的参数:', options);
  postId.value = Number(options.id);
  currentImageId.value = 0;
  // 重置折叠状态
  expandedComments.value = new Set();
  fetchPostDetail(postId.value);
  fetchComments(postId.value);
  fetchPostStatus(postId.value);

  // 如果是从评论图标点击进入，自动滚动到评论区域
  if (options.scrollToComment === 'true') {
    setTimeout(() => {
      scrollToCommentSection();
    }, 1000); // 等待页面数据加载完成后再滚动
  }
});

onMounted(() => {
  // 监听用户退出登录事件，重置点赞收藏状态
  uni.$on('userLogout', () => {
    console.log('详情页收到用户退出登录事件，重置状态')
    // 重置点赞收藏状态
    isLiked.value = false;
    isCollected.value = false;
    // 重置计数
    likeCount.value = postData.value?.likeCount || 0;
    collectCount.value = postData.value?.collectCount || 0;
    // 重新获取帖子状态（此时会以游客身份，无法获取状态）
    fetchPostStatus(postId.value);
  })
})

// 页面显示时检查是否需要刷新
onShow(() => {
  const app = getApp()
  if (app.globalData?.needRefreshPostDetail && app.globalData?.refreshPostId === postId.value) {
    console.log('检测到帖子编辑，刷新详情页数据')
    // 重新获取帖子详情
    fetchPostDetail(postId.value)
    fetchComments(postId.value)
    fetchPostStatus(postId.value)

    // 清除刷新标记
    app.globalData.needRefreshPostDetail = false
    app.globalData.refreshPostId = null
  }
});

onUnmounted(() => {
  // 移除事件监听
  uni.$off('userLogout')
});

// 新增返回方法
// const goBack = () => {
//   console.log('go back');
//   uni.navigateBack();
// };

// 修改图片预览方法
const currentImageId = ref(0); // 初始化为 0

// 监听 swiper 滑动事件
const onSwiperChange: SwiperChangeHandler = (e) => {
  console.log('swiper change event:', e);

  let newIndex: number | undefined;

  // 处理不同的事件参数结构
  if (typeof e === 'number') {
    // 直接传递数字索引
    newIndex = e;
  } else if (e && typeof e.current === 'number') {
    // uview-plus swiper事件格式: { current: number }
    newIndex = e.current;
  } else if (e && e.detail && typeof e.detail.current === 'number') {
    // uni-app原生swiper事件格式: { detail: { current: number } }
    newIndex = e.detail.current;
  }

  // 更新当前图片索引
  if (typeof newIndex === 'number' && newIndex >= 0) {
    currentImageId.value = newIndex;
    console.log('swiper index updated to:', newIndex);
  } else {
    console.warn('Invalid swiper change event:', e);
  }
};

// 修改图片预览方法
const previewImage = (urls: string[]) => {
  uni.previewImage({
    urls: urls,
    current: urls[currentImageId.value]
  });
};
</script>

<style lang="scss">
.post-detail-page {
  font-size: 14px;
  padding: 0 10px;
  padding-bottom: 48px;

  .post-content-section {
    background-color: #fff;
    margin-top: 10px;
    padding: 10px;

    .post-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .info {
        margin-left: 10px;
        flex: 1;

        .user-info {
          display: flex;
          flex-direction: column;

          .name-row {
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .name {
              font-weight: bold;
              margin-right: 8px;
            }

            .sex {
              margin-right: 8px;
            }
          }
        }

        .post-time {
          font-size: 12px;
          color: #999;
        }
      }

      .post-actions {
        margin-left: auto;
        display: flex;
        align-items: center;

        .action-btn {
          display: flex;
          align-items: center;
          margin-left: 8px;
          font-size: 12px;
          color: #666;
          background-color: #3c9cff;
          cursor: pointer;

          text {
            margin-left: 3px;
          }
        }
      }
    }

    .post-files {
      margin-top: 15px;

      .custom-swiper {
        width: 100%;
        border-radius: 8px;
        overflow: hidden;

        :deep(.u-swiper__wrapper) {
          border-radius: 8px;
        }

        :deep(.u-swiper__item) {
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f5f5;

          .u-image {
            width: 100%;
            height: 100%;

            .u-image__image {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 8px;
            }
          }

          image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
          }
        }

        :deep(.u-swiper__indicator) {
          bottom: 10px;
        }
      }

      .single-image {
        width: 100%;
        border-radius: 8px;
        overflow: hidden;

        :deep(.u-image__image) {
          border-radius: 8px;
        }
      }
    }

    .post-footer {
      margin-top: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .post-footer-left {
        display: flex;
        align-items: center;
      }

      .post-footer-right {
        display: flex;
        align-items: center;

        .action-btn {
          display: flex;
          align-items: center;
          margin-left: 15px;
          font-size: 12px;
          color: #666;

          text {
            margin-left: 3px;
          }
        }
      }
    }
  }

  .comment-section {
    background-color: #fff;
    padding: 0 15px;
    margin-top: 10px;

    .comment-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0 10px 0;
      border-bottom: 1px solid #f5f5f5;

      .comment-count {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .comment-stats {
        .like-collect-text {
          font-size: 12px;
          color: #999;
        }
      }
    }

    .quick-comment-input {
      display: flex;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid #f5f5f5;

      .input-placeholder {
        flex: 1;
        margin: 0 12px;
        padding: 8px 15px;
        background-color: #f8f8f8;
        border-radius: 20px;

        text {
          font-size: 14px;
          color: #999;
        }
      }

      .input-actions {
        display: flex;
        align-items: center;
        gap: 15px;
      }
    }

    .comment-list {
      padding-top: 10px;

      .comment-item-container {
        margin-bottom: 12px;

        .main-comment {
          display: flex;
          padding: 8px 0;

          .comment-avatar {
            margin-right: 12px;
          }

          .comment-main {
            flex: 1;

            .comment-header-info {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 8px;

              .user-info {
                display: flex;
                align-items: center;
                gap: 8px;

                .username {
                  font-size: 14px;
                  font-weight: 600;
                  color: #333;
                }
              }

              .comment-actions {
                display: flex;
                align-items: center;
              }
            }

            .comment-content {
              .content-text {
                font-size: 15px;
                line-height: 1.4;
                color: #333;
                display: inline;

                .comment-time {
                  font-size: 11px;
                  color: #999;
                  margin-left: 8px;
                }

                .reply-text,
                .delete-text {
                  font-size: 11px;
                  color: #666;
                  cursor: pointer;
                  margin-left: 6px;
                }

                .delete-text {
                  color: #ff4757;
                }
              }
            }
          }
        }

        .reply-toggle-section {
          margin-left: 0px;
          padding: 4px 0 6px 0;

          .reply-toggle-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            background-color: transparent;
            border-radius: 12px;
            width: fit-content;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
              background-color: #f5f5f5;
            }

            &:active {
              background-color: #e9ecef;
            }

            .toggle-text {
              font-size: 11px;
              color: #007AFF;
              font-weight: 500;
            }
          }
        }

        .reply-list {
          margin-left: 48px;
          padding-left: 15px;
          animation: slideDown 0.3s ease-out;

          .reply-item {
            display: flex;
            padding: 6px 0;
            margin-bottom: 6px;

            .reply-avatar {
              margin-right: 10px;
            }

            .reply-main {
              flex: 1;

              .reply-header {
                display: flex;
                align-items: center;
                gap: 6px;
                margin-bottom: 4px;

                .reply-username {
                  font-size: 13px;
                  font-weight: 600;
                  color: #333;
                }
              }

              .reply-content {
                .reply-text-content {
                  font-size: 14px;
                  line-height: 1.4;
                  color: #333;
                  display: inline;

                  .reply-time {
                    font-size: 10px;
                    color: #999;
                    margin-left: 6px;
                  }

                  .reply-action,
                  .delete-text {
                    font-size: 10px;
                    color: #666;
                    cursor: pointer;
                    margin-left: 5px;
                  }

                  .delete-text {
                    color: #ff4757;
                  }
                }
              }
            }

            .reply-actions {
              display: flex;
              align-items: flex-start;
              padding-top: 2px;
            }
          }
        }
      }
    }
  }

  // 添加展开动画
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .post-detail-footer {
    padding-bottom: 15px;
    text-align: center;

    .u-divider {
      width: 200px !important;
      margin: 0 auto !important;
    }
  }


  .post-footer-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    padding: 10px;
    flex-direction: row;
    background-color: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 800;

    .u-input {
      margin: 0 5px !important;
      height: 24px !important;
    }

    .comment-tools-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 150px;

      .comment-tool {
        display: flex;
        align-items: center;
      }
    }

    .u-button {
      width: 80px !important;
    }
  }

  .comment-popup-wrap {
    padding: 10px;

    .foot-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;

      .tools {
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
    }
  }
}
</style>