package com.haolinkyou.vo;

import lombok.Data;

import java.util.Date;

/**
 * 用户收藏视图对象
 */
@Data
public class UserCollectVo {
    
    /**
     * 收藏ID
     */
    private Long id;
    
    /**
     * 帖子ID
     */
    private Long postId;
    
    /**
     * 帖子标题
     */
    private String title;
    
    /**
     * 帖子内容
     */
    private String postContent;
    
    /**
     * 帖子图片（逗号分隔的文件路径）
     */
    private String postImages;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 发布用户头像
     */
    private String userAvatar;
    
    /**
     * 发布用户昵称
     */
    private String nickname;
    
    /**
     * 点赞数
     */
    private Integer likeCount;
    
    /**
     * 评论数
     */
    private Integer commentCount;
    
    /**
     * 收藏数
     */
    private Integer collectCount;
    
    /**
     * 帖子创建时间
     */
    private Date createdTime;
    
    /**
     * 收藏时间
     */
    private Date collectTime;
    
    /**
     * 帖子状态（0-待审核，1-已发布，2-已拒绝）
     */
    private Integer status;
}
