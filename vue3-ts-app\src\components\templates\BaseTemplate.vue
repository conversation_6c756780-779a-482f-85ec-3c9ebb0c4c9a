<template>
  <view class="base-template">
    <!-- 标题输入 -->
    <view class="form-item">
      <view class="form-label">
        <text>标题</text>
        <text v-if="titleRequired" class="required">*</text>
      </view>
      <up-input
        v-model="formData.title"
        :placeholder="titlePlaceholder"
        :maxlength="100"
        @input="handleTitleChange"
        @blur="validateTitle"
      />
      <view v-if="errors.title" class="error-text">{{ errors.title }}</view>
    </view>

    <!-- 内容输入 -->
    <view class="form-item">
      <view class="form-label">
        <text>内容</text>
        <text class="required">*</text>
      </view>
      <up-textarea
        v-model="formData.content"
        :placeholder="contentPlaceholder"
        :maxlength="2000"
        :autoHeight="true"
        :height="150"
        @input="handleContentChange"
        @blur="validateContent"
      />
      <view v-if="errors.content" class="error-text">{{ errors.content }}</view>
    </view>

    <!-- 媒体上传 -->
    <view v-if="showMediaUpload" class="form-item">
      <view class="form-label">
        <text>{{ mediaLabel }}</text>
        <text v-if="mediaRequired" class="required">*</text>
      </view>
      
      <!-- 文件上传区域 -->
      <view class="upload-section">
        <!-- 自定义文件上传和预览 -->
        <view class="custom-upload-container">
          <!-- 文件预览列表 -->
          <view class="file-preview-list">
            <view
              v-for="(file, index) in formData.fileList"
              :key="index"
              class="file-preview-item"
            >
              <!-- 图片预览 -->
              <image
                v-if="isImageFile(file)"
                :src="getFilePreviewUrl(file)"
                class="preview-image"
                mode="aspectFill"
                @click="previewFile(file)"
              />
              <!-- 视频预览 -->
              <view v-else-if="isVideoFile(file)" class="video-preview" @click="previewFile(file)">
                <video
                  :src="getFilePreviewUrl(file)"
                  class="preview-video"
                  :show-center-play-btn="false"
                  :controls="false"
                  :show-play-btn="false"
                  :show-fullscreen-btn="false"
                  :show-progress="false"
                  :enable-progress-gesture="false"
                  object-fit="cover"
                  muted
                  preload="metadata"
                />
                <!-- 播放图标覆盖层 -->
                <view class="video-play-overlay">
                  <up-icon name="play-circle-fill" size="32" color="rgba(255,255,255,0.9)"></up-icon>
                </view>
              </view>
              <!-- 删除按钮 -->
              <view class="delete-btn" @click.stop="deleteFile(index)">
                <up-icon name="close" size="10" color="#fff"></up-icon>
              </view>
            </view>

            <!-- 上传按钮 -->
            <view
              v-if="formData.fileList.length < maxImageCount"
              class="upload-btn"
              @click="selectFiles"
            >
              <up-icon name="plus" size="18" color="#c0c4cc"></up-icon>
              <text class="upload-text">添加</text>
            </view>
          </view>
        </view>

        <!-- 隐藏的up-upload组件，用于文件选择 -->
        <view style="position: absolute; left: -9999px; top: -9999px; opacity: 0; pointer-events: none;">
          <up-upload
            ref="uploadRef"
            :fileList="[]"
            @afterRead="afterRead"
            name="upload"
            multiple
            :maxCount="maxImageCount"
            accept="all"
            :previewFullImage="false"
            width="50"
            height="50"
            :maxSize="100 * 1024 * 1024"
            @oversize="onOversize"
          />
        </view>
      </view>

      <view v-if="errors.fileList" class="error-text">{{ errors.fileList }}</view>
      <view class="help-text">{{ mediaHelpText }}</view>
    </view>
    
    <!-- 联系方式 -->
    <view v-if="showContact" class="form-item">
      <view class="form-label">
        <text>联系方式</text>
        <text v-if="contactRequired" class="required">*</text>
      </view>
      <up-input
        v-model="formData.contact"
        :placeholder="contactPlaceholder"
        @input="handleContactChange"
        @blur="validateContact"
      />
      <view v-if="errors.contact" class="error-text">{{ errors.contact }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { validateField } from '@/utils/validation'
import { detectFileType } from '@/types/publish'

interface Props {
  // 标题相关
  titleRequired?: boolean
  titlePlaceholder?: string

  // 内容相关
  contentPlaceholder?: string

  // 媒体上传相关
  showMediaUpload?: boolean
  mediaRequired?: boolean
  mediaLabel?: string
  mediaHelpText?: string
  maxImageCount?: number

  // 联系方式相关
  showContact?: boolean
  contactRequired?: boolean
  contactPlaceholder?: string
  contactType?: 'phone' | 'email' | 'any'

  // 初始数据
  initialData?: Record<string, any>
}

interface Emits {
  (e: 'update:data', data: Record<string, any>): void
  (e: 'update:valid', valid: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  titleRequired: false,
  titlePlaceholder: '请输入标题（可选）',
  contentPlaceholder: '请输入内容',

  // 媒体上传相关
  showMediaUpload: true,
  mediaRequired: false,
  mediaLabel: '媒体文件',
  mediaHelpText: '支持jpg、png图片和mp4、avi视频混合上传，总数最多9个',
  maxImageCount: 9,

  showContact: false,
  contactRequired: false,
  contactPlaceholder: '请输入联系方式',
  contactType: 'any'
})

const emit = defineEmits<Emits>()

// 表单数据
const formData = ref({
  title: '',
  content: '',
  fileList: [] as any[],
  contact: '',
  ...props.initialData
})

// 错误信息
const errors = ref<Record<string, string>>({})

// 当前媒体类型 (0: 图片, 1: 视频)
const currentMediaType = ref(0)

// 初始化处理
if (props.initialData?.fileList && Array.isArray(props.initialData.fileList)) {
  formData.value.fileList = props.initialData.fileList.map((file: any) => ({
    ...file,
    isExisting: file.isExisting !== false // 默认为已存在文件
  }))
}

if (props.initialData?.currentMediaType !== undefined) {
  currentMediaType.value = props.initialData.currentMediaType
}

// 验证表单是否有效
const isValid = computed(() => {
  return Object.keys(errors.value).length === 0 &&
         formData.value.content.trim() !== '' &&
         (!props.titleRequired || formData.value.title.trim() !== '') &&
         (!props.mediaRequired || formData.value.fileList.length > 0) &&
         (!props.contactRequired || formData.value.contact.trim() !== '')
})

// 监听数据变化
watch(formData, (newData) => {
  emit('update:data', newData)
}, { deep: true })

// 监听验证状态变化
watch(isValid, (valid) => {
  emit('update:valid', valid)
})



// 验证标题
const validateTitle = () => {
  if (props.titleRequired) {
    const result = validateField(formData.value.title, {
      required: true,
      maxLength: 100,
      fieldName: '标题'
    })
    
    if (!result.valid && result.message) {
      errors.value.title = result.message
    } else {
      delete errors.value.title
    }
  }
}

// 验证内容
const validateContent = () => {
  const result = validateField(formData.value.content, {
    required: true,
    maxLength: 2000,
    fieldName: '内容'
  })
  
  if (!result.valid && result.message) {
    errors.value.content = result.message
  } else {
    delete errors.value.content
  }
}

// 验证联系方式
const validateContact = () => {
  if (props.showContact && props.contactRequired) {
    const result = validateField(formData.value.contact, {
      required: true,
      type: props.contactType === 'any' ? undefined : props.contactType,
      fieldName: '联系方式'
    })

    if (!result.valid && result.message) {
      errors.value.contact = result.message
    } else {
      delete errors.value.contact
    }
  } else if (props.showContact && formData.value.contact) {
    // 非必填但有值时，也要验证格式
    const result = validateField(formData.value.contact, {
      required: false,
      type: props.contactType === 'any' ? undefined : props.contactType,
      fieldName: '联系方式'
    })

    if (!result.valid && result.message) {
      errors.value.contact = result.message
    } else {
      delete errors.value.contact
    }
  }
}

// 处理标题变化
const handleTitleChange = () => {
  if (props.titleRequired) {
    validateTitle()
  }
}

// 处理内容变化
const handleContentChange = () => {
  validateContent()
}

// 处理联系方式变化
const handleContactChange = () => {
  if (props.showContact && props.contactRequired) {
    validateContact()
  }
}

// 智能切换媒体类型（基于文件内容自动判断）
const switchMediaType = (type: number) => {
  // 注意：现在媒体类型由文件内容自动决定，不再支持手动切换
  // 保留此方法是为了兼容性，但实际切换逻辑在 afterRead 中处理
  console.log('媒体类型已自动切换到:', type === 0 ? '图片模式' : '视频模式')
}

// 批量校验和过滤文件的辅助函数
const batchValidateAndFilterFiles = (newFiles: any[], existingFiles: any[]) => {
  const validFiles: any[] = []
  const invalidFiles: any[] = []
  const duplicateFiles: any[] = []

  // 支持的格式定义（与后端保持一致）
  const supportedImageTypes = ['image/jpeg', 'image/jpg', 'image/png']
  const supportedImageExtensions = ['.jpg', '.jpeg', '.png']
  const supportedVideoTypes = ['video/mp4', 'video/avi']
  const supportedVideoExtensions = ['.mp4', '.avi']

  for (const file of newFiles) {
    // 检查是否重复
    const isDuplicate = existingFiles.some((existingFile: any) => {
      return existingFile.name === file.name &&
             existingFile.size === file.size &&
             existingFile.lastModified === file.lastModified
    })

    if (isDuplicate) {
      duplicateFiles.push(file)
      continue
    }

    // 获取文件信息
    let mimeType = ''
    let fileName = ''

    if (file.file && file.file.type) {
      mimeType = file.file.type.toLowerCase()
      fileName = file.file.name || file.name || ''
    } else if (file.type) {
      mimeType = file.type.toLowerCase()
      fileName = file.name || ''
    } else {
      fileName = file.name || ''
    }

    const fileExtension = fileName.toLowerCase().match(/\.[^.]+$/)?.[0] || ''

    // 验证文件格式
    let isValid = false

    // 检查图片格式
    if (mimeType.startsWith('image/') || supportedImageExtensions.includes(fileExtension)) {
      isValid = supportedImageTypes.includes(mimeType) || supportedImageExtensions.includes(fileExtension)
    }
    // 检查视频格式
    else if (mimeType.startsWith('video/') || supportedVideoExtensions.includes(fileExtension)) {
      isValid = supportedVideoTypes.includes(mimeType) || supportedVideoExtensions.includes(fileExtension)
    }

    if (isValid) {
      validFiles.push(file)
    } else {
      invalidFiles.push(file)
    }
  }

  // 生成错误信息
  let errorMessage = ''
  const errorParts: string[] = []

  if (invalidFiles.length > 0) {
    errorParts.push(`${invalidFiles.length}个文件格式不支持`)
  }
  if (duplicateFiles.length > 0) {
    errorParts.push(`${duplicateFiles.length}个文件已存在`)
  }

  if (errorParts.length > 0) {
    if (validFiles.length > 0) {
      errorMessage = `已添加${validFiles.length}个文件，${errorParts.join('，')}`
    } else {
      errorMessage = errorParts.join('，') + '，支持jpg、png图片和mp4、avi视频'
    }
  }

  return {
    validFiles,
    invalidFiles,
    duplicateFiles,
    errorMessage
  }
}

// 更新媒体类型的辅助函数
const updateMediaType = () => {
  const allTypes = formData.value.fileList.map(file => file.detectedType || detectFileType(file))
  const hasImages = allTypes.includes('image')
  const hasVideos = allTypes.includes('video')

  if (hasImages && hasVideos) {
    currentMediaType.value = 2 // 混合模式
  } else if (hasVideos) {
    currentMediaType.value = 1 // 视频模式
  } else {
    currentMediaType.value = 0 // 图片模式
  }
}

// 文件预览相关方法
const uploadRef = ref()

// 判断是否为图片文件
const isImageFile = (file: any) => {
  const type = detectFileType(file)
  return type === 'image'
}

// 判断是否为视频文件
const isVideoFile = (file: any) => {
  const type = detectFileType(file)
  return type === 'video'
}

// 获取文件预览URL
const getFilePreviewUrl = (file: any) => {
  if (file.url) {
    return file.url
  }
  if (file.file) {
    return URL.createObjectURL(file.file)
  }
  return ''
}

// 手动触发文件选择
const selectFiles = () => {
  // 在H5环境下直接使用原生input，因为uview-plus在H5下可能有兼容问题
  if (typeof document !== 'undefined') {
    const input = document.createElement('input')
    input.type = 'file'
    input.multiple = true
    input.accept = 'image/jpeg,image/jpg,image/png,video/mp4,video/avi'
    input.style.display = 'none'

    input.onchange = (event: any) => {
      const files = Array.from(event.target.files || [])
      if (files.length === 0) return

      // 转换为组件格式
      const convertedFiles = files.map((file: any) => ({
        file: file,
        url: URL.createObjectURL(file),
        name: file.name,
        size: file.size
      }))

      // 调用afterRead处理
      afterRead({ file: convertedFiles })

      // 清理input元素
      document.body.removeChild(input)
    }

    document.body.appendChild(input)
    input.click()
  } else {
    // 非H5环境，尝试使用uview-plus组件
    if (uploadRef.value && uploadRef.value.chooseFile) {
      uploadRef.value.chooseFile()
    } else {
      uni.showToast({
        title: '当前环境不支持文件选择',
        icon: 'none'
      })
    }
  }
}

// 预览文件（点击放大查看）
const previewFile = (file: any) => {
  const url = getFilePreviewUrl(file)
  if (!url) return

  if (isImageFile(file)) {
    // 图片预览
    uni.previewImage({
      urls: formData.value.fileList
        .filter(f => isImageFile(f))
        .map(f => getFilePreviewUrl(f)),
      current: url
    })
  } else if (isVideoFile(file)) {
    // 视频全屏播放
    if (uni.previewMedia) {
      uni.previewMedia({
        sources: [{
          url: url,
          type: 'video'
        }],
        current: 0
      })
    } else {
      // 降级方案：显示提示
      uni.showModal({
        title: '视频播放',
        content: '当前环境不支持全屏视频播放，请在视频控件中直接播放',
        showCancel: false
      })
    }
  }
}

// 防止重复调用的标志
let isProcessing = false

// 上传组件：读取文件后的处理（图片视频混合模式）
const afterRead = (event: any) => {
  // 防止重复处理
  if (isProcessing) {
    return
  }

  isProcessing = true

  try {
    // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
    const newFiles = Array.isArray(event.file) ? event.file : [event.file]

    // 批量校验和过滤文件
    const validationResult = batchValidateAndFilterFiles(newFiles, formData.value.fileList)

    if (validationResult.validFiles.length === 0) {
      // 如果没有有效文件，显示错误信息
      if (validationResult.errorMessage) {
        uni.showToast({
          title: validationResult.errorMessage,
          icon: 'none',
          duration: 3000
        })
      }
      return
    }

    // 检查总数量限制
    const totalCount = formData.value.fileList.length + validationResult.validFiles.length
    if (totalCount > props.maxImageCount) {
      const allowedCount = props.maxImageCount - formData.value.fileList.length
      if (allowedCount > 0) {
        validationResult.validFiles = validationResult.validFiles.slice(0, allowedCount)
        validationResult.errorMessage = `最多只能选择${props.maxImageCount}个文件，已添加${allowedCount}个`
      } else {
        uni.showToast({
          title: `最多只能选择${props.maxImageCount}个文件`,
          icon: 'none',
          duration: 3000
        })
        return
      }
    }

    // 添加有效文件到列表
    const processedFiles = validationResult.validFiles.map(file => ({
      ...file,
      detectedType: detectFileType(file),
      isExisting: false
    }))

    formData.value.fileList = [...formData.value.fileList, ...processedFiles]

    // 更新媒体类型
    updateMediaType()

    // 显示结果提示
    const successCount = validationResult.validFiles.length

    if (validationResult.errorMessage) {
      // 有部分文件被过滤，显示详细信息
      uni.showToast({
        title: validationResult.errorMessage,
        icon: 'none',
        duration: 3000
      })
    } else if (successCount > 0) {
      // 全部成功
      uni.showToast({
        title: `已添加${successCount}个文件`,
        icon: 'success'
      })
    }

  } finally {
    // 延迟重置标志，防止快速连续调用
    setTimeout(() => {
      isProcessing = false
    }, 100)
  }

  // 清除错误信息
  if (props.mediaRequired) {
    delete errors.value.fileList
  }
}

// 上传组件：删除文件
const deleteFile = (indexOrEvent: any) => {
  let index: number

  // 兼容两种调用方式：直接传索引 或 传event对象
  if (typeof indexOrEvent === 'number') {
    index = indexOrEvent
  } else {
    index = indexOrEvent.index
  }

  formData.value.fileList.splice(index, 1)

  // 更新媒体类型
  updateMediaType()

  uni.showToast({
    title: '文件已删除',
    icon: 'success'
  })

  // 检查是否需要显示错误信息
  if (props.mediaRequired && formData.value.fileList.length === 0) {
    errors.value.fileList = '请至少上传一个文件'
  }
}

// 上传组件：文件大小超出限制
const onOversize = () => {
  const maxSizeMB = currentMediaType.value === 0 ? 10 : 100
  uni.showToast({
    title: `文件大小不能超过${maxSizeMB}MB`,
    icon: 'none'
  })
}



// 暴露验证方法
const validate = () => {
  validateTitle()
  validateContent()
  validateContact()



  // 验证新版本媒体上传
  if (props.mediaRequired && formData.value.fileList.length === 0) {
    errors.value.fileList = '请至少上传一个文件'
  }

  return isValid.value
}

// 初始化文件列表（支持从外部传入已存在的文件）
const initializeFileList = (fileListData: any[]) => {
  formData.value.fileList = fileListData.map((file: any) => ({
    ...file,
    isExisting: true // 标记为已存在的文件
  }))
}

// 获取处理后的文件数据（区分已存在文件和新文件）
const getProcessedFiles = () => {
  const existingFiles: string[] = []
  const newFiles: any[] = []

  for (const fileItem of formData.value.fileList) {
    if (fileItem.isExisting && fileItem.url) {
      // 已存在的文件，提取相对路径
      let filePath = fileItem.url
      // 移除服务器地址前缀（支持多种格式）
      if (filePath.startsWith('http://localhost:3205/')) {
        filePath = filePath.replace('http://localhost:3205/', '')
      } else if (filePath.startsWith('http://')) {
        // 处理其他 http 地址，提取路径部分
        const url = new URL(filePath)
        filePath = url.pathname.substring(1) // 移除开头的 /
      }
      existingFiles.push(filePath)
    } else if (!fileItem.isExisting) {
      // 新添加的文件，需要上传，添加检测到的文件类型
      const detectedType = detectFileType(fileItem)
      newFiles.push({
        ...fileItem,
        detectedType
      })
    }
  }

  return {
    existingFiles,
    newFiles,
    currentMediaType: currentMediaType.value
  }
}

// 设置媒体类型
const setMediaType = (type: number) => {
  switchMediaType(type)
}

// 清空文件列表
const clearFiles = () => {
  formData.value.fileList = []
  delete errors.value.fileList
}

// 重置所有表单数据
const resetForm = () => {
  formData.value.title = ''
  formData.value.content = ''
  formData.value.contact = ''
  formData.value.fileList = []

  // 清空所有错误信息
  errors.value = {}

  // 重置媒体类型为混合模式
  currentMediaType.value = 2
}

// 获取表单数据用于提交（父组件调用）
const getFormData = () => {
  return {
    title: formData.value.title.trim(),
    content: formData.value.content.trim(),
    contact: formData.value.contact.trim(),
    ...getProcessedFiles()
  }
}

// 暴露给父组件
defineExpose({
  validate,
  formData,
  isValid,
  initializeFileList,
  getProcessedFiles,
  getFormData,
  currentMediaType,
  setMediaType,
  clearFiles,
  resetForm
})
</script>

<style scoped lang="scss">
.base-template {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff3b30;
}

.error-text {
  font-size: 12px;
  color: #ff3b30;
}

.help-text {
  font-size: 12px;
  color: #999;
}

.media-type-indicator {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f0f8ff;
  border-radius: 6px;
  border-left: 3px solid #5677fc;
}

.media-type-text {
  font-size: 14px;
  font-weight: 500;
  color: #5677fc;
}

.media-type-hint {
  font-size: 12px;
  color: #666;
}

.upload-section {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 确保上传组件横向排列 - 3张图片横排 */
:deep(.u-upload) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-start;
}

:deep(.u-upload__wrap) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  justify-content: flex-start;
}

:deep(.u-upload__wrap__preview) {
  margin-right: 0 !important;
  margin-bottom: 0 !important;
  flex: 0 0 auto;
}

/* 确保每行最多3张图片 */
:deep(.u-upload__wrap__preview:nth-child(3n+1)) {
  clear: left;
}

/* 上传按钮也要遵循3张横排的规则 */
:deep(.u-upload__button) {
  margin-right: 0 !important;
  margin-bottom: 0 !important;
  flex: 0 0 auto;
}

/* 强制3张图片横排布局 */
:deep(.u-upload__wrap) {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  width: 100%;
}

/* 确保图片和上传按钮都按网格布局 */
:deep(.u-upload__wrap > *) {
  width: 100% !important;
  height: auto !important;
}

.upload-subtitle {
  font-size: 12px;
  color: #666;
  display: block;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 100%;
  height: 105px;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s ease;

  &:hover {
    border-color: #5677fc;
    background: #f0f8ff;
  }
}

.upload-text {
  font-size: 11px;
  color: #666;
  margin-top: 2px;
  font-weight: 500;
  text-align: center;
}

.upload-tip {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
  text-align: center;
}

/* 自定义文件上传和预览样式 */
.custom-upload-container {
  width: 100%;
}

.file-preview-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  width: 100%;
}

.file-preview-item {
  position: relative;
  width: 100%;
  height: 85px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  cursor: pointer;
}

.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 2;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  padding: 8px;
  transition: all 0.3s ease;
}

.video-preview:hover .video-play-overlay {
  background-color: rgba(0, 0, 0, 0.5);
  transform: translate(-50%, -50%) scale(1.1);
}

.video-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.delete-btn {
  position: absolute;
  top: 3px;
  right: 3px;
  width: 16px;
  height: 16px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(2px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.upload-btn {
  width: 100%;
  height: 85px;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 3px;
  transition: all 0.3s ease;

  &:active {
    border-color: #5677fc;
    background: #f0f8ff;
  }
}

.upload-btn .upload-text {
  font-size: 11px;
  color: #666;
  margin-top: 0;
}
</style>
