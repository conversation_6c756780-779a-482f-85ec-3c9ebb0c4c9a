package com.haolinkyou.api.vo;

import lombok.Data;

@Data
public class PostsListVo {
    private Long id;
    private Long userId;               // 帖子作者ID，用于权限判断
    private String title;
    private String content;
    private Integer userGender;        // 对应 u.gender AS user_gender
    private String nickname;       // 对应 u.nickname AS user_nickname
    private Boolean userIsVerified;    // 对应 u.is_verified AS user_is_verified
    private String userRole;           // 对应 u.user_role AS user_role
    private Integer fileType;          // 对应 pf.type AS file_type
    private String fileList;           // 对应 pf.content AS file_list（注意下划线转驼峰）
    private Long categoryId;
    private String categoryName;       // 对应 c.category_name
    private Integer viewCount;
    private Integer likeCount;
    private Integer commentCount;
    private Integer collectCount;
    private Integer status;
    private Integer topStatus;
    private Long templateId;           // 模板ID
    private String templateType;       // 模板类型
    private String templateData;       // 模板数据JSON
    private String createdTime;
    private String updatedTime;
    private Boolean isLiked;           // 用户是否已点赞
    private Boolean isCollected;       // 用户是否已收藏
}
