import { http } from '@/utils/http'
import type { Result, PageResult, BaseSearchParams } from '@/types'
import type { PostManagement } from '@/types/admin'
import type { AuthApplication, Feedback } from '@/types/user'

/**
 * 待审核项目类型
 */
export type PendingItemType = 'post' | 'auth' | 'feedback'

/**
 * 待审核项目状态
 */
export interface PendingItemStatus {
  post: 0 | 1 | 2  // 0-待审核 1-已通过 2-已拒绝
  auth: 0 | 1 | 2  // 0-待审核 1-已通过 2-已拒绝
  feedback: 0 | 1 | 2 | 3  // 0-待处理 1-已采纳 2-已处理 3-已拒绝
}

/**
 * 待审核搜索参数
 */
export interface PendingReviewsSearchParams extends BaseSearchParams {
  type?: PendingItemType
  status?: number
  startTime?: string
  endTime?: string
}

/**
 * 待审核统计数据
 */
export interface PendingReviewsStats {
  totalPending: number
  pendingPosts: number
  pendingAuth: number
  pendingFeedback: number
  todayPending: number
  weeklyPending: number
}

/**
 * 审核操作参数
 */
export interface ReviewActionParams {
  id: number
  action: 'approve' | 'reject'
  note?: string
  reason?: string
}

/**
 * 批量审核参数
 */
export interface BatchReviewParams {
  ids: number[]
  action: 'approve' | 'reject'
  note?: string
  reason?: string
}

/**
 * 获取待审核统计数据（使用现有接口组合）
 */
export const getPendingReviewsStatsAPI = async () => {
  try {
    // 获取帖子统计
    const postStatsRes = await http<Result<{ total: number; pending: number; approved: number; rejected: number }>>({
      method: 'GET',
      url: '/posts/admin/stats'
    });

    // 获取管理员统计（包含待审核认证和反馈数据）
    const adminStatsRes = await http<Result<{ pendingAuth: number; feedbackCount: number }>>({
      method: 'GET',
      url: '/admin/stats'
    });

    // 组合统计数据
    const stats: PendingReviewsStats = {
      totalPending: 0,
      pendingPosts: 0,
      pendingAuth: 0,
      pendingFeedback: 0,
      todayPending: 0,
      weeklyPending: 0
    };

    if (postStatsRes.success && postStatsRes.data) {
      stats.pendingPosts = postStatsRes.data.pending || 0;
    }

    if (adminStatsRes.success && adminStatsRes.data) {
      stats.pendingAuth = adminStatsRes.data.pendingAuth || 0;
      stats.pendingFeedback = adminStatsRes.data.feedbackCount || 0;
    }

    stats.totalPending = stats.pendingPosts + stats.pendingAuth + stats.pendingFeedback;

    return {
      success: true,
      data: stats,
      message: '获取统计数据成功'
    } as Result<PendingReviewsStats>;

  } catch (error) {
    console.error('获取待审核统计数据失败:', error);
    return {
      success: false,
      data: null,
      message: '获取统计数据失败'
    } as Result<PendingReviewsStats>;
  }
}

/**
 * 获取待审核帖子列表
 */
export const getPendingPostsAPI = (params: PendingReviewsSearchParams) => {
  const queryParams = new URLSearchParams()
  if (params.page) queryParams.append('page', params.page.toString())
  if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
  if (params.keyword) queryParams.append('title', params.keyword) // 使用title参数搜索
  if (params.status !== undefined) queryParams.append('status', params.status.toString())
  if (params.startTime) queryParams.append('startTime', params.startTime)
  if (params.endTime) queryParams.append('endTime', params.endTime)

  const queryString = queryParams.toString()
  const url = queryString ? `/posts/admin/list?${queryString}` : '/posts/admin/list'

  return http<Result<PageResult<PostManagement>>>({
    method: 'GET',
    url: url
  })
}

/**
 * 获取待审核认证申请列表
 */
export const getPendingAuthAPI = (params: PendingReviewsSearchParams) => {
  const queryParams = new URLSearchParams()
  if (params.page) queryParams.append('page', params.page.toString())
  if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
  if (params.keyword) queryParams.append('keyword', params.keyword)
  if (params.status !== undefined) queryParams.append('status', params.status.toString())

  const queryString = queryParams.toString()
  const url = queryString ? `/verification/applications?${queryString}` : '/verification/applications'

  return http<Result<PageResult<AuthApplication>>>({
    method: 'GET',
    url: url
  })
}

/**
 * 获取待处理反馈列表
 */
export const getPendingFeedbackAPI = (params: PendingReviewsSearchParams) => {
  const queryParams = new URLSearchParams()
  if (params.page) queryParams.append('page', params.page.toString())
  if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
  if (params.keyword) queryParams.append('keyword', params.keyword)
  if (params.status !== undefined) queryParams.append('status', params.status.toString())

  const queryString = queryParams.toString()
  const url = queryString ? `/feedback/admin/list?${queryString}` : '/feedback/admin/list'

  return http<Result<PageResult<Feedback>>>({
    method: 'GET',
    url: url
  })
}

/**
 * 审核帖子
 */
export const reviewPostAPI = (params: ReviewActionParams) => {
  const url = params.action === 'approve' 
    ? `/posts/admin/approve/${params.id}`
    : `/posts/admin/reject/${params.id}`

  return http<Result<string>>({
    method: 'POST',
    url: url,
    data: params.action === 'reject' ? { reason: params.reason } : {}
  })
}

/**
 * 审核认证申请
 */
export const reviewAuthAPI = (params: ReviewActionParams) => {
  return http<Result<string>>({
    method: 'POST',
    url: `/verification/admin/review/${params.id}`,
    data: {
      action: params.action,
      note: params.note,
      reason: params.reason
    }
  })
}

/**
 * 处理反馈
 */
export const reviewFeedbackAPI = (params: ReviewActionParams) => {
  return http<Result<string>>({
    method: 'POST',
    url: `/feedback/admin/process/${params.id}`,
    data: {
      action: params.action,
      note: params.note,
      reason: params.reason
    }
  })
}

/**
 * 批量审核帖子
 */
export const batchReviewPostsAPI = (params: BatchReviewParams) => {
  return http<Result<string>>({
    method: 'POST',
    url: '/posts/admin/batch-review',
    data: params
  })
}

/**
 * 批量审核认证申请
 */
export const batchReviewAuthAPI = (params: BatchReviewParams) => {
  return http<Result<string>>({
    method: 'POST',
    url: '/verification/admin/batch-review',
    data: params
  })
}

/**
 * 批量处理反馈
 */
export const batchReviewFeedbackAPI = (params: BatchReviewParams) => {
  return http<Result<string>>({
    method: 'POST',
    url: '/feedback/admin/batch-process',
    data: params
  })
}
