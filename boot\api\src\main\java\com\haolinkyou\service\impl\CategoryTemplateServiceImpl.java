package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.entity.CategoryTemplates;
import com.haolinkyou.entity.TemplateFields;
import com.haolinkyou.mapper.CategoryTemplatesMapper;
import com.haolinkyou.service.ICategoryTemplateService;
import com.haolinkyou.service.ITemplateFieldsService;
// 移除未使用的导入
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 分类模板服务实现类
 */
@Service
public class CategoryTemplateServiceImpl extends ServiceImpl<CategoryTemplatesMapper, CategoryTemplates> implements ICategoryTemplateService {

    private static final Logger log = LoggerFactory.getLogger(CategoryTemplateServiceImpl.class);

    @Autowired
    private ITemplateFieldsService templateFieldsService;

    // 移除未使用的 ObjectMapper

    @Override
    public List<CategoryTemplates> getTemplatesByCategoryId(Long categoryId) {
        try {
            QueryWrapper<CategoryTemplates> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("category_id", categoryId)
                       .eq("status", 1)
                       .orderByAsc("sort_order")
                       .orderByDesc("is_default");
            
            return list(queryWrapper);
        } catch (Exception e) {
            log.error("获取分类模板失败，分类ID: {}", categoryId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public CategoryTemplates getDefaultTemplate(Long categoryId) {
        try {
            QueryWrapper<CategoryTemplates> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("category_id", categoryId)
                       .eq("is_default", true)
                       .eq("status", 1)
                       .last("LIMIT 1");
            
            CategoryTemplates template = getOne(queryWrapper);
            
            // 如果没有默认模板，创建一个
            if (template == null) {
                createDefaultTemplate(categoryId);
                template = getOne(queryWrapper);
            }
            
            return template;
        } catch (Exception e) {
            log.error("获取默认模板失败，分类ID: {}", categoryId, e);
            return null;
        }
    }

    @Override
    public TemplateConfig getTemplateConfig(Long templateId) {
        try {
            CategoryTemplates template = getById(templateId);
            if (template == null) {
                return null;
            }

            List<TemplateFields> fields = templateFieldsService.getFieldsByTemplateId(templateId);

            TemplateConfig config = new TemplateConfig();
            config.setTemplate(template);
            config.setFields(fields);

            return config;
        } catch (Exception e) {
            log.error("获取模板配置失败，模板ID: {}", templateId, e);
            return null;
        }
    }

    @Override
    @Transactional
    public boolean saveTemplateConfig(TemplateConfig templateConfig) {
        try {
            CategoryTemplates template = templateConfig.getTemplate();
            List<TemplateFields> fields = templateConfig.getFields();

            // 保存或更新模板
            boolean templateSaved;
            if (template.getId() == null) {
                templateSaved = save(template);
            } else {
                templateSaved = updateById(template);
            }

            if (!templateSaved) {
                return false;
            }

            // 保存字段
            return templateFieldsService.saveTemplateFields(template.getId(), fields);

        } catch (Exception e) {
            log.error("保存模板配置失败", e);
            return false;
        }
    }

    @Override
    public ValidationResult validateTemplateData(Long templateId, Map<String, Object> data) {
        try {
            List<TemplateFields> fields = templateFieldsService.getFieldsByTemplateId(templateId);
            List<String> errors = new ArrayList<>();
            Map<String, Object> processedData = new HashMap<>();

            for (TemplateFields field : fields) {
                String fieldName = field.getFieldName();
                Object value = data.get(fieldName);

                // 检查必填字段
                if (field.getRequired() && (value == null || value.toString().trim().isEmpty())) {
                    errors.add(field.getFieldLabel() + "是必填字段");
                    continue;
                }

                // 根据字段类型进行验证和处理
                Object processedValue = validateAndProcessField(field, value);
                if (processedValue != null) {
                    processedData.put(fieldName, processedValue);
                }
            }

            ValidationResult result = new ValidationResult(errors.isEmpty(), errors);
            result.setProcessedData(processedData);
            return result;

        } catch (Exception e) {
            log.error("验证模板数据失败，模板ID: {}", templateId, e);
            return new ValidationResult(false, Arrays.asList("数据验证异常"));
        }
    }

    @Override
    @Transactional
    public boolean createDefaultTemplate(Long categoryId) {
        try {
            // 检查是否已存在默认模板
            QueryWrapper<CategoryTemplates> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("category_id", categoryId)
                       .eq("is_default", true);
            
            if (count(queryWrapper) > 0) {
                return true; // 已存在默认模板
            }

            // 创建默认模板
            CategoryTemplates template = new CategoryTemplates();
            template.setCategoryId(categoryId);
            template.setTemplateName("默认模板");
            template.setDescription("系统默认发帖模板");
            template.setIsDefault(true);
            template.setStatus(1);
            template.setSortOrder(0);

            boolean saved = save(template);
            if (saved) {
                // 创建默认字段
                templateFieldsService.createDefaultFields(template.getId(), categoryId);
            }

            return saved;
        } catch (Exception e) {
            log.error("创建默认模板失败，分类ID: {}", categoryId, e);
            return false;
        }
    }

    /**
     * 验证和处理字段值
     */
    private Object validateAndProcessField(TemplateFields field, Object value) {
        if (value == null) {
            return null;
        }

        String fieldType = field.getFieldType();
        String valueStr = value.toString().trim();

        switch (fieldType) {
            case "text":
            case "textarea":
                return valueStr;
            case "number":
                try {
                    return Integer.parseInt(valueStr);
                } catch (NumberFormatException e) {
                    return null;
                }
            case "date":
                // 这里可以添加日期格式验证
                return valueStr;
            case "select":
            case "radio":
                // 这里可以验证选项是否有效
                return valueStr;
            case "checkbox":
                // 处理多选值
                if (value instanceof List) {
                    return value;
                } else {
                    return Arrays.asList(valueStr);
                }
            default:
                return valueStr;
        }
    }
}
