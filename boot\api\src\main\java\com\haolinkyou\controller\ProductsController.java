package com.haolinkyou.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.Products;
import com.haolinkyou.entity.UserRedemptions;
import com.haolinkyou.service.IProductsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 积分商品控制器
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@RestController
@RequestMapping("/api/products")
public class ProductsController {

    @Autowired
    private IProductsService productsService;

    /**
     * 获取可兑换商品列表
     */
    @GetMapping("/list")
    public Result<List<Products>> getProductsList() {
        try {
            List<Products> products = productsService.getAvailableProducts();
            return Result.success(products);
        } catch (Exception e) {
            return Result.error("获取商品列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取商品列表（分页，管理员用）
     */
    @GetMapping("/admin/list")
    public Result<Page<Products>> getProductsPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer status,
            HttpServletRequest request) {
        try {
            // 这里应该添加管理员权限验证
            Page<Products> products = productsService.getProductsPage(page, size, status);
            return Result.success(products);
        } catch (Exception e) {
            return Result.error("获取商品列表失败: " + e.getMessage());
        }
    }

    /**
     * 兑换商品
     */
    @PostMapping("/redeem")
    public Result<IProductsService.RedeemResult> redeemProduct(
            @RequestBody RedeemRequest request,
            HttpServletRequest httpRequest) {
        try {
            Long userId = (Long) httpRequest.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 参数验证
            if (request.getProductId() == null || request.getQuantity() == null || request.getQuantity() <= 0) {
                return Result.error("参数错误");
            }

            IProductsService.RedeemResult result = productsService.redeemProduct(
                userId, 
                request.getProductId(), 
                request.getQuantity(),
                request.getShippingAddress(),
                request.getContactPhone(),
                request.getRemark()
            );

            if (result.isSuccess()) {
                return Result.success(result, result.getMessage());
            } else {
                return Result.error(result.getMessage());
            }
        } catch (Exception e) {
            return Result.error("兑换失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户兑换记录
     */
    @GetMapping("/redemptions")
    public Result<Page<UserRedemptions>> getUserRedemptions(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            Page<UserRedemptions> redemptions = productsService.getUserRedemptions(userId, page, size);
            return Result.success(redemptions);
        } catch (Exception e) {
            return Result.error("获取兑换记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有兑换记录（管理员）
     */
    @GetMapping("/admin/redemptions")
    public Result<Page<UserRedemptions>> getAllRedemptions(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer status,
            HttpServletRequest request) {
        try {
            // 这里应该添加管理员权限验证
            Page<UserRedemptions> redemptions = productsService.getAllRedemptions(page, size, status);
            return Result.success(redemptions);
        } catch (Exception e) {
            return Result.error("获取兑换记录失败: " + e.getMessage());
        }
    }

    /**
     * 更新兑换订单状态（管理员）
     */
    @PostMapping("/admin/redemptions/{redemptionId}/status")
    public Result<Boolean> updateRedemptionStatus(
            @PathVariable Long redemptionId,
            @RequestBody UpdateStatusRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 这里应该添加管理员权限验证
            boolean result = productsService.updateRedemptionStatus(
                redemptionId, 
                request.getStatus(), 
                request.getRemark()
            );

            if (result) {
                return Result.success(true, "状态更新成功");
            } else {
                return Result.error("状态更新失败");
            }
        } catch (Exception e) {
            return Result.error("状态更新失败: " + e.getMessage());
        }
    }

    /**
     * 兑换请求DTO
     */
    public static class RedeemRequest {
        private Long productId;
        private Integer quantity;
        private String shippingAddress;
        private String contactPhone;
        private String remark;

        // Getters and Setters
        public Long getProductId() { return productId; }
        public void setProductId(Long productId) { this.productId = productId; }

        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }

        public String getShippingAddress() { return shippingAddress; }
        public void setShippingAddress(String shippingAddress) { this.shippingAddress = shippingAddress; }

        public String getContactPhone() { return contactPhone; }
        public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }

        public String getRemark() { return remark; }
        public void setRemark(String remark) { this.remark = remark; }
    }

    /**
     * 状态更新请求DTO
     */
    public static class UpdateStatusRequest {
        private Integer status;
        private String remark;

        // Getters and Setters
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }

        public String getRemark() { return remark; }
        public void setRemark(String remark) { this.remark = remark; }
    }
}