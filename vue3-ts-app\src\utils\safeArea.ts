import { ref } from "vue";

/**
 * 获取页面安全区域距离
 * @returns {Object} 包含顶部导航栏、内容区域和底部的padding值
 */
export function useSafeArea() {
  const { safeAreaInsets } = uni.getSystemInfoSync();
  const pageHeaderPaddingTop = ref("10px");
  const mainContentPaddingTop = ref("45px");
  const safeAreaBottom = ref("0px");

  // #ifdef MP-WEIXIN
  pageHeaderPaddingTop.value = safeAreaInsets!.top + 10 + "px";
  mainContentPaddingTop.value = safeAreaInsets!.top + 40 + "px";
  safeAreaBottom.value = safeAreaInsets!.bottom - 10 + "px";
  // #endif
  // #ifdef H5
  pageHeaderPaddingTop.value = "10px";
  mainContentPaddingTop.value = "45px";
  safeAreaBottom.value = "10px";
  // #endif

  return {
    pageHeaderPaddingTop,
    mainContentPaddingTop,
    safeAreaBottom,
  };
}
