package com.haolinkyou.service;

import com.haolinkyou.entity.Products;
import com.haolinkyou.entity.UserRedemptions;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 积分商品服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
public interface IProductsService extends IService<Products> {

    /**
     * 获取商品列表（上架的商品）
     * 
     * @return 商品列表
     */
    List<Products> getAvailableProducts();

    /**
     * 获取商品列表（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param status 状态筛选
     * @return 商品分页
     */
    Page<Products> getProductsPage(Integer page, Integer size, Integer status);

    /**
     * 兑换商品
     * 
     * @param userId 用户ID
     * @param productId 商品ID
     * @param quantity 兑换数量
     * @param shippingAddress 收货地址
     * @param contactPhone 联系电话
     * @param remark 备注
     * @return 兑换结果
     */
    RedeemResult redeemProduct(Long userId, Long productId, Integer quantity, 
                              String shippingAddress, String contactPhone, String remark);

    /**
     * 获取用户兑换记录（分页）
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 兑换记录分页
     */
    Page<UserRedemptions> getUserRedemptions(Long userId, Integer page, Integer size);

    /**
     * 获取所有兑换记录（管理员）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param status 状态筛选
     * @return 兑换记录分页
     */
    Page<UserRedemptions> getAllRedemptions(Integer page, Integer size, Integer status);

    /**
     * 更新兑换订单状态
     * 
     * @param redemptionId 兑换记录ID
     * @param status 新状态
     * @param remark 备注
     * @return 是否成功
     */
    boolean updateRedemptionStatus(Long redemptionId, Integer status, String remark);

    /**
     * 检查并更新商品库存状态
     * 
     * @param productId 商品ID
     */
    void checkAndUpdateProductStatus(Long productId);

    /**
     * 兑换结果类
     */
    class RedeemResult {
        private boolean success;
        private String message;
        private Long redemptionId;

        public RedeemResult() {}

        public RedeemResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public RedeemResult(boolean success, String message, Long redemptionId) {
            this.success = success;
            this.message = message;
            this.redemptionId = redemptionId;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public Long getRedemptionId() { return redemptionId; }
        public void setRedemptionId(Long redemptionId) { this.redemptionId = redemptionId; }
    }
}