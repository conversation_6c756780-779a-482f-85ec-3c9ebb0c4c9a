<template>
  <view class="announcement-post-detail">
    <!-- 公告标题 -->
    <view class="announcement-header">
      <view class="title-row">
        <view class="announcement-title">{{ post.title }}</view>
        <view class="priority-badge" :class="priorityClass">
          {{ priorityText }}
        </view>
      </view>
      
      <!-- 有效期 -->
      <view v-if="post.validDate" class="valid-date">
        <text class="label">有效期至：</text>
        <text class="date">{{ formatDate(post.validDate) }}</text>
        <text v-if="isExpired" class="expired-tag">已过期</text>
      </view>
      
      <!-- 目标对象 -->
      <view class="target-audience">
        <text class="label">目标对象：</text>
        <text class="audience">{{ formatTargetAudience(post.targetAudience) }}</text>
      </view>
    </view>
    
    <!-- 基础内容 -->
    <BasePostDetail :post="post" />
    
    <!-- 公告状态 -->
    <view class="announcement-status">
      <view class="status-item">
        <text class="status-label">公告状态：</text>
        <text class="status-value" :class="statusClass">{{ statusText }}</text>
      </view>
      <view v-if="post.contact" class="status-item">
        <text class="status-label">咨询联系：</text>
        <text class="status-value">{{ post.contact }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import BasePostDetail from './BasePostDetail.vue'
import { getTargetAudienceOptionsAPI, type TargetAudienceOption } from '@/services/roleService'

interface Props {
  post: {
    id?: number
    title: string
    content: string
    images?: string[]
    contact?: string
    priority: string
    validDate?: string
    targetAudience?: string[]
    createdTime: string
    updatedTime?: string
    [key: string]: any
  }
}

const props = defineProps<Props>()

// 重要程度映射
const priorityMap = {
  'normal': { text: '普通', class: 'normal' },
  'important': { text: '重要', class: 'important' },
  'urgent': { text: '紧急', class: 'urgent' }
}

// 目标对象映射（动态获取）
const targetAudienceMap = ref<Map<string, string>>(new Map())

// 加载目标对象映射
const loadTargetAudienceMap = async () => {
  try {
    const options = await getTargetAudienceOptionsAPI()
    const map = new Map<string, string>()
    options.forEach(option => {
      map.set(option.value, option.label)
    })
    targetAudienceMap.value = map
  } catch (error) {
    console.error('加载目标对象映射失败:', error)
    // 使用默认映射作为后备
    targetAudienceMap.value = new Map([
      ['owner', '全体业主'],
      ['tenant', '租户'],
      ['property', '物业人员'],
      ['committee', '业委会成员']
    ])
  }
}

// 重要程度文本和样式
const priorityText = computed(() => {
  return priorityMap[props.post.priority as keyof typeof priorityMap]?.text || '普通'
})

const priorityClass = computed(() => {
  return priorityMap[props.post.priority as keyof typeof priorityMap]?.class || 'normal'
})

// 是否过期
const isExpired = computed(() => {
  if (!props.post.validDate) return false
  return new Date(props.post.validDate) < new Date()
})

// 状态文本和样式
const statusText = computed(() => {
  if (isExpired.value) return '已过期'
  if (props.post.validDate) return '有效中'
  return '长期有效'
})

const statusClass = computed(() => {
  if (isExpired.value) return 'expired'
  return 'active'
})

// 格式化日期
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 格式化目标对象
const formatTargetAudience = (audiences?: string[]) => {
  if (!audiences || audiences.length === 0) {
    return '所有用户'
  }

  return audiences.map(audience =>
    targetAudienceMap.value.get(audience) || audience
  ).join('、')
}

// 组件挂载时加载目标对象映射
onMounted(() => {
  loadTargetAudienceMap()
})
</script>

<style scoped lang="scss">
.announcement-post-detail {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.announcement-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 16px;
}

.title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.announcement-title {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
  
  &.normal {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  &.important {
    background-color: #ffc107;
    color: #333;
  }
  
  &.urgent {
    background-color: #dc3545;
    color: white;
  }
}

.valid-date, .target-audience {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  opacity: 0.8;
  margin-right: 8px;
}

.date, .audience {
  font-weight: 500;
}

.expired-tag {
  margin-left: 8px;
  padding: 2px 6px;
  background-color: #dc3545;
  color: white;
  border-radius: 4px;
  font-size: 12px;
}

.announcement-status {
  padding: 16px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.status-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.status-value {
  font-size: 14px;
  font-weight: 500;
  
  &.active {
    color: #28a745;
  }
  
  &.expired {
    color: #dc3545;
  }
}
</style>
