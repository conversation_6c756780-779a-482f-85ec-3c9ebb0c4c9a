// 帖子详情页面相关类型定义

import type { PostsList, Comments } from './PostsList';

// 帖子详情扩展类型
export interface PostDetail extends PostsList {
  // 扩展字段可以在这里添加
}

// 评论扩展类型
export interface CommentDetail extends Comments {
  // 扩展字段可以在这里添加
}

// 当前用户信息
export interface CurrentUser {
  id: number | null;
  avatarText: string;
  nickName: string;
  status: string;
}

// 帖子状态响应
export interface PostStatusResponse {
  isLiked: boolean;
  isCollected: boolean;
  likeCount: number;
  collectCount: number;
}

// 评论提交参数
export interface CommentSubmitParams {
  postId: number;
  userId: number;
  content: string;
  parentCommentId?: number | string | null;
  replyToUserId?: number | null;
}

// 评论删除参数
export interface CommentDeleteParams {
  id: number;
}

// 图片预览配置
export interface ImagePreviewOptions {
  urls: string[];
  current: string;
}

// Swiper变化事件 - 支持多种可能的事件结构
export interface SwiperChangeEvent {
  current: number;
  detail?: {
    current: number;
  };
}

// uni-app原生swiper事件
export interface UniSwiperChangeEvent {
  detail: {
    current: number;
    source: string;
  };
}

// uview-plus swiper事件
export interface UviewSwiperChangeEvent {
  current: number;
}

// Swiper事件处理函数类型 - 支持多种事件格式
export type SwiperChangeHandler = (e: number | UviewSwiperChangeEvent | UniSwiperChangeEvent | any) => void;

// 媒体文件类型
export type MediaFileType = '0' | '1' | 0 | 1; // 0-图片 1-视频

// 操作状态
export interface OperationStatus {
  isProcessingLike: boolean;
  isProcessingCollect: boolean;
  isLiked: boolean;
  isCollected: boolean;
  likeCount: number;
  collectCount: number;
}

// 评论展开状态
export type ExpandedCommentsState = Set<number>;

// 回复目标
export type ReplyTarget = string | number | null;

// 弹窗状态
export interface PopupState {
  show: boolean;
  activeReplyId: ReplyTarget;
  commentContent: string;
  commentTip: string;
  buttonTip: string;
}

// 帖子详情页面状态
export interface PostDetailPageState {
  postId: number;
  postData: PostsList;
  commentList: Comments[];
  currentImageId: number;
  expandedComments: ExpandedCommentsState;
  popup: PopupState;
  operation: OperationStatus;
}

// 时间格式化函数类型
export type TimeFormatter = (timeStr: string | undefined) => string;

// 回复内容格式化函数类型
export type ReplyContentFormatter = (reply: Comments) => string;

// 权限检查函数类型
export type PermissionChecker = (comment: Comments) => boolean;

// API响应类型别名
export type PostDetailApiResponse = Result<PostsList>;
export type CommentsApiResponse = Result<Comments[]>;
export type CommentSubmitApiResponse = Result<any>;
export type CommentDeleteApiResponse = Result<any>;

// 导入Result类型
import type { Result } from './common';
