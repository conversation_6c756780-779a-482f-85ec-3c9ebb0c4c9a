package com.haolinkyou.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haolinkyou.entity.PostLikes;

/**
 * 帖子点赞服务接口
 */
public interface IPostLikesService extends IService<PostLikes> {
    
    /**
     * 点赞或取消点赞
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 操作结果：true-点赞成功，false-取消点赞成功
     */
    boolean toggleLike(Long postId, Long userId);
    
    /**
     * 检查用户是否已点赞该帖子
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return true-已点赞，false-未点赞
     */
    boolean isUserLiked(Long postId, Long userId);
    
    /**
     * 获取帖子的点赞数
     * @param postId 帖子ID
     * @return 点赞数
     */
    int getPostLikeCount(Long postId);
}
