/*
 * @Author: Rock
 * @Date: 2025-04-09 19:56:09
 * @LastEditors: Rock
 * @LastEditTime: 2025-04-15 21:35:17
 * @Description:
 */
import { createPinia } from "pinia";
import persist from "pinia-plugin-persistedstate";

// 创建 Pinia 实例
const pinia = createPinia();

// 将 Pinia 实例添加到 Vue 应用
pinia.use(persist);

// 默认导出,以便在main.ts中使用
export default pinia;

// 导出 store 模块
export * from "./modules/member";
