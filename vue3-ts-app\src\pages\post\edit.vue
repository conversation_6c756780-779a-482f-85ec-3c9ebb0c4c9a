<template>
  <view class="edit-post-page">
    <!-- 页面头部 -->
    <up-navbar
      title="编辑帖子"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 内容区域 -->
    <view class="content-area" :style="{ marginTop: mainContentPaddingTop }">
      <!-- 文本输入区域 -->
      <view class="text-input-section">
        <up-textarea
          v-model="content"
          placeholder="添加你的描述（选填）"
          :maxlength="500"
          :showWordLimit="true"
          :autoHeight="true"
          :height="200"
          border="none"
          fontSize="16"
          placeholderStyle="color: #c0c4cc"
        />
      </view>

      <!-- 媒体类型选择 -->
      <view class="media-type-section">
        <view class="media-tabs">
          <view
            class="media-tab"
            :class="{ active: currentMediaType === 0 }"
            @click="switchMediaType(0)"
          >
            图片
          </view>
          <view
            class="media-tab"
            :class="{ active: currentMediaType === 1 }"
            @click="switchMediaType(1)"
          >
            视频
          </view>
        </view>
      </view>

      <!-- 文件上传区域 -->
      <view class="upload-section">
        <up-upload
          :fileList="fileList"
          @afterRead="afterRead"
          @delete="deleteFile"
          name="upload"
          multiple
          :maxCount="currentMediaType === 0 ? 9 : 1"
          :accept="currentMediaType === 0 ? 'image' : 'video'"
          :previewFullImage="true"
          width="160"
          height="160"
          uploadIcon="camera-fill"
          uploadIconColor="#c0c4cc"
          :uploadText="currentMediaType === 0 ? '选择图片' : '选择视频'"
          :maxSize="currentMediaType === 0 ? 10 * 1024 * 1024 : 100 * 1024 * 1024"
          @oversize="onOversize"
        >
          <template #default>
            <view class="upload-placeholder">
              <up-icon
                :name="currentMediaType === 0 ? 'camera-fill' : 'play-circle-fill'"
                size="40"
                color="#c0c4cc"
              ></up-icon>
              <text class="upload-text">
                {{ currentMediaType === 0 ? '选择图片' : '选择视频' }}
              </text>
              <text class="upload-tip">
                {{ currentMediaType === 0 ? '最多9张，单张不超过10MB' : '最多1个，不超过100MB' }}
              </text>
            </view>
          </template>
        </up-upload>


      </view>

      <!-- 分类选择 -->
      <view class="category-section">
        <view class="section-title">选择分类</view>
        <view class="category-list">
          <view
            v-for="category in categories"
            :key="category.id"
            class="category-item"
            :class="{ active: selectedCategoryId === category.id }"
            @click="selectCategory(category.id)"
          >
            {{ category.categoryName }}
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮区域 -->
    <view class="bottom-actions" :style="{ paddingBottom: safeAreaBottom + 'px' }">
      <up-button
        type="primary"
        size="large"
        @click="handleCancel"
        customStyle="flex: 1; margin-right: 10px;"
      >
        取消
      </up-button>
      <up-button
        type="primary"
        size="large"
        @click="handleSave"
        :loading="isSaving"
        customStyle="flex: 2; background-color: #5677fc;"
      >
        保存
      </up-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSafeArea } from '@/utils/safeArea'
import { get } from '@/utils/http'
import { useMemberStore } from '@/stores'
import type { Categories } from '@/types/Categories'
import type { PostsList } from '@/types/PostsList'
import type { ApiResponse } from '@/types/ApiResponse'

// 安全区域
const { mainContentPaddingTop, safeAreaBottom } = useSafeArea()

// 用户状态
const memberStore = useMemberStore()

// 页面参数
const postId = ref<number>(0)
const originalPost = ref<PostsList | null>(null)

// 表单数据
const content = ref('')
const currentMediaType = ref(0) // 0-图片 1-视频
const fileList = ref<any[]>([])
const selectedCategoryId = ref<number>(-1)
const isSaving = ref(false)

// 分类数据
const categories = ref<Categories[]>([])

// 获取页面参数
onMounted(async () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.id) {
    postId.value = parseInt(options.id)

    // 先获取分类列表，再加载帖子数据，确保分类数据可用于回显
    await fetchCategories()
    await loadPostData()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const res = await get<ApiResponse<Categories[]>>('/categories/list')
    if (res.success) {
      categories.value = res.data || []
    }
  } catch (error) {
    uni.showToast({
      title: '获取分类失败',
      icon: 'none'
    })
  }
}

// 加载帖子数据
const loadPostData = async () => {
  try {
    const response = await get<ApiResponse<PostsList>>('/posts/detail', {
      id: postId.value
    })

    if (response.success && response.data) {
      originalPost.value = response.data

      // 回显数据到表单
      content.value = response.data.content || ''

      // 处理分类回显
      selectedCategoryId.value = response.data.categoryId || -1

      // 判断媒体类型
      if (response.data.fileType !== null) {
        currentMediaType.value = parseInt(response.data.fileType.toString()) || 0
      }

      // 回显文件列表（如果有的话）
      if (response.data.fileList && response.data.fileList.trim()) {
        // 将逗号分隔的文件路径字符串转换为数组
        const filePathArray = response.data.fileList.split(',').filter((path: string) => path.trim())

        // 将文件路径转换为文件列表格式，添加服务器地址前缀
        fileList.value = filePathArray.map((filePath: string, index: number) => {
          const trimmedPath = filePath.trim()
          // 如果文件路径不是完整URL，则添加服务器地址前缀
          const fullUrl = trimmedPath.startsWith('http') ? trimmedPath : `http://localhost:3205/${trimmedPath}`

          return {
            url: fullUrl,
            name: `文件${index + 1}`,
            status: 'success',
            message: '已上传',
            isExisting: true // 标记为已存在的文件
          }
        })
      }
    } else {
      uni.showToast({
        title: '帖子数据加载失败',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  } catch (error) {
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
}

// 切换媒体类型
const switchMediaType = (type: number) => {
  if (type !== currentMediaType.value) {
    // 切换类型时，清空新添加的文件，保留已存在的文件
    fileList.value = fileList.value.filter(file => file.isExisting)
    currentMediaType.value = type
  }
}

// 选择分类
const selectCategory = (categoryId: number) => {
  selectedCategoryId.value = categoryId
}

// 上传组件：读取文件后的处理
const afterRead = (event: any) => {
  // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
  let lists = [].concat(event.file)

  lists.forEach((item: any) => {
    // 为每个文件添加状态信息
    const fileObj = {
      ...item,
      status: 'ready', // 准备状态，保存时上传
      message: '准备上传',
      isExisting: false // 标记为新添加的文件
    }

    fileList.value.push(fileObj)
  })

  uni.showToast({
    title: `已选择${lists.length}个文件`,
    icon: 'success'
  })
}

// 上传组件：删除文件
const deleteFile = (event: any) => {
  fileList.value.splice(event.index, 1)
  uni.showToast({
    title: '文件已删除',
    icon: 'success'
  })
}

// 上传组件：文件大小超出限制
const onOversize = () => {
  const maxSizeMB = currentMediaType.value === 0 ? 10 : 100
  uni.showToast({
    title: `文件大小不能超过${maxSizeMB}MB`,
    icon: 'none'
  })
}

// 取消编辑
const handleCancel = () => {
  uni.showModal({
    title: '确认取消',
    content: '取消编辑将丢失所有修改，确定要取消吗？',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    }
  })
}

// 保存编辑
const handleSave = async () => {
  // 检查登录状态
  if (!memberStore.profile?.token) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  // 验证内容
  if (!content.value.trim()) {
    uni.showToast({
      title: '请输入内容',
      icon: 'none'
    })
    return
  }

  if (selectedCategoryId.value === -1) {
    uni.showToast({
      title: '请选择分类',
      icon: 'none'
    })
    return
  }

  isSaving.value = true

  try {
    // 处理文件：区分已存在文件和新文件
    const existingFiles: string[] = []
    const newFiles: any[] = []

    for (const fileItem of fileList.value) {
      if (fileItem.isExisting) {
        // 已存在的文件，提取相对路径
        const filePath = fileItem.url.replace('http://localhost:3205/', '')
        existingFiles.push(filePath)
      } else {
        // 新添加的文件，需要上传
        newFiles.push(fileItem)
      }
    }

    // 使用FormData方式提交，包含新文件
    const formData = new FormData()
    formData.append('id', postId.value.toString())
    formData.append('content', content.value.trim())
    formData.append('category_id', selectedCategoryId.value.toString())
    formData.append('currentFileType', currentMediaType.value.toString())

    // 添加已存在的文件路径
    if (existingFiles.length > 0) {
      formData.append('existingFiles', existingFiles.join(','))
    }

    // 添加新文件
    if (newFiles.length > 0) {
      for (let i = 0; i < newFiles.length; i++) {
        const fileItem = newFiles[i]
        if (fileItem.file) {
          formData.append('filesList', fileItem.file, fileItem.file.name || fileItem.name)
        }
      }
    }

    // 获取token
    const token = memberStore.profile?.token || uni.getStorageSync('token') || ''

    // 调用编辑API
    const response = await fetch('/api/posts/edit', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.success) {
      uni.showToast({
        title: '保存成功，重新进入审核',
        icon: 'success'
      })

      // 设置全局刷新标记
      const app = getApp()
      if (!app.globalData) {
        app.globalData = {}
      }
      app.globalData.needRefreshHome = true
      app.globalData.needRefreshPostDetail = true
      app.globalData.refreshPostId = postId.value

      // 返回上一页（详情页）
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      throw new Error(result.message || '保存失败')
    }

  } catch (error) {
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none'
    })
  } finally {
    isSaving.value = false
  }
}
</script>

<style scoped lang="scss">
.edit-post-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.content-area {
  flex: 1;
  padding: 0 20px;
}

.text-input-section {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.media-type-section {
  margin-bottom: 20px;
}

.media-tabs {
  display: flex;
  background-color: white;
  border-radius: 12px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.media-tab {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s ease;
  cursor: pointer;

  &.active {
    background-color: #5677fc;
    color: white;
  }
}

.upload-section {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 160px;
  height: 160px;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  background: #fafafa;
  transition: all 0.3s ease;

  &:hover {
    border-color: #5677fc;
    background: #f0f8ff;
  }
}

.upload-text {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
  font-weight: 500;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  text-align: center;
}

.file-status {
  display: flex;
  align-items: center;
  margin-top: 15px;
  padding: 10px 15px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #19be6b;
}

.status-text {
  font-size: 14px;
  color: #19be6b;
  margin-left: 8px;
}

.category-section {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.category-item {
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-size: 14px;
  color: #666;
  background-color: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;

  &.active {
    background-color: #5677fc;
    border-color: #5677fc;
    color: white;
  }

  &:hover:not(.active) {
    border-color: #5677fc;
    color: #5677fc;
  }
}

.bottom-actions {
  display: flex;
  padding: 20px;
  background-color: white;
  border-top: 1px solid #f0f0f0;
  gap: 10px;
}
</style>
