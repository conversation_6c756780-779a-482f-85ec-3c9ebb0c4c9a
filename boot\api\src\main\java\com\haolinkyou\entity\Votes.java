package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("votes")
public class Votes {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long postId;
    
    private String title;
    
    private String description;
    
    private String options; // JSON格式存储选项列表
    
    private String voteType; // single:单选 multiple:多选
    
    private Boolean isAnonymous;
    
    private LocalDateTime endTime;
    
    private Integer status;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
    
    @TableLogic
    private Integer delFlag;
}