package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@TableName("feedback")
@EqualsAndHashCode(callSuper = true)
public class Feedback extends BaseEntity {

    private Long userId;

    private String content;

    private String contactInfo;

    private Integer status;

    private Date processTime;

    private Long processUserId;

    private String processNote;
}
