package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName("user_points")
@EqualsAndHashCode(callSuper = true)
public class UserPoints extends BaseEntity {

    private Long userId;

    private String type;

    private Integer points;

    private String description;

    private Long relatedId;

    // 排除 updatedTime 字段，因为 user_points 表中没有这个字段
    @TableField(exist = false)
    private java.util.Date updatedTime;
}
