<template>
  <view class="base-template">
    <!-- 标题输入 -->
    <view class="form-item">
      <view class="form-label">
        <text>标题</text>
        <text v-if="titleRequired" class="required">*</text>
      </view>
      <up-input
        v-model="formData.title"
        :placeholder="titlePlaceholder"
        :maxlength="100"
        @input="handleTitleChange"
        @blur="validateTitle"
      />
      <view v-if="errors.title" class="error-text">{{ errors.title }}</view>
    </view>

    <!-- 内容输入 -->
    <view class="form-item">
      <view class="form-label">
        <text>内容</text>
        <text class="required">*</text>
      </view>
      <up-textarea
        v-model="formData.content"
        :placeholder="contentPlaceholder"
        :maxlength="2000"
        :autoHeight="true"
        :height="150"
        @input="handleContentChange"
        @blur="validateContent"
      />
      <view v-if="errors.content" class="error-text">{{ errors.content }}</view>
    </view>

    <!-- 媒体上传 -->
    <view v-if="showMediaUpload" class="form-item">
      <view class="form-label">
        <text>{{ mediaLabel }}</text>
        <text v-if="mediaRequired" class="required">*</text>
      </view>
      
      <!-- 文件上传区域 -->
      <view class="upload-section">
        <up-upload
          :fileList="formData.fileList"
          @afterRead="afterRead"
          @delete="deleteFile"
          name="upload"
          multiple
          :maxCount="currentMediaType === 0 ? maxImageCount : 1"
          accept="image"
          :previewFullImage="true"
          width="160"
          height="160"
          uploadIcon="camera-fill"
          uploadIconColor="#c0c4cc"
          :uploadText="'选择文件'"
          :maxSize="100 * 1024 * 1024"
          @oversize="onOversize"
        >
          <template #default>
            <view class="upload-placeholder">
              <up-icon
                :name="currentMediaType === 0 ? 'camera-fill' : 'play-circle-fill'"
                size="40"
                color="#c0c4cc"
              ></up-icon>
              <text class="upload-text">
                {{ currentMediaType === 0 ? '选择图片' : '选择视频' }}
              </text>
              <text class="upload-tip">
                {{ currentMediaType === 0 ? '支持JPG、PNG等格式' : '支持MP4、MOV等格式' }}
              </text>
            </view>
          </template>
        </up-upload>
      </view>

      <view v-if="errors.fileList" class="error-text">{{ errors.fileList }}</view>
      <view class="help-text">{{ mediaHelpText }}</view>
    </view>

    <!-- 兼容旧版本的图片上传 -->
    <view v-if="showImageUpload && !showMediaUpload" class="form-item">
      <view class="form-label">
        <text>{{ imageLabel }}</text>
        <text v-if="imageRequired" class="required">*</text>
      </view>
      <up-upload
        :fileList="formData.images"
        @afterRead="handleImageUpload"
        @delete="handleImageDelete"
        name="upload"
        multiple
        :maxCount="maxImageCount"
        accept="image"
        :previewFullImage="true"
        width="160"
        height="160"
        uploadIcon="camera-fill"
        uploadIconColor="#c0c4cc"
        uploadText="选择图片"
        :maxSize="10 * 1024 * 1024"
        @oversize="handleImageOversize"
      />
      <view v-if="errors.images" class="error-text">{{ errors.images }}</view>
      <view class="help-text">{{ imageHelpText }}</view>
    </view>

    <!-- 联系方式 -->
    <view v-if="showContact" class="form-item">
      <view class="form-label">
        <text>联系方式</text>
        <text v-if="contactRequired" class="required">*</text>
      </view>
      <up-input
        v-model="formData.contact"
        :placeholder="contactPlaceholder"
        @input="handleContactChange"
        @blur="validateContact"
      />
      <view v-if="errors.contact" class="error-text">{{ errors.contact }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { validateField } from '@/utils/validation'
import { detectFileType, processFileSelection } from '@/types/publish'

interface Props {
  // 标题相关
  titleRequired?: boolean
  titlePlaceholder?: string

  // 内容相关
  contentPlaceholder?: string

  // 媒体上传相关
  showMediaUpload?: boolean
  mediaRequired?: boolean
  mediaLabel?: string
  mediaHelpText?: string
  maxImageCount?: number
  showMediaTypeSwitch?: boolean
  defaultMediaType?: number

  // 兼容旧的图片上传属性
  showImageUpload?: boolean
  imageRequired?: boolean
  imageLabel?: string
  imageHelpText?: string

  // 联系方式相关
  showContact?: boolean
  contactRequired?: boolean
  contactPlaceholder?: string
  contactType?: 'phone' | 'email' | 'any'

  // 初始数据
  initialData?: Record<string, any>
}

interface Emits {
  (e: 'update:data', data: Record<string, any>): void
  (e: 'update:valid', valid: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  titleRequired: false,
  titlePlaceholder: '请输入标题（可选）',
  contentPlaceholder: '请输入内容',

  // 媒体上传相关
  showMediaUpload: false,
  mediaRequired: false,
  mediaLabel: '媒体文件',
  mediaHelpText: '图片：最多9张，单张不超过10MB；视频：最多1个，不超过100MB',
  maxImageCount: 9,
  showMediaTypeSwitch: true,
  defaultMediaType: 0,

  // 兼容旧的图片上传属性
  showImageUpload: true,
  imageRequired: false,
  imageLabel: '相关图片',
  imageHelpText: '最多可上传9张图片，单张不超过10MB',

  showContact: false,
  contactRequired: false,
  contactPlaceholder: '请输入联系方式',
  contactType: 'any'
})

const emit = defineEmits<Emits>()

// 表单数据
const formData = ref({
  title: '',
  content: '',
  images: [] as any[], // 兼容旧版本
  fileList: [] as any[], // 新的文件列表
  contact: '',
  ...props.initialData
})

// 错误信息
const errors = ref<Record<string, string>>({})

// 当前媒体类型 (0: 图片, 1: 视频)
const currentMediaType = ref(props.defaultMediaType)

// 初始化处理
if (props.initialData?.fileList && Array.isArray(props.initialData.fileList)) {
  formData.value.fileList = props.initialData.fileList.map((file: any) => ({
    ...file,
    isExisting: file.isExisting !== false // 默认为已存在文件
  }))
}

if (props.initialData?.currentMediaType !== undefined) {
  currentMediaType.value = props.initialData.currentMediaType
}

// 验证表单是否有效
const isValid = computed(() => {
  return Object.keys(errors.value).length === 0 &&
         formData.value.content.trim() !== '' &&
         (!props.titleRequired || formData.value.title.trim() !== '') &&
         (!props.imageRequired || formData.value.images.length > 0) &&
         (!props.mediaRequired || formData.value.fileList.length > 0) &&
         (!props.contactRequired || formData.value.contact.trim() !== '')
})

// 监听数据变化
watch(formData, (newData) => {
  emit('update:data', newData)
}, { deep: true })

// 监听验证状态变化
watch(isValid, (valid) => {
  emit('update:valid', valid)
})

// 监听 fileList 变化，同步到 images（兼容性）
watch(() => formData.value.fileList, (newFileList) => {
  if (props.showImageUpload && !props.showMediaUpload) {
    // 只有在使用旧版本图片上传时才同步
    return
  }
  // 将 fileList 中的图片文件同步到 images
  formData.value.images = newFileList.filter((file: any) =>
    file.type && file.type.startsWith('image/')
  )
}, { deep: true })

// 监听 images 变化，同步到 fileList（兼容性）
watch(() => formData.value.images, (newImages) => {
  if (props.showMediaUpload) {
    // 只有在使用新版本媒体上传时才同步
    return
  }
  // 将 images 同步到 fileList
  formData.value.fileList = [...newImages]
}, { deep: true })

// 验证标题
const validateTitle = () => {
  if (props.titleRequired) {
    const result = validateField(formData.value.title, {
      required: true,
      maxLength: 100,
      fieldName: '标题'
    })
    
    if (!result.valid && result.message) {
      errors.value.title = result.message
    } else {
      delete errors.value.title
    }
  }
}

// 验证内容
const validateContent = () => {
  const result = validateField(formData.value.content, {
    required: true,
    maxLength: 2000,
    fieldName: '内容'
  })
  
  if (!result.valid && result.message) {
    errors.value.content = result.message
  } else {
    delete errors.value.content
  }
}

// 验证联系方式
const validateContact = () => {
  if (props.showContact && props.contactRequired) {
    const result = validateField(formData.value.contact, {
      required: true,
      type: props.contactType === 'any' ? undefined : props.contactType,
      fieldName: '联系方式'
    })

    if (!result.valid && result.message) {
      errors.value.contact = result.message
    } else {
      delete errors.value.contact
    }
  } else if (props.showContact && formData.value.contact) {
    // 非必填但有值时，也要验证格式
    const result = validateField(formData.value.contact, {
      required: false,
      type: props.contactType === 'any' ? undefined : props.contactType,
      fieldName: '联系方式'
    })

    if (!result.valid && result.message) {
      errors.value.contact = result.message
    } else {
      delete errors.value.contact
    }
  }
}

// 处理标题变化
const handleTitleChange = () => {
  if (props.titleRequired) {
    validateTitle()
  }
}

// 处理内容变化
const handleContentChange = () => {
  validateContent()
}

// 处理联系方式变化
const handleContactChange = () => {
  if (props.showContact && props.contactRequired) {
    validateContact()
  }
}

// 智能切换媒体类型（基于文件内容自动判断）
const switchMediaType = (type: number) => {
  // 注意：现在媒体类型由文件内容自动决定，不再支持手动切换
  // 保留此方法是为了兼容性，但实际切换逻辑在 afterRead 中处理
  console.log('媒体类型已自动切换到:', type === 0 ? '图片模式' : '视频模式')
}

// 防止重复调用的标志
let isProcessing = false

// 上传组件：读取文件后的处理（智能文件类型检测）
const afterRead = (event: any) => {
  // 防止重复处理
  if (isProcessing) {
    return
  }

  isProcessing = true

  try {
    // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
    const newFiles = Array.isArray(event.file) ? event.file : [event.file]

    // 简单的重复检查：检查是否已经存在相同的文件
    const existingFiles = formData.value.fileList
    const uniqueNewFiles = newFiles.filter((newFile: any) => {
      return !existingFiles.some((existingFile: any) => {
        return existingFile.name === newFile.name &&
               existingFile.size === newFile.size &&
               existingFile.lastModified === newFile.lastModified
      })
    })

    if (uniqueNewFiles.length === 0) {
      uni.showToast({
        title: '文件已存在',
        icon: 'none'
      })
      return
    }

    // 使用智能文件处理函数
    const result = processFileSelection(
      formData.value.fileList,
      uniqueNewFiles,
      props.maxImageCount
    )

    // 更新文件列表和媒体类型
    formData.value.fileList = result.fileList
    currentMediaType.value = result.mediaType

    // 显示提示信息
    if (result.message) {
      uni.showToast({
        title: result.message,
        icon: 'none',
        duration: 2000
      })
    } else {
      uni.showToast({
        title: `已选择${uniqueNewFiles.length}个文件`,
        icon: 'success'
      })
    }
  } finally {
    // 延迟重置标志，防止快速连续调用
    setTimeout(() => {
      isProcessing = false
    }, 100)
  }

  // 清除错误信息
  if (props.mediaRequired || props.imageRequired) {
    delete errors.value.fileList
    delete errors.value.images
  }
}

// 上传组件：删除文件
const deleteFile = (event: any) => {
  formData.value.fileList.splice(event.index, 1)
  uni.showToast({
    title: '文件已删除',
    icon: 'success'
  })

  // 检查是否需要显示错误信息
  if ((props.mediaRequired || props.imageRequired) && formData.value.fileList.length === 0) {
    errors.value.fileList = '请至少上传一个文件'
  }
}

// 上传组件：文件大小超出限制
const onOversize = () => {
  const maxSizeMB = currentMediaType.value === 0 ? 10 : 100
  uni.showToast({
    title: `文件大小不能超过${maxSizeMB}MB`,
    icon: 'none'
  })
}

// 兼容旧版本的图片处理方法
const handleImageUpload = (event: any) => {
  const files = Array.isArray(event.file) ? event.file : [event.file]
  formData.value.images.push(...files)

  if (props.imageRequired) {
    delete errors.value.images
  }
}

const handleImageDelete = (event: any) => {
  formData.value.images.splice(event.index, 1)

  if (props.imageRequired && formData.value.images.length === 0) {
    errors.value.images = '请至少上传一张图片'
  }
}

const handleImageOversize = () => {
  uni.showToast({
    title: '图片大小不能超过10MB',
    icon: 'none'
  })
}

// 暴露验证方法
const validate = () => {
  validateTitle()
  validateContent()
  validateContact()

  // 验证旧版本图片上传
  if (props.imageRequired && formData.value.images.length === 0) {
    errors.value.images = '请至少上传一张图片'
  }

  // 验证新版本媒体上传
  if (props.mediaRequired && formData.value.fileList.length === 0) {
    errors.value.fileList = '请至少上传一个文件'
  }

  return isValid.value
}

// 初始化文件列表（支持从外部传入已存在的文件）
const initializeFileList = (fileListData: any[]) => {
  formData.value.fileList = fileListData.map((file: any) => ({
    ...file,
    isExisting: true // 标记为已存在的文件
  }))
}

// 获取处理后的文件数据（区分已存在文件和新文件）
const getProcessedFiles = () => {
  const existingFiles: string[] = []
  const newFiles: any[] = []

  for (const fileItem of formData.value.fileList) {
    if (fileItem.isExisting && fileItem.url) {
      // 已存在的文件，提取相对路径
      let filePath = fileItem.url
      // 移除服务器地址前缀（支持多种格式）
      if (filePath.startsWith('http://localhost:3205/')) {
        filePath = filePath.replace('http://localhost:3205/', '')
      } else if (filePath.startsWith('http://')) {
        // 处理其他 http 地址，提取路径部分
        const url = new URL(filePath)
        filePath = url.pathname.substring(1) // 移除开头的 /
      }
      existingFiles.push(filePath)
    } else if (!fileItem.isExisting) {
      // 新添加的文件，需要上传，添加检测到的文件类型
      const detectedType = detectFileType(fileItem)
      newFiles.push({
        ...fileItem,
        detectedType
      })
    }
  }

  return {
    existingFiles,
    newFiles,
    currentMediaType: currentMediaType.value
  }
}

// 设置媒体类型
const setMediaType = (type: number) => {
  switchMediaType(type)
}

// 清空文件列表
const clearFiles = () => {
  formData.value.fileList = []
  formData.value.images = []
  delete errors.value.fileList
  delete errors.value.images
}

// 获取表单数据用于提交（父组件调用）
const getFormData = () => {
  return {
    title: formData.value.title.trim(),
    content: formData.value.content.trim(),
    contact: formData.value.contact.trim(),
    ...getProcessedFiles()
  }
}

// 暴露给父组件
defineExpose({
  validate,
  formData,
  isValid,
  initializeFileList,
  getProcessedFiles,
  getFormData,
  currentMediaType,
  setMediaType,
  clearFiles
})
</script>

<style scoped lang="scss">
.base-template {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff3b30;
}

.error-text {
  font-size: 12px;
  color: #ff3b30;
}

.help-text {
  font-size: 12px;
  color: #999;
}

.media-type-indicator {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f0f8ff;
  border-radius: 6px;
  border-left: 3px solid #5677fc;
}

.media-type-text {
  font-size: 14px;
  font-weight: 500;
  color: #5677fc;
}

.media-type-hint {
  font-size: 12px;
  color: #666;
}

.upload-section {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.upload-subtitle {
  font-size: 12px;
  color: #666;
  display: block;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  height: 100%;
  border: 2px dashed #e0e0e0;
  border-radius: 12px;
  background-color: #fafafa;
  transition: all 0.3s ease;

  &:hover {
    border-color: #5677fc;
    background: #f0f8ff;
  }
}

.upload-text {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
  font-weight: 500;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  text-align: center;
}
</style>
