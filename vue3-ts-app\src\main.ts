/*
 * @Author: Rock
 * @Date: 2025-04-02 08:30:10
 * @LastEditors: Rock
 * @LastEditTime: 2025-04-02 09:33:49
 * @Description:
 */
import { createSSRApp } from "vue";
import uviewPlus from "uview-plus";
import pinia from "./stores";
import App from "./App.vue";
import { vPermission } from "./directives/permission";
export function createApp() {
  const app = createSSRApp(App);
  app.use(uviewPlus).use(pinia);

  // 注册权限指令
  app.directive('permission', vPermission);

  return {
    app,
  };
}
