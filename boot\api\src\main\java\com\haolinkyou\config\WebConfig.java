package com.haolinkyou.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private JwtInterceptor jwtInterceptor;

    @Autowired
    private OptionalJwtInterceptor optionalJwtInterceptor;

    @Value("${file.upload.dir}")
    private String uploadDir;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins(
                    "http://localhost:3000",
                    "http://127.0.0.1:3000",
                    "http://localhost:5173",
                    "http://127.0.0.1:5173",
                    "http://localhost:8080",
                    "http://127.0.0.1:8080"
                )
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false)
                .maxAge(3600);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 可选JWT拦截器 - 用于既允许游客访问，又需要获取登录用户信息的接口
        registry.addInterceptor(optionalJwtInterceptor)
                .addPathPatterns("/api/posts/listAll"); // 只对listAll接口使用可选认证

        // 必需JWT拦截器 - 用于必须登录才能访问的接口
        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/api/**") // 拦截所有API请求
                .excludePathPatterns(
                    "/api/login/**",           // 排除登录相关接口（包括生成模拟token）
                    "/api/posts/list",         // 排除帖子列表查看（允许游客访问）
                    "/api/posts/listAll",      // 排除帖子完整列表（使用可选认证拦截器）
                    "/api/posts/detail",       // 排除帖子详情查看（允许游客访问）
                    "/api/posts/*/detail",     // 排除帖子详情查看（允许游客访问）
                    "/api/categories/**",      // 排除分类接口（允许游客访问）
                    "/api/comments/**",        // 排除评论查看接口（允许游客访问）
                    "/api/users/list",         // 排除用户列表（如果需要的话）
                    "/api/post-likes/count",   // 排除点赞数查询（允许游客查看）
                    "/api/user-collects/count", // 排除收藏数查询（允许游客查看）
                    "/api/files/test",         // 排除文件上传测试接口
                    "/api/files/testSingle"    // 排除单文件上传测试接口
                );
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源映射，将文件路径映射到文件系统路径
        // 匹配日期格式的路径，如: /2025-07-27/filename.jpg
        registry.addResourceHandler("/20??-??-??/**")
                .addResourceLocations("file:" + uploadDir)
                .setCachePeriod(3600); // 设置缓存时间为1小时

        System.out.println("配置静态资源映射: /20??-??-??/** -> file:" + uploadDir);
    }
}
