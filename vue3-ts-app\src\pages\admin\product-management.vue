<template>
  <view class="product-management-page">
    <!-- 页面头部 -->
    <up-navbar
      title="商品管理"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    >
      <template #right>
        <up-icon name="plus" size="20" color="#1890ff" @click="handleAddProduct"></up-icon>
      </template>
    </up-navbar>

    <!-- 搜索区域 -->
    <view class="search-section" :style="{ marginTop: mainContentPaddingTop }">
      <up-search
        v-model="searchKeyword"
        placeholder="搜索商品名称"
        @search="handleSearch"
        @clear="handleClearSearch"
        :showAction="false"
      ></up-search>
    </view>

    <!-- 商品列表 -->
    <view class="product-list">
      <view 
        class="product-item" 
        v-for="product in productList" 
        :key="product.id"
        @click="handleProductDetail(product)"
      >
        <!-- 商品图片 -->
        <image 
          :src="product.image || '/static/images/default-product.png'"
          class="product-image"
          mode="aspectFill"
        ></image>

        <!-- 商品信息 -->
        <view class="product-info">
          <text class="product-name">{{ product.name }}</text>
          <text class="product-desc">{{ product.description }}</text>
          <view class="product-details">
            <text class="product-points">{{ product.points }}积分</text>
            <text class="product-stock">库存: {{ product.stock }}</text>
          </view>
          <view class="product-meta">
            <text class="create-time">{{ formatTime(product.createdTime) }}</text>
            <view 
              class="status-badge"
              :class="product.status === 'active' ? 'status-active' : 'status-inactive'"
            >
              {{ product.status === 'active' ? '上架' : '下架' }}
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="product-actions">
          <up-icon 
            name="edit-pen" 
            size="18" 
            color="#1890ff" 
            @click.stop="handleEditProduct(product)"
          ></up-icon>
          <up-icon 
            name="trash" 
            size="18" 
            color="#ff4d4f" 
            @click.stop="handleDeleteProduct(product)"
          ></up-icon>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore">
      <up-loadmore 
        :status="loadStatus"
        @loadmore="loadMoreProducts"
      ></up-loadmore>
    </view>

    <!-- 商品详情弹窗 -->
    <up-popup 
      v-model:show="showProductDetailModal" 
      mode="center" 
      :round="10"
      :closeable="true"
      @close="closeProductDetailModal"
    >
      <view class="product-detail-modal" v-if="selectedProduct">
        <view class="modal-header">
          <text class="modal-title">商品详情</text>
          <up-icon name="close" @click="closeProductDetailModal" size="20" color="#999"></up-icon>
        </view>
        <view class="modal-content">
          <image 
            :src="selectedProduct.image || '/static/images/default-product.png'"
            class="detail-image"
            mode="aspectFill"
          ></image>
          <view class="detail-item">
            <text class="detail-label">商品名称:</text>
            <text class="detail-value">{{ selectedProduct.name }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">商品描述:</text>
            <text class="detail-value">{{ selectedProduct.description }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">所需积分:</text>
            <text class="detail-value">{{ selectedProduct.points }}积分</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">库存数量:</text>
            <text class="detail-value">{{ selectedProduct.stock }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">商品状态:</text>
            <text class="detail-value">{{ selectedProduct.status === 'active' ? '上架' : '下架' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">创建时间:</text>
            <text class="detail-value">{{ formatDateTime(selectedProduct.createdTime) }}</text>
          </view>
        </view>
        <view class="modal-footer">
          <up-button 
            text="编辑商品" 
            type="primary" 
            size="small"
            @click="handleEditProduct(selectedProduct)"
          ></up-button>
          <up-button 
            :text="selectedProduct.status === 'active' ? '下架商品' : '上架商品'" 
            :type="selectedProduct.status === 'active' ? 'warning' : 'success'"
            size="small"
            plain
            @click="handleToggleStatus(selectedProduct)"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useSafeArea } from '@/utils/safeArea';
import type { Product, LoadStatus } from '@/types/admin';

const { mainContentPaddingTop } = useSafeArea();

// 搜索关键词
const searchKeyword = ref<string>('');

// 商品列表
const productList = ref<Product[]>([]);

// 分页相关
const hasMore = ref<boolean>(true);
const loadStatus = ref<LoadStatus>('loadmore');

// 弹窗相关
const showProductDetailModal = ref<boolean>(false);
const selectedProduct = ref<Product | null>(null);

// 初始化数据
const initData = async () => {
  loadProductList();
};

// 加载商品列表
const loadProductList = async () => {
  try {
    // TODO: 调用真实API获取商品列表
    // const res = await getProductList({
    //   page: currentPage.value,
    //   pageSize: pageSize.value
    // });
    // productList.value = res.data.records;

    // 暂时清空列表，等待真实API接入
    productList.value = [];
  } catch (error) {
    console.error('加载商品列表失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  }
};

onMounted(() => {
  initData();
});

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 添加商品
const handleAddProduct = () => {
  uni.navigateTo({
    url: '/pages/admin/product-add'
  });
};

// 搜索商品
const handleSearch = (keyword: string) => {
  console.log('搜索商品:', keyword);
  // 这里应该调用实际的搜索API
};

// 清除搜索
const handleClearSearch = () => {
  searchKeyword.value = '';
  loadProductList();
};

// 加载更多商品
const loadMoreProducts = () => {
  loadStatus.value = 'loading';
  // 模拟加载更多
  setTimeout(() => {
    loadStatus.value = 'loadmore';
    hasMore.value = false; // 模拟没有更多数据
  }, 1000);
};

// 查看商品详情
const handleProductDetail = (product: Product) => {
  selectedProduct.value = product;
  showProductDetailModal.value = true;
};

// 关闭商品详情弹窗
const closeProductDetailModal = () => {
  showProductDetailModal.value = false;
  selectedProduct.value = null;
};

// 编辑商品
const handleEditProduct = (product: Product) => {
  uni.navigateTo({
    url: `/pages/admin/product-add?id=${product.id}`
  });
  closeProductDetailModal();
};

// 删除商品
const handleDeleteProduct = (product: Product) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除商品"${product.name}"吗？此操作不可恢复！`,
    success: (res) => {
      if (res.confirm) {
        // 这里应该调用实际的删除API
        const index = productList.value.findIndex(p => p.id === product.id);
        if (index > -1) {
          productList.value.splice(index, 1);
        }
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
    }
  });
};

// 切换商品状态
const handleToggleStatus = (product: Product) => {
  const action = product.status === 'active' ? '下架' : '上架';
  uni.showModal({
    title: `确认${action}`,
    content: `确定要${action}商品"${product.name}"吗？`,
    success: (res) => {
      if (res.confirm) {
        // 这里应该调用实际的API
        product.status = product.status === 'active' ? 'inactive' : 'active';
        uni.showToast({
          title: `${action}成功`,
          icon: 'success'
        });
        closeProductDetailModal();
      }
    }
  });
};

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  return date.toLocaleDateString();
};

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '未知';
  return dateStr;
};
</script>

<style lang="scss">
.product-management-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  // 搜索区域
  .search-section {
    background: #fff;
    padding: 15px 20px;
    margin-bottom: 10px;
  }

  // 商品列表
  .product-list {
    padding: 0 20px;

    .product-item {
      background: #fff;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      display: flex;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }

      .product-image {
        width: 80px;
        height: 80px;
        border-radius: 6px;
        margin-right: 15px;
        flex-shrink: 0;
      }

      .product-info {
        flex: 1;

        .product-name {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          display: block;
          margin-bottom: 6px;
        }

        .product-desc {
          font-size: 14px;
          color: #666;
          display: block;
          margin-bottom: 8px;
          line-height: 1.3;
        }

        .product-details {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .product-points {
            font-size: 16px;
            font-weight: bold;
            color: #ff6b35;
            margin-right: 15px;
          }

          .product-stock {
            font-size: 12px;
            color: #999;
          }
        }

        .product-meta {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .create-time {
            font-size: 12px;
            color: #999;
          }

          .status-badge {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;

            &.status-active {
              background: #f6ffed;
              color: #52c41a;
            }

            &.status-inactive {
              background: #fff1f0;
              color: #ff4d4f;
            }
          }
        }
      }

      .product-actions {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 10px;
        margin-left: 10px;
      }
    }
  }

  // 加载更多
  .load-more {
    padding: 20px;
  }

  // 商品详情弹窗
  .product-detail-modal {
    width: 350px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;

      .modal-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }

    .modal-content {
      padding: 20px;
      max-height: 400px;
      overflow-y: auto;

      .detail-image {
        width: 100%;
        height: 150px;
        border-radius: 6px;
        margin-bottom: 15px;
      }

      .detail-item {
        display: flex;
        margin-bottom: 12px;

        .detail-label {
          font-size: 14px;
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .detail-value {
          font-size: 14px;
          color: #333;
          flex: 1;
        }
      }
    }

    .modal-footer {
      display: flex;
      gap: 10px;
      padding: 0 20px 20px;

      :deep(.u-button) {
        flex: 1;
        height: 36px;
      }
    }
  }
}
</style>
