package com.haolinkyou.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haolinkyou.entity.TemplateFields;

import java.util.List;

/**
 * 模板字段服务接口
 */
public interface ITemplateFieldsService extends IService<TemplateFields> {

    /**
     * 根据模板ID获取字段列表
     * @param templateId 模板ID
     * @return 字段列表
     */
    List<TemplateFields> getFieldsByTemplateId(Long templateId);

    /**
     * 批量保存模板字段
     * @param templateId 模板ID
     * @param fields 字段列表
     * @return 是否成功
     */
    boolean saveTemplateFields(Long templateId, List<TemplateFields> fields);

    /**
     * 删除模板的所有字段
     * @param templateId 模板ID
     * @return 是否成功
     */
    boolean deleteFieldsByTemplateId(Long templateId);

    /**
     * 创建默认字段
     * @param templateId 模板ID
     * @param categoryId 分类ID
     * @return 是否成功
     */
    boolean createDefaultFields(Long templateId, Long categoryId);
}
