// UI组件相关类型定义

// 加载状态
export type LoadStatus = 'loadmore' | 'loading' | 'nomore';

// 按钮类型
export type ButtonType = 'primary' | 'success' | 'info' | 'warning' | 'error';

// 按钮尺寸
export type ButtonSize = 'large' | 'normal' | 'small' | 'mini';

// 标签类型
export type TagType = 'primary' | 'success' | 'info' | 'warning' | 'error';

// 模态框配置
export interface ModalConfig {
  title?: string;
  content?: string;
  showCancel?: boolean;
  cancelText?: string;
  confirmText?: string;
  confirmColor?: string;
}

// Toast配置
export interface ToastConfig {
  title: string;
  icon?: 'success' | 'error' | 'loading' | 'none';
  duration?: number;
  mask?: boolean;
}

// 选择器选项
export interface PickerOption {
  label: string;
  value: any;
  disabled?: boolean;
  children?: PickerOption[];
}

// 表单验证规则
export interface FormRule {
  required?: boolean;
  message?: string;
  pattern?: RegExp;
  min?: number;
  max?: number;
  validator?: (value: any) => boolean | string;
}

// 表单字段配置
export interface FormField {
  key: string;
  label: string;
  type: 'input' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'upload';
  placeholder?: string;
  rules?: FormRule[];
  options?: PickerOption[];
  disabled?: boolean;
  readonly?: boolean;
}

// 列表项配置
export interface ListItem {
  id: string | number;
  title: string;
  subtitle?: string;
  description?: string;
  icon?: string;
  image?: string;
  badge?: string | number;
  disabled?: boolean;
  clickable?: boolean;
}

// 标签页配置
export interface TabItem {
  name: string;
  title: string;
  badge?: string | number;
  disabled?: boolean;
}

// 导航栏配置
export interface NavBarConfig {
  title?: string;
  leftText?: string;
  rightText?: string;
  leftIcon?: string;
  rightIcon?: string;
  fixed?: boolean;
  placeholder?: boolean;
  safeAreaInsetTop?: boolean;
}

// 搜索配置
export interface SearchConfig {
  placeholder?: string;
  maxLength?: number;
  clearable?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  showAction?: boolean;
  actionText?: string;
}

// 分页配置
export interface PaginationConfig {
  current: number;
  pageSize: number;
  total: number;
  showTotal?: boolean;
  showPageSize?: boolean;
  pageSizeOptions?: number[];
}

// 上拉加载配置
export interface PullUpConfig {
  threshold?: number;
  loadingText?: string;
  noMoreText?: string;
  errorText?: string;
}

// 下拉刷新配置
export interface PullDownConfig {
  threshold?: number;
  refreshingText?: string;
  pullingText?: string;
  loosingText?: string;
}

// 空状态配置
export interface EmptyConfig {
  image?: string;
  text?: string;
  description?: string;
  buttonText?: string;
}

// 骨架屏配置
export interface SkeletonConfig {
  rows?: number;
  rowsHeight?: number | number[];
  title?: boolean;
  titleHeight?: number;
  avatar?: boolean;
  avatarSize?: number;
  avatarShape?: 'circle' | 'square';
  loading?: boolean;
  animate?: boolean;
}

// 轮播图配置
export interface SwiperConfig {
  autoplay?: boolean;
  interval?: number;
  duration?: number;
  circular?: boolean;
  vertical?: boolean;
  indicatorDots?: boolean;
  indicatorColor?: string;
  indicatorActiveColor?: string;
}

// 图片预览配置
export interface ImagePreviewConfig {
  urls: string[];
  current?: number;
  showIndex?: boolean;
  closeOnClickOverlay?: boolean;
  showMenuByLongpress?: boolean;
}

// 操作菜单项
export interface ActionSheetItem {
  name: string;
  value?: any;
  color?: string;
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
}

// 操作菜单配置
export interface ActionSheetConfig {
  actions: ActionSheetItem[];
  title?: string;
  description?: string;
  cancelText?: string;
  closeOnClickAction?: boolean;
  closeOnClickOverlay?: boolean;
  safeAreaInsetBottom?: boolean;
}
