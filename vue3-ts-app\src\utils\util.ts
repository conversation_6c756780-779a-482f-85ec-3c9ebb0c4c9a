/*
 * @Author: Rock
 * @Date: 2025-04-20 19:29:34
 * @LastEditors: Rock
 * @LastEditTime: 2025-06-07 16:07:10
 * @Description:
 */
/**
 * 赋值响应式对象
 * @param originObj 原始对象
 * @param dataObj 数据对象
 * 注意不要使用该方法清空对象，清空整个对象请使用clearReactive
 */
export const setReactive = (
  originObj: Record<string, any> | {},
  dataObj: Record<string, any>
) => {
  const keys = Object.keys(dataObj || {});
  const oldKeys = Object.keys(originObj || {});
  if (keys.length > 0) {
    keys.forEach((key) => {
      const newVal = dataObj[key];
      if (
        typeof newVal === "object" &&
        newVal !== null &&
        !Array.isArray(newVal)
      ) {
        const obj = reactive({});

        setReactive(obj, newVal);
        (originObj as Record<string, any>)[key] = obj;
      } else {
        (originObj as Record<string, any>)[key] = dataObj[key];
      }
      // ;(originObj as Record<string, any>)[key] = dataObj[key]
    });

    oldKeys.forEach((oKey) => {
      if (!keys.includes(oKey)) delete (originObj as Record<string, any>)[oKey];
    });
  } else {
    originObj = reactive(dataObj);
  }
};

// 处理文件列表，将相对路径转换为完整URL
export const setFileList = (fileList: string | null): string[] => {
  if (!fileList || fileList.trim() === '') {
    return [];
  }

  const tempList: string[] = fileList.split(",").filter(url => url.trim() !== '');
  const fileListData: string[] = tempList.map((url: string) => {
    return `http://localhost:3205/${url.trim()}`;
  });

  return fileListData;
};