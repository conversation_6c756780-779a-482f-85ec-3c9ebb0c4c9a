<template>
  <!-- 权限验证加载中 -->
  <view v-if="permissionLoading" class="permission-loading">
    <view class="loading-content">
      <up-loading-icon mode="circle" size="24" :color="loadingColor"></up-loading-icon>
      <text class="loading-text">{{ loadingText }}</text>
    </view>
  </view>
  
  <!-- 权限验证失败 -->
  <view v-else-if="!hasPagePermission && showErrorPage" class="permission-denied">
    <view class="denied-content">
      <up-icon name="lock" :color="errorColor" size="48"></up-icon>
      <text class="denied-title">{{ errorTitle }}</text>
      <text class="denied-message">{{ errorMessage }}</text>
      <up-button 
        v-if="showRetryButton" 
        type="primary" 
        size="small" 
        @click="handleRetry"
        :loading="retrying"
      >
        {{ retryText }}
      </up-button>
    </view>
  </view>
  
  <!-- 页面内容 -->
  <view v-else-if="hasPagePermission" class="page-content">
    <slot></slot>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { usePagePermission } from '@/composables/usePagePermission'

interface Props {
  // 页面路径
  pagePath: string
  // 加载文本
  loadingText?: string
  // 加载颜色
  loadingColor?: string
  // 错误标题
  errorTitle?: string
  // 错误消息
  errorMessage?: string
  // 错误颜色
  errorColor?: string
  // 是否显示错误页面（false时直接不渲染任何内容）
  showErrorPage?: boolean
  // 是否显示重试按钮
  showRetryButton?: boolean
  // 重试按钮文本
  retryText?: string
  // 自定义初始化函数
  initCallback?: () => void | Promise<void>
}

const props = withDefaults(defineProps<Props>(), {
  loadingText: '权限验证中...',
  loadingColor: '#1890ff',
  errorTitle: '权限不足',
  errorMessage: '您没有访问此页面的权限',
  errorColor: '#ccc',
  showErrorPage: false,
  showRetryButton: true,
  retryText: '重试'
})

// 使用权限验证组合式函数
const {
  permissionLoading,
  hasPagePermission,
  permissionError,
  recheckPermission
} = usePagePermission(props.pagePath, props.initCallback)

// 重试状态
const retrying = ref(false)

// 处理重试
const handleRetry = async () => {
  retrying.value = true
  try {
    await recheckPermission()
  } finally {
    retrying.value = false
  }
}

// 暴露状态给父组件
defineExpose({
  permissionLoading,
  hasPagePermission,
  permissionError,
  recheckPermission
})

// 发出事件
const emit = defineEmits<{
  permissionGranted: []
  permissionDenied: [error: string | null]
  permissionLoading: [loading: boolean]
}>()

// 监听权限状态变化并发出事件
watch(permissionLoading, (loading) => {
  emit('permissionLoading', loading)
})

watch(hasPagePermission, (hasPermission) => {
  if (hasPermission) {
    emit('permissionGranted')
  } else {
    emit('permissionDenied', permissionError.value)
  }
})
</script>

<style scoped lang="scss">
.permission-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  
  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    
    .loading-text {
      font-size: 14px;
      color: #666;
    }
  }
}

.permission-denied {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  
  .denied-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 40px 20px;
    text-align: center;
    
    .denied-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
    
    .denied-message {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
    }
  }
}

.page-content {
  width: 100%;
  height: 100%;
}
</style>
