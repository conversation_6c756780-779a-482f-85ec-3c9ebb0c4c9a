package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.entity.PostLikes;
import com.haolinkyou.entity.Posts;
import com.haolinkyou.mapper.PostLikesMapper;
import com.haolinkyou.service.IPostLikesService;
import com.haolinkyou.service.IPostsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 帖子点赞服务实现类
 */
@Service
public class PostLikesServiceImpl extends ServiceImpl<PostLikesMapper, PostLikes> implements IPostLikesService {
    
    @Autowired
    private PostLikesMapper postLikesMapper;
    
    @Autowired
    private IPostsService postsService;
    
    @Override
    @Transactional
    public boolean toggleLike(Long postId, Long userId) {
        if (postId == null || userId == null) {
            throw new RuntimeException("帖子ID和用户ID不能为空");
        }

        // 检查当前点赞状态
        int existingCount = postLikesMapper.checkUserLiked(postId, userId);
        boolean currentlyLiked = existingCount > 0;

        if (currentlyLiked) {
            // 当前已点赞，执行取消点赞
            int affected = postLikesMapper.removeLike(postId, userId);
            if (affected > 0) {
                // 更新帖子点赞数
                updatePostLikeCount(postId);
                return false; // 返回false表示已取消点赞
            } else {
                throw new RuntimeException("取消点赞失败");
            }
        } else {
            // 当前未点赞，执行点赞操作
            int affected = postLikesMapper.addLike(postId, userId);
            if (affected > 0) {
                // 更新帖子点赞数
                updatePostLikeCount(postId);
                return true; // 返回true表示已点赞
            } else {
                throw new RuntimeException("点赞失败");
            }
        }
    }
    
    @Override
    public boolean isUserLiked(Long postId, Long userId) {
        if (postId == null || userId == null) {
            return false;
        }
        return postLikesMapper.checkUserLiked(postId, userId) > 0;
    }
    
    @Override
    public int getPostLikeCount(Long postId) {
        if (postId == null) {
            return 0;
        }
        return postLikesMapper.getPostLikeCount(postId);
    }
    
    /**
     * 更新帖子的点赞数
     * @param postId 帖子ID
     */
    private void updatePostLikeCount(Long postId) {
        int likeCount = getPostLikeCount(postId);

        UpdateWrapper<Posts> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", postId)
                    .set("like_count", likeCount);

        postsService.update(updateWrapper);
    }
}
