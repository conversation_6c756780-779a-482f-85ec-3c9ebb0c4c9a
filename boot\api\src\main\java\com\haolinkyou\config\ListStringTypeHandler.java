package com.haolinkyou.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 自定义类型处理器，用于处理 List<String> 与 JSON 字段的转换
 */
@MappedTypes({List.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class ListStringTypeHandler extends BaseTypeHandler<List<String>> {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = objectMapper.writeValueAsString(parameter);
            System.out.println("保存到数据库的JSON: " + json);
            ps.setString(i, json);
        } catch (JsonProcessingException e) {
            System.err.println("序列化List<String>失败: " + e.getMessage());
            throw new SQLException("序列化List<String>失败", e);
        }
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        System.out.println("从数据库读取的JSON (" + columnName + "): " + json);
        return parseJson(json);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        System.out.println("从数据库读取的JSON (索引" + columnIndex + "): " + json);
        return parseJson(json);
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        System.out.println("从存储过程读取的JSON: " + json);
        return parseJson(json);
    }

    private List<String> parseJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            System.out.println("JSON为空，返回null");
            return null;
        }
        try {
            List<String> result = objectMapper.readValue(json, new TypeReference<List<String>>() {});
            System.out.println("反序列化成功，结果: " + result);
            return result;
        } catch (JsonProcessingException e) {
            System.err.println("反序列化List<String>失败: " + e.getMessage());
            System.err.println("原始JSON: " + json);
            return null;
        }
    }
}
