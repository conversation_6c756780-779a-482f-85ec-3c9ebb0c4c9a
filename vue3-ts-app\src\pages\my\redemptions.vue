<template>
  <view class="redemptions-page">
    <!-- 页面头部 -->
    <view class="page-header" :style="{ paddingTop: pageHeaderPaddingTop }">
      <view class="header-content">
        <up-icon name="arrow-left" size="20" color="#333" @click="goBack"></up-icon>
        <text class="page-title">兑换记录</text>
        <view class="header-placeholder"></view>
      </view>
    </view>

    <!-- 状态筛选 -->
    <view class="filter-tabs">
      <view 
        v-for="(tab, index) in statusTabs" 
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <view v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</view>
      </view>
    </view>

    <!-- 兑换记录列表 -->
    <view class="records-section">
      <view v-if="redemptionList.length === 0 && !loading" class="empty-state">
        <up-empty
          mode="data"
          text="暂无兑换记录"
          textSize="14"
        ></up-empty>
      </view>

      <view class="records-list">
        <view v-for="record in redemptionList" :key="record.id" class="record-item">
          <view class="record-header">
            <text class="record-date">{{ formatDate(record.createdTime) }}</text>
            <view class="record-status" :class="getStatusClass(record.status)">
              <text class="status-text">{{ getStatusText(record.status) }}</text>
            </view>
          </view>
          
          <view class="record-content">
            <view class="product-info">
              <up-image
                :src="record.productImage || '/static/images/default-product.png'"
                width="60px"
                height="60px"
                mode="aspectFill"
                :fade="true"
              ></up-image>
              <view class="product-details">
                <text class="product-name">{{ record.productName }}</text>
                <text class="product-quantity">数量：{{ record.quantity }}</text>
                <text class="product-points">{{ record.pointsUsed }}积分</text>
              </view>
            </view>
            
            <view class="record-actions">
              <up-button
                v-if="record.status === 0"
                text="取消兑换"
                type="info"
                size="mini"
                @click="cancelRedemption(record)"
              ></up-button>
              <up-button
                text="查看详情"
                type="primary"
                size="mini"
                @click="viewDetail(record)"
              ></up-button>
            </view>
          </view>
          
          <view v-if="record.shippingAddress" class="shipping-info">
            <view class="info-row">
              <up-icon name="map" size="14" color="#999"></up-icon>
              <text class="info-text">{{ record.shippingAddress }}</text>
            </view>
            <view v-if="record.contactPhone" class="info-row">
              <up-icon name="phone" size="14" color="#999"></up-icon>
              <text class="info-text">{{ record.contactPhone }}</text>
            </view>
          </view>
          
          <view v-if="record.remark" class="record-remark">
            <text class="remark-label">备注：</text>
            <text class="remark-text">{{ record.remark }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <up-loadmore
      :status="loadStatus"
      :loading-text="loadingText"
      :loadmore-text="loadmoreText"
      :nomore-text="nomoreText"
      @loadmore="loadMore"
    />

    <!-- 详情弹窗 -->
    <up-popup
      v-model:show="showDetailModal"
      mode="center"
      :round="10"
      :closeable="true"
    >
      <view class="detail-modal" v-if="selectedRecord">
        <view class="modal-header">
          <text class="modal-title">兑换详情</text>
        </view>
        
        <view class="modal-content">
          <view class="detail-item">
            <text class="detail-label">商品名称</text>
            <text class="detail-value">{{ selectedRecord.productName }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">兑换数量</text>
            <text class="detail-value">{{ selectedRecord.quantity }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">消耗积分</text>
            <text class="detail-value">{{ selectedRecord.pointsUsed }}积分</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">兑换时间</text>
            <text class="detail-value">{{ formatDateTime(selectedRecord.createdTime) }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">订单状态</text>
            <text class="detail-value" :style="{ color: getStatusColor(selectedRecord.status) }">
              {{ getStatusText(selectedRecord.status) }}
            </text>
          </view>
          
          <view v-if="selectedRecord.shippingAddress" class="detail-item">
            <text class="detail-label">收货地址</text>
            <text class="detail-value">{{ selectedRecord.shippingAddress }}</text>
          </view>
          
          <view v-if="selectedRecord.contactPhone" class="detail-item">
            <text class="detail-label">联系电话</text>
            <text class="detail-value">{{ selectedRecord.contactPhone }}</text>
          </view>
          
          <view v-if="selectedRecord.remark" class="detail-item">
            <text class="detail-label">备注信息</text>
            <text class="detail-value">{{ selectedRecord.remark }}</text>
          </view>
          
          <view v-if="selectedRecord.adminRemark" class="detail-item">
            <text class="detail-label">处理备注</text>
            <text class="detail-value">{{ selectedRecord.adminRemark }}</text>
          </view>
        </view>
        
        <view class="modal-actions">
          <up-button
            text="关闭"
            type="primary"
            size="normal"
            @click="closeDetailModal"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useMemberStore } from '@/stores'
import { get, post } from '@/utils/http'
import { useSafeArea } from '@/utils/safeArea'

const { pageHeaderPaddingTop } = useSafeArea()
const memberStore = useMemberStore()

// 状态筛选
const statusTabs = ref([
  { name: '全部', value: null, count: 0 },
  { name: '待发货', value: 0, count: 0 },
  { name: '已发货', value: 1, count: 0 },
  { name: '已完成', value: 2, count: 0 },
  { name: '已取消', value: -1, count: 0 }
])
const currentTab = ref(0)

// 兑换记录
const redemptionList = ref<any[]>([])
const loading = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)
const loadStatus = ref<'loading' | 'loadmore' | 'nomore'>('loadmore')
const loadingText = ref('正在加载...')
const loadmoreText = ref('上拉加载更多')
const nomoreText = ref('已经到底了')

// 详情弹窗
const showDetailModal = ref(false)
const selectedRecord = ref<any>(null)

// 获取兑换记录
const fetchRedemptions = async (isRefresh = false) => {
  if (isRefresh) {
    currentPage.value = 1
    hasMore.value = true
    loadStatus.value = 'loading'
  }

  loading.value = true

  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      status: statusTabs.value[currentTab.value].value
    }

    const res = await get('/products/redemptions', params)

    if (res.success) {
      const newRecords = res.data.records || []

      if (isRefresh) {
        redemptionList.value = newRecords
      } else {
        redemptionList.value.push(...newRecords)
      }

      // 判断是否还有更多数据
      if (newRecords.length < pageSize.value) {
        hasMore.value = false
        loadStatus.value = 'nomore'
      } else {
        loadStatus.value = 'loadmore'
      }
    }
  } catch (error) {
    console.error('获取兑换记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 切换状态标签
const switchTab = (index: number) => {
  currentTab.value = index
  fetchRedemptions(true)
}

// 加载更多
const loadMore = () => {
  if (!hasMore.value || loading.value) return
  
  currentPage.value++
  fetchRedemptions()
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '待发货',
    1: '已发货',
    2: '已完成',
    '-1': '已取消'
  }
  return statusMap[status] || '未知'
}

// 获取状态样式类
const getStatusClass = (status: number) => {
  const classMap: Record<number, string> = {
    0: 'pending',
    1: 'shipped',
    2: 'completed',
    '-1': 'cancelled'
  }
  return classMap[status] || ''
}

// 获取状态颜色
const getStatusColor = (status: number) => {
  const colorMap: Record<number, string> = {
    0: '#ff9500',
    1: '#007aff',
    2: '#34c759',
    '-1': '#ff3b30'
  }
  return colorMap[status] || '#666'
}

// 格式化日期
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 86400000)
  
  if (date >= today) {
    return `今天 ${date.toTimeString().slice(0, 5)}`
  } else if (date >= yesterday) {
    return `昨天 ${date.toTimeString().slice(0, 5)}`
  } else {
    return `${date.getMonth() + 1}月${date.getDate()}日`
  }
}

// 格式化完整日期时间
const formatDateTime = (dateStr: string) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 取消兑换
const cancelRedemption = async (record: any) => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个兑换订单吗？积分将会退还。',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await post(`/products/redemptions/${record.id}/cancel`, {})
          
          if (response.success) {
            uni.showToast({
              title: '取消成功',
              icon: 'success'
            })
            fetchRedemptions(true)
          } else {
            uni.showToast({
              title: response.message || '取消失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('取消兑换失败:', error)
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 查看详情
const viewDetail = (record: any) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false
  selectedRecord.value = null
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

onMounted(() => {
  fetchRedemptions(true)
})
</script>

<style scoped lang="scss">
.redemptions-page {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  border-bottom: 1px solid #f0f0f0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-placeholder {
  width: 20px;
}

.filter-tabs {
  background-color: white;
  display: flex;
  padding: 12px 16px;
  gap: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 0;
  
  &.active {
    .tab-text {
      color: #5677fc;
      font-weight: 500;
    }
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #5677fc;
      border-radius: 1px;
    }
  }
}

.tab-text {
  font-size: 14px;
  color: #666;
}

.tab-badge {
  background-color: #ff3b30;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.records-section {
  padding: 16px;
}

.record-item {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.record-date {
  font-size: 12px;
  color: #999;
}

.record-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  
  &.pending {
    background-color: #fff3cd;
    color: #856404;
  }
  
  &.shipped {
    background-color: #cce5ff;
    color: #004085;
  }
  
  &.completed {
    background-color: #d4edda;
    color: #155724;
  }
  
  &.cancelled {
    background-color: #f8d7da;
    color: #721c24;
  }
}

.status-text {
  font-size: 10px;
}

.record-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.product-info {
  display: flex;
  gap: 12px;
  flex: 1;
}

.product-details {
  flex: 1;
}

.product-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4px;
}

.product-quantity {
  font-size: 12px;
  color: #666;
  display: block;
  margin-bottom: 4px;
}

.product-points {
  font-size: 12px;
  color: #ff6b35;
  font-weight: 600;
}

.record-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.shipping-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-text {
  font-size: 12px;
  color: #666;
  flex: 1;
}

.record-remark {
  background-color: #f0f0f0;
  border-radius: 8px;
  padding: 8px;
}

.remark-label {
  font-size: 12px;
  color: #999;
}

.remark-text {
  font-size: 12px;
  color: #666;
}

.empty-state {
  padding: 40px 20px;
}

.detail-modal {
  width: 320px;
  background: white;
  border-radius: 10px;
  overflow: hidden;
}

.modal-header {
  padding: 20px 20px 0;
  text-align: center;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.modal-content {
  padding: 20px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 14px;
  color: #666;
  min-width: 80px;
}

.detail-value {
  font-size: 14px;
  color: #333;
  flex: 1;
  text-align: right;
}

.modal-actions {
  padding: 0 20px 20px;
}
</style>
