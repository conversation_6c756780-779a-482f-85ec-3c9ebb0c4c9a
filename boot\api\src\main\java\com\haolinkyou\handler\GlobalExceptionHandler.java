package com.haolinkyou.handler;

import com.haolinkyou.common.result.Result;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MultipartException;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MultipartException.class)
    public Result<?> handleMultipartException(MultipartException ex) {
        System.out.println("Multipart异常: " + ex.getMessage());
        ex.printStackTrace();
        return Result.error("文件上传异常：" + ex.getMessage());
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public Result<?> handleIllegalArgument(IllegalArgumentException ex) {
        return Result.error("参数错误：" + ex.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public Result<?> handleException(Exception ex) {
        System.out.println("全局异常处理: " + ex.getClass().getSimpleName() + " - " + ex.getMessage());
        ex.printStackTrace();
        return Result.error("系统发生异常：" + ex.getMessage());
    }
}
