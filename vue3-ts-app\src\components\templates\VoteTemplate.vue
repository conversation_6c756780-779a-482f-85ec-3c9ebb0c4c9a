<template>
  <view class="vote-template">
    <!-- 基础信息 -->
    <BaseTemplate
      ref="baseTemplateRef"
      :titleRequired="true"
      :titlePlaceholder="`请填写投票表决主题`"
      :contentPlaceholder="`请详细说明投票表决的发起背景和目的，以便大家更好的参与`"
      :showContact="false"
      :showMediaUpload="false"
      :initialData="formData"
      @update:data="handleBaseDataChange"
      @update:valid="handleBaseValidChange"
    />
    
    <!-- 投票选项 -->
    <view class="options-section">
      <view class="section-title">投票选项</view>
      <view class="form-item">
        <view class="form-label">
          <text>选项设置</text>
          <text class="required">*</text>
        </view>
        
        <view class="options-list">
          <view 
            v-for="(option, index) in formData.options" 
            :key="index"
            class="option-item"
          >
            <view class="option-input-wrapper">
              <up-input
                v-model="option.text"
                :placeholder="`选项 ${index + 1}`"
                @input="handleOptionChange"
                @blur="validateOptions"
              />
              <view
                v-if="formData.options.length > 2"
                class="delete-option-btn"
                @click="removeOption(index)"
              >
                <up-icon name="close-circle-fill" color="#ff4757" size="20"></up-icon>
              </view>
            </view>
          </view>
        </view>
        
        <view class="option-actions">
          <up-button
            v-if="formData.options.length < 10"
            type="primary"
            size="small"
            @click="addOption"
          >
            添加选项
          </up-button>
        </view>
        
        <view v-if="errors.options" class="error-text">{{ errors.options }}</view>
        <view class="help-text">至少需要2个选项，最多10个选项</view>
      </view>
    </view>
    
    <!-- 投票设置 -->
    <view class="vote-settings">
      <view class="section-title">投票设置</view>
      
      <view class="form-item">
        <view class="form-label">
          <text>选择方式</text>
        </view>
        <up-radio-group v-model="formData.multiSelect" @change="handleMultiSelectChange">
          <up-radio
            name="false"
            label="单选（只能选择一个选项）"
            :customStyle="{ marginBottom: '8px' }"
          />
          <up-radio
            name="true"
            label="多选（可以选择多个选项）"
            :customStyle="{ marginBottom: '8px' }"
          />
        </up-radio-group>
      </view>

      <view class="form-item">
        <view class="form-label">
          <text>是否匿名</text>
        </view>
        <up-radio-group v-model="formData.anonymous" @change="handleAnonymousChange">
          <up-radio
            name="true"
            label="匿名投票（不显示投票人）"
            :customStyle="{ marginBottom: '8px' }"
          />
          <up-radio
            name="false"
            label="实名投票（显示投票人）"
            :customStyle="{ marginBottom: '8px' }"
          />
        </up-radio-group>
      </view>
    </view>
    
    <!-- 截止时间 -->
    <view class="form-item">
      <view class="form-label">
        <text>截止时间</text>
      </view>
      <up-datetime-picker
        hasInput
        v-model:show="showDeadlinePicker"
        v-model="deadlineValue"
        mode="datetime"
        :minDate="minDate"
        placeholder="选择截止时间（可选）"
        format="YYYY-MM-DD HH:mm"
        @confirm="handleDeadlineConfirm"
        @cancel="() => showDeadlinePicker = false"
      />
      <view class="help-text">不填则长期有效</view>
    </view>
    
    <!-- 仅对谁可见 -->
    <view class="form-item">
      <view class="form-label">
        <text>仅对谁可见</text>
      </view>
      <up-checkbox-group v-model="formData.targetAudience" @change="handleTargetChange">
        <up-checkbox
          v-for="option in targetOptions"
          :key="option.value"
          :name="option.value"
          :label="option.label"
          :customStyle="{ marginBottom: '8px' }"
        />
      </up-checkbox-group>
      <view v-if="errors.targetAudience" class="error-text">{{ errors.targetAudience }}</view>
      <view class="help-text">不选择则所有用户可参与，选择特定对象则仅其可参与</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BaseTemplate from './BaseTemplate.vue'
import { getTargetAudienceOptionsAPI, type TargetAudienceOption } from '@/services/roleService'

// 选项接口
interface VoteOption {
  text: string
}

// 表单数据
const formData = ref({
  title: '',
  content: '',
  images: [] as any[], // 兼容旧版本
  fileList: [] as any[], // 新版本文件列表
  options: [
    { text: '' },
    { text: '' }
  ] as VoteOption[],
  multiSelect: 'false',
  anonymous: 'true',
  deadline: '',
  targetAudience: [] as string[]
})

// 基础模板引用
const baseTemplateRef = ref()

// 验证错误
const errors = ref<any>({})

// 基础模板验证状态
const baseValid = ref(false)

// 目标对象选项（从接口动态获取）
const targetOptions = ref<TargetAudienceOption[]>([])

// 日期选择器
const showDeadlinePicker = ref(false)
const deadlineValue = ref<number>(Date.now() + 7 * 24 * 60 * 60 * 1000) // 默认一周后
const minDate = Date.now()

// 确保时间值始终是有效的时间戳
const ensureValidTimestamp = (value: any): number => {
  if (typeof value === 'number' && !isNaN(value) && value > 0) {
    return value
  }
  if (typeof value === 'string' && value) {
    const timestamp = new Date(value).getTime()
    if (!isNaN(timestamp)) {
      return timestamp
    }
  }
  return Date.now()
}

// 加载目标对象选项
const loadTargetOptions = async () => {
  try {
    const options = await getTargetAudienceOptionsAPI()
    targetOptions.value = options
  } catch (error) {
    console.error('加载目标对象选项失败:', error)
    targetOptions.value = []
  }
}

// 组件挂载时加载目标对象选项
onMounted(() => {
  loadTargetOptions()
  
  // 确保时间值是有效的
  deadlineValue.value = ensureValidTimestamp(deadlineValue.value)
})

// 监听时间值变化，确保始终是有效的时间戳
watch(deadlineValue, (newValue) => {
  if (typeof newValue !== 'number' || isNaN(newValue) || newValue <= 0) {
    deadlineValue.value = Date.now() + 7 * 24 * 60 * 60 * 1000
  }
})

// 处理截止时间确认
const handleDeadlineConfirm = (event: any) => {
  try {
    const timestamp = ensureValidTimestamp(event?.value)
    deadlineValue.value = timestamp
    
    // 格式化显示时间
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    const dateTimeStr = `${year}-${month}-${day} ${hour}:${minute}`
    
    formData.value.deadline = dateTimeStr
    showDeadlinePicker.value = false
  } catch (error) {
    console.error('截止时间确认处理失败:', error)
    showDeadlinePicker.value = false
  }
}

// 处理目标对象变化
const handleTargetChange = (selectedValues: string[]) => {
  formData.value.targetAudience = selectedValues
  delete errors.value.targetAudience
}

// 处理基础模板数据变化
const handleBaseDataChange = (data: Record<string, any>) => {
  Object.assign(formData.value, data)
  
  // 如果content为空，自动生成基于选项的内容
  if (!data.content || data.content.trim() === '') {
    generateContentFromOptions()
  }
}

// 根据选项自动生成content内容
const generateContentFromOptions = () => {
  if (formData.value.options && formData.value.options.length > 0) {
    const optionTexts = formData.value.options
      .filter(opt => opt.text && opt.text.trim())
      .map((opt, index) => `${index + 1}. ${opt.text}`)
      .join('\n')
    
    if (optionTexts) {
      formData.value.content = `投票选项：\n${optionTexts}`
    }
  }
}

// 处理基础模板验证状态变化
const handleBaseValidChange = (valid: boolean) => {
  baseValid.value = valid
}

// 添加选项
const addOption = () => {
  if (formData.value.options.length < 10) {
    formData.value.options.push({ text: '' })
  }
}

// 删除选项
const removeOption = (index: number) => {
  if (formData.value.options.length > 2) {
    formData.value.options.splice(index, 1)
    validateOptions()
  }
}

// 处理选项变化
const handleOptionChange = () => {
  validateOptions()
}

// 处理多选变化
const handleMultiSelectChange = () => {
  // 可以在这里添加额外的逻辑
}

// 处理匿名变化
const handleAnonymousChange = () => {
  // 可以在这里添加额外的逻辑
}

// 验证选项
const validateOptions = () => {
  const validOptions = formData.value.options.filter(option => option.text.trim())
  
  if (validOptions.length < 2) {
    errors.value.options = '至少需要2个有效选项'
    return false
  } else {
    delete errors.value.options
    // 选项变化时，重新生成content
    generateContentFromOptions()
    return true
  }
}

// 验证整个表单
const validate = () => {
  let isValid = true
  
  // 验证基础模板
  if (baseTemplateRef.value) {
    const baseData = baseTemplateRef.value.getFormData()
    Object.assign(formData.value, baseData)
    isValid = baseTemplateRef.value.validate() && isValid
  }
  
  // 验证选项
  isValid = validateOptions() && isValid
  
  return isValid
}

// 计算验证状态
const isValid = computed(() => {
  const validOptions = formData.value.options.filter(option => option.text.trim())
  return baseValid.value && validOptions.length >= 2 && !errors.value.options
})

// 监听验证状态变化
watch(isValid, (valid) => {
  console.log('VoteTemplate: 验证状态变化', valid)
  emit('update:valid', valid)
}, { immediate: true })

// 监听表单数据变化，向父组件发送数据
watch(formData, (newData) => {
  console.log('VoteTemplate: 数据变化', newData)
  emit('update:data', newData)
}, { deep: true, immediate: true })

// 组件接口
const emit = defineEmits<{
  'update:data': [Record<string, any>]
  'update:valid': [boolean]
}>()

// 重置表单数据
const resetForm = () => {
  formData.value = {
    title: '',
    content: '',
    images: [],
    fileList: [],
    options: [
      { text: '' },
      { text: '' }
    ],
    multiSelect: 'false',
    anonymous: 'true',
    deadline: '',
    targetAudience: []
  }

  // 清空错误信息
  errors.value = {}
  
  // 重置基础模板
  if (baseTemplateRef.value && baseTemplateRef.value.resetForm) {
    baseTemplateRef.value.resetForm()
  }
}

defineExpose({
  validate,
  formData,
  isValid,
  resetForm
})
</script>

<style scoped lang="scss">
.vote-template {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #ff6b6b;
}

.options-section {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  margin-bottom: 12px;
}

.option-input-wrapper {
  display: flex;
  align-items: center;
  position: relative;
}

.option-input-wrapper :deep(.up-input) {
  flex: 1;
  padding-right: 40px;
}

.delete-option-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  padding: 4px;
  cursor: pointer;
  z-index: 10;

  &:hover {
    opacity: 0.7;
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }
}

.option-actions {
  margin-top: 12px;
}

.vote-settings {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff4757;
  margin-left: 4px;
}

.error-text {
  color: #ff4757;
  font-size: 12px;
  margin-top: 4px;
}

.help-text {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}
</style>
