<template>
  <view class="post-management-page">
    <!-- 页面头部 -->
    <up-navbar
      title="动态管理"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 统计数据 -->
    <view class="stats-section" :style="{ marginTop: mainContentPaddingTop }">
      <view class="stats-grid">
        <view
          class="stat-item"
          :class="{ active: currentStatusFilter === null }"
          @click="handleStatusFilter(null)"
        >
          <text class="stat-number">{{ stats.total }}</text>
          <text class="stat-label">总数</text>
        </view>
        <view
          class="stat-item"
          :class="{ active: currentStatusFilter === 0 }"
          @click="handleStatusFilter(0)"
        >
          <text class="stat-number pending">{{ stats.pending }}</text>
          <text class="stat-label">待审核</text>
        </view>
        <view
          class="stat-item"
          :class="{ active: currentStatusFilter === 1 }"
          @click="handleStatusFilter(1)"
        >
          <text class="stat-number approved">{{ stats.approved }}</text>
          <text class="stat-label">已通过</text>
        </view>
        <view
          class="stat-item"
          :class="{ active: currentStatusFilter === 2 }"
          @click="handleStatusFilter(2)"
        >
          <text class="stat-number rejected">{{ stats.rejected }}</text>
          <text class="stat-label">已拒绝</text>
        </view>
      </view>
    </view>

    <!-- 搜索和筛选 -->
    <view class="search-section">
      <up-search
        v-model="searchKeyword"
        placeholder="搜索动态内容或用户"
        @search="handleSearch"
        @clear="handleClearSearch"
        :showAction="false"
      ></up-search>
      <view class="category-tabs">
        <view
          class="category-tab"
          v-for="tab in categoryTabs"
          :key="tab.id"
          :class="{ active: currentCategory === tab.id }"
          @click="handleCategoryChange(tab.id)"
        >
          {{ tab.name }}
        </view>
      </view>
      <!-- 当前筛选状态提示 -->
      <view class="filter-tip" v-if="currentStatusFilter !== null">
        <text class="tip-text">当前筛选：{{ getStatusText(currentStatusFilter) }}</text>
        <up-icon
          name="close-circle-fill"
          size="16"
          color="#999"
          @click="handleStatusFilter(null)"
        ></up-icon>
      </view>
    </view>

    <!-- 动态列表 -->
    <view class="post-list">
      <view
        class="post-item"
        v-for="post in postList"
        :key="post.id"
        @click="handlePostClick(post)"
      >
        <!-- 用户信息 -->
        <view class="post-header">
          <up-avatar
            :text="post.user?.nickname || '用户'"
            fontSize="18"
            randomBgColor
          ></up-avatar>
          <view class="info">
            <view class="user-info">
              <view class="name-row">
                <view class="name">{{ post.user?.nickname || '匿名用户' }}</view>
                <!-- 分类标签 -->
                <up-tag
                  :text="getCategoryText(post.categoryId || post.category)"
                  size="mini"
                  color="#1890ff"
                  bgColor="#e6f7ff"
                  borderColor="transparent"
                  shape="circle"
                  plain
                  plainFill
                />
                <!-- 状态标签 -->
                <up-tag
                  :text="getStatusText(post.status)"
                  size="mini"
                  :color="getStatusTagColor(post.status).color"
                  :bgColor="getStatusTagColor(post.status).bgColor"
                  borderColor="transparent"
                  shape="circle"
                  plain
                  plainFill
                  style="margin-left: 4px;"
                />
              </view>
            </view>
            <view class="post-time">{{ formatPostTime(post) }}</view>
          </view>
        </view>

        <!-- 动态内容 -->
        <view class="post-content">
          <text class="content-text">{{ post.content }}</text>
          
          <!-- 图片/视频 -->
          <view class="media-grid">
            <!-- 有图片时显示图片 -->
            <template v-if="getPostImages(post).length > 0">
              <image
                v-for="(img, index) in getPostImages(post).slice(0, 3)"
                :key="index"
                :src="img"
                class="media-item"
                mode="aspectFill"
                @error="handleImageError"
              ></image>
            </template>


          </view>
        </view>

        <!-- 动态统计 -->
        <view class="post-stats">
          <view class="stat-item">
            <up-icon name="thumb-up" size="14" color="#999"></up-icon>
            <text class="stat-text">{{ post.likeCount || 0 }}</text>
          </view>
          <view class="stat-item">
            <up-icon name="chat" size="14" color="#999"></up-icon>
            <text class="stat-text">{{ post.commentCount || 0 }}</text>
          </view>
          <view class="stat-item">
            <up-icon name="star" size="14" color="#999"></up-icon>
            <text class="stat-text">{{ post.collectCount || 0 }}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="post-actions">
          <!-- 待审核状态：显示通过和拒绝按钮 -->
          <template v-if="post.status === 0 || post.status === 'pending'">
            <up-button
              text="通过"
              type="success"
              size="mini"
              @click.stop="handleApprove(post)"
            ></up-button>
            <up-button
              text="拒绝"
              type="error"
              size="mini"
              plain
              @click.stop="handleReject(post)"
            ></up-button>
          </template>

          <!-- 已通过状态：可以重新拒绝 -->
          <template v-if="post.status === 1 || post.status === 'approved'">
            <up-button
              text="拒绝"
              type="warning"
              size="mini"
              plain
              @click.stop="handleReject(post)"
            ></up-button>
          </template>

          <!-- 已拒绝状态：可以重新通过 -->
          <template v-if="post.status === 2 || post.status === 'rejected'">
            <up-button
              text="通过"
              type="success"
              size="mini"
              @click.stop="handleApprove(post)"
            ></up-button>
          </template>

          <!-- 删除按钮：所有状态都可以删除 -->
          <up-button
            text="删除"
            type="error"
            size="mini"
            plain
            @click.stop="handleDelete(post)"
          ></up-button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore">
      <up-loadmore 
        :status="loadStatus"
        @loadmore="loadMorePosts"
      ></up-loadmore>
    </view>



    <!-- 拒绝原因输入弹窗 -->
    <up-popup 
      v-model:show="showRejectModal" 
      mode="center" 
      :round="10"
      :closeable="true"
      @close="closeRejectModal"
    >
      <view class="reject-modal">
        <view class="modal-header">
          <text class="modal-title">拒绝原因</text>
          <up-icon name="close" @click="closeRejectModal" size="20" color="#999"></up-icon>
        </view>
        <view class="modal-content">
          <up-textarea 
            v-model="rejectReason"
            placeholder="请输入拒绝原因..."
            :maxlength="200"
            count
            :autoHeight="true"
            :height="100"
          ></up-textarea>
        </view>
        <view class="modal-footer">
          <up-button 
            text="确认拒绝" 
            type="error" 
            :disabled="!rejectReason.trim()"
            @click="confirmReject"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useSafeArea } from '@/utils/safeArea';
import { setFileList } from '@/utils/util';
import { getCategoryListAPI, formatCategoriesForTabs, getCategoryNameById } from '@/services/categoryService';
import {
  getAdminPostListAPI,
  getPostStatsAPI,
  approvePostAPI,
  rejectPostAPI,
  deletePostAPI,

  type PostManagementQuery
} from '@/services/postManagementService';
import { checkBackendAccess } from '@/services/permissionService';
import { formatPostTime } from '@/utils/timeFormat';
import type { PostManagement, LoadStatus } from '@/types/admin';
import type { Categories } from '@/types/Categories';

const { mainContentPaddingTop } = useSafeArea();

// 搜索关键词
const searchKeyword = ref<string>('');

// 当前分类
const currentCategory = ref<string>('all');

// 当前状态筛选
const currentStatusFilter = ref<number | null>(null);

// 分类相关数据
const categoriesData = ref<Categories[]>([]);
const categoryTabs = ref([{ name: '全部', id: 'all' }]);

// 统计数据
const stats = reactive({
  total: 0,
  pending: 0,
  approved: 0,
  rejected: 0
});

// 动态列表
const postList = ref<PostManagement[]>([]);

// 分页相关
const currentPage = ref<number>(1);
const pageSize = ref<number>(10);
const hasMore = ref<boolean>(true);
const loadStatus = ref<LoadStatus>('loadmore');

// 弹窗相关
const showRejectModal = ref<boolean>(false);
const selectedPost = ref<PostManagement | null>(null);
const rejectReason = ref<string>('');

// 获取分类列表
const fetchCategories = async (): Promise<void> => {
  try {
    const res = await getCategoryListAPI();
    if (res.success && res.data) {
      categoriesData.value = res.data;
      // 格式化为选项卡数据，但保持原有的字符串ID格式
      const formattedTabs = formatCategoriesForTabs(res.data);
      categoryTabs.value = [
        { name: '全部', id: 'all' },
        ...formattedTabs.slice(1).map(tab => ({
          name: tab.name,
          id: tab.id.toString()
        }))
      ];
      console.log('获取分类列表成功:', categoryTabs.value);
    } else {
      console.error('获取分类列表失败:', res.message);
    }
  } catch (error) {
    console.error('获取分类列表异常:', error);
  }
};

// 获取统计数据
const fetchStats = async (): Promise<void> => {
  try {
    const res = await getPostStatsAPI();
    if (res.success && res.data) {
      stats.total = res.data.total;
      stats.pending = res.data.pending;
      stats.approved = res.data.approved;
      stats.rejected = res.data.rejected;
      console.log('获取统计数据成功:', res.data);
    } else {
      console.error('获取统计数据失败:', res.message);
    }
  } catch (error) {
    console.error('获取统计数据异常:', error);
  }
};

// 检查权限
const checkPermission = async (): Promise<boolean> => {
  try {
    const hasAccess = await checkBackendAccess();
    if (!hasAccess) {
      uni.showModal({
        title: '权限不足',
        content: '您没有权限访问管理后台',
        showCancel: false,
        success: () => {
          uni.navigateBack();
        }
      });
      return false;
    }
    return true;
  } catch (error) {
    console.error('权限检查失败:', error);
    uni.showToast({
      title: '权限检查失败',
      icon: 'none'
    });
    return false;
  }
};

// 初始化数据
const initData = async () => {
  // 检查权限
  const hasPermission = await checkPermission();
  if (!hasPermission) {
    return;
  }

  // 获取分类列表
  await fetchCategories();

  // 获取统计数据
  await fetchStats();

  // 加载动态列表
  loadPostList();
};

// 加载动态列表
const loadPostList = async (isLoadMore: boolean = false) => {
  try {
    loadStatus.value = 'loading';

    const params: PostManagementQuery = {
      page: isLoadMore ? currentPage.value + 1 : 1,
      pageSize: pageSize.value,
      categoryId: currentCategory.value === 'all' ? undefined : Number(currentCategory.value),
      title: searchKeyword.value || undefined,
      status: currentStatusFilter.value ?? undefined
    };

    const res = await getAdminPostListAPI(params);

    if (res.success && res.data) {
      const newPosts = res.data.records;

      // 处理帖子数据，包括文件列表
      newPosts.forEach((post: PostManagement) => {
        // 处理文件列表 - 将fileList字符串转换为fileListData数组
        if (post.fileList && post.fileList.trim()) {
          (post as any).fileListData = setFileList(post.fileList);
          console.log(`帖子${post.id}的文件列表:`, post.fileList, '->', (post as any).fileListData);
        } else {
          (post as any).fileListData = [];
        }
      });

      if (isLoadMore) {
        // 加载更多，追加到现有列表
        postList.value = [...postList.value, ...newPosts];
        currentPage.value += 1;
      } else {
        // 重新加载，替换现有列表
        postList.value = newPosts;
        currentPage.value = 1;
      }

      // 判断是否还有更多数据
      hasMore.value = res.data.current < res.data.pages;
      loadStatus.value = hasMore.value ? 'loadmore' : 'nomore';

      console.log('加载帖子列表成功:', newPosts.length, '条数据');
    } else {
      console.error('加载帖子列表失败:', res.message);
      uni.showToast({
        title: res.message || '加载失败',
        icon: 'none'
      });
      loadStatus.value = 'loadmore';
    }
  } catch (error) {
    console.error('加载帖子列表异常:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
    loadStatus.value = 'loadmore';
  }
};

onMounted(() => {
  initData();
});

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 搜索动态
const handleSearch = (keyword: string) => {
  console.log('搜索动态:', keyword);
  searchKeyword.value = keyword;
  loadPostList(); // 重新加载列表
};

// 清除搜索
const handleClearSearch = () => {
  searchKeyword.value = '';
  loadPostList(); // 重新加载列表
};

// 分类变更
const handleCategoryChange = (category: string) => {
  currentCategory.value = category;
  console.log('分类筛选:', category);
  loadPostList(); // 重新加载列表
};

// 状态筛选
const handleStatusFilter = (status: number | null) => {
  currentStatusFilter.value = status;
  console.log('=== 状态筛选 ===');
  console.log('选择的状态:', status);
  console.log('当前分类:', currentCategory.value);
  console.log('搜索关键词:', searchKeyword.value);
  loadPostList(); // 重新加载列表
};

// 加载更多动态
const loadMorePosts = () => {
  if (hasMore.value && loadStatus.value !== 'loading') {
    loadPostList(true); // 加载更多
  }
};



// 点击帖子跳转到详情页面
const handlePostClick = (post: PostManagement) => {
  console.log('点击帖子，准备跳转到详情页面，帖子ID:', post.id);
  uni.navigateTo({
    url: `/pages/post/detail?id=${post.id}`,
    success: () => {
      console.log('跳转成功');
    },
    fail: (err) => {
      console.error('跳转失败:', err);
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
};



// 通过审核
const handleApprove = async (post: PostManagement) => {
  uni.showModal({
    title: '确认通过',
    content: '确定要通过这条动态的审核吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await approvePostAPI(post.id);
          if (result.success) {
            post.status = 1; // 更新本地状态
            uni.showToast({
              title: '审核通过',
              icon: 'success'
            });
            // 刷新统计数据
            await fetchStats();
          } else {
            uni.showToast({
              title: result.message || '操作失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('审核通过失败:', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 拒绝审核
const handleReject = (post: PostManagement) => {
  selectedPost.value = post;
  showRejectModal.value = true;
};

// 关闭拒绝弹窗
const closeRejectModal = () => {
  showRejectModal.value = false;
  rejectReason.value = '';
};

// 确认拒绝
const confirmReject = async () => {
  if (!rejectReason.value.trim()) {
    uni.showToast({
      title: '请输入拒绝原因',
      icon: 'none'
    });
    return;
  }

  if (!selectedPost.value) {
    return;
  }

  try {
    const result = await rejectPostAPI(selectedPost.value.id, rejectReason.value.trim());
    if (result.success) {
      selectedPost.value.status = 2; // 更新本地状态
      selectedPost.value.rejectReason = rejectReason.value;

      uni.showToast({
        title: '已拒绝审核',
        icon: 'success'
      });

      closeRejectModal();
      // 刷新统计数据
      await fetchStats();
    } else {
      uni.showToast({
        title: result.message || '操作失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('拒绝审核失败:', error);
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    });
  }
};

// 删除动态
const handleDelete = async (post: PostManagement) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条动态吗？此操作不可恢复！',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await deletePostAPI(post.id);
          if (result.success) {
            // 从列表中移除
            const index = postList.value.findIndex(p => p.id === post.id);
            if (index > -1) {
              postList.value.splice(index, 1);
            }
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
            // 刷新统计数据
            await fetchStats();
          } else {
            uni.showToast({
              title: result.message || '删除失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('删除动态失败:', error);
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          });
        }
      }
    }
  });
};



// 获取状态文本
const getStatusText = (status: string | number) => {
  const statusMap: Record<string, string> = {
    '0': '待审核',
    'pending': '待审核',
    '1': '已通过',
    'approved': '已通过',
    '2': '已拒绝',
    'rejected': '已拒绝'
  };
  return statusMap[String(status)] || '待审核';
};

// 获取状态标签颜色
const getStatusTagColor = (status: string | number) => {
  const colorMap: Record<string, { color: string; bgColor: string }> = {
    '0': { color: '#faad14', bgColor: '#fff7e6' },
    'pending': { color: '#faad14', bgColor: '#fff7e6' },
    '1': { color: '#52c41a', bgColor: '#f6ffed' },
    'approved': { color: '#52c41a', bgColor: '#f6ffed' },
    '2': { color: '#ff4d4f', bgColor: '#fff2f0' },
    'rejected': { color: '#ff4d4f', bgColor: '#fff2f0' }
  };
  return colorMap[String(status)] || { color: '#faad14', bgColor: '#fff7e6' };
};

// 获取分类文本
const getCategoryText = (categoryId?: number | string) => {
  if (!categoryId) return '未分类';

  // 如果是数字ID，从分类数据中查找
  if (typeof categoryId === 'number') {
    return getCategoryNameById(categoriesData.value, categoryId);
  }

  // 如果是字符串，从选项卡中查找
  const tab = categoryTabs.value.find(tab => tab.id === categoryId);
  return tab?.name || '未分类';
};

// 获取帖子图片列表
const getPostImages = (post: PostManagement): string[] => {
  // 优先使用处理后的fileListData
  if ((post as any).fileListData && Array.isArray((post as any).fileListData)) {
    return (post as any).fileListData;
  }

  // 兼容原有的images字段
  if (post.images && post.images.length > 0) {
    return post.images;
  }

  // 如果fileListData不存在，尝试实时处理fileList
  if (post.fileList && post.fileList.trim()) {
    return setFileList(post.fileList);
  }

  return [];
};

// 处理图片加载错误
const handleImageError = (e: any) => {
  console.warn('图片加载失败:', e);
  // 可以在这里设置默认图片或其他处理逻辑
};






</script>

<style lang="scss">
.post-management-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  // 统计数据区域
  .stats-section {
    background: #fff;
    padding: 20px;
    margin-bottom: 10px;

    .stats-grid {
      display: flex;
      justify-content: space-between;

      .stat-item {
        text-align: center;
        flex: 1;
        padding: 8px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #f5f5f5;
        }

        &.active {
          background: #e6f7ff;
          border: 1px solid #1890ff;
        }

        .stat-number {
          display: block;
          font-size: 24px;
          font-weight: bold;
          color: #333;
          margin-bottom: 4px;

          &.pending {
            color: #faad14;
          }

          &.approved {
            color: #52c41a;
          }

          &.rejected {
            color: #ff4d4f;
          }
        }

        .stat-label {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }

  // 搜索区域
  .search-section {
    background: #fff;
    padding: 15px 20px;
    margin-bottom: 10px;

    .filter-tip {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      padding: 8px 12px;
      background: #f0f0f0;
      border-radius: 6px;

      .tip-text {
        font-size: 12px;
        color: #666;
      }
    }

    .category-tabs {
      display: flex;
      margin-top: 15px;
      gap: 10px;
      overflow-x: auto;

      .category-tab {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        color: #666;
        background: #f0f0f0;
        cursor: pointer;
        white-space: nowrap;
        flex-shrink: 0;

        &.active {
          background: #1890ff;
          color: #fff;
        }
      }
    }
  }

  // 动态列表
  .post-list {
    background: #fff;

    .post-item {
      padding: 15px 20px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;

      &:active {
        background: #f5f5f5;
      }

      .post-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        .info {
          flex: 1;
          margin-left: 12px;

          .user-info {
            .name-row {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 4px;

              .name {
                font-size: 14px;
                font-weight: 500;
                color: #333;
              }

              .sex {
                display: flex;
                align-items: center;
              }
            }
          }

          .post-time {
            font-size: 12px;
            color: #999;
          }
        }


      }

      .post-content {
        margin-bottom: 10px;

        .content-text {
          font-size: 14px;
          color: #333;
          line-height: 1.4;
          display: block;
          margin-bottom: 8px;
        }

        .media-grid {
          display: flex;
          gap: 5px;
          position: relative;

          .media-item {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            background: #f5f5f5;


          }

          .more-media {
            position: absolute;
            right: 0;
            top: 0;
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.5);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            font-size: 12px;
          }
        }
      }

      .post-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 10px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 4px;

          .stat-text {
            font-size: 12px;
            color: #999;
          }
        }
      }

      .post-actions {
        display: flex;
        align-items: center;
        gap: 10px;

        :deep(.u-button) {
          height: 28px;
          padding: 0 12px;
        }
      }
    }
  }

  // 加载更多
  .load-more {
    padding: 20px;
  }



  // 拒绝原因弹窗
  .reject-modal {
    width: 300px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;

      .modal-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }

    .modal-content {
      padding: 20px;

      :deep(.u-textarea) {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        padding: 10px;
      }
    }

    .modal-footer {
      padding: 0 20px 20px;

      :deep(.u-button) {
        width: 100%;
        height: 44px;
        border-radius: 22px;
      }
    }
  }
}
</style>
