<template>
  <view class="sign-records-page">
    <!-- 页面头部 -->
    <view class="page-header" :style="{ paddingTop: pageHeaderPaddingTop }">
      <view class="header-content">
        <up-icon name="arrow-left" size="20" color="#333" @click="goBack"></up-icon>
        <text class="page-title">签到记录</text>
        <view class="header-placeholder"></view>
      </view>
    </view>

    <!-- 签到统计 -->
    <view class="sign-stats">
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ signStats.totalDays }}</text>
          <text class="stats-label">累计签到</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ signStats.continuousDays }}</text>
          <text class="stats-label">连续签到</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ signStats.totalPoints }}</text>
          <text class="stats-label">获得积分</text>
        </view>
      </view>
    </view>

    <!-- 签到日历 -->
    <view class="calendar-section">
      <view class="calendar-header">
        <text class="calendar-title">{{ currentMonth }}</text>
        <view class="month-nav">
          <up-icon name="arrow-left" size="16" @click="prevMonth"></up-icon>
          <up-icon name="arrow-right" size="16" @click="nextMonth"></up-icon>
        </view>
      </view>
      
      <view class="calendar-grid">
        <view class="weekdays">
          <text v-for="day in weekdays" :key="day" class="weekday">{{ day }}</text>
        </view>
        <view class="calendar-days">
          <view 
            v-for="day in calendarDays" 
            :key="day.date"
            class="calendar-day"
            :class="{
              'other-month': day.isOtherMonth,
              'signed': day.isSigned,
              'today': day.isToday
            }"
          >
            <text class="day-number">{{ day.day }}</text>
            <view v-if="day.isSigned" class="sign-dot"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 签到记录列表 -->
    <view class="records-section">
      <view class="section-header">
        <text class="section-title">签到记录</text>
      </view>
      
      <view class="records-list">
        <view v-if="signRecords.length === 0 && !loading" class="empty-state">
          <up-empty
            mode="data"
            text="暂无签到记录"
            textSize="14"
          ></up-empty>
        </view>

        <view v-for="record in signRecords" :key="record.id" class="record-item">
          <view class="record-date">
            <text class="date-text">{{ formatDate(record.signDate) }}</text>
            <text class="weekday-text">{{ getWeekday(record.signDate) }}</text>
          </view>
          <view class="record-content">
            <view class="record-info">
              <text class="continuous-text">连续{{ record.continuousDays }}天</text>
              <text class="points-text">+{{ record.pointsEarned }}积分</text>
            </view>
            <view class="record-badge">
              <up-icon name="checkmark-circle-fill" color="#52c41a" size="16"></up-icon>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <up-loadmore
      :status="loadStatus"
      :loading-text="loadingText"
      :loadmore-text="loadmoreText"
      :nomore-text="nomoreText"
      @loadmore="loadMore"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useMemberStore } from '@/stores'
import { get } from '@/utils/http'
import { useSafeArea } from '@/utils/safeArea'

const { pageHeaderPaddingTop } = useSafeArea()
const memberStore = useMemberStore()

// 签到统计
const signStats = reactive({
  totalDays: 0,
  continuousDays: 0,
  totalPoints: 0
})

// 日历相关
const currentDate = ref(new Date())
const weekdays = ref(['日', '一', '二', '三', '四', '五', '六'])
const signedDates = ref<string[]>([])

// 签到记录
const signRecords = ref<any[]>([])
const loading = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)
const loadStatus = ref('loadmore')
const loadingText = ref('正在加载...')
const loadmoreText = ref('上拉加载更多')
const nomoreText = ref('已经到底了')

// 当前月份显示
const currentMonth = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth() + 1
  return `${year}年${month}月`
})

// 日历天数
const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  
  // 获取当月第一天和最后一天
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  // 获取第一天是星期几
  const firstDayWeek = firstDay.getDay()
  
  const days = []
  
  // 添加上个月的日期
  for (let i = firstDayWeek - 1; i >= 0; i--) {
    const date = new Date(firstDay.getTime() - (i + 1) * 86400000)
    days.push({
      date: date.toISOString().split('T')[0],
      day: date.getDate(),
      isOtherMonth: true,
      isSigned: false,
      isToday: false
    })
  }
  
  // 添加当月的日期
  for (let day = 1; day <= lastDay.getDate(); day++) {
    const date = new Date(year, month, day)
    const dateStr = date.toISOString().split('T')[0]
    const today = new Date().toISOString().split('T')[0]
    
    days.push({
      date: dateStr,
      day: day,
      isOtherMonth: false,
      isSigned: signedDates.value.includes(dateStr),
      isToday: dateStr === today
    })
  }
  
  // 补充下个月的日期，确保总共42个格子（6行7列）
  const remainingDays = 42 - days.length
  for (let day = 1; day <= remainingDays; day++) {
    const date = new Date(year, month + 1, day)
    days.push({
      date: date.toISOString().split('T')[0],
      day: day,
      isOtherMonth: true,
      isSigned: false,
      isToday: false
    })
  }
  
  return days
})

// 获取签到统计
const fetchSignStats = async () => {
  try {
    const res = await get('/sign/status')
    if (res.success) {
      signStats.continuousDays = res.data.continuousDays || 0
      // 这里可以添加更多统计数据的API调用
    }
  } catch (error) {
    console.error('获取签到统计失败:', error)
  }
}

// 获取签到记录
const fetchSignRecords = async (isRefresh = false) => {
  if (isRefresh) {
    currentPage.value = 1
    hasMore.value = true
    loadStatus.value = 'loading'
  }

  loading.value = true

  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value
    }

    const res = await get('/sign/records', params)

    if (res.success) {
      const newRecords = res.data.records || []

      if (isRefresh) {
        signRecords.value = newRecords
      } else {
        signRecords.value.push(...newRecords)
      }

      // 提取签到日期用于日历显示
      const dates = newRecords.map((record: any) => record.signDate)
      signedDates.value = [...new Set([...signedDates.value, ...dates])]

      // 计算统计数据
      signStats.totalDays = signRecords.value.length
      signStats.totalPoints = signRecords.value.reduce((sum, record) => sum + record.pointsEarned, 0)

      // 判断是否还有更多数据
      if (newRecords.length < pageSize.value) {
        hasMore.value = false
        loadStatus.value = 'nomore'
      } else {
        loadStatus.value = 'loadmore'
      }
    }
  } catch (error) {
    console.error('获取签到记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (!hasMore.value || loading.value) return
  
  currentPage.value++
  fetchSignRecords()
}

// 上一个月
const prevMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
}

// 下一个月
const nextMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
}

// 格式化日期
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}月${date.getDate()}日`
}

// 获取星期几
const getWeekday = (dateStr: string) => {
  const date = new Date(dateStr)
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return weekdays[date.getDay()]
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

onMounted(() => {
  fetchSignStats()
  fetchSignRecords(true)
})
</script>

<style scoped lang="scss">
.sign-records-page {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  border-bottom: 1px solid #f0f0f0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-placeholder {
  width: 20px;
}

.sign-stats {
  padding: 16px;
}

.stats-card {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-around;
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #5677fc;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #999;
}

.calendar-section {
  background-color: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 16px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.calendar-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.month-nav {
  display: flex;
  gap: 16px;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-bottom: 8px;
}

.weekday {
  text-align: center;
  font-size: 12px;
  color: #999;
  padding: 8px 0;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
}

.calendar-day {
  position: relative;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  
  &.other-month {
    .day-number {
      color: #ccc;
    }
  }
  
  &.signed {
    background-color: #e6f7ff;
    
    .day-number {
      color: #1890ff;
      font-weight: 600;
    }
  }
  
  &.today {
    background-color: #5677fc;
    
    .day-number {
      color: white;
      font-weight: 600;
    }
  }
}

.day-number {
  font-size: 14px;
  color: #333;
}

.sign-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 4px;
  height: 4px;
  background-color: #52c41a;
  border-radius: 50%;
}

.records-section {
  background-color: white;
  margin: 0 16px;
  border-radius: 12px;
  padding: 16px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.record-date {
  margin-right: 16px;
  text-align: center;
  min-width: 60px;
}

.date-text {
  display: block;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 2px;
}

.weekday-text {
  font-size: 10px;
  color: #999;
}

.record-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.continuous-text {
  font-size: 14px;
  color: #333;
}

.points-text {
  font-size: 12px;
  color: #52c41a;
  font-weight: 500;
}

.empty-state {
  padding: 40px 20px;
}
</style>
