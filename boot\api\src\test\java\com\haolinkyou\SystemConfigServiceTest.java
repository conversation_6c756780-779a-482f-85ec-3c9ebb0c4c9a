package com.haolinkyou;

import com.haolinkyou.entity.SystemConfig;
import com.haolinkyou.service.ISystemConfigService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * 系统配置服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class SystemConfigServiceTest {

    @Autowired
    private ISystemConfigService systemConfigService;

    @Test
    public void testGetAllGroups() {
        try {
            List<String> groups = systemConfigService.getAllGroups();
            System.out.println("配置分组列表: " + groups);
            assert groups != null;
        } catch (Exception e) {
            System.out.println("测试获取分组列表时出现异常: " + e.getMessage());
        }
    }

    @Test
    public void testGetConfigsByGroup() {
        try {
            List<SystemConfig> configs = systemConfigService.getConfigsByGroup("basic");
            System.out.println("基础配置列表: " + configs);
            assert configs != null;
        } catch (Exception e) {
            System.out.println("测试获取基础配置时出现异常: " + e.getMessage());
        }
    }

    @Test
    public void testGetConfigValue() {
        try {
            String siteName = systemConfigService.getConfigValue("site_name");
            System.out.println("网站名称: " + siteName);
        } catch (Exception e) {
            System.out.println("测试获取配置值时出现异常: " + e.getMessage());
        }
    }
}
