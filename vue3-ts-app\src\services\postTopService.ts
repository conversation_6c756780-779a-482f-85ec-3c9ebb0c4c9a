import { get, post, del } from '@/utils/http'
import type { Result } from '@/types/ApiResponse'
import type { TopConfig, TopCheckResult, TopResult, TopCostResult } from '@/types/PostTop'
import type { PostsList } from '@/types/PostsList'

/**
 * 帖子置顶相关的API服务
 */

/**
 * 获取置顶配置
 */
export const getTopConfigAPI = () => {
  return get<Result<TopConfig>>('/posts/top/config')
}

/**
 * 检查帖子是否可以置顶
 * @param postId 帖子ID
 */
export const checkCanTopAPI = (postId: number) => {
  return get<Result<TopCheckResult>>(`/posts/${postId}/top/check`)
}

/**
 * 置顶帖子
 * @param postId 帖子ID
 * @param hours 置顶时长（小时）
 */
export const topPostAPI = (postId: number, hours: number) => {
  return post<Result<TopResult>>(`/posts/${postId}/top`, { hours })
}

/**
 * 取消置顶
 * @param postId 帖子ID
 */
export const cancelTopAPI = (postId: number) => {
  return del<Result<boolean>>(`/posts/${postId}/top`)
}

/**
 * 获取用户的置顶帖子列表
 */
export const getMyTopPostsAPI = () => {
  return get<Result<PostsList[]>>('/posts/top/my')
}

/**
 * 计算置顶费用
 * @param hours 置顶时长（小时）
 */
export const getTopCostAPI = (hours: number) => {
  return get<Result<TopCostResult>>('/posts/top/cost', { hours })
}
