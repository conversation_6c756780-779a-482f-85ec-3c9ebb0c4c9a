package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BaseEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(fill= FieldFill.INSERT)
    private Date createdTime;

    @TableField(fill= FieldFill.INSERT_UPDATE)
    private Date updatedTime;

    @TableLogic
    private Integer delFlag;

}
