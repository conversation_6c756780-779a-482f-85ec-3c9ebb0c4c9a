<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.haolinkyou.mapper.CommentsMapper">
    <select id="getCommentsByPostId" resultType="com.haolinkyou.api.vo.CommentsVo" >
        SELECT
            c.id AS comment_id,
            c.post_id,
            c.user_id,
            c.parent_id AS parent_comment_id,
            u.nickname AS username,
            pu.nickname AS parent_username,
            c.content,
            c.created_time,
            c.like_count
        FROM
            comments c
                LEFT JOIN users u ON c.user_id = u.id AND u.del_flag = 0
                LEFT JOIN comments pc ON c.parent_id = pc.id AND pc.del_flag = 0
                LEFT JOIN users pu ON pc.user_id = pu.id AND pu.del_flag = 0
        WHERE
            c.del_flag = 0
          AND c.post_id = #{postId}
        ORDER BY
            c.created_time DESC;
    </select>
</mapper>
