package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户收藏实体类
 */
@Data
@TableName("user_collects")
public class UserCollects implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 帖子ID
     */
    private Long postId;

    /**
     * 用户ID
     */
    private Long userId;

    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;

    @TableLogic
    private Integer delFlag;

    // 注意：这个表没有 updated_time 字段，所以不继承 BaseEntity
}
