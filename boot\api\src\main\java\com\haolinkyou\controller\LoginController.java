package com.haolinkyou.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.common.utils.JwtUtil;
import com.haolinkyou.common.utils.Md5Util;
import com.haolinkyou.entity.Users;
import com.haolinkyou.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 登录控制器
 */
@RestController
@RequestMapping("/api/login")
public class LoginController {
    
    @Autowired
    private IUserService userService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 用户名密码登录
     * @param loginRequest 登录请求
     * @return 登录结果
     */
    @PostMapping("")
    public Result<Map<String, Object>> login(@RequestBody LoginRequest loginRequest) {
        try {
            // 查询用户
            QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", loginRequest.getAccount())
                       .or()
                       .eq("mobile", loginRequest.getAccount());
            
            Users user = userService.getOne(queryWrapper);
            
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 验证密码（这里假设密码是MD5加密的）
            String encryptedPassword = Md5Util.getMD5(loginRequest.getPassword());
            if (!encryptedPassword.equals(user.getPassword())) {
                return Result.error("密码错误");
            }
            
            // 生成JWT token
            String token = jwtUtil.generateToken(user.getId(), user.getUsername(), user.getNickname());
            
            // 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", user.getId());
            result.put("account", user.getUsername());
            result.put("nickname", user.getNickname());
            result.put("avatar", user.getAvatarUrl());
            result.put("mobile", user.getMobile());
            result.put("token", token);
            result.put("authType", 1);
            result.put("isAdmin", "admin".equals(user.getUserRole())); // 基于角色判断管理员
            
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }
    
    /**
     * 微信小程序登录（简化版）
     * @param wxLoginRequest 微信登录请求
     * @return 登录结果
     */
    @PostMapping("/wxMin/simple")
    public Result<Map<String, Object>> wxMinSimpleLogin(@RequestBody WxLoginRequest wxLoginRequest) {
        try {
            // 根据手机号查询或创建用户
            QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("mobile", wxLoginRequest.getPhoneNumber());
            
            Users user = userService.getOne(queryWrapper);
            
            if (user == null) {
                // 创建新用户
                user = new Users();
                user.setMobile(wxLoginRequest.getPhoneNumber());
                user.setUsername("用户" + wxLoginRequest.getPhoneNumber());
                user.setNickname("用户" + wxLoginRequest.getPhoneNumber().substring(7)); // 取后4位
                user.setAvatarUrl("http://yjy-xiaotuxian-dev.oss-cn-beijing.aliyuncs.com/picture/2021-04-06/db628d42-88a7-46e7-abb8-659448c33081.png");
                user.setPassword(Md5Util.getMD5("123456")); // 设置默认密码
                user.setUserRole("guest"); // 设置默认角色为游客
                user.setStatus(1);
                userService.save(user);
            }
            
            // 生成JWT token
            String token = jwtUtil.generateToken(user.getId(), user.getUsername(), user.getNickname());

            // 获取用户统计信息
            Map<String, Object> userStats = userService.getUserStats(user.getId());

            // 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", user.getId());
            result.put("account", user.getUsername());
            result.put("nickname", user.getNickname());
            result.put("avatar", user.getAvatarUrl());
            result.put("mobile", user.getMobile());
            result.put("token", token);
            result.put("authType", 1);
            result.put("isAdmin", "admin".equals(user.getUserRole())); // 基于角色判断管理员

            // 添加统计信息
            result.put("postCount", userStats.get("postCount"));
            result.put("favoriteCount", userStats.get("favoriteCount"));
            result.put("pointCount", userStats.get("pointCount"));

            return Result.success(result);
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取所有测试用户列表（用于模拟登录选择）
     */
    @GetMapping("/users")
    public Result<List<Map<String, Object>>> getTestUsers() {
        try {
            // 查询所有用户
            List<Users> users = userService.list();

            // 转换为前端需要的格式
            List<Map<String, Object>> userList = users.stream().map(user -> {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", user.getId());
                userInfo.put("username", user.getUsername());
                userInfo.put("nickname", user.getNickname());
                userInfo.put("avatar", user.getAvatarUrl());
                userInfo.put("mobile", user.getMobile());
                userInfo.put("userRole", user.getUserRole());
                userInfo.put("isVerified", user.getIsVerified());
                userInfo.put("verificationType", user.getVerificationType());
                userInfo.put("houseNumber", user.getHouseNumber());
                userInfo.put("realName", user.getRealName());
                userInfo.put("isAdmin", "admin".equals(user.getUserRole()));
                return userInfo;
            }).collect(Collectors.toList());

            return Result.success(userList);
        } catch (Exception e) {
            return Result.error("获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 验证token有效性
     * @param request HTTP请求
     * @return 验证结果
     */
    @GetMapping("/validate")
    public Result<Map<String, Object>> validateToken(javax.servlet.http.HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        String username = (String) request.getAttribute("username");
        String nickname = (String) request.getAttribute("nickname");

        if (userId != null) {
            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("username", username);
            result.put("nickname", nickname);
            return Result.success(result);
        } else {
            return Result.error("Token无效");
        }
    }
    
    /**
     * 登录请求DTO
     */
    public static class LoginRequest {
        private String account;
        private String password;
        
        public String getAccount() {
            return account;
        }
        
        public void setAccount(String account) {
            this.account = account;
        }
        
        public String getPassword() {
            return password;
        }
        
        public void setPassword(String password) {
            this.password = password;
        }
    }
    
    /**
     * 微信登录请求DTO
     */
    public static class WxLoginRequest {
        private String phoneNumber;
        private String code;
        private String encryptedData;
        private String iv;
        
        public String getPhoneNumber() {
            return phoneNumber;
        }
        
        public void setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
        }
        
        public String getCode() {
            return code;
        }
        
        public void setCode(String code) {
            this.code = code;
        }
        
        public String getEncryptedData() {
            return encryptedData;
        }
        
        public void setEncryptedData(String encryptedData) {
            this.encryptedData = encryptedData;
        }
        
        public String getIv() {
            return iv;
        }
        
        public void setIv(String iv) {
            this.iv = iv;
        }
    }

    /**
     * 为模拟用户生成真实的JWT token
     * 用于开发和测试环境
     */
    @PostMapping("/generateMockToken")
    public Result<Map<String, Object>> generateMockToken(@RequestBody MockTokenRequest request) {
        try {
            // 生成真实的JWT token
            String token = jwtUtil.generateToken(request.getUserId(), request.getUsername(), request.getNickname());

            // 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("userId", request.getUserId());
            result.put("username", request.getUsername());
            result.put("nickname", request.getNickname());

            return Result.success(result);
        } catch (Exception e) {
            return Result.error("生成token失败：" + e.getMessage());
        }
    }

    /**
     * 模拟token请求参数
     */
    public static class MockTokenRequest {
        private Long userId;
        private String username;
        private String nickname;

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
    }


}
