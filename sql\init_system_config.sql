-- =============================================
-- 小区社交APP功能完善 - 初始化系统配置数据
-- 创建时间: 2025-01-26
-- 说明: 添加积分系统、签到、置顶等功能的配置项
-- =============================================

USE `demo`;

-- ----------------------------
-- 初始化积分系统相关配置
-- ----------------------------
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `group_name`, `sort_order`, `is_system`) VALUES
('points_daily_limit', '100', 'number', '每日积分获取上限', 'points', 1, 1),
('points_sign_daily', '5', 'number', '每日签到积分', 'points', 2, 1),
('points_post_create', '10', 'number', '发帖获得积分', 'points', 3, 1),
('points_comment_create', '2', 'number', '评论获得积分', 'points', 4, 1),
('points_like_give', '1', 'number', '点赞获得积分', 'points', 5, 1),
('points_collect_give', '1', 'number', '收藏获得积分', 'points', 6, 1),
('points_continuous_sign_bonus', '2', 'number', '连续签到额外奖励', 'points', 7, 1),
('points_post_top_cost_per_hour', '10', 'number', '帖子置顶每小时消耗积分', 'points', 8, 1),
('points_post_top_max_hours', '168', 'number', '帖子置顶最大时长(小时)', 'points', 9, 1),
('points_post_top_min_hours', '1', 'number', '帖子置顶最小时长(小时)', 'points', 10, 1),
('points_post_top_default_hours', '24', 'number', '帖子置顶默认时长(小时)', 'points', 11, 1)
ON DUPLICATE KEY UPDATE 
    `config_value` = VALUES(`config_value`),
    `description` = VALUES(`description`),
    `updated_time` = CURRENT_TIMESTAMP;

-- ----------------------------
-- 初始化分类模板配置数据
-- ----------------------------
-- 三期公告模板
INSERT INTO `category_templates` (`category_id`, `template_name`, `template_config`, `is_default`, `status`) VALUES
(1, '公告模板', '{
  "name": "公告模板",
  "fields": [
    {"name": "title", "label": "公告标题", "type": "text", "required": true, "maxLength": 100},
    {"name": "content", "label": "公告内容", "type": "textarea", "required": true, "maxLength": 2000},
    {"name": "priority", "label": "优先级", "type": "select", "options": ["普通", "重要", "紧急"], "required": false},
    {"name": "effectiveDate", "label": "生效日期", "type": "date", "required": false},
    {"name": "attachments", "label": "附件", "type": "file", "multiple": true, "required": false}
  ]
}', 1, 1)
ON DUPLICATE KEY UPDATE 
    `template_config` = VALUES(`template_config`),
    `updated_time` = CURRENT_TIMESTAMP;

-- 邻友互助模板
INSERT INTO `category_templates` (`category_id`, `template_name`, `template_config`, `is_default`, `status`) VALUES
(2, '互助模板', '{
  "name": "互助模板",
  "fields": [
    {"name": "helpType", "label": "求助类型", "type": "select", "options": ["生活服务", "技能交换", "物品借用", "紧急求助", "其他"], "required": true},
    {"name": "description", "label": "详细描述", "type": "textarea", "required": true, "maxLength": 1000},
    {"name": "contact", "label": "联系方式", "type": "text", "required": true, "maxLength": 50},
    {"name": "urgency", "label": "紧急程度", "type": "select", "options": ["不急", "一般", "紧急"], "required": false},
    {"name": "reward", "label": "酬谢说明", "type": "text", "required": false, "maxLength": 100}
  ]
}', 1, 1)
ON DUPLICATE KEY UPDATE 
    `template_config` = VALUES(`template_config`),
    `updated_time` = CURRENT_TIMESTAMP;

-- 组团邀约模板
INSERT INTO `category_templates` (`category_id`, `template_name`, `template_config`, `is_default`, `status`) VALUES
(3, '团购模板', '{
  "name": "团购模板",
  "fields": [
    {"name": "productName", "label": "商品名称", "type": "text", "required": true, "maxLength": 100},
    {"name": "originalPrice", "label": "原价", "type": "number", "required": true},
    {"name": "groupPrice", "label": "团购价", "type": "number", "required": true},
    {"name": "minQuantity", "label": "成团人数", "type": "number", "required": true},
    {"name": "deadline", "label": "截止时间", "type": "datetime", "required": true},
    {"name": "description", "label": "商品描述", "type": "textarea", "required": false, "maxLength": 1000},
    {"name": "images", "label": "商品图片", "type": "image", "multiple": true, "required": false}
  ]
}', 1, 1)
ON DUPLICATE KEY UPDATE 
    `template_config` = VALUES(`template_config`),
    `updated_time` = CURRENT_TIMESTAMP;

-- 二手闲置模板
INSERT INTO `category_templates` (`category_id`, `template_name`, `template_config`, `is_default`, `status`) VALUES
(4, '二手商品模板', '{
  "name": "二手商品模板",
  "fields": [
    {"name": "productName", "label": "商品名称", "type": "text", "required": true, "maxLength": 100},
    {"name": "price", "label": "售价", "type": "number", "required": true},
    {"name": "condition", "label": "成色", "type": "select", "options": ["全新", "九成新", "八成新", "七成新", "其他"], "required": true},
    {"name": "category", "label": "商品分类", "type": "select", "options": ["电子产品", "家具家电", "服装鞋帽", "母婴用品", "图书文具", "其他"], "required": true},
    {"name": "description", "label": "商品描述", "type": "textarea", "required": false, "maxLength": 1000},
    {"name": "images", "label": "商品图片", "type": "image", "multiple": true, "required": true},
    {"name": "contact", "label": "联系方式", "type": "text", "required": true, "maxLength": 50}
  ]
}', 1, 1)
ON DUPLICATE KEY UPDATE 
    `template_config` = VALUES(`template_config`),
    `updated_time` = CURRENT_TIMESTAMP;

-- 房东直租模板
INSERT INTO `category_templates` (`category_id`, `template_name`, `template_config`, `is_default`, `status`) VALUES
(5, '租房模板', '{
  "name": "租房模板",
  "fields": [
    {"name": "houseType", "label": "房屋类型", "type": "select", "options": ["一室一厅", "两室一厅", "三室一厅", "三室两厅", "其他"], "required": true},
    {"name": "area", "label": "面积(平米)", "type": "number", "required": true},
    {"name": "rent", "label": "租金(元/月)", "type": "number", "required": true},
    {"name": "deposit", "label": "押金", "type": "text", "required": true, "maxLength": 50},
    {"name": "floor", "label": "楼层", "type": "text", "required": false, "maxLength": 20},
    {"name": "facilities", "label": "配套设施", "type": "textarea", "required": false, "maxLength": 500},
    {"name": "requirements", "label": "租房要求", "type": "textarea", "required": false, "maxLength": 500},
    {"name": "contact", "label": "联系方式", "type": "text", "required": true, "maxLength": 50},
    {"name": "images", "label": "房屋图片", "type": "image", "multiple": true, "required": false}
  ]
}', 1, 1)
ON DUPLICATE KEY UPDATE 
    `template_config` = VALUES(`template_config`),
    `updated_time` = CURRENT_TIMESTAMP;

-- =============================================
-- 系统配置数据初始化完成
-- 包含积分系统配置和5个分类的发帖模板配置
-- =============================================