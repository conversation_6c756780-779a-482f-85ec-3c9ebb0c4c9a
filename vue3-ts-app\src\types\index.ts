// 统一导出所有类型定义

// API响应相关
export * from './ApiResponse';
export * from './common';

// 业务数据相关
export * from './PostsList';
export * from './Categories';
export * from './user';
export * from './product';
export * from './publish';
export * from './postDetail';

// 管理相关
export * from './admin';

// UI组件相关
export * from './ui';

// 用户状态相关
export * from './member.d';

// 常用类型别名
export type ID = string | number;
// Timestamp 类型已在 common.ts 中定义
export type Optional<T> = T | undefined;
export type Nullable<T> = T | null;

// 分页相关
export interface PageParams {
  page: number;
  pageSize: number;
}

export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// 排序相关
export interface SortParams {
  field: string;
  order: 'asc' | 'desc';
}

// 时间范围
export interface TimeRange {
  startTime?: string;
  endTime?: string;
}

// 搜索参数基类
export interface BaseSearchParams extends PageParams {
  keyword?: string;
  status?: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

// 操作结果
export interface OperationResult {
  success: boolean;
  message?: string;
  data?: any;
}

// 文件信息
export interface FileInfo {
  name: string;
  size: number;
  type: string;
  url?: string;
  path?: string;
}

// 地理位置
export interface GeoLocation {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  altitudeAccuracy?: number;
  heading?: number;
  speed?: number;
}

// DeviceInfo 类型已在 common.ts 中定义

// 网络状态
export interface NetworkInfo {
  isConnected: boolean;
  networkType: string; // wifi/2g/3g/4g/5g/unknown/none
}

// 应用配置
export interface AppConfig {
  version: string;
  buildNumber: string;
  environment: 'development' | 'staging' | 'production';
  apiBaseUrl: string;
  uploadUrl: string;
  staticUrl: string;
}

// 主题配置
export interface ThemeConfig {
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  shadowColor: string;
}

// 权限相关
export interface Permission {
  code: string;
  name: string;
  description?: string;
  type: 'menu' | 'button' | 'api';
  parentCode?: string;
}

export interface Role {
  code: string;
  name: string;
  description?: string;
  permissions: string[];
}

// 缓存配置
export interface CacheConfig {
  key: string;
  ttl?: number; // 过期时间(秒)
  version?: string;
}

// 错误信息
export interface ErrorInfo {
  code: string | number;
  message: string;
  details?: any;
  timestamp?: string;
  path?: string;
}

// LogLevel 类型已在 common.ts 中定义

// 日志信息
export interface LogInfo {
  level: import('./common').LogLevel;
  message: string;
  data?: any;
  timestamp: string;
  source?: string;
}
