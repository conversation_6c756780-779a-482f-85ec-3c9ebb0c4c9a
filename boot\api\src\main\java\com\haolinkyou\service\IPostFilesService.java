/*
 * @Author: Rock
 * @Date: 2025-05-01 01:33:15
 * @LastEditors: Rock
 * @LastEditTime: 2025-05-01 06:04:03
 * @Description: 
 */
package com.haolinkyou.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IPostFilesService {
  List<String> uploadPostFiles(Long postId,Long userId, Integer type, MultipartFile[] files) throws Exception;
  Long getLastInsertedFileId();
  Long saveOrUpdatePostFiles(Long postId, Long userId, Integer type, List<String> allFilePaths, long totalSize);
}
