package com.haolinkyou.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haolinkyou.entity.PostLikes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 帖子点赞Mapper接口
 */
@Mapper
public interface PostLikesMapper extends BaseMapper<PostLikes> {

    /**
     * 检查用户是否已点赞该帖子
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 点赞记录数量
     */
    int checkUserLiked(@Param("postId") Long postId, @Param("userId") Long userId);

    /**
     * 获取帖子的点赞数
     * @param postId 帖子ID
     * @return 点赞数
     */
    int getPostLikeCount(@Param("postId") Long postId);

    /**
     * 添加点赞记录
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 影响的行数
     */
    int addLike(@Param("postId") Long postId, @Param("userId") Long userId);

    /**
     * 取消点赞（物理删除）
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 影响的行数
     */
    int removeLike(@Param("postId") Long postId, @Param("userId") Long userId);
}
