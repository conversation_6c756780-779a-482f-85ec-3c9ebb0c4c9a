<template>
  <view class="vote-post-detail">
    <!-- 投票标题和状态 -->
    <view class="vote-header">
      <view class="title-row">
        <view class="vote-title">{{ post.title }}</view>
        <view class="vote-status-badge" :class="statusClass">
          {{ statusText }}
        </view>
      </view>
      
      <!-- 投票信息 -->
      <view class="vote-meta">
        <view class="meta-item">
          <up-icon name="list" size="14" color="rgba(255,255,255,0.8)"></up-icon>
          <text class="meta-text">{{ post.options?.length || 0 }}个选项</text>
        </view>
        <view class="meta-item">
          <up-icon name="account" size="14" color="rgba(255,255,255,0.8)"></up-icon>
          <text class="meta-text">{{ totalVotes }}人投票</text>
        </view>
        <view class="meta-item">
          <up-icon :name="post.voteType === 'single' ? 'radio-button-on' : 'checkbox-marked'" size="14" color="rgba(255,255,255,0.8)"></up-icon>
          <text class="meta-text">{{ post.voteType === 'single' ? '单选' : '多选' }}</text>
        </view>
        <view v-if="post.isAnonymous" class="meta-item">
          <up-icon name="eye-off" size="14" color="rgba(255,255,255,0.8)"></up-icon>
          <text class="meta-text">匿名投票</text>
        </view>
      </view>
      
      <!-- 截止时间 -->
      <view v-if="post.deadline" class="deadline-info">
        <text class="deadline-label">截止时间：</text>
        <text class="deadline-value">{{ formatDateTime(post.deadline) }}</text>
        <text v-if="isExpired" class="expired-tag">已截止</text>
      </view>
      
      <view v-if="!isExpired && timeRemaining" class="time-remaining">
        <text class="remaining-text">剩余时间：{{ timeRemaining }}</text>
      </view>
    </view>
    
    <!-- 基础内容 -->
    <BasePostDetail :post="post" />
    
    <!-- 投票选项和结果 -->
    <view class="vote-options">
      <view class="section-title">
        <text>投票选项</text>
        <text v-if="showResults" class="results-note">（显示实时结果）</text>
      </view>
      
      <view class="options-list">
        <view v-for="(option, index) in post.options" :key="index" class="option-item" :class="{ voted: hasVotedForOption(index) }">
          <view class="option-content">
            <view class="option-header">
              <view class="option-indicator">
                <view v-if="post.voteType === 'single'" class="radio-indicator" :class="{ selected: hasVotedForOption(index) }">
                  <view v-if="hasVotedForOption(index)" class="radio-dot"></view>
                </view>
                <view v-else class="checkbox-indicator" :class="{ selected: hasVotedForOption(index) }">
                  <up-icon v-if="hasVotedForOption(index)" name="checkmark" size="12" color="white"></up-icon>
                </view>
              </view>
              <text class="option-text">{{ typeof option === 'string' ? option : option.text }}</text>
            </view>
            
            <!-- 投票结果 -->
            <view v-if="showResults" class="option-result">
              <view class="result-bar">
                <view class="result-fill" :style="{ width: getOptionPercentage(index) + '%' }"></view>
              </view>
              <view class="result-info">
                <text class="vote-count">{{ getOptionVoteCount(index) }}票</text>
                <text class="vote-percentage">{{ getOptionPercentage(index) }}%</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 投票统计 -->
    <view v-if="showResults && totalVotes > 0" class="vote-stats">
      <view class="section-title">投票统计</view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{ totalVotes }}</text>
          <text class="stat-label">总投票数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ participantCount }}</text>
          <text class="stat-label">参与人数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ leadingOption.percentage }}%</text>
          <text class="stat-label">最高得票率</text>
        </view>
      </view>
      
      <!-- 领先选项 -->
      <view v-if="leadingOption.text" class="leading-option">
        <text class="leading-label">当前领先：</text>
        <text class="leading-text">{{ leadingOption.text }}</text>
        <text class="leading-votes">（{{ leadingOption.votes }}票）</text>
      </view>
    </view>
    
    <!-- 目标对象 -->
    <view v-if="post.targetAudience && post.targetAudience.length > 0" class="target-section">
      <view class="section-title">目标对象</view>
      <view class="target-tags">
        <up-tag
          v-for="target in post.targetAudience"
          :key="target"
          :text="formatTargetAudience(target)"
          color="#6f42c1"
          size="mini"
          plain
          plainFill
          borderColor="transparent"
        />
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-section">
      <up-button
        v-if="!isExpired && !hasVoted"
        text="立即投票"
        type="primary"
        size="large"
        @click="handleVote"
        :customStyle="{ borderRadius: '8px', background: 'linear-gradient(135deg, #6f42c1 0%, #007bff 100%)' }"
      />
      <up-button
        v-else-if="hasVoted && !isExpired"
        text="修改投票"
        type="info"
        size="large"
        plain
        @click="handleVote"
        :customStyle="{ borderRadius: '8px' }"
      />
      <view v-if="canViewDetails" class="view-details-btn">
        <up-button
          text="查看详细结果"
          type="success"
          size="large"
          plain
          @click="handleViewDetails"
          :customStyle="{ borderRadius: '8px', marginTop: '12px' }"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import BasePostDetail from './BasePostDetail.vue'
import { getTargetAudienceOptionsAPI } from '@/services/roleService'
import { useMemberStore } from '@/stores'

interface Props {
  post: {
    id?: number
    title: string
    content: string
    images?: string[]
    options: Array<{
      text: string
      votes?: number
    }> | string[]
    voteType: 'single' | 'multiple'
    isAnonymous: boolean
    deadline?: string
    targetAudience?: string[]
    voteResults?: { [key: number]: number }
    userVotes?: number[]
    totalVotes?: number
    participantCount?: number
    createdTime: string
    updatedTime?: string
    hasVoted?: boolean
    canViewDetails?: boolean
    showResults?: boolean
    [key: string]: any
  }
}

const props = defineProps<Props>()
const memberStore = useMemberStore()

// 目标对象映射（动态获取）
const targetAudienceMap = ref<Map<string, string>>(new Map())

// 加载目标对象映射
const loadTargetAudienceMap = async () => {
  try {
    const options = await getTargetAudienceOptionsAPI()
    const map = new Map<string, string>()
    options.forEach(optionItem => {
      map.set(optionItem.value, optionItem.label)
    })
    targetAudienceMap.value = map
  } catch (error) {
    console.error('加载目标对象映射失败:', error)
    // 使用默认映射作为后备
    targetAudienceMap.value = new Map([
      ['owner', '全体业主'],
      ['tenant', '租户'],
      ['property', '物业人员'],
      ['committee', '业委会成员'],
      ['all', '所有用户']
    ])
  }
}

// 是否过期
const isExpired = computed(() => {
  if (!props.post.deadline) return false
  return new Date(props.post.deadline) < new Date()
})

// 状态文本和样式
const statusText = computed(() => {
  if (isExpired.value) return '已截止'
  if (totalVotes.value > 0) return '进行中'
  return '待投票'
})

const statusClass = computed(() => {
  if (isExpired.value) return 'expired'
  if (totalVotes.value > 0) return 'active'
  return 'pending'
})

// 剩余时间
const timeRemaining = computed(() => {
  if (!props.post.deadline || isExpired.value) return null
  
  const now = new Date()
  const deadline = new Date(props.post.deadline)
  const diff = deadline.getTime() - now.getTime()
  
  if (diff <= 0) return null
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) return `${days}天${hours}小时`
  if (hours > 0) return `${hours}小时${minutes}分钟`
  return `${minutes}分钟`
})

// 总投票数
const totalVotes = computed(() => {
  if (props.post.totalVotes !== undefined) return props.post.totalVotes
  
  if (props.post.voteResults) {
    return Object.values(props.post.voteResults).reduce((sum, count) => sum + count, 0)
  }
  
  if (Array.isArray(props.post.options) && props.post.options[0] && typeof props.post.options[0] === 'object') {
    return props.post.options.reduce((sum, optionItem) => {
      const option = optionItem as { text: string; votes?: number }
      return sum + (option.votes || 0)
    }, 0)
  }
  
  return 0
})

// 参与人数
const participantCount = computed(() => {
  return props.post.participantCount || totalVotes.value
})

// 是否已投票
const hasVoted = computed(() => {
  return props.post.hasVoted || (props.post.userVotes && props.post.userVotes.length > 0)
})

// 是否显示结果
const showResults = computed(() => {
  return props.post.showResults !== false && (hasVoted.value || isExpired.value || totalVotes.value > 0)
})

// 是否可以查看详细结果
const canViewDetails = computed(() => {
  return props.post.canViewDetails || 
         memberStore.profile?.isAdmin || 
         hasVoted.value
})

// 检查是否为某个选项投票
const hasVotedForOption = (index: number) => {
  return props.post.userVotes?.includes(index) || false
};

// 获取选项投票数
const getOptionVoteCount = (index: number) => {
  if (props.post.voteResults) {
    return props.post.voteResults[index] || 0
  }
  
  if (Array.isArray(props.post.options) && props.post.options[index] && typeof props.post.options[index] === 'object') {
    const option = props.post.options[index] as { text: string; votes?: number }
    return option.votes || 0
  }
  
  return 0
}

// 获取选项投票百分比
const getOptionPercentage = (index: number) => {
  if (totalVotes.value === 0) return 0
  const votes = getOptionVoteCount(index)
  return Math.round((votes / totalVotes.value) * 100)
}

// 领先选项
const leadingOption = computed(() => {
  if (!props.post.options || props.post.options.length === 0) {
    return { text: '', votes: 0, percentage: 0 }
  }
  
  let maxVotes = 0
  let maxIndex = 0
  
  props.post.options.forEach((option, index) => {
    const votes = getOptionVoteCount(index)
    if (votes > maxVotes) {
      maxVotes = votes
      maxIndex = index
    }
  })
  
  const optionText = typeof props.post.options[maxIndex] === 'string' 
    ? props.post.options[maxIndex] as string
    : (props.post.options[maxIndex] as { text: string; votes?: number }).text
  
  return {
    text: optionText,
    votes: maxVotes,
    percentage: getOptionPercentage(maxIndex)
  }
})

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化目标对象
const formatTargetAudience = (audience: string) => {
  return targetAudienceMap.value.get(audience) || audience
}

// 处理投票
const handleVote = () => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  // 直接跳转到投票页面，不再显示弹窗
  uni.navigateTo({
    url: `/pages/vote/participate?id=${props.post.id}`
  })
}

// 查看详细结果
const handleViewDetails = () => {
  // 直接跳转到结果页面
  uni.navigateTo({
    url: `/pages/vote/results?id=${props.post.id}`
  })
}

// 定义事件
const emit = defineEmits<{
  vote: [postId: number]
  viewResults: [postId: number]
}>()

// 组件挂载时加载目标对象映射
onMounted(() => {
  loadTargetAudienceMap()
})
</script>

<style scoped lang="scss">
.vote-post-detail {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.vote-header {
  background: linear-gradient(135deg, #6f42c1 0%, #007bff 100%);
  color: white;
  padding: 20px 16px;
}

.title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.vote-title {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.vote-status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
  
  &.pending {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  &.active {
    background-color: #ffc107;
    color: #333;
  }
  
  &.expired {
    background-color: #dc3545;
    color: white;
  }
}

.vote-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-text {
  font-size: 14px;
  opacity: 0.9;
}

.deadline-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.deadline-label {
  opacity: 0.8;
  margin-right: 8px;
}

.deadline-value {
  font-weight: 500;
}

.expired-tag {
  margin-left: 8px;
  padding: 2px 6px;
  background-color: #dc3545;
  color: white;
  border-radius: 4px;
  font-size: 12px;
}

.time-remaining {
  text-align: center;
}

.remaining-text {
  font-size: 14px;
  font-weight: bold;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 12px;
}

.vote-options {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.results-note {
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  
  &.voted {
    background-color: #e3f2fd;
    border-color: #2196f3;
  }
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-indicator {
  flex-shrink: 0;
}

.radio-indicator {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.selected {
    border-color: #2196f3;
  }
}

.radio-dot {
  width: 8px;
  height: 8px;
  background-color: #2196f3;
  border-radius: 50%;
}

.checkbox-indicator {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.selected {
    background-color: #2196f3;
    border-color: #2196f3;
  }
}

.option-text {
  font-size: 15px;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

.option-result {
  margin-left: 32px;
}

.result-bar {
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
}

.result-fill {
  height: 100%;
  background: linear-gradient(90deg, #6f42c1 0%, #007bff 100%);
  transition: width 0.5s ease;
}

.result-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vote-count, .vote-percentage {
  font-size: 12px;
  color: #666;
}

.vote-stats {
  padding: 16px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #6f42c1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.leading-option {
  text-align: center;
  padding: 12px;
  background-color: white;
  border-radius: 8px;
  border: 2px solid #6f42c1;
}

.leading-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.leading-text {
  font-size: 16px;
  font-weight: bold;
  color: #6f42c1;
  margin-right: 8px;
}

.leading-votes {
  font-size: 14px;
  color: #666;
}

.target-section {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.target-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.action-section {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.view-details-btn {
  margin-top: 12px;
}
</style>