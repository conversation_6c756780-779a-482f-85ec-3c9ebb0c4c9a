<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.haolinkyou.mapper.UserMapper">
    <select id="selectByName" resultType="com.haolinkyou.entity.Users" parameterType="String">
        select * from users where username like concat('%', #{name}, '%') and is_deleted=0
    </select>
<!-- 两个参数就不需要置顶parameterType-->
    <select id="selectByNamePage" resultType="com.haolinkyou.entity.Users">
            select * from users where username like concat('%', #{name}, '%') and is_deleted=0
    </select>
    <delete id="delByUserId">
        delete
        from users
        where user_id = #{userId,jdbcType=NUMERIC}
    </delete>

    <!-- 获取用户发布的动态数量 -->
    <select id="getUserPostCount" resultType="int">
        SELECT COUNT(*)
        FROM posts
        WHERE user_id = #{userId}
          AND del_flag = 0
    </select>

    <!-- 获取用户收藏的帖子数量 -->
    <select id="getUserCollectCount" resultType="int">
        SELECT COUNT(*)
        FROM user_collects
        WHERE user_id = #{userId}
          AND del_flag = 0
    </select>
</mapper>
