package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.api.vo.CommentsVo;
import com.haolinkyou.entity.Comments;
import com.haolinkyou.event.CommentCountUpdateEvent;
import com.haolinkyou.mapper.CommentsMapper;
import com.haolinkyou.service.ICommentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CommentsServiceImpl extends ServiceImpl<CommentsMapper, Comments> implements ICommentsService {
    @Autowired
    private CommentsMapper commentsMapper;

    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    @Override
    public List<CommentsVo> getCommentsByPostId(Integer postId) {
        List<CommentsVo> allComments = commentsMapper.getCommentsByPostId(postId);

        // 1. 构建 commentId -> comment 的映射
        Map<Long, CommentsVo> commentMap = allComments.stream()
                .collect(Collectors.toMap(CommentsVo::getCommentId, Function.identity()));

        // 2. 找到所有顶级评论
        List<CommentsVo> rootComments = new ArrayList<>();
        for (CommentsVo comment : allComments) {
            if (comment.getParentCommentId() == null || comment.getParentCommentId() == 0L) {
                rootComments.add(comment);
            }
        }

        // 3. 将所有非顶级评论直接归到其顶级评论的 replies 下
        for (CommentsVo comment : allComments) {
            if (comment.getParentCommentId() != null && comment.getParentCommentId() != 0L) {
                // 找到顶级父评论
                CommentsVo topParent = comment;
                while (topParent.getParentCommentId() != null && topParent.getParentCommentId() != 0L) {
                    CommentsVo parent = commentMap.get(topParent.getParentCommentId());
                    if (parent == null) break;
                    topParent = parent;
                }
                // 只要不是自己回复自己，且顶级评论存在
                if (topParent != null && !topParent.getCommentId().equals(comment.getCommentId())) {
                    topParent.getReplies().add(comment);
                }
            }
        }

        // 4. 顶级评论的 replies 里可能有多余的嵌套，需要清理（只保留二级）
        for (CommentsVo root : rootComments) {
            for (CommentsVo reply : root.getReplies()) {
                reply.getReplies().clear();
            }
        }

         // 5. 排序：顶级评论按commentId降序，replies按commentId正序
        rootComments.sort((a, b) -> Long.compare(b.getCommentId(), a.getCommentId()));
        for (CommentsVo root : rootComments) {
            root.getReplies().sort((a, b) -> Long.compare(a.getCommentId(), b.getCommentId()));
        }

        return rootComments;
    }

    @Override
    public List<Comments> listAll() {
        return this.list();
    }

    @Override
    public boolean addComment(Comments comment) {
        // 参数验证
        if (comment == null) {
            throw new IllegalArgumentException("评论信息不能为空");
        }
        if (comment.getPostId() == null) {
            throw new IllegalArgumentException("帖子ID不能为空");
        }
        if (comment.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (!StringUtils.hasText(comment.getContent())) {
            throw new IllegalArgumentException("评论内容不能为空");
        }
        
        // 设置默认值
        if (comment.getStatus() == null) {
            comment.setStatus(1); // 默认状态为已审核
        }
        if (comment.getLikeCount() == null) {
            comment.setLikeCount(0); // 默认点赞数为0
        }
        if (comment.getReplyCount() == null) {
            comment.setReplyCount(0); // 默认回复数为0
        }
        // parentCommentId 为 null 表示顶级评论，数据库允许为 NULL
        
        // 保存评论
        boolean result = this.save(comment);

        // 如果保存成功，发布评论数更新事件
        if (result) {
            eventPublisher.publishEvent(new CommentCountUpdateEvent(this, comment.getPostId()));
        }

        return result;
    }

    @Override
    public boolean editComment(Comments comment) {
        // 参数验证
        if (comment == null || comment.getId() == null) {
            throw new IllegalArgumentException("评论ID不能为空");
        }
        if (!StringUtils.hasText(comment.getContent())) {
            throw new IllegalArgumentException("评论内容不能为空");
        }
        
        return this.updateById(comment);
    }

    @Override
    public boolean deleteComment(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("评论ID不能为空");
        }
        
        // 获取评论信息，用于更新帖子评论数
        Comments comment = this.getById(id);
        if (comment == null) {
            return false;
        }
        
        // 使用逻辑删除而不是物理删除，保持与统计逻辑一致
        comment.setDelFlag(1);
        boolean result = this.updateById(comment);

        // 如果删除成功，发布评论数更新事件
        if (result) {
            eventPublisher.publishEvent(new CommentCountUpdateEvent(this, comment.getPostId()));
        }

        return result;
    }
}
