package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.entity.UserRoles;
import com.haolinkyou.mapper.UserRolesMapper;
import com.haolinkyou.service.UserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRolesMapper, UserRoles> implements UserRoleService {

    @Autowired
    private UserRolesMapper userRolesMapper;

    @Override
    public UserRoles getRoleByCode(String roleCode) {
        return userRolesMapper.getRoleByCode(roleCode);
    }

    @Override
    public List<UserRoles> getAllActiveRoles() {
        return userRolesMapper.getAllActiveRoles();
    }

    @Override
    public boolean hasPermission(String userRole, String permission) {
        if (userRole == null || permission == null) {
            return false;
        }

        UserRoles role = getRoleByCode(userRole);
        if (role == null || role.getPermissions() == null) {
            return false;
        }

        return role.getPermissions().contains(permission);
    }

    @Override
    public List<String> getUserPermissions(String userRole) {
        if (userRole == null) {
            return new ArrayList<>();
        }

        UserRoles role = getRoleByCode(userRole);
        if (role == null || role.getPermissions() == null) {
            return new ArrayList<>();
        }

        return role.getPermissions();
    }

    @Override
    public boolean isAdmin(String userRole) {
        // 管理员角色：admin, community
        return "admin".equals(userRole) || "community".equals(userRole);
    }
}
