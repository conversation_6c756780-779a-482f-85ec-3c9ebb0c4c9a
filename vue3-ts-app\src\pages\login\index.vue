<!--
 * @Author: Rock
 * @Date: 2025-04-15 20:56:56
 * @LastEditors: Rock
 * @LastEditTime: 2025-07-13 21:14:16
 * @Description: 
-->
<template>
  <view class="login-page">
    <view class="login-content">
      <!-- #ifdef MP-WEIXIN -->
      <up-button class="login-btn" text="微信一键登录" color="linear-gradient(to right,  #4396f7 0%,
        #00e2fa 80%,#00e2fa 100%)" shape="circle" open-type="getPhoneNumber"
        @getphonenumber="onGetphonenumber"></up-button>
      <up-divider text="其它登录方式" textSize="12px"></up-divider>
      <!-- #endif -->

      <!-- 模拟用户登录区域 -->
      <view class="mock-login-section">
        <text class="section-title">选择测试用户登录</text>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
          <up-loading-icon mode="spinner"></up-loading-icon>
          <text class="loading-text">加载用户列表中...</text>
        </view>

        <!-- 用户列表 -->
        <view v-else class="user-list">
          <view
            v-for="user in testUsers"
            :key="user.id"
            class="user-item"
            @click="handleUserLogin(user)"
          >
            <up-avatar
              :src="user.avatar"
              :size="50"
              shape="circle"
              class="user-avatar"
            ></up-avatar>

            <view class="user-info">
              <text class="user-nickname">{{ user.nickname }}</text>
              <text class="user-mobile">{{ user.mobile }}</text>
              <text class="user-role">{{ getRoleDisplayName(user.userRole) }}</text>
              <text v-if="user.isVerified" class="user-verified">✓ 已认证</text>
            </view>

            <view class="user-actions">
              <up-icon name="arrow-right" size="16" color="#999"></up-icon>
            </view>
          </view>
        </view>

        <!-- 刷新按钮 -->
        <up-button
          text="刷新用户列表"
          type="info"
          size="small"
          :loading="loading"
          @click="fetchTestUsers"
          class="refresh-btn"
        ></up-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { postLoginWxMinAPI, postLoginWxMinSimpleAPI, getTestUsersAPI, type TestUser } from '@/services/login'
import { useMemberStore } from '@/stores'

// 响应式数据
const loading = ref<boolean>(false)
const testUsers = ref<TestUser[]>([])
const memberStore = useMemberStore()


// 获取测试用户列表
const fetchTestUsers = async (): Promise<void> => {
  loading.value = true
  try {
    const res = await getTestUsersAPI()
    if (res.success) {
      testUsers.value = res.data || []
      console.log('获取测试用户列表成功:', testUsers.value)
    } else {
      uni.showToast({
        title: '获取用户列表失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 角色显示名称映射
const getRoleDisplayName = (role: string): string => {
  const roleMap: Record<string, string> = {
    'guest': '游客',
    'owner': '业主',
    'tenant': '租户',
    'property': '物业',
    'committee': '业委会',
    'community': '社区管理员',
    'admin': '系统管理员'
  }
  return roleMap[role] || role
}

// 处理用户登录
const handleUserLogin = async (user: TestUser): Promise<void> => {
  try {
    console.log('选择用户登录:', user)

    // 显示加载提示
    uni.showLoading({
      title: '登录中...'
    })

    // 调用登录API
    const response = await postLoginWxMinSimpleAPI(user.mobile)
    console.log('登录API响应:', response)

    if (response.success && response.data) {
      // 构建用户信息
      const userInfo = {
        id: response.data.id,
        account: response.data.account,
        nickname: response.data.nickname || user.nickname,
        avatar: response.data.avatar || user.avatar,
        mobile: response.data.mobile,
        token: response.data.token,
        authType: response.data.authType,
        isAdmin: response.data.isAdmin,
        userRole: response.data.userRole || user.userRole,
        postCount: response.data.postCount || 0,
        favoriteCount: response.data.favoriteCount || 0,
        pointCount: response.data.pointCount || 0
      }

      // 保存用户信息到store
      memberStore.setProfile(userInfo as any)

      // 隐藏加载提示
      uni.hideLoading()

      // 显示登录成功提示
      uni.showToast({
        title: `登录成功！欢迎 ${userInfo.nickname}`,
        icon: 'success'
      })

      console.log('登录成功，用户信息:', userInfo)

      // 发送登录成功事件
      uni.$emit('userLogin')

      // 跳转到"我的"页面
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/my/index',
          success: () => {
            console.log('成功跳转到我的页面')
            uni.$emit('refreshMyPage')
          }
        })
      }, 1000)
    } else {
      uni.hideLoading()
      throw new Error('登录API调用失败: ' + (response.message || '未知错误'))
    }
  } catch (error: any) {
    uni.hideLoading()
    console.error('用户登录失败:', error)
    uni.showToast({
      title: '登录失败: ' + (error.message || '请重试'),
      icon: 'none'
    })
  }
}

// 获取code登录凭证
const wxCode = ref(''); // 存储微信登录的code

// 页面加载时获取用户列表
onMounted(() => {
  fetchTestUsers()

  // 只在小程序环境中获取微信登录code
  // #ifdef MP-WEIXIN
  uni.login().then(({ code }) => {
    wxCode.value = code
  }).catch((error) => {
    console.error('获取微信登录code失败:', error)
  })
  // #endif

  // H5环境中不需要获取微信code
  // #ifdef H5
  console.log('H5环境，跳过微信登录code获取')
  // #endif
})




// 正式环境获取手机号（微信小程序专用）
const onGetphonenumber: UniHelper.ButtonOnGetphonenumber = async (ev) => {
  // 获取用户手机号
  if (ev.detail.errMsg === 'getPhoneNumber:ok') {
    // 发送请求获取用户手机号
    uni.request({
      url: 'https://example.com/getPhoneNumber', // 替换为你的后端接口地址
      method: 'POST',
      data: {
        code: wxCode.value, // 微信登录的code
        encryptedData: ev.detail.encryptedData, // 加密数据
        iv: ev.detail.iv // 初始向量
      },
      success: (res) => {
        // 处理返回的数据
        console.log(res.data);
      },
      fail: (err) => {
        // 处理错误
        console.error(err);
      }
    });
  } else {
    // 用户拒绝授权
    uni.showToast({ title: ev.detail.errMsg, icon: 'none' });
  }
}
</script>

<style lang="scss">
.login-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .login-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 90vw;
    max-width: 400px;
    background: white;
    border-radius: 20px;
    padding: 30px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }
}

.mock-login-section {
  width: 100%;
  margin-top: 20px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 20px;
    display: block;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 20px;

    .loading-text {
      margin-top: 10px;
      font-size: 14px;
      color: #666;
    }
  }

  .user-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
  }

  .user-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background: #e3f2fd;
      border-color: #2196f3;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .user-avatar {
    margin-right: 15px;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .user-info {
    flex: 1;
    display: flex;
    flex-direction: column;

    .user-nickname {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 4px;
    }

    .user-mobile {
      font-size: 14px;
      color: #666;
      margin-bottom: 2px;
    }

    .user-role {
      font-size: 12px;
      color: #4396f7;
      font-weight: 500;
      margin-bottom: 2px;
    }

    .user-verified {
      font-size: 12px;
      color: #4caf50;
      font-weight: 500;
    }
  }

  .user-actions {
    display: flex;
    align-items: center;
  }

  .refresh-btn {
    margin-top: 10px;
    width: 100%;
  }
}

.login-btn {
  margin: 10px 0;
  width: 100%;
}
</style>