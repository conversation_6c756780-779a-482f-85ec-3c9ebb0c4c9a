package com.haolinkyou.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

//处理器
@Component
public class MyMetaHandler implements MetaObjectHandler {

    //在插入数据时填充字段
    @Override
    public void insertFill(MetaObject metaObject) {
        //设置创建时间字段
        setFieldValByName("createdTime", new Date(), metaObject);
        //设置更新时间字段
        setFieldValByName("updatedTime", new Date(), metaObject);
    }

    //在更新数据时填充字段
    @Override
    public void updateFill(MetaObject metaObject) {
        //设置更新时间字段
        setFieldValByName("updatedTime", new Date(), metaObject);
    }
}
