package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 积分商品实体类
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@TableName("products")
public class Products extends BaseEntity {

    private String name;

    private String description;

    private String image;

    private Integer points;

    private Integer stock;

    private Integer salesCount;

    private Integer sortOrder;

    private Integer status;

    // Constructors
    public Products() {}

    // Getters and Setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getImage() { return image; }
    public void setImage(String image) { this.image = image; }

    public Integer getPoints() { return points; }
    public void setPoints(Integer points) { this.points = points; }

    public Integer getStock() { return stock; }
    public void setStock(Integer stock) { this.stock = stock; }

    public Integer getSalesCount() { return salesCount; }
    public void setSalesCount(Integer salesCount) { this.salesCount = salesCount; }

    public Integer getSortOrder() { return sortOrder; }
    public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }

    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }

    @Override
    public String toString() {
        return "Products{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", image='" + image + '\'' +
                ", points=" + points +
                ", stock=" + stock +
                ", salesCount=" + salesCount +
                ", sortOrder=" + sortOrder +
                ", status=" + status +
                ", createdTime=" + getCreatedTime() +
                ", updatedTime=" + getUpdatedTime() +
                ", delFlag=" + getDelFlag() +
                '}';
    }
}