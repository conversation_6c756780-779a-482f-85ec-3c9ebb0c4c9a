package com.haolinkyou.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haolinkyou.entity.AuthApplications;

public interface AuthApplicationsService extends IService<AuthApplications> {

    /**
     * 根据用户ID获取认证申请记录
     * @param userId 用户ID
     * @return 认证申请记录
     */
    AuthApplications getByUserId(Long userId);

    /**
     * 检查用户是否已有认证申请记录
     * @param userId 用户ID
     * @return 是否已有记录
     */
    boolean hasApplication(Long userId);

    /**
     * 提交认证申请
     * @param application 认证申请数据
     * @return 是否成功
     */
    boolean submitApplication(AuthApplications application);

    /**
     * 更新认证申请
     * @param application 认证申请数据
     * @return 是否成功
     */
    boolean updateApplication(AuthApplications application);

    /**
     * 获取原始的 documents 字段数据（用于调试）
     * @param userId 用户ID
     * @return documents 字段的原始字符串
     */
    String getRawDocumentsByUserId(Long userId);
}
