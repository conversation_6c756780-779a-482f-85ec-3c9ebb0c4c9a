<template>
  <view class="vote-participate-page">
    <!-- 页面头部 -->
    <up-navbar
      title="参与投票"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 内容区域 -->
    <view class="content-area" :style="{ marginTop: mainContentPaddingTop }">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <up-loading-icon mode="spinner" size="40"></up-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 投票内容 -->
      <view v-else-if="voteData" class="vote-container">
        <!-- 投票标题和描述 -->
        <view class="vote-header">
          <view class="vote-title">{{ voteData.title }}</view>
          <view v-if="voteData.description" class="vote-description">
            {{ voteData.description }}
          </view>
          <view class="vote-info">
            <text class="info-item">发起人：{{ voteData.creatorName }}</text>
            <text class="info-item" v-if="voteData.endTime">
              截止时间：{{ formatDateTime(voteData.endTime) }}
            </text>
            <text class="info-item">
              参与人数：{{ voteData.participantCount || 0 }}
            </text>
            <text class="info-item">
              投票方式：{{ voteData.voteType === 'single' ? '单选' : '多选' }}
            </text>
            <text class="info-item">
              {{ voteData.isAnonymous ? '匿名投票' : '实名投票' }}
            </text>
          </view>
        </view>

        <!-- 已投票提示 -->
        <view v-if="voteData.hasVoted" class="voted-notice">
          <up-icon name="checkmark-circle-fill" size="24" color="#52c41a"></up-icon>
          <text class="notice-text">您已参与此投票，以下是您的选择。目前共有 {{ voteData.participantCount }} 人参与投票</text>
        </view>

        <!-- 投票选项 -->
        <view class="options-container">
          <view class="options-title">{{ voteData.hasVoted ? '您的投票选择：' : '请选择您的投票选项：' }}</view>
          
          <!-- 单选投票 -->
          <view v-if="voteData.voteType === 'single'" class="vote-options">
            <up-radio-group
              v-model="selectedOptions"
              placement="column"
              @change="handleOptionChange"
              :disabled="voteData.hasVoted"
            >
              <view
                v-for="(option, index) in voteData.options"
                :key="option.id || index"
                class="option-item"
              >
                <up-radio
                  :name="option.id || index"
                  :label="option.text"
                  :disabled="voteData.hasVoted"
                  customStyle="width: 100%;"
                ></up-radio>
                <view v-if="option.description" class="option-description">
                  {{ option.description }}
                </view>
              </view>
            </up-radio-group>
          </view>

          <!-- 多选投票 -->
          <view v-else class="vote-options">
            <up-checkbox-group
              v-model="selectedOptions"
              placement="column"
              @change="handleOptionChange"
              :disabled="voteData.hasVoted"
            >
              <view
                v-for="(option, index) in voteData.options"
                :key="option.id || index"
                class="option-item"
              >
                <up-checkbox
                  :name="option.id || index"
                  :label="option.text"
                  :disabled="voteData.hasVoted"
                  customStyle="width: 100%;"
                ></up-checkbox>
                <view v-if="option.description" class="option-description">
                  {{ option.description }}
                </view>
              </view>
            </up-checkbox-group>
          </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-container">
          <up-button
            v-if="!voteData.hasVoted"
            type="primary"
            size="large"
            @click="handleSubmit"
            :loading="submitting"
            :disabled="!canSubmit"
            customStyle="background-color: #5677fc; border-radius: 12px;"
          >
            {{ submitting ? '提交中...' : '提交投票' }}
          </up-button>
          
          <!-- 查看结果按钮 -->
          <up-button
            type="default"
            size="large"
            @click="handleViewResults"
            customStyle="margin-top: 12px; border-radius: 12px;"
          >
            查看投票结果
          </up-button>
        </view>
      </view>

      <!-- 错误状态 -->
      <view v-else class="error-container">
        <up-icon name="warning-fill" size="60" color="#ff6b6b"></up-icon>
        <text class="error-text">{{ errorMessage || '加载失败，请重试' }}</text>
        <up-button
          type="primary"
          size="small"
          @click="loadVoteData"
          customStyle="margin-top: 20px;"
        >
          重新加载
        </up-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useSafeArea } from '@/utils/safeArea'
import { get, post } from '@/utils/http'
import { useMemberStore } from '@/stores'
import { formatDateTime } from '@/utils/timeFormat'

// 安全区域
const { mainContentPaddingTop } = useSafeArea()

// 用户状态
const memberStore = useMemberStore()

// 页面参数
const postId = ref<number>(0)

// 页面状态
const loading = ref(true)
const submitting = ref(false)
const errorMessage = ref('')

// 投票数据
const voteData = ref<any>(null)
const selectedOptions = ref<any>([])

// 获取页面参数
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.id) {
    postId.value = parseInt(options.id)
    loadVoteData()
  } else {
    errorMessage.value = '参数错误'
    loading.value = false
  }
})

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 加载投票数据
const loadVoteData = async () => {
  loading.value = true
  errorMessage.value = ''

  try {
    // 首先获取投票详情和用户参与状态
    const voteDetailResponse = await get(`/vote/detail?postId=${postId.value}`)
    
    if (voteDetailResponse.success && voteDetailResponse.data) {
      const voteDetail = voteDetailResponse.data
      const vote = voteDetail.vote
      
      // 解析选项数据
      let options = []
      if (typeof vote.options === 'string') {
        try {
          options = JSON.parse(vote.options)
        } catch (error) {
          console.error('解析选项数据失败:', error)
        }
      } else if (vote.options) {
        options = vote.options
      }

      // 构建投票数据
      voteData.value = {
        id: vote.id,
        title: vote.title,
        description: vote.description,
        creatorName: '系统', // 可以从帖子信息获取
        endTime: vote.endTime,
        participantCount: voteDetail.participantCount || 0,
        options: options.map((option, index) => ({
          id: index,
          text: typeof option === 'string' ? option : option.text
        })),
        voteType: vote.voteType,
        isAnonymous: vote.isAnonymous,
        hasVoted: voteDetail.hasVoted
      }

      // 如果用户已投票，回显选择
      if (voteDetail.hasVoted && voteDetail.userSelectedOptions) {
        if (voteData.value.voteType === 'single') {
          selectedOptions.value = voteDetail.userSelectedOptions[0] || ''
        } else {
          selectedOptions.value = voteDetail.userSelectedOptions || []
        }
      } else {
        // 初始化选择状态
        if (voteData.value.voteType === 'single') {
          selectedOptions.value = ''
        } else {
          selectedOptions.value = []
        }
      }

      console.log('投票数据加载成功:', voteData.value)
    } else {
      throw new Error(voteDetailResponse.message || '获取投票数据失败')
    }
  } catch (error) {
    console.error('加载投票数据失败:', error)
    errorMessage.value = error.message || '网络错误，请重试'
  } finally {
    loading.value = false
  }
}

// 处理选项变化
const handleOptionChange = (value: any) => {
  console.log('选项变化:', value)
  selectedOptions.value = value
}

// 检查是否可以提交
const canSubmit = computed(() => {
  if (!voteData.value || voteData.value.hasVoted) return false

  if (voteData.value.voteType === 'single') {
    return selectedOptions.value !== '' && selectedOptions.value !== null
  } else {
    return Array.isArray(selectedOptions.value) && selectedOptions.value.length > 0
  }
})

// 提交投票
const handleSubmit = async () => {
  // 检查登录状态
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  // 检查是否已投票
  if (voteData.value.hasVoted) {
    uni.showToast({
      title: '您已投票过了',
      icon: 'none'
    })
    return
  }

  // 检查是否可以提交
  if (!canSubmit.value) {
    uni.showToast({
      title: '请选择投票选项',
      icon: 'none'
    })
    return
  }

  submitting.value = true

  try {
    // 构建提交数据
    const submitData = {
      postId: postId.value,
      voteId: voteData.value.id,
      selectedOptions: Array.isArray(selectedOptions.value) 
        ? selectedOptions.value 
        : [selectedOptions.value]
    }

    console.log('提交投票数据:', submitData)

    const response = await post('/vote/submit', submitData)

    if (response.success) {
      uni.showToast({
        title: '投票成功',
        icon: 'success'
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      throw new Error(response.message || '投票失败')
    }
  } catch (error) {
    console.error('提交投票失败:', error)
    uni.showToast({
      title: error.message || '投票失败，请重试',
      icon: 'none'
    })
  } finally {
    submitting.value = false
  }
}

// 查看投票结果
const handleViewResults = () => {
  uni.navigateTo({
    url: `/pages/vote/results?id=${postId.value}`
  })
}
</script>

<style scoped lang="scss">
.vote-participate-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.content-area {
  padding: 0 20px 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #666;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-text {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}

.vote-container {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.vote-header {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.vote-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.4;
}

.vote-description {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
}

.vote-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-item {
  font-size: 12px;
  color: #999;
}

.options-container {
  padding: 24px;
}

.options-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.vote-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-item {
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s ease;

  &:hover {
    border-color: #5677fc;
    background-color: #f0f8ff;
  }
}

.option-description {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.voted-notice {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
  margin-bottom: 20px;
}

.notice-text {
  margin-left: 8px;
  font-size: 14px;
  color: #52c41a;
}

.submit-container {
  padding: 24px;
  background-color: #f8f9fa;
}
</style>