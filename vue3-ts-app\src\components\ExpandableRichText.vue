<template>
  <view class="expandable-rich-text">
    <view class="content-container" :class="{ expanded: isExpanded }">
      <RichTextRenderer
        :content="displayContent"
        :selectable="selectable"
        @linkClick="handleLinkClick"
        @imageClick="handleImageClick"
      />
      
      <!-- 渐变遮罩 -->
      <view v-if="showGradient" class="gradient-mask"></view>
    </view>
    
    <!-- 展开/收起按钮 -->
    <view v-if="needExpand" class="expand-button" @click="toggleExpand">
      <text class="expand-text">{{ isExpanded ? '收起' : '展开' }}</text>
      <up-icon 
        :name="isExpanded ? 'arrow-up' : 'arrow-down'" 
        size="14" 
        color="#007bff"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import RichTextRenderer from './RichTextRenderer.vue'

interface Props {
  content?: string // 富文本内容，可选
  maxLines?: number // 最大显示行数
  maxLength?: number // 最大字符长度
  selectable?: boolean // 是否可选择文本
}

const props = withDefaults(defineProps<Props>(), {
  content: '', // 默认为空字符串
  maxLines: 3,
  maxLength: 200,
  selectable: true
})

const emit = defineEmits<{
  linkClick: [url: string]
  imageClick: [src: string]
  expand: [expanded: boolean]
}>()

const isExpanded = ref(false)

// 是否需要展开功能
const needExpand = computed(() => {
  const content = props.content || ''
  if (!content) return false
  
  // 根据字符长度判断
  const textContent = stripHtml(content)
  return textContent.length > props.maxLength
})

// 显示的内容
const displayContent = computed(() => {
  const content = props.content || ''
  if (!content) return ''
  
  if (isExpanded.value || !needExpand.value) {
    return content
  }
  
  // 截取内容
  const textContent = stripHtml(content)
  if (textContent.length <= props.maxLength) {
    return content
  }
  
  // 简单截取，保持HTML结构
  return truncateHtml(content, props.maxLength)
})

// 是否显示渐变遮罩
const showGradient = computed(() => {
  return needExpand.value && !isExpanded.value
})

// 移除HTML标签，获取纯文本
const stripHtml = (html: string): string => {
  if (!html) return ''
  return html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim()
}

// 智能截取HTML内容
const truncateHtml = (html: string, maxLength: number): string => {
  if (!html) return ''
  const textContent = stripHtml(html)
  if (textContent.length <= maxLength) {
    return html
  }
  
  // 简单的截取策略：按字符数截取
  let currentLength = 0
  let result = ''
  let inTag = false
  
  for (let i = 0; i < html.length; i++) {
    const char = html[i]
    
    if (char === '<') {
      inTag = true
      result += char
    } else if (char === '>') {
      inTag = false
      result += char
    } else if (inTag) {
      result += char
    } else {
      if (currentLength >= maxLength) {
        break
      }
      result += char
      if (char !== ' ' && char !== '\n' && char !== '\t') {
        currentLength++
      }
    }
  }
  
  return result + '...'
}

// 切换展开状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
  emit('expand', isExpanded.value)
  
  // 展开时触发轻微震动
  if (isExpanded.value) {
    uni.vibrateShort({
      type: 'light'
    })
  }
}

// 处理链接点击
const handleLinkClick = (url: string) => {
  // 检查是否是外部链接
  if (url.startsWith('http://') || url.startsWith('https://')) {
    uni.showModal({
      title: '打开外部链接',
      content: `即将打开：${url}`,
      confirmText: '打开',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 在小程序中打开外部链接
          uni.navigateTo({
            url: `/pages/webview/index?url=${encodeURIComponent(url)}`
          })
        }
      }
    })
  } else {
    // 内部链接直接跳转
    uni.navigateTo({
      url: url
    })
  }
  
  emit('linkClick', url)
}

// 处理图片点击
const handleImageClick = (src: string) => {
  // 预览图片
  uni.previewImage({
    urls: [src],
    current: src
  })
  
  emit('imageClick', src)
}
</script>

<style scoped lang="scss">
.expandable-rich-text {
  position: relative;
}

.content-container {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:not(.expanded) {
    max-height: calc(1.6em * 3); // 3行的高度
  }
  
  &.expanded {
    max-height: none;
  }
}

.gradient-mask {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(transparent, white);
  pointer-events: none;
}

.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 0;
  margin-top: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.expand-text {
  font-size: 14px;
  color: #007bff;
  font-weight: 500;
}
</style>