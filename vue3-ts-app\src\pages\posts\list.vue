<template>
  <view class="posts-list-page">
    <!-- 搜索和筛选栏 -->
    <view class="search-filter-bar" :style="{ paddingTop: safeAreaTop + 'px' }">
      <view class="search-section">
        <up-search
          v-model="searchKeyword"
          placeholder="搜索帖子标题、内容..."
          :show-action="false"
          bg-color="#f5f5f5"
          @search="handleSearch"
          @clear="handleClearSearch"
          @input="debouncedSearch"
          @focus="handleSearchFocus"
        />
      </view>
      
      <view class="filter-section">
        <view class="filter-tabs">
          <view
            v-for="category in categories"
            :key="category.id"
            class="filter-tab"
            :class="{ active: selectedCategoryId === category.id }"
            @click="handleCategoryFilter(category.id)"
          >
            <text class="category-name">{{ category.categoryName }}</text>
            <view v-if="category.postCount !== undefined" class="category-count">
              {{ category.postCount }}
            </view>
          </view>
        </view>
        
        <view class="sort-filter">
          <up-dropdown>
            <up-dropdown-item
              v-model="sortType"
              title="排序"
              :options="sortOptions"
              @change="handleSortChange"
            />
          </up-dropdown>
        </view>
      </view>
    </view>

    <!-- 刷新提示 -->
    <view v-if="refreshing" class="refresh-indicator">
      <view class="refresh-content">
        <up-loading-icon mode="spinner" size="16" color="#007bff" />
        <text class="refresh-text">正在刷新...</text>
      </view>
    </view>

    <!-- 帖子列表 -->
    <view class="posts-list">
      <up-list
        @scrolltolower="debouncedLoadMore"
        :show-scrollbar="false"
        :refresher-enabled="true"
        :refresher-triggered="refreshing"
        @refresherrefresh="handleRefresh"
        :lower-threshold="100"
      >
        <view v-if="postsList.length === 0 && !loading" class="empty-state">
          <up-empty
            mode="list"
            text="暂无帖子"
            textColor="#999"
            textSize="16"
          />
          <view class="retry-button" @click="handleRefresh">
            <up-button
              type="primary"
              size="small"
              plain
              text="重新加载"
            />
          </view>
        </view>
        
        <view v-for="(post, index) in postsList" :key="post.id || index" class="post-item">
          <PostListItem
            :post="post"
            @click="handlePostClick"
            @like="handleLike"
            @collect="handleCollect"
            @delete="handleDelete"
            @share="handleShare"
          />
        </view>
        
        <!-- 加载更多 -->
        <view v-if="showLoadingMore" class="loading-more">
          <up-loading-icon mode="spinner" size="20" color="#007bff" />
          <text class="loading-text">正在加载更多...</text>
          <view v-if="totalCount > 0" class="loading-progress">
            <text class="progress-text">{{ loadedCount }}/{{ totalCount }}</text>
            <view class="progress-bar">
              <view 
                class="progress-fill" 
                :style="{ width: (loadedCount / totalCount * 100) + '%' }"
              ></view>
            </view>
          </view>
        </view>
        
        <!-- 没有更多数据 -->
        <view v-if="showNoMore" class="no-more">
          <up-divider text="没有更多了" textSize="12px" />
          <view class="load-count-info">
            <text class="count-text">已加载 {{ postsList.length }} 条内容</text>
          </view>
        </view>
        
        <!-- 加载失败重试 -->
        <view v-if="!loading && hasMore && postsList.length > 0" class="load-more-retry">
          <up-button
            type="primary"
            size="small"
            plain
            text="点击加载更多"
            @click="loadMore"
          />
        </view>
      </up-list>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onShow, onHide, onUnmounted, nextTick } from 'vue'
import { useSafeArea } from '@/utils/safeArea'
import { get } from '@/utils/http'
import { useMemberStore } from '@/stores'
import PostListItem from '@/components/PostListItem.vue'
import { toggleLike, toggleCollect, deletePost } from '@/services/likeCollectService'
import { getCategoryListAPI, formatCategoriesForTabs } from '@/services/categoryService'
import { postStateManager } from '@/utils/postStateManager'
import type { PostsList, Categories, Result } from '@/types'

const { safeAreaTop } = useSafeArea()
const memberStore = useMemberStore()

// 搜索和筛选状态
const searchKeyword = ref('')
const selectedCategoryId = ref<number>(0)
const sortType = ref('latest')

// 列表数据
const postsList = ref<PostsList[]>([])
const categories = ref<Categories[]>([])
const loading = ref(false)
const refreshing = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = 10
const totalCount = ref(0) // 总数据量
const loadedCount = ref(0) // 已加载数量
const lastRefreshTime = ref(0) // 最后刷新时间

// 排序选项
const sortOptions = [
  { label: '最新发布', value: 'latest' },
  { label: '最多点赞', value: 'likes' },
  { label: '最多评论', value: 'comments' },
  { label: '最多收藏', value: 'collects' }
]

// 页面加载
onMounted(() => {
  // 恢复筛选状态
  const savedCategoryId = uni.getStorageSync('selectedCategoryId')
  if (savedCategoryId !== undefined && savedCategoryId !== null) {
    selectedCategoryId.value = savedCategoryId
  }
  
  // 恢复最后刷新时间
  const savedRefreshTime = uni.getStorageSync('last_refresh_time')
  if (savedRefreshTime) {
    lastRefreshTime.value = savedRefreshTime
  }
  
  loadCategories()
  loadPostsList()
  
  // 检查是否需要提示刷新
  checkRefreshPrompt()
  
  // 监听帖子状态变更
  postStateManager.onStateChange(handlePostStateChange)
})

// 页面卸载时清理监听
onUnmounted(() => {
  postStateManager.offStateChange(handlePostStateChange)
})

// 处理帖子状态变更
const handlePostStateChange = (data: { postId: number; state: any }) => {
  const postIndex = postsList.value.findIndex(post => post.id === data.postId)
  if (postIndex !== -1) {
    const post = postsList.value[postIndex]
    post.isLiked = data.state.isLiked
    post.isCollected = data.state.isCollected
    post.likeCount = data.state.likeCount
    post.collectCount = data.state.collectCount
    post.commentCount = data.state.commentCount
    post.viewCount = data.state.viewCount
  }
}

// 检查刷新提示
const checkRefreshPrompt = () => {
  if (lastRefreshTime.value === 0) return
  
  const now = Date.now()
  const timeDiff = now - lastRefreshTime.value
  const oneHour = 60 * 60 * 1000
  
  // 如果超过1小时未刷新，显示提示
  if (timeDiff > oneHour) {
    setTimeout(() => {
      uni.showModal({
        title: '内容更新提示',
        content: '您已经很久没有刷新了，是否要获取最新内容？',
        confirmText: '刷新',
        cancelText: '稍后',
        success: (res) => {
          if (res.confirm) {
            handleRefresh()
          }
        }
      })
    }, 2000) // 延迟2秒显示，避免影响页面加载
  }
}

// 页面显示时刷新
onShow(() => {
  // 检查是否需要刷新
  const app = getApp()
  if (app.globalData?.needRefreshPostsList) {
    app.globalData.needRefreshPostsList = false
    clearScrollPosition() // 刷新时清除滚动位置
    handleRefresh()
  } else {
    // 检查是否需要智能刷新
    checkSmartRefresh()
    // 恢复滚动位置
    restoreScrollPosition()
  }
})

// 智能刷新检测
const checkSmartRefresh = () => {
  if (lastRefreshTime.value === 0) return
  
  const now = Date.now()
  const timeDiff = now - lastRefreshTime.value
  const fiveMinutes = 5 * 60 * 1000
  
  // 如果超过5分钟未刷新，且列表不为空，静默刷新
  if (timeDiff > fiveMinutes && postsList.value.length > 0) {
    console.log('执行智能刷新')
    // 静默刷新，不显示loading状态
    loadPostsList(true).then(() => {
      // 如果有新内容，显示提示
      const newCount = postsList.value.length - loadedCount.value
      if (newCount > 0) {
        uni.showToast({
          title: `发现${newCount}条新内容`,
          icon: 'none',
          duration: 2000
        })
      }
    })
  }
}

// 页面隐藏时保存滚动位置
onHide(() => {
  // 获取当前滚动位置并保存
  uni.createSelectorQuery().selectViewport().scrollOffset((res) => {
    if (res && res.scrollTop) {
      saveScrollPosition(res.scrollTop)
    }
  }).exec()
})

// 加载分类列表
const loadCategories = async () => {
  try {
    const res = await get<Result<Categories[]>>('/categories/list')
    if (res.success && res.data) {
      // 过滤用户有权限查看的分类
      const availableCategories = res.data.filter(category => {
        // 检查分类状态
        if (category.status !== 1) return false
        
        // 根据用户角色和权限过滤分类
        const userRole = memberStore.profile?.role || 'guest'
        
        // 这里可以根据category_permissions表进行权限控制
        // 暂时根据角色进行基本过滤
        switch (category.categoryCode) {
          case 'announcement': // 三期公告
            // 所有用户都可以查看公告
            return true
          case 'help': // 邻友互助
            // 所有用户都可以查看互助信息
            return true
          case 'group': // 组团邀约
            // 所有用户都可以查看组团信息
            return true
          case 'secondhand': // 二手闲置
            // 所有用户都可以查看二手信息
            return true
          case 'rent': // 房东直租
            // 所有用户都可以查看租房信息
            return true
          default:
            return true
        }
      })
      
      categories.value = [
        { id: 0, categoryName: '全部', categoryCode: 'all' },
        ...availableCategories
      ]
      
      console.log(`加载分类成功: ${availableCategories.length}个分类`)
    }
  } catch (error) {
    console.error('加载分类失败:', error)
    uni.showToast({
      title: '加载分类失败',
      icon: 'none'
    })
  }
}

// 加载帖子列表
const loadPostsList = async (reset = false) => {
  if (loading.value && !reset) return
  
  loading.value = true
  
  try {
    const params = {
      page: reset ? 1 : currentPage.value,
      pageSize,
      categoryId: selectedCategoryId.value || undefined,
      title: searchKeyword.value || undefined,
      sortBy: sortType.value,
      // 添加状态过滤，只显示已审核通过的帖子
      status: 1
    }
    
    const res = await get<Result<{ records: PostsList[], total: number }>>('/posts/listAll', params)
    
    if (res.success && res.data) {
      const newPosts = res.data.records || []
      
      // 处理帖子数据，确保必要字段存在
      const processedPosts = newPosts.map((post, index) => ({
        ...post,
        likeCount: post.likeCount || 0,
        commentCount: post.commentCount || 0,
        collectCount: post.collectCount || 0,
        viewCount: post.viewCount || 0,
        isLiked: post.isLiked || false,
        isCollected: post.isCollected || false,
        fileListData: post.fileListData || [],
        isProcessing: false, // 初始化处理状态
        loadIndex: reset ? index : postsList.value.length + index // 用于懒加载判断
      }))
      
      // 应用缓存的状态
      postStateManager.batchUpdatePosts(processedPosts)
      
      if (reset) {
        postsList.value = processedPosts
        currentPage.value = 2 // 下次加载第2页
        loadedCount.value = processedPosts.length
      } else {
        postsList.value.push(...processedPosts)
        currentPage.value++
        loadedCount.value += processedPosts.length
      }
      
      // 更新总数量
      if (res.data.total !== undefined) {
        totalCount.value = res.data.total
      }
      
      hasMore.value = newPosts.length === pageSize
      
      console.log(`加载帖子成功: ${newPosts.length}条, 总计: ${postsList.value.length}条`)
      
      // 如果是首次加载且没有数据，显示空状态
      if (reset && processedPosts.length === 0) {
        console.log('没有找到符合条件的帖子')
      }
    } else {
      console.error('加载帖子失败:', res.message)
      if (reset || postsList.value.length === 0) {
        uni.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        })
      }
    }
  } catch (error) {
    console.error('加载帖子列表异常:', error)
    if (reset || postsList.value.length === 0) {
      uni.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    }
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  hasMore.value = true
  loadPostsList(true)
}

// 清除搜索
const handleClearSearch = () => {
  searchKeyword.value = ''
  handleSearch()
}

// 防抖搜索
let searchTimer: NodeJS.Timeout | null = null
const debouncedSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    handleSearch()
  }, 500)
}

// 搜索框聚焦处理
const handleSearchFocus = () => {
  // 跳转到专门的搜索页面
  uni.navigateTo({
    url: '/pages/search/search'
  })
}

// 滚动位置记忆
const SCROLL_POSITION_KEY = 'posts_list_scroll_position'
const savedScrollTop = ref(0)

// 保存滚动位置
const saveScrollPosition = (scrollTop: number) => {
  savedScrollTop.value = scrollTop
  uni.setStorageSync(SCROLL_POSITION_KEY, scrollTop)
}

// 恢复滚动位置
const restoreScrollPosition = () => {
  try {
    const scrollTop = uni.getStorageSync(SCROLL_POSITION_KEY)
    if (scrollTop && typeof scrollTop === 'number') {
      savedScrollTop.value = scrollTop
      // 延迟恢复滚动位置，确保列表已渲染
      nextTick(() => {
        setTimeout(() => {
          uni.pageScrollTo({
            scrollTop: scrollTop,
            duration: 0
          })
        }, 100)
      })
    }
  } catch (error) {
    console.error('恢复滚动位置失败:', error)
  }
}

// 清除滚动位置记忆
const clearScrollPosition = () => {
  savedScrollTop.value = 0
  uni.removeStorageSync(SCROLL_POSITION_KEY)
}

// 分类筛选
const handleCategoryFilter = (categoryId: number) => {
  if (selectedCategoryId.value === categoryId) return // 避免重复点击
  
  selectedCategoryId.value = categoryId
  currentPage.value = 1
  hasMore.value = true
  
  // 保存筛选状态到本地存储
  uni.setStorageSync('selectedCategoryId', categoryId)
  
  // 显示筛选提示
  const categoryName = categories.value.find(cat => cat.id === categoryId)?.categoryName || '全部'
  if (categoryId !== 0) {
    uni.showToast({
      title: `筛选: ${categoryName}`,
      icon: 'none',
      duration: 1500
    })
  }
  
  loadPostsList(true)
}

// 排序变更
const handleSortChange = () => {
  currentPage.value = 1
  hasMore.value = true
  loadPostsList(true)
}

// 下拉刷新
const handleRefresh = async () => {
  // 检查刷新频率限制
  const now = Date.now()
  const timeSinceLastRefresh = now - lastRefreshTime.value
  const minRefreshInterval = 3000 // 最小刷新间隔3秒
  
  if (timeSinceLastRefresh < minRefreshInterval && lastRefreshTime.value > 0) {
    uni.showToast({
      title: '刷新太频繁，请稍后再试',
      icon: 'none',
      duration: 1500
    })
    refreshing.value = false
    return
  }
  
  console.log('开始下拉刷新')
  refreshing.value = true
  currentPage.value = 1
  hasMore.value = true
  
  // 清除滚动位置记忆
  clearScrollPosition()
  
  try {
    // 同时刷新分类和帖子列表
    const [categoriesResult, postsResult] = await Promise.all([
      loadCategories(),
      loadPostsList(true)
    ])
    
    // 检查是否有新内容
    const hasNewContent = postsList.value.length > 0
    
    if (hasNewContent) {
      uni.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
      
      // 触发轻微震动反馈
      uni.vibrateShort({
        type: 'light'
      })
    } else {
      uni.showToast({
        title: '暂无新内容',
        icon: 'none',
        duration: 1500
      })
    }
    
    // 更新最后刷新时间
    lastRefreshTime.value = Date.now()
    uni.setStorageSync('last_refresh_time', lastRefreshTime.value)
    
  } catch (error) {
    console.error('刷新失败:', error)
    uni.showToast({
      title: '刷新失败，请检查网络',
      icon: 'none',
      duration: 2000
    })
  } finally {
    refreshing.value = false
  }
}

// 预加载阈值（当剩余项目少于此数量时开始预加载）
const preloadThreshold = 5

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value && postsList.value.length > 0) {
    console.log('触发加载更多，当前页:', currentPage.value)
    loadPostsList()
  }
}

// 检查是否需要预加载
const checkPreload = (scrollTop: number, containerHeight: number) => {
  if (!hasMore.value || loading.value) return
  
  const itemHeight = 200 // 估算的每个帖子项目高度
  const totalHeight = postsList.value.length * itemHeight
  const scrollBottom = scrollTop + containerHeight
  const remainingHeight = totalHeight - scrollBottom
  const remainingItems = Math.ceil(remainingHeight / itemHeight)
  
  // 当剩余项目少于阈值时，开始预加载
  if (remainingItems <= preloadThreshold) {
    console.log('触发预加载，剩余项目:', remainingItems)
    loadMore()
  }
}

// 防抖处理的加载更多
let loadMoreTimer: NodeJS.Timeout | null = null
const debouncedLoadMore = () => {
  if (loadMoreTimer) {
    clearTimeout(loadMoreTimer)
  }
  loadMoreTimer = setTimeout(() => {
    loadMore()
  }, 300)
}

// 无限滚动加载状态
const isLoadingMore = computed(() => loading.value && postsList.value.length > 0)
const showLoadingMore = computed(() => isLoadingMore.value && hasMore.value)
const showNoMore = computed(() => !hasMore.value && postsList.value.length > 0 && !loading.value)

// 帖子点击
const handlePostClick = (post: PostsList) => {
  uni.navigateTo({
    url: `/pages/post/detail?id=${post.id}`
  })
}

// 分享处理
const handleShare = (post: PostsList) => {
  // 构建分享内容
  const shareTitle = post.title || '查看这个有趣的内容'
  const shareContent = post.content ? post.content.substring(0, 100) + '...' : '来看看这个内容吧'
  
  // 使用uni-app的分享API
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    title: shareTitle,
    summary: shareContent,
    href: `https://your-domain.com/post/${post.id}`, // 替换为实际域名
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: (err) => {
      console.error('分享失败:', err)
      // 降级处理：复制链接到剪贴板
      uni.setClipboardData({
        data: `${shareTitle}\n${shareContent}\n查看详情: https://your-domain.com/post/${post.id}`,
        success: () => {
          uni.showToast({
            title: '链接已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    }
  })
}

// 点赞处理
const handleLike = async (post: PostsList) => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  // 防止重复点击
  if (post.isProcessing) return
  
  try {
    // 设置处理状态
    const postIndex = postsList.value.findIndex(p => p.id === post.id)
    if (postIndex !== -1) {
      postsList.value[postIndex].isProcessing = true
    }
    
    const result = await toggleLike(post.id)
    
    if (result.success && result.data) {
      // 更新本地状态
      if (postIndex !== -1) {
        postsList.value[postIndex].isLiked = result.data.isLiked
        postsList.value[postIndex].likeCount = result.data.likeCount
      }
      
      // 更新状态管理器
      postStateManager.updatePostState(post.id, {
        isLiked: result.data.isLiked,
        likeCount: result.data.likeCount
      })
      
      uni.showToast({
        title: result.data.isLiked ? '点赞成功' : '取消点赞',
        icon: 'success',
        duration: 1500
      })
    } else {
      throw new Error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  } finally {
    // 清除处理状态
    const postIndex = postsList.value.findIndex(p => p.id === post.id)
    if (postIndex !== -1) {
      postsList.value[postIndex].isProcessing = false
    }
  }
}

// 收藏处理
const handleCollect = async (post: PostsList) => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  // 防止重复点击
  if (post.isProcessing) return
  
  try {
    // 设置处理状态
    const postIndex = postsList.value.findIndex(p => p.id === post.id)
    if (postIndex !== -1) {
      postsList.value[postIndex].isProcessing = true
    }
    
    const result = await toggleCollect(post.id)
    
    if (result.success && result.data) {
      // 更新本地状态
      if (postIndex !== -1) {
        postsList.value[postIndex].isCollected = result.data.isCollected
        postsList.value[postIndex].collectCount = result.data.collectCount
      }
      
      // 更新状态管理器
      postStateManager.updatePostState(post.id, {
        isCollected: result.data.isCollected,
        collectCount: result.data.collectCount
      })
      
      uni.showToast({
        title: result.data.isCollected ? '收藏成功' : '取消收藏',
        icon: 'success',
        duration: 1500
      })
    } else {
      throw new Error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  } finally {
    // 清除处理状态
    const postIndex = postsList.value.findIndex(p => p.id === post.id)
    if (postIndex !== -1) {
      postsList.value[postIndex].isProcessing = false
    }
  }
}

// 删除处理
const handleDelete = async (post: PostsList) => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  // 检查删除权限
  const canDelete = post.userId === memberStore.profile.id || memberStore.profile.isAdmin
  if (!canDelete) {
    uni.showToast({
      title: '无权限删除此帖子',
      icon: 'none'
    })
    return
  }
  
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条帖子吗？删除后无法恢复。',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await deletePost(post.id)
          
          if (result.success) {
            // 从列表中移除
            const postIndex = postsList.value.findIndex(p => p.id === post.id)
            if (postIndex !== -1) {
              postsList.value.splice(postIndex, 1)
            }
            
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          } else {
            throw new Error(result.message || '删除失败')
          }
        } catch (error) {
          console.error('删除操作失败:', error)
          uni.showToast({
            title: '删除失败，请重试',
            icon: 'none'
          })
        }
      }
    }
  })
}
</script>

<style scoped lang="scss">
.posts-list-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-filter-bar {
  background-color: white;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-section {
  margin-bottom: 12px;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-tabs {
  display: flex;
  gap: 8px;
  flex: 1;
  overflow-x: auto;
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background-color: #f8f9fa;
  border-radius: 16px;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:active {
    transform: scale(0.95);
  }
  
  &.active {
    background-color: #007bff;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    
    .category-count {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
    }
  }
}

.category-name {
  font-size: 14px;
}

.category-count {
  background-color: #e9ecef;
  color: #666;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
  line-height: 1.2;
}

.sort-filter {
  margin-left: 12px;
}

.refresh-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgba(0, 123, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 8px 0;
  transform: translateY(-100%);
  animation: slideDown 0.3s ease-out forwards;
}

.refresh-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.refresh-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

@keyframes slideDown {
  to {
    transform: translateY(0);
  }
}

.posts-list {
  padding: 16px;
}

.post-item {
  margin-bottom: 16px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.retry-button {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 8px;
}

.loading-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
}

.progress-text {
  font-size: 12px;
  color: #999;
}

.progress-bar {
  width: 100px;
  height: 2px;
  background-color: #e9ecef;
  border-radius: 1px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #007bff;
  transition: width 0.3s ease;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.no-more {
  padding: 20px;
  text-align: center;
}

.load-count-info {
  margin-top: 8px;
}

.count-text {
  font-size: 12px;
  color: #999;
}

.load-more-retry {
  padding: 20px;
  text-align: center;
}
</style>