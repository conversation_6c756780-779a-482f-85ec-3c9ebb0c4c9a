package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.haolinkyou.entity.Posts;
import com.haolinkyou.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;

/**
 * 帖子置顶服务实现类
 */
@Service
public class PostTopServiceImpl implements IPostTopService {
    
    @Autowired
    private IPostsService postsService;
    
    @Autowired
    private IPointsService pointsService;
    
    @Autowired
    private ISystemConfigService systemConfigService;
    
    @Override
    @Transactional
    public TopResult topPost(Long postId, Long userId, Integer hours) {
        try {
            // 1. 检查是否可以置顶
            TopCheckResult checkResult = checkCanTop(postId, userId);
            if (!checkResult.isCanTop()) {
                return new TopResult(false, checkResult.getReason());
            }
            
            // 2. 计算置顶费用
            Integer cost = getTopCost(hours);
            
            // 3. 扣除积分
            boolean pointsDeducted = pointsService.deductPoints(userId, cost, "post_top", "帖子置顶", postId);
            if (!pointsDeducted) {
                return new TopResult(false, "积分扣除失败，可能余额不足");
            }
            
            // 4. 更新帖子置顶状态
            Date expireTime = new Date(System.currentTimeMillis() + hours * 60 * 60 * 1000L);
            
            UpdateWrapper<Posts> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", postId)
                        .set("is_top", true)
                        .set("top_expire_time", expireTime);
            
            boolean updated = postsService.update(updateWrapper);
            if (!updated) {
                // 如果更新失败，需要回滚积分
                pointsService.addPoints(userId, cost, "post_top_refund", "置顶失败退款", postId);
                return new TopResult(false, "置顶失败，请重试");
            }
            
            return new TopResult(true, "置顶成功", cost, expireTime.getTime());
            
        } catch (Exception e) {
            System.out.println("置顶帖子异常: " + e.getMessage());
            return new TopResult(false, "系统异常，请重试");
        }
    }
    
    @Override
    @Transactional
    public boolean cancelTop(Long postId, Long userId) {
        try {
            // 1. 检查帖子是否存在且属于用户
            Posts post = postsService.getById(postId);
            if (post == null) {
                return false;
            }
            
            if (!post.getUserId().equals(userId)) {
                return false;
            }
            
            // 2. 检查是否已置顶
            if (post.getIsTop() == null || !post.getIsTop()) {
                return false;
            }
            
            // 3. 取消置顶
            UpdateWrapper<Posts> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", postId)
                        .set("is_top", false)
                        .set("top_expire_time", null);
            
            return postsService.update(updateWrapper);
            
        } catch (Exception e) {
            System.out.println("取消置顶异常: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public TopCheckResult checkCanTop(Long postId, Long userId) {
        try {
            // 1. 检查帖子是否存在
            Posts post = postsService.getById(postId);
            if (post == null) {
                return new TopCheckResult(false, "帖子不存在");
            }
            
            // 2. 检查是否为帖子作者
            if (!post.getUserId().equals(userId)) {
                return new TopCheckResult(false, "只能置顶自己的帖子");
            }
            
            // 3. 检查帖子状态
            if (post.getStatus() != 1) {
                return new TopCheckResult(false, "只有已审核通过的帖子才能置顶");
            }
            
            // 4. 检查是否已经置顶
            if (post.getIsTop() != null && post.getIsTop()) {
                // 检查是否过期
                if (post.getTopExpireTime() != null && post.getTopExpireTime().after(new Date())) {
                    return new TopCheckResult(false, "帖子已经置顶中");
                }
            }
            
            // 5. 检查用户积分
            TopConfig config = getTopConfig();
            Integer requiredPoints = config.getCostPerHour() * config.getDefaultHours();
            Integer userPoints = pointsService.getUserPointsBalance(userId);
            
            if (userPoints < requiredPoints) {
                return new TopCheckResult(false, "积分不足", requiredPoints, userPoints);
            }
            
            return new TopCheckResult(true, "可以置顶", requiredPoints, userPoints);
            
        } catch (Exception e) {
            System.out.println("检查置顶权限异常: " + e.getMessage());
            return new TopCheckResult(false, "系统异常");
        }
    }
    
    @Override
    public Integer getTopCost(Integer hours) {
        TopConfig config = getTopConfig();
        return config.getCostPerHour() * hours;
    }
    
    @Override
    public TopConfig getTopConfig() {
        TopConfig config = new TopConfig();
        
        // 从系统配置中获取置顶相关配置
        config.setCostPerHour(systemConfigService.getIntValue("points_post_top_cost_per_hour", 10));
        config.setMaxHours(systemConfigService.getIntValue("points_post_top_max_hours", 168)); // 7天
        config.setMinHours(systemConfigService.getIntValue("points_post_top_min_hours", 1));
        config.setDefaultHours(systemConfigService.getIntValue("points_post_top_default_hours", 24));
        
        return config;
    }
    
    @Override
    @Transactional
    public int processExpiredTopPosts() {
        try {
            // 查找所有过期的置顶帖子
            QueryWrapper<Posts> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_top", true)
                       .lt("top_expire_time", new Date());
            
            List<Posts> expiredPosts = postsService.list(queryWrapper);
            
            if (expiredPosts.isEmpty()) {
                return 0;
            }
            
            // 批量取消置顶
            UpdateWrapper<Posts> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("is_top", true)
                        .lt("top_expire_time", new Date())
                        .set("is_top", false)
                        .set("top_expire_time", null);
            
            boolean updated = postsService.update(updateWrapper);
            
            if (updated) {
                System.out.println("处理过期置顶帖子: " + expiredPosts.size() + " 个");
                return expiredPosts.size();
            }
            
            return 0;
            
        } catch (Exception e) {
            System.out.println("处理过期置顶帖子异常: " + e.getMessage());
            return 0;
        }
    }
    
    @Override
    public List<Posts> getUserTopPosts(Long userId) {
        try {
            QueryWrapper<Posts> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("is_top", true)
                       .gt("top_expire_time", new Date())
                       .orderByDesc("top_expire_time");
            
            return postsService.list(queryWrapper);
            
        } catch (Exception e) {
            System.out.println("获取用户置顶帖子异常: " + e.getMessage());
            return Collections.emptyList();
        }
    }
}
