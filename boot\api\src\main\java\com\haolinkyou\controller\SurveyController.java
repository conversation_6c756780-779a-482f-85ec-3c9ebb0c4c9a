package com.haolinkyou.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.Surveys;
import com.haolinkyou.entity.SurveyAnswers;
import com.haolinkyou.entity.Users;
import com.haolinkyou.entity.Posts;
import com.haolinkyou.mapper.SurveysMapper;
import com.haolinkyou.mapper.SurveyAnswersMapper;
import com.haolinkyou.mapper.PostsMapper;
import com.haolinkyou.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

@RestController
@RequestMapping("/api/survey")
public class SurveyController {

    @Autowired
    private SurveysMapper surveysMapper;
    
    @Autowired
    private SurveyAnswersMapper surveyAnswersMapper;
    
    @Autowired
    private PostsMapper postsMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 提交调查问卷
     */
    @PostMapping("/submit")
    public Result<String> submitSurvey(@RequestBody Map<String, Object> requestData, HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 获取提交数据
            Long postId = Long.valueOf(requestData.get("postId").toString());
            Long surveyId = Long.valueOf(requestData.get("surveyId").toString());
            List<Map<String, Object>> answers = (List<Map<String, Object>>) requestData.get("answers");

            // 检查调查是否存在
            Surveys survey = surveysMapper.selectById(surveyId);
            if (survey == null) {
                return Result.error("调查不存在");
            }

            // 检查调查是否已截止
            if (survey.getEndTime() != null && survey.getEndTime().isBefore(LocalDateTime.now())) {
                return Result.error("调查已截止");
            }

            // 检查用户是否已经参与过
            QueryWrapper<SurveyAnswers> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("survey_id", surveyId)
                       .eq("user_id", userId);
            SurveyAnswers existingAnswer = surveyAnswersMapper.selectOne(queryWrapper);
            
            if (existingAnswer != null) {
                return Result.error("您已经参与过此调查");
            }

            // 保存答案到数据库
            SurveyAnswers surveyAnswer = new SurveyAnswers();
            surveyAnswer.setSurveyId(surveyId);
            surveyAnswer.setUserId(userId);
            surveyAnswer.setAnswers(objectMapper.writeValueAsString(answers));
            surveyAnswer.setIsAnonymous(survey.getIsAnonymous());

            int result = surveyAnswersMapper.insert(surveyAnswer);
            if (result > 0) {
                return Result.success("提交成功");
            } else {
                return Result.error("提交失败");
            }

        } catch (Exception e) {
            System.err.println("调查问卷提交失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("提交失败: " + e.getMessage());
        }
    }

    /**
     * 获取调查问卷结果
     */
    @GetMapping("/results")
    public Result<Map<String, Object>> getSurveyResults(@RequestParam Long postId, HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 根据帖子ID查找调查
            QueryWrapper<Surveys> surveyQuery = new QueryWrapper<>();
            surveyQuery.eq("post_id", postId);
            Surveys survey = surveysMapper.selectOne(surveyQuery);
            
            if (survey == null) {
                return Result.error("调查不存在");
            }

            // 获取所有回答
            QueryWrapper<SurveyAnswers> answerQuery = new QueryWrapper<>();
            answerQuery.eq("survey_id", survey.getId());
            List<SurveyAnswers> allAnswers = surveyAnswersMapper.selectList(answerQuery);

            Map<String, Object> results = new HashMap<>();
            
            // 统计结果
            Map<String, Map<String, Integer>> questionResults = new HashMap<>();
            Map<String, List<Map<String, Object>>> textAnswers = new HashMap<>();
            
            for (SurveyAnswers answer : allAnswers) {
                try {
                    List<Map<String, Object>> answerList = objectMapper.readValue(answer.getAnswers(), new com.fasterxml.jackson.core.type.TypeReference<List<Map<String, Object>>>() {});
                    
                    for (Map<String, Object> answerItem : answerList) {
                        String questionId = answerItem.get("questionId").toString();
                        Object answerValue = answerItem.get("answer");
                        
                        if (answerValue instanceof String) {
                            // 文本答案
                            textAnswers.computeIfAbsent(questionId, k -> new ArrayList<>());
                            Map<String, Object> textAnswer = new HashMap<>();
                            
                            if (!answer.getIsAnonymous()) {
                                Users user = userMapper.selectById(answer.getUserId());
                                textAnswer.put("username", user != null ? user.getNickname() : "未知用户");
                            } else {
                                textAnswer.put("username", "匿名用户");
                            }
                            
                            textAnswer.put("content", answerValue);
                            textAnswer.put("createdTime", answer.getCreatedTime().toString());
                            textAnswer.put("isAnonymous", answer.getIsAnonymous());
                            textAnswers.get(questionId).add(textAnswer);
                        } else {
                            // 选择题答案
                            questionResults.computeIfAbsent(questionId, k -> new HashMap<>());
                            Map<String, Integer> optionCounts = questionResults.get(questionId);
                            
                            if (answerValue instanceof List) {
                                // 多选
                                List<Object> selectedOptions = (List<Object>) answerValue;
                                for (Object option : selectedOptions) {
                                    String optionStr = option.toString();
                                    optionCounts.put(optionStr, optionCounts.getOrDefault(optionStr, 0) + 1);
                                }
                            } else {
                                // 单选
                                String optionStr = answerValue.toString();
                                optionCounts.put(optionStr, optionCounts.getOrDefault(optionStr, 0) + 1);
                            }
                        }
                    }
                } catch (JsonProcessingException e) {
                    System.err.println("解析答案JSON失败: " + e.getMessage());
                }
            }
            
            results.put("questionResults", questionResults);
            results.put("textAnswers", textAnswers);
            results.put("totalParticipants", allAnswers.size());

            return Result.success(results);

        } catch (Exception e) {
            System.err.println("获取调查问卷结果失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("获取结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取调查详情和用户参与状态
     */
    @GetMapping("/detail")
    public Result<Map<String, Object>> getSurveyDetail(@RequestParam Long postId, HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            
            // 首先根据帖子ID查找调查
            Surveys survey = null;
            
                        // 方法1：通过post_id直接查找
            QueryWrapper<Surveys> surveyQuery = new QueryWrapper<>();
            surveyQuery.eq("post_id", postId);
            // 移除 del_flag 的硬编码，依赖MyBatis-Plus的逻辑删除
            survey = surveysMapper.selectOne(surveyQuery);
            
            // 方法2：如果没找到，通过帖子的template_data查找
            if (survey == null) {
                Posts post = postsMapper.selectById(postId);
                if (post != null && "survey".equals(post.getTemplateType())) {
                    // 从模板数据中提取调查信息，创建临时调查对象
                    try {
                        Map<String, Object> templateData = objectMapper.readValue(post.getTemplateData(), new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});
                        survey = new Surveys();
                        survey.setId(postId); // 使用帖子ID作为调查ID
                        survey.setPostId(postId);
                        survey.setTitle(post.getTitle());
                        survey.setEndTime(templateData.get("endTime") != null ? 
                            LocalDateTime.parse(templateData.get("endTime").toString()) : null);
                        survey.setIsAnonymous(Boolean.TRUE.equals(templateData.get("isAnonymous")));
                        survey.setQuestions(objectMapper.writeValueAsString(templateData.get("questions")));
                    } catch (Exception e) {
                        System.err.println("解析帖子模板数据失败: " + e.getMessage());
                        return Result.error("调查数据格式错误");
                    }
                }
            }
            
            if (survey == null) {
                return Result.error("调查不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("survey", survey);
            
            // 检查用户是否已参与
            boolean hasParticipated = false;
            List<Map<String, Object>> userAnswer = null;
            
            if (userId != null) {
                QueryWrapper<SurveyAnswers> answerQuery = new QueryWrapper<>();
                answerQuery.eq("survey_id", survey.getId())
                          .eq("user_id", userId);
                SurveyAnswers existingAnswer = surveyAnswersMapper.selectOne(answerQuery);
                
                if (existingAnswer != null) {
                    hasParticipated = true;
                    try {
                        userAnswer = objectMapper.readValue(existingAnswer.getAnswers(), new com.fasterxml.jackson.core.type.TypeReference<List<Map<String, Object>>>() {});
                    } catch (JsonProcessingException e) {
                        System.err.println("解析用户答案失败: " + e.getMessage());
                    }
                }
            }
            
            result.put("hasParticipated", hasParticipated);
            result.put("userAnswer", userAnswer);
            
            // 获取参与人数统计
            QueryWrapper<SurveyAnswers> countQuery = new QueryWrapper<>();
            countQuery.eq("survey_id", survey.getId());
            long participantCount = surveyAnswersMapper.selectCount(countQuery);
            result.put("participantCount", participantCount);

            return Result.success(result);

        } catch (Exception e) {
            System.err.println("获取调查详情失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("获取调查详情失败: " + e.getMessage());
        }
    }
}