import { get } from '@/utils/http'
import type { Result } from '@/types/ApiResponse'
import type { Categories } from '@/types/Categories'

/**
 * 分类相关的API服务
 */

/**
 * 获取分类列表
 */
export const getCategoryListAPI = () => {
  return get<Result<Categories[]>>('/categories/list')
}

/**
 * 格式化分类数据为选项卡格式
 * @param categories 分类数据
 * @returns 格式化后的选项卡数据
 */
export const formatCategoriesForTabs = (categories: Categories[]) => {
  const tabs = [{ name: '全部', id: 0 }] // 添加"全部"选项
  
  if (categories && categories.length > 0) {
    categories.forEach(category => {
      if (category.status === 1) { // 只显示启用的分类
        tabs.push({
          name: category.categoryName,
          id: category.id
        })
      }
    })
  }
  
  return tabs
}

/**
 * 根据分类ID获取分类名称
 * @param categories 分类列表
 * @param categoryId 分类ID
 * @returns 分类名称
 */
export const getCategoryNameById = (categories: Categories[], categoryId: number): string => {
  if (categoryId === 0) return '全部'
  
  const category = categories.find(cat => cat.id === categoryId)
  return category?.categoryName || '未知分类'
}
