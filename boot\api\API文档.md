# 评论接口API文档

## 添加评论接口

### 接口信息
- **URL**: `//comments/addComment`
- **方法**: `POST`
- **描述**: 添加新评论

### 请求参数

#### 请求体 (JSON)
```json
{
    "postId": 1,
    "userId": 1,
    "content": "这是一条评论内容",
    "filesId": null,
    "parentCommentId": null
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| postId | Long | 是 | 帖子ID |
| userId | Long | 是 | 用户ID |
| content | String | 是 | 评论内容 |
| filesId | Long | 否 | 附件ID（可选） |
| parentCommentId | Long | 否 | 父评论ID（用于回复功能，顶级评论为null） |

### 响应格式

#### 成功响应
```json
{
    "success": true,
    "message": "评论添加成功",
    "code": null,
    "data": true
}
```

#### 失败响应
```json
{
    "success": false,
    "message": "帖子ID不能为空",
    "code": null,
    "data": null
}
```

### 错误码说明
- 参数错误：请求参数不符合要求
- 系统错误：服务器内部错误

### 使用示例

#### cURL示例
```bash
curl -X POST http://localhost:8080//comments/addComment \
  -H "Content-Type: application/json" \
  -d '{
    "postId": 1,
    "userId": 1,
    "content": "这是一条测试评论"
  }'
```

#### JavaScript示例
```javascript
fetch('//comments/addComment', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        postId: 1,
        userId: 1,
        content: '这是一条测试评论'
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('评论添加成功');
    } else {
        console.error('评论添加失败:', data.message);
    }
});
```

## 其他评论接口

### 获取帖子评论列表
- **URL**: `/comments/getCommentsByPostId?postId=1`
- **方法**: `GET`
- **描述**: 获取指定帖子的评论列表（支持嵌套回复）

### 修改评论
- **URL**: `/comments/editComment`
- **方法**: `POST`
- **描述**: 修改评论内容

### 删除评论
- **URL**: `/comments/deleteComment?id=1`
- **方法**: `POST`
- **描述**: 删除指定评论

### 获取所有评论
- **URL**: `/comments/listAll`
- **方法**: `GET`
- **描述**: 获取所有评论列表 