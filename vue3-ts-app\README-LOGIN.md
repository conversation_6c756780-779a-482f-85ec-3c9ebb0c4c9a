# 登录系统使用说明

## 功能概述

为了完成点赞、收藏功能的测试，我们完善了登录系统，支持模拟用户登录。

## 主要功能

### 1. 登录页面 (`/pages/login/index.vue`)

**功能特性：**
- 支持微信小程序登录（生产环境）
- 支持模拟登录（开发测试环境）
- 提供3个预设测试用户
- 美观的用户选择界面

**测试用户：**
1. **幸福派乐然** (ID: 1) - 普通用户
2. **热心邻居** (ID: 2) - 普通用户  
3. **社区管理员** (ID: 3) - 管理员用户

**使用方法：**
- H5环境：直接点击用户头像登录
- 小程序环境：使用"模拟登录"按钮

### 2. 个人中心页面 (`/pages/my/index.vue`)

**新增功能：**
- 显示当前登录状态
- 显示用户ID（便于测试）
- 登录/退出按钮
- 自动跳转到登录页面

### 3. 测试页面 (`/pages/test-like.vue`)

**功能特性：**
- 显示当前登录状态
- 自动使用当前登录用户ID
- 完整的点赞收藏功能测试
- 详细的API响应日志
- 快速登录入口

**测试功能：**
- 切换点赞状态
- 切换收藏状态
- 检查点赞/收藏状态
- 获取点赞/收藏数量

### 4. 首页快速入口

在首页右上角添加了设置图标，点击可快速进入测试页面。

## 使用流程

### 开发测试流程

1. **启动应用**
   ```bash
   # 进入前端目录
   cd vue3-ts-app
   
   # 启动开发服务器
   npm run dev:h5
   ```

2. **登录测试用户**
   - 访问 `/pages/login/index` 或点击个人中心的"登录"按钮
   - 选择任意测试用户登录
   - 系统会自动跳转到首页

3. **测试点赞收藏功能**
   
   **方式一：在帖子列表/详情页测试**
   - 浏览帖子列表，点击心形图标（点赞）
   - 点击星形图标（收藏）
   - 查看控制台日志了解API调用情况

   **方式二：使用专门的测试页面**
   - 点击首页右上角设置图标
   - 或直接访问 `/pages/test-like`
   - 输入帖子ID进行测试
   - 查看详细的API响应信息

4. **查看测试结果**
   - 控制台会显示详细的API调用日志
   - 测试页面会显示完整的响应数据
   - UI会实时更新点赞/收藏状态

## 技术实现

### 登录状态管理
- 使用 Pinia Store 管理用户状态
- 支持数据持久化（localStorage/uni.storage）
- 自动同步登录状态到各个页面

### API响应格式
- 后端使用 `Result<T>` 格式：`{success, message, data}`
- 前端正确处理响应格式
- 统一的错误处理和用户反馈

### 用户体验优化
- 登录状态检查和提示
- 操作成功/失败的Toast反馈
- 网络错误处理
- 实时UI状态更新

## 故障排除

### 常见问题

1. **点击没有反应**
   - 检查是否已登录
   - 查看控制台错误日志
   - 确认后端服务是否启动

2. **API调用失败**
   - 检查网络连接
   - 确认API地址配置
   - 查看后端服务日志

3. **登录状态丢失**
   - 检查浏览器存储权限
   - 清除缓存后重新登录

### 调试技巧

1. **使用测试页面**
   - 提供最详细的调试信息
   - 可以独立测试各个API

2. **查看控制台日志**
   - 所有API调用都有详细日志
   - 包含请求参数和响应数据

3. **检查网络面板**
   - 查看实际的HTTP请求
   - 确认请求格式和响应格式

## 下一步

1. **完善用户管理**
   - 添加更多测试用户
   - 支持用户权限管理

2. **增强测试功能**
   - 批量测试功能
   - 性能测试工具

3. **生产环境配置**
   - 配置真实的微信登录
   - 添加用户注册功能
