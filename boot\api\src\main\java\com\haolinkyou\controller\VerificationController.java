package com.haolinkyou.controller;

import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.AuthApplications;
import com.haolinkyou.entity.UserRoles;
import com.haolinkyou.service.AuthApplicationsService;
import com.haolinkyou.service.UserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 认证相关控制器
 */
@RestController
@RequestMapping("/api/verification")
@CrossOrigin(
    origins = {
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:8080",
        "http://127.0.0.1:8080"
    },
    methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.OPTIONS},
    allowedHeaders = "*",
    allowCredentials = "false"
)
public class VerificationController {

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private AuthApplicationsService authApplicationsService;

    /**
     * 身份类型选项DTO
     */
    public static class IdentityTypeOption {
        private String code;
        private String name;
        private String description;
        private Integer sortOrder;

        public IdentityTypeOption() {}

        public IdentityTypeOption(String code, String name, String description, Integer sortOrder) {
            this.code = code;
            this.name = name;
            this.description = description;
            this.sortOrder = sortOrder;
        }

        // Getters and Setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public Integer getSortOrder() { return sortOrder; }
        public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }
    }

    /**
     * 获取身份类型选项列表
     */
    @GetMapping("/identity-types")
    public Result<List<IdentityTypeOption>> getIdentityTypes() {
        try {
            System.out.println("=== 获取身份类型选项列表 ===");

            // 从数据库获取所有启用的角色
            List<UserRoles> allRoles = userRoleService.getAllActiveRoles();

            // 过滤出可用于认证的身份类型（排除 guest 和 admin）
            List<String> verificationRoles = Arrays.asList("owner", "tenant", "property", "committee");

            List<IdentityTypeOption> identityTypes = new ArrayList<>();

            for (UserRoles role : allRoles) {
                if (verificationRoles.contains(role.getRoleCode())) {
                    IdentityTypeOption option = new IdentityTypeOption(
                        role.getRoleCode(),
                        role.getRoleName(),
                        role.getRoleDescription(),
                        role.getSortOrder()
                    );
                    identityTypes.add(option);
                }
            }

            // 按排序顺序排序
            identityTypes.sort(Comparator.comparing(IdentityTypeOption::getSortOrder));

            System.out.println("从数据库获取身份类型选项数量: " + identityTypes.size());
            for (IdentityTypeOption option : identityTypes) {
                System.out.println("  - " + option.getCode() + ": " + option.getName() + " (" + option.getDescription() + ")");
            }

            return Result.success(identityTypes);

        } catch (Exception e) {
            System.err.println("获取身份类型选项失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("获取身份类型选项失败");
        }
    }

    /**
     * 获取当前用户的认证申请状态
     */
    @GetMapping("/status")
    public Result<AuthApplications> getVerificationStatus(HttpServletRequest request) {
        try {
            System.out.println("=== 获取认证申请状态 ===");

            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                System.out.println("用户未登录");
                return Result.error("用户未登录");
            }

            System.out.println("用户ID: " + userId);

            // 查询用户的认证申请记录
            AuthApplications application = authApplicationsService.getByUserId(userId);

            if (application != null) {
                System.out.println("找到认证申请记录，状态: " + application.getStatus());
                System.out.println("认证申请详细信息:");
                System.out.println("  ID: " + application.getId());
                System.out.println("  真实姓名: " + application.getRealName());
                System.out.println("  手机号: " + application.getPhone());
                System.out.println("  身份类型: " + application.getIdentityType());
                System.out.println("  房号: " + application.getHouseNumber());
                System.out.println("  文件列表: " + application.getDocuments());
                System.out.println("  文件列表类型: " + (application.getDocuments() != null ? application.getDocuments().getClass().getName() : "null"));
                System.out.println("  文件列表大小: " + (application.getDocuments() != null ? application.getDocuments().size() : "null"));
                System.out.println("  备注: " + application.getRemark());
                System.out.println("  状态: " + application.getStatus());

                // 尝试直接查询数据库原始数据
                try {
                    AuthApplications rawApplication = authApplicationsService.getById(application.getId());
                    System.out.println("直接通过ID查询的文件列表: " + rawApplication.getDocuments());

                    // 尝试使用原生SQL查询
                    System.out.println("=== 执行原生SQL查询 ===");
                    String rawDocuments = authApplicationsService.getRawDocumentsByUserId(userId);
                    System.out.println("原生SQL查询的documents字段: " + rawDocuments);
                } catch (Exception e) {
                    System.err.println("直接查询失败: " + e.getMessage());
                }

                return Result.success(application);
            } else {
                System.out.println("未找到认证申请记录");
                return Result.success(null);
            }

        } catch (Exception e) {
            System.err.println("获取认证申请状态失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("获取认证申请状态失败");
        }
    }

    /**
     * 根据用户ID获取认证状态（用于其他页面显示用户认证状态）
     */
    @GetMapping("/user-status/{userId}")
    public Result<AuthApplications> getUserAuthStatus(@PathVariable Long userId) {
        try {
            System.out.println("=== 获取用户认证状态 ===");
            System.out.println("用户ID: " + userId);

            // 查询用户的认证申请记录
            AuthApplications application = authApplicationsService.getByUserId(userId);

            if (application != null) {
                System.out.println("找到认证申请记录，状态: " + application.getStatus());
                return Result.success(application);
            } else {
                System.out.println("未找到认证申请记录");
                return Result.success(null);
            }

        } catch (Exception e) {
            System.err.println("获取用户认证状态失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("获取用户认证状态失败");
        }
    }

    /**
     * 获取认证申请列表（管理员使用）
     */
    @GetMapping("/applications")
    public Result<List<AuthApplications>> getAuthApplications(
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            System.out.println("=== 获取认证申请列表 ===");
            System.out.println("状态筛选: " + status);
            System.out.println("页码: " + page + ", 页大小: " + pageSize);

            // 构建查询条件
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<AuthApplications> queryWrapper =
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();

            // 只查询未删除的记录
            queryWrapper.eq("del_flag", 0);

            // 状态筛选
            if (status != null) {
                queryWrapper.eq("status", status);
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("created_time");

            // 分页查询
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<AuthApplications> pageParam =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page, pageSize);

            com.baomidou.mybatisplus.extension.plugins.pagination.Page<AuthApplications> result =
                authApplicationsService.page(pageParam, queryWrapper);

            List<AuthApplications> applications = result.getRecords();

            System.out.println("查询到认证申请数量: " + applications.size());

            return Result.success(applications);

        } catch (Exception e) {
            System.err.println("获取认证申请列表失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("获取认证申请列表失败");
        }
    }

    /**
     * 认证申请参数DTO
     */
    public static class VerificationApplicationRequest {
        private String realName;
        private String phone;
        private String houseNumber;
        private String identityType;
        private List<String> documents;
        private String remark;

        // Getters and Setters
        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }

        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }

        public String getHouseNumber() { return houseNumber; }
        public void setHouseNumber(String houseNumber) { this.houseNumber = houseNumber; }

        public String getIdentityType() { return identityType; }
        public void setIdentityType(String identityType) { this.identityType = identityType; }

        public List<String> getDocuments() { return documents; }
        public void setDocuments(List<String> documents) { this.documents = documents; }

        public String getRemark() { return remark; }
        public void setRemark(String remark) { this.remark = remark; }
    }

    /**
     * 认证申请响应DTO
     */
    public static class VerificationApplicationResponse {
        private Long id;
        private Integer status;
        private String message;

        public VerificationApplicationResponse() {}

        public VerificationApplicationResponse(Long id, Integer status, String message) {
            this.id = id;
            this.status = status;
            this.message = message;
        }

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    /**
     * 提交认证申请
     */
    @PostMapping("/apply")
    public Result<VerificationApplicationResponse> submitApplication(
            @RequestBody VerificationApplicationRequest request,
            HttpServletRequest httpRequest) {
        try {
            System.out.println("=== 提交认证申请 ===");
            System.out.println("真实姓名: " + request.getRealName());
            System.out.println("联系电话: " + request.getPhone());
            System.out.println("房屋号码: " + request.getHouseNumber());
            System.out.println("身份类型: " + request.getIdentityType());
            System.out.println("证明文件: " + (request.getDocuments() != null ? request.getDocuments().size() : 0) + "个");
            System.out.println("备注说明: " + request.getRemark());

            // 从token中获取用户ID
            Long userId = (Long) httpRequest.getAttribute("userId");
            if (userId == null) {
                System.out.println("用户未登录");
                return Result.error("用户未登录");
            }

            System.out.println("用户ID: " + userId);
            System.out.println("接收到的认证申请数据:");
            System.out.println("  真实姓名: " + request.getRealName());
            System.out.println("  手机号: " + request.getPhone());
            System.out.println("  房号: " + request.getHouseNumber());
            System.out.println("  身份类型: " + request.getIdentityType());
            System.out.println("  文件列表: " + request.getDocuments());
            System.out.println("  备注: " + request.getRemark());

            // 检查用户是否已有认证申请记录
            AuthApplications existingApplication = authApplicationsService.getByUserId(userId);

            AuthApplications application = new AuthApplications();
            application.setUserId(userId);
            application.setRealName(request.getRealName());
            application.setPhone(request.getPhone());
            application.setHouseNumber(request.getHouseNumber());
            application.setIdentityType(request.getIdentityType());
            application.setDocuments(request.getDocuments());
            application.setRemark(request.getRemark());

            System.out.println("设置到实体类的文件列表: " + application.getDocuments());

            boolean success;
            Long applicationId;

            if (existingApplication != null) {
                // 更新现有记录
                application.setId(existingApplication.getId());
                application.setStatus(0); // 重新设置为待审核状态
                success = authApplicationsService.updateApplication(application);
                applicationId = existingApplication.getId();
                System.out.println("更新现有认证申请记录，ID: " + applicationId);
            } else {
                // 创建新记录
                success = authApplicationsService.submitApplication(application);
                applicationId = application.getId();
                System.out.println("创建新认证申请记录，ID: " + applicationId);
            }

            if (success) {
                VerificationApplicationResponse response = new VerificationApplicationResponse(
                    applicationId,
                    0, // 0-待审核
                    "认证申请已提交，请等待审核"
                );

                System.out.println("认证申请提交成功，申请ID: " + applicationId);
                return Result.success(response);
            } else {
                return Result.error("认证申请提交失败");
            }

        } catch (Exception e) {
            System.err.println("提交认证申请失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("提交认证申请失败");
        }
    }
}
