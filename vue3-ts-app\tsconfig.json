{
  "extends": "@vue/tsconfig/tsconfig.json",
  "compilerOptions": {
    "ignoreDeprecations": "5.0",
    "skipLibCheck": true,
    "allowJs": true,
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
        // "src/*"
      ]
    },
    "lib": [
      "esnext",
      "dom"
    ],
    "types": [
      "@dcloudio/types",
      "@types/wechat-miniprogram",
      "@uni-helper/uni-app-types",
      "@uni-helper/uni-ui-types",
      "uview-plus/types"
    ]
  },
  "vueCompilerOptions": {
    "plugins": [
      "@uni-helper/uni-app-types/volar-plugin"
    ],
    "nativeTags": [
      "block",
      "component",
      "template",
      "slot"
    ]
  },
  "include": [
    // include包含的形式命名的文件可以被全局访问
    // 解决import { XXX } from '@/types/home' 找不到的问题;
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/**/*.d.ts",
    // 加上下面这项才能自动引入函数
    "src/auto-imports.d.ts",
  ]
}