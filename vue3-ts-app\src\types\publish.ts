// 发布相关类型定义

// 媒体类型
export enum MediaType {
  IMAGE = 0,
  VIDEO = 1
}

// 文件上传状态
export enum UploadStatus {
  WAITING = 'waiting',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 文件类型检测结果
export type FileTypeResult = 'image' | 'video' | 'unknown'

// 上传文件项
export interface UploadFileItem {
  id?: string;
  name: string;
  url?: string;
  file?: File;
  status: UploadStatus;
  progress?: number;
  error?: string;
  size?: number;
  type?: string;
  isExisting?: boolean; // 是否为已存在的文件
  detectedType?: FileTypeResult; // 检测到的文件类型
}

// 发布表单
export interface PublishForm {
  title?: string;
  content: string;
  categoryId: number;
  mediaType: MediaType;
  fileList: UploadFileItem[];
  location?: LocationInfo;
}

// 位置信息
export interface LocationInfo {
  latitude: number;
  longitude: number;
  address: string;
  name?: string;
}

// 发布响应
export interface PublishResponse {
  id: number;
  message: string;
  fileList?: string;
}

// 文件上传配置
export interface UploadConfig {
  maxCount: number; // 最大文件数量
  maxSize: number; // 最大文件大小(字节)
  accept: string[]; // 允许的文件类型
  multiple: boolean; // 是否支持多选
}

// 媒体类型配置
export const MEDIA_TYPE_CONFIG: Record<MediaType, UploadConfig> = {
  [MediaType.IMAGE]: {
    maxCount: 9,
    maxSize: 10 * 1024 * 1024, // 10MB
    accept: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
    multiple: true
  },
  [MediaType.VIDEO]: {
    maxCount: 1,
    maxSize: 100 * 1024 * 1024, // 100MB
    accept: ['video/mp4', 'video/mov', 'video/avi'],
    multiple: false
  }
};

// 发布状态
export enum PublishStatus {
  DRAFT = 0,      // 草稿
  PENDING = 1,    // 待审核
  APPROVED = 2,   // 已通过
  REJECTED = 3    // 已拒绝
}

// 文件类型检测工具函数
export const detectFileType = (file: File | any): FileTypeResult => {
  if (!file) return 'unknown'

  // 如果是已存在的文件对象，通过URL判断
  if (file.url && typeof file.url === 'string') {
    const url = file.url.toLowerCase()
    if (url.match(/\.(jpg|jpeg|png|gif|webp|bmp)(\?.*)?$/)) return 'image'
    if (url.match(/\.(mp4|mov|avi|wmv|flv|webm|mkv)(\?.*)?$/)) return 'video'
  }

  // 如果有file属性（uview-plus格式）
  if (file.file && file.file.type) {
    const mimeType = file.file.type.toLowerCase()
    if (mimeType.startsWith('image/')) return 'image'
    if (mimeType.startsWith('video/')) return 'video'
  }

  // 直接检查type属性
  if (file.type) {
    const mimeType = file.type.toLowerCase()
    if (mimeType.startsWith('image/')) return 'image'
    if (mimeType.startsWith('video/')) return 'video'
  }

  // 通过文件名扩展名判断
  if (file.name) {
    const name = file.name.toLowerCase()
    if (name.match(/\.(jpg|jpeg|png|gif|webp|bmp)$/)) return 'image'
    if (name.match(/\.(mp4|mov|avi|wmv|flv|webm|mkv)$/)) return 'video'
  }

  return 'unknown'
}

// 严格的文件格式验证函数
export const validateFileFormat = (file: File | any): { valid: boolean; message?: string } => {
  if (!file) {
    return { valid: false, message: '文件不存在' }
  }

  // 支持的图片格式
  const supportedImageTypes = ['image/jpeg', 'image/jpg', 'image/png']
  const supportedImageExtensions = ['.jpg', '.jpeg', '.png']

  // 支持的视频格式（与后端保持一致）
  const supportedVideoTypes = ['video/mp4', 'video/avi']
  const supportedVideoExtensions = ['.mp4', '.avi']

  let mimeType = ''
  let fileName = ''

  // 获取MIME类型和文件名
  if (file.file && file.file.type) {
    mimeType = file.file.type.toLowerCase()
    fileName = file.file.name || file.name || ''
  } else if (file.type) {
    mimeType = file.type.toLowerCase()
    fileName = file.name || ''
  } else {
    fileName = file.name || ''
  }

  // 检查文件扩展名
  const fileExtension = fileName.toLowerCase().match(/\.[^.]+$/)?.[0] || ''

  // 验证图片格式
  if (mimeType.startsWith('image/') || supportedImageExtensions.includes(fileExtension)) {
    if (mimeType && !supportedImageTypes.includes(mimeType)) {
      return { valid: false, message: '图片格式仅支持jpg、jpeg、png' }
    }
    if (fileExtension && !supportedImageExtensions.includes(fileExtension)) {
      return { valid: false, message: '图片格式仅支持jpg、jpeg、png' }
    }
    return { valid: true }
  }

  // 验证视频格式
  if (mimeType.startsWith('video/') || supportedVideoExtensions.includes(fileExtension)) {
    if (mimeType && !supportedVideoTypes.includes(mimeType)) {
      return { valid: false, message: '视频格式仅支持mp4、avi' }
    }
    if (fileExtension && !supportedVideoExtensions.includes(fileExtension)) {
      return { valid: false, message: '视频格式仅支持mp4、avi' }
    }
    return { valid: true }
  }

  return { valid: false, message: '不支持的文件格式，请选择图片或视频文件' }
}

// 智能文件处理函数
export const processFileSelection = (
  currentFileList: UploadFileItem[],
  newFiles: any[],
  maxImageCount: number = 9
): {
  fileList: UploadFileItem[];
  mediaType: MediaType;
  message?: string;
} => {
  if (!newFiles || newFiles.length === 0) {
    return { fileList: currentFileList, mediaType: MediaType.IMAGE }
  }

  // 首先验证所有新文件的格式
  for (const file of newFiles) {
    const validation = validateFileFormat(file)
    if (!validation.valid) {
      return {
        fileList: currentFileList,
        mediaType: 2 as MediaType, // MIXED
        message: validation.message
      }
    }
  }

  // 检测新文件的类型
  const newFileTypes = newFiles.map(detectFileType)
  const hasVideo = newFileTypes.includes('video')
  const hasImage = newFileTypes.includes('image')
  const hasUnknown = newFileTypes.includes('unknown')

  // 如果有未知类型文件，过滤掉
  if (hasUnknown) {
    newFiles = newFiles.filter(file => detectFileType(file) !== 'unknown')
  }

  let resultFileList: UploadFileItem[] = []
  let resultMediaType: MediaType = MediaType.IMAGE
  let message: string | undefined

  if (hasVideo) {
    // 选择了视频：清空所有文件，只保留第一个视频
    const videoFile = newFiles.find(file => detectFileType(file) === 'video')
    if (videoFile) {
      resultFileList = [{
        ...videoFile,
        detectedType: 'video',
        isExisting: false
      }]
      resultMediaType = MediaType.VIDEO
      message = '已切换到视频模式，只能选择一个视频文件'
    }
  } else if (hasImage) {
    // 选择了图片：保留现有图片，添加新图片（避免重复）
    const existingImages = currentFileList.filter(file =>
      file.detectedType === 'image' || detectFileType(file) === 'image'
    )
    const newImages = newFiles.filter(file => detectFileType(file) === 'image')

    // 检查新文件是否已存在，避免重复添加
    const uniqueNewImages = newImages.filter(newFile => {
      return !existingImages.some(existingFile => {
        // 通过文件名和大小判断是否为同一文件
        return existingFile.name === newFile.name &&
               existingFile.size === newFile.size
      })
    })

    resultFileList = [...existingImages, ...uniqueNewImages.map(file => ({
      ...file,
      detectedType: 'image' as FileTypeResult,
      isExisting: false
    }))]

    // 限制最多图片数量
    if (resultFileList.length > maxImageCount) {
      resultFileList = resultFileList.slice(0, maxImageCount)
      message = `最多只能选择${maxImageCount}张图片`
    }

    // 如果有重复文件，给出提示
    if (uniqueNewImages.length < newImages.length) {
      const duplicateCount = newImages.length - uniqueNewImages.length
      message = `已过滤${duplicateCount}个重复文件`
    }

    resultMediaType = MediaType.IMAGE
  }

  return {
    fileList: resultFileList,
    mediaType: resultMediaType,
    message
  }
}
