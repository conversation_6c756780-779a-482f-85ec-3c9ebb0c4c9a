package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.entity.AuthApplications;
import com.haolinkyou.mapper.AuthApplicationsMapper;
import com.haolinkyou.service.AuthApplicationsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class AuthApplicationsServiceImpl extends ServiceImpl<AuthApplicationsMapper, AuthApplications> implements AuthApplicationsService {

    @Autowired
    private AuthApplicationsMapper authApplicationsMapper;

    @Override
    public AuthApplications getByUserId(Long userId) {
        return authApplicationsMapper.getByUserId(userId);
    }

    @Override
    public boolean hasApplication(Long userId) {
        return authApplicationsMapper.countByUserId(userId) > 0;
    }

    @Override
    @Transactional
    public boolean submitApplication(AuthApplications application) {
        try {
            // 设置创建时间
            application.setCreatedTime(new Date());
            // 设置默认状态为待审核
            application.setStatus(0);
            // 设置删除标记为未删除
            application.setDelFlag(0);
            
            return save(application);
        } catch (Exception e) {
            System.err.println("提交认证申请失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateApplication(AuthApplications application) {
        try {
            // 设置更新时间
            application.setUpdatedTime(new Date());

            return updateById(application);
        } catch (Exception e) {
            System.err.println("更新认证申请失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public String getRawDocumentsByUserId(Long userId) {
        return authApplicationsMapper.getRawDocumentsByUserId(userId);
    }
}
