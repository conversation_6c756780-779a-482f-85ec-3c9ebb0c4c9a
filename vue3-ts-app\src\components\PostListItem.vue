<template>
  <view class="post-list-item" @click="handleClick">
    <!-- 帖子头部 -->
    <view class="post-header">
      <up-avatar :text="post.nickname" :size="40" randomBgColor />
      <view class="user-info">
        <view class="user-name-row">
          <text class="user-name">{{ post.nickname }}</text>
          <up-icon
            v-if="post.userGender !== 0"
            :name="post.userGender === 1 ? 'man' : 'woman'"
            size="16"
            :color="post.userGender === 1 ? '#4396F7' : '#FF2442'"
          />
          <!-- 用户认证状态标签 -->
          <up-tag
            :text="getRoleStatusTag(post.userIsVerified, post.userRole).text"
            size="mini"
            :color="getRoleStatusTag(post.userIsVerified, post.userRole).color"
            :bgColor="getRoleStatusTag(post.userIsVerified, post.userRole).bgColor"
            borderColor="transparent"
            shape="circle"
            plain
            plainFill
          />
        </view>
        <view class="post-meta">
          <text class="post-time">{{ formatPostTime(post) }}</text>
          <text class="category-name">{{ post.categoryName }}</text>
          <!-- 置顶标识 -->
          <view v-if="post.isTop" class="top-badge">
            <up-icon name="arrow-up" size="12" color="#ff6b6b" />
            <text class="top-text">置顶</text>
          </view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="post-actions" @click.stop>
        <up-icon
          v-if="canDelete"
          name="more-dot-fill"
          size="20"
          color="#999"
          @click="showActionSheet = true"
        />
      </view>
    </view>
    
    <!-- 帖子内容 -->
    <view class="post-content">
      <!-- 标题 -->
      <view v-if="post.title" class="post-title" v-html="getHighlightedContent(post.title)"></view>
      
      <!-- 内容预览 -->
      <view class="post-text">
        <ExpandableRichText
          :content="getHighlightedContent(post.content)"
          :maxLength="150"
          :maxLines="3"
          @linkClick="handleContentLinkClick"
          @imageClick="handleContentImageClick"
        />
      </view>
      
      <!-- 模板特殊内容预览 -->
      <view v-if="templatePreview" class="template-preview" :class="`template-${templatePreview.type}`">
        <!-- 活动预览 -->
        <view v-if="templatePreview.type === 'activity'" class="activity-preview">
          <view class="preview-header">
            <up-icon name="calendar" size="16" color="#4CAF50" />
            <text class="preview-type">活动</text>
            <view class="status-badge" :class="getStatusClass(templatePreview.preview.status)">
              {{ templatePreview.preview.status }}
            </view>
          </view>
          <view class="preview-content">
            <text class="activity-name">{{ templatePreview.preview.title }}</text>
            <text v-if="templatePreview.preview.subtitle" class="activity-time">{{ templatePreview.preview.subtitle }}</text>
            <text v-if="templatePreview.preview.location" class="activity-location">
              <up-icon name="map-pin" size="12" color="#666" />
              {{ templatePreview.preview.location }}
            </text>
            <view class="activity-stats">
              <text class="activity-participants">
                {{ templatePreview.preview.participants }}/{{ templatePreview.preview.maxParticipants }}人参与
              </text>
              <text v-if="templatePreview.preview.fee" class="activity-fee">
                {{ getFeeText(templatePreview.preview.fee) }}
              </text>
            </view>
          </view>
        </view>
        
        <!-- 团购预览 -->
        <view v-else-if="templatePreview.type === 'groupbuy'" class="groupbuy-preview">
          <view class="preview-header">
            <up-icon name="shopping-cart" size="16" color="#FF9800" />
            <text class="preview-type">团购</text>
            <view class="status-badge" :class="getStatusClass(templatePreview.preview.status)">
              {{ templatePreview.preview.status }}
            </view>
          </view>
          <view class="preview-content">
            <text class="product-name">{{ templatePreview.preview.title }}</text>
            <view class="price-info">
              <text v-if="templatePreview.preview.originalPrice" class="original-price">
                原价 ¥{{ templatePreview.preview.originalPrice }}
              </text>
              <text class="group-price">团购价 ¥{{ templatePreview.preview.groupPrice }}</text>
            </view>
            <view class="group-stats">
              <text class="group-progress">
                {{ templatePreview.preview.currentCount }}/{{ templatePreview.preview.targetCount }}人参团
              </text>
              <text v-if="templatePreview.preview.location" class="pickup-location">
                <up-icon name="map-pin" size="12" color="#666" />
                {{ templatePreview.preview.location }}
              </text>
            </view>
          </view>
        </view>
        
        <!-- 投票预览 -->
        <view v-else-if="templatePreview.type === 'vote'" class="vote-preview">
          <view class="preview-header">
            <up-icon name="checkmark-circle" size="16" color="#2196F3" />
            <text class="preview-type">投票</text>
            <view class="status-badge" :class="getStatusClass(templatePreview.preview.status)">
              {{ templatePreview.preview.status }}
            </view>
          </view>
          <view class="preview-content">
            <text class="vote-title">{{ templatePreview.preview.title }}</text>
            <view class="vote-stats">
              <text class="vote-info">{{ templatePreview.preview.optionCount }}个选项 · {{ templatePreview.preview.voteType }}</text>
              <text class="vote-count">{{ templatePreview.preview.totalVotes }}人参与投票</text>
            </view>
            <view v-if="templatePreview.preview.isAnonymous" class="vote-anonymous">
              <up-icon name="eye-off" size="12" color="#999" />
              <text class="anonymous-text">匿名投票</text>
            </view>
          </view>
        </view>
        
        <!-- 调查预览 -->
        <view v-else-if="templatePreview.type === 'survey'" class="survey-preview">
          <view class="preview-header">
            <up-icon name="list" size="16" color="#9C27B0" />
            <text class="preview-type">调查</text>
            <view class="status-badge" :class="getStatusClass(templatePreview.preview.status)">
              {{ templatePreview.preview.status }}
            </view>
          </view>
          <view class="preview-content">
            <text class="survey-title">{{ templatePreview.preview.title }}</text>
            <view class="survey-stats">
              <text class="survey-info">{{ templatePreview.preview.questionCount }}个问题</text>
              <text class="survey-responses">{{ templatePreview.preview.responses }}人参与调查</text>
            </view>
            <view v-if="templatePreview.preview.isAnonymous" class="survey-anonymous">
              <up-icon name="eye-off" size="12" color="#999" />
              <text class="anonymous-text">匿名调查</text>
            </view>
          </view>
        </view>
        
        <!-- 公告预览 -->
        <view v-else-if="templatePreview.type === 'announcement'" class="announcement-preview">
          <view class="preview-header">
            <up-icon name="bell" size="16" color="#F44336" />
            <text class="preview-type">公告</text>
            <view class="importance-badge" :class="`importance-${templatePreview.preview.importance}`">
              {{ getImportanceText(templatePreview.preview.importance) }}
            </view>
            <view v-if="templatePreview.preview.isUrgent" class="urgent-badge">
              <up-icon name="warning" size="12" color="#FF4757" />
              <text class="urgent-text">紧急</text>
            </view>
          </view>
          <view class="preview-content">
            <text class="announcement-title">{{ templatePreview.preview.title }}</text>
            <view class="announcement-info">
              <text v-if="templatePreview.preview.publisher" class="publisher">
                发布者: {{ templatePreview.preview.publisher }}
              </text>
              <text v-if="templatePreview.preview.effectiveDate" class="effective-date">
                生效时间: {{ formatDate(templatePreview.preview.effectiveDate) }}
              </text>
              <text v-if="templatePreview.preview.targetAudience" class="target-audience">
                目标对象: {{ templatePreview.preview.targetAudience }}
              </text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 媒体文件 -->
      <view v-if="post.fileListData && post.fileListData.length > 0" class="post-media">
        <view v-if="post.fileListData.length === 1" class="single-media">
          <view class="media-wrapper" @click.stop="previewMedia(post.fileListData, 0)">
            <!-- 单个图片 -->
            <up-image
              v-if="detectMediaType(post.fileListData[0]) === 'image'"
              :src="post.fileListData[0]"
              width="100%"
              height="200"
              radius="8"
              mode="aspectFill"
            />
            <!-- 单个视频 -->
            <view v-else-if="detectMediaType(post.fileListData[0]) === 'video'" class="video-preview">
              <video
                :src="post.fileListData[0]"
                :poster="getVideoPoster(post.fileListData[0])"
                :show-center-play-btn="false"
                :show-play-btn="false"
                :show-fullscreen-btn="false"
                :show-progress="false"
                :controls="false"
                :autoplay="false"
                muted
                style="width: 100%; height: 200px; border-radius: 8px;"
              />
              <view class="video-overlay">
                <view class="play-button">
                  <up-icon name="play-circle-fill" size="50" color="rgba(255, 255, 255, 0.9)" />
                </view>
                <view class="video-badge">
                  <up-icon name="video" size="12" color="white" />
                  <text class="video-text">视频</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-else class="multi-media">
          <view
            v-for="(mediaUrl, index) in post.fileListData.slice(0, 3)"
            :key="index"
            class="media-item"
            @click.stop="previewMedia(post.fileListData, index)"
          >
            <view class="media-wrapper">
              <!-- 多个媒体中的图片 -->
              <up-image
                v-if="detectMediaType(mediaUrl) === 'image'"
                :src="mediaUrl"
                width="100%"
                height="80"
                radius="4"
                mode="aspectFill"
              />
              <!-- 多个媒体中的视频 -->
              <view v-else-if="detectMediaType(mediaUrl) === 'video'" class="video-preview small">
                <video
                  :src="mediaUrl"
                  :poster="getVideoPoster(mediaUrl)"
                  :show-center-play-btn="false"
                  :show-play-btn="false"
                  :show-fullscreen-btn="false"
                  :show-progress="false"
                  :controls="false"
                  :autoplay="false"
                  muted
                  style="width: 100%; height: 80px; border-radius: 4px;"
                />
                <view class="video-overlay small">
                  <view class="play-button small">
                    <up-icon name="play-circle-fill" size="24" color="rgba(255, 255, 255, 0.9)" />
                  </view>
                  <view class="video-badge small">
                    <up-icon name="video" size="8" color="white" />
                  </view>
                </view>
              </view>
              
              <!-- 更多文件提示 -->
              <view v-if="index === 2 && post.fileListData.length > 3" class="more-overlay">
                <text class="more-text">+{{ post.fileListData.length - 3 }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 帖子底部 -->
    <view class="post-footer">
      <view class="stats">
        <view class="stat-item like-item" :class="{ active: post.isLiked, loading: likeLoading }" @click.stop="handleLike">
          <up-loading-icon v-if="likeLoading" mode="spinner" size="16" color="#ff6b6b" />
          <up-icon
            v-else
            :name="post.isLiked ? 'thumb-up-fill' : 'thumb-up'"
            size="16"
            :color="post.isLiked ? '#ff6b6b' : '#999'"
            class="like-icon"
            :class="{ 'animate-like': post.isLiked && !likeLoading }"
          />
          <text class="stat-text" :class="{ active: post.isLiked }">{{ formatCount(post.likeCount || 0) }}</text>
        </view>
        
        <view class="stat-item comment-item">
          <up-icon name="chat" size="16" color="#999" />
          <text class="stat-text">{{ formatCount(post.commentCount || 0) }}</text>
        </view>
        
        <view class="stat-item collect-item" :class="{ active: post.isCollected, loading: collectLoading }" @click.stop="handleCollect">
          <up-loading-icon v-if="collectLoading" mode="spinner" size="16" color="#ffc107" />
          <up-icon
            v-else
            :name="post.isCollected ? 'star-fill' : 'star'"
            size="16"
            :color="post.isCollected ? '#ffc107' : '#999'"
            class="collect-icon"
            :class="{ 'animate-collect': post.isCollected && !collectLoading }"
          />
          <text class="stat-text" :class="{ active: post.isCollected }">{{ formatCount(post.collectCount || 0) }}</text>
        </view>
        
        <view class="stat-item view-item">
          <up-icon name="eye" size="16" color="#999" />
          <text class="stat-text">{{ formatCount(post.viewCount || 0) }}</text>
        </view>
      </view>
      
      <view class="actions">
        <up-icon name="share" size="16" color="#999" @click.stop="handleShare" />
      </view>
    </view>
    
    <!-- 操作菜单 -->
    <up-action-sheet
      :show="showActionSheet"
      :actions="actionSheetActions"
      @close="showActionSheet = false"
      @select="handleActionSelect"
    />
    
    <!-- 媒体预览组件 -->
    <MediaPreview
      :show="showMediaPreview"
      :mediaList="previewMediaList"
      :initialIndex="previewInitialIndex"
      @close="closeMediaPreview"
      @change="handleMediaPreviewChange"
    />
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { getRoleStatusTag } from '@/utils/rolePermissions'
import { useMemberStore } from '@/stores'
import { formatPostTime } from '@/utils/timeFormat'
import MediaPreview from './MediaPreview.vue'
import ExpandableRichText from './ExpandableRichText.vue'
import type { PostsList } from '@/types'

interface Props {
  post: PostsList
  highlightKeyword?: string // 高亮关键词
}

const props = defineProps<Props>()
const memberStore = useMemberStore()

const showActionSheet = ref(false)
const likeLoading = ref(false)
const collectLoading = ref(false)

// 事件定义
const emit = defineEmits<{
  click: [post: PostsList]
  like: [post: PostsList]
  collect: [post: PostsList]
  delete: [post: PostsList]
  share: [post: PostsList]
}>()

// 是否可以删除
const canDelete = computed(() => {
  if (!memberStore.profile?.id) return false
  return props.post.userId === memberStore.profile.id || memberStore.profile.isAdmin
})

// 操作菜单选项
const actionSheetActions = computed(() => {
  const actions = []
  
  if (canDelete.value) {
    actions.push({
      name: '删除',
      color: '#ff6b6b'
    })
  }
  
  actions.push({
    name: '举报',
    color: '#999'
  })
  
  return actions
})

// 模板预览组件
const templatePreview = computed(() => {
  if (!props.post.templateData) return null
  
  let templateData = {}
  if (typeof props.post.templateData === 'string') {
    try {
      templateData = JSON.parse(props.post.templateData)
    } catch (error) {
      return null
    }
  } else {
    templateData = props.post.templateData
  }
  
  // 根据模板数据类型返回预览信息
  if (templateData.activityType || templateData.activityName) {
    const now = new Date()
    const deadline = templateData.registrationDeadline ? new Date(templateData.registrationDeadline) : null
    const activityDate = templateData.activityDate ? new Date(templateData.activityDate) : null
    
    let status = '报名中'
    if (deadline && deadline < now) {
      status = '报名截止'
    } else if (activityDate && activityDate < now) {
      status = '已结束'
    }
    
    return {
      type: 'activity',
      data: templateData,
      preview: {
        title: templateData.activityName || templateData.title || '活动',
        subtitle: templateData.activityDate ? 
          `${templateData.activityDate} ${templateData.startTime || ''} - ${templateData.endTime || ''}`.trim() :
          `${templateData.startTime || ''} - ${templateData.endTime || ''}`.trim(),
        status,
        participants: templateData.currentParticipants || 0,
        maxParticipants: templateData.maxParticipants || templateData.participantLimit || 0,
        location: templateData.activityLocation || templateData.location,
        fee: templateData.feeType || templateData.activityFee
      }
    }
  }
  
  if (templateData.originalPrice || templateData.groupPrice) {
    const now = new Date()
    const deadline = templateData.deadline ? new Date(templateData.deadline) : null
    const endTime = templateData.endTime ? new Date(templateData.endTime) : null
    
    let status = '团购中'
    if (deadline && deadline < now) {
      status = '已截止'
    } else if (endTime && endTime < now) {
      status = '已结束'
    }
    
    return {
      type: 'groupbuy',
      data: templateData,
      preview: {
        title: templateData.productName || templateData.title || '团购商品',
        originalPrice: templateData.originalPrice,
        groupPrice: templateData.groupPrice || templateData.price,
        currentCount: templateData.currentCount || templateData.joinedCount || 0,
        targetCount: templateData.targetCount || templateData.minCount || 0,
        deadline: templateData.deadline || templateData.endTime,
        status,
        location: templateData.pickupLocation || templateData.location
      }
    }
  }
  
  if (templateData.options && Array.isArray(templateData.options)) {
    const now = new Date()
    const deadline = templateData.deadline ? new Date(templateData.deadline) : null
    
    let status = '投票中'
    if (deadline && deadline < now) {
      status = '已截止'
    }
    
    return {
      type: 'vote',
      data: templateData,
      preview: {
        title: templateData.voteTitle || templateData.title || '投票表决',
        optionCount: templateData.options.length,
        voteType: templateData.voteType === 'single' ? '单选投票' : '多选投票',
        deadline: templateData.deadline,
        totalVotes: templateData.totalVotes || templateData.voteCount || 0,
        status,
        isAnonymous: templateData.isAnonymous || templateData.anonymous
      }
    }
  }
  
  if (templateData.questions && Array.isArray(templateData.questions)) {
    const now = new Date()
    const deadline = templateData.deadline ? new Date(templateData.deadline) : null
    
    let status = '调查中'
    if (deadline && deadline < now) {
      status = '已截止'
    }
    
    return {
      type: 'survey',
      data: templateData,
      preview: {
        title: templateData.surveyTitle || templateData.title || '问卷调查',
        questionCount: templateData.questions.length,
        deadline: templateData.deadline,
        responses: templateData.responses || templateData.responseCount || 0,
        status,
        isAnonymous: templateData.isAnonymous || templateData.anonymous
      }
    }
  }
  
  if (templateData.importance || templateData.announcementType) {
    return {
      type: 'announcement',
      data: templateData,
      preview: {
        title: templateData.announcementTitle || templateData.title || '公告通知',
        importance: templateData.importance || 'medium',
        effectiveDate: templateData.effectiveDate || templateData.effective_date,
        targetAudience: templateData.targetAudience || templateData.target_audience,
        publisher: templateData.publisher || '管理员',
        isUrgent: templateData.isUrgent || templateData.urgent
      }
    }
  }
  
  return null
})


// 获取内容预览
const getContentPreview = (content: string) => {
  if (!content) return ''
  // 移除HTML标签并截取前100个字符
  const plainText = content.replace(/<[^>]*>/g, '')
  return plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText
}

// 获取重要程度文本
const getImportanceText = (level: string) => {
  const levels = {
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return levels[level] || level
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

// 事件处理
const handleClick = () => {
  emit('click', props.post)
}

const handleLike = async () => {
  if (likeLoading.value) return
  
  // 乐观更新
  const originalIsLiked = props.post.isLiked
  const originalLikeCount = props.post.likeCount || 0
  
  // 立即更新UI
  props.post.isLiked = !originalIsLiked
  props.post.likeCount = originalIsLiked ? originalLikeCount - 1 : originalLikeCount + 1
  
  likeLoading.value = true
  
  try {
    emit('like', props.post)
    
    // 触发震动反馈
    if (props.post.isLiked) {
      uni.vibrateShort({
        type: 'light'
      })
    }
  } catch (error) {
    // 如果操作失败，回滚状态
    props.post.isLiked = originalIsLiked
    props.post.likeCount = originalLikeCount
    
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  } finally {
    // 延迟重置loading状态，给用户视觉反馈
    setTimeout(() => {
      likeLoading.value = false
    }, 300)
  }
}

const handleCollect = async () => {
  if (collectLoading.value) return
  
  // 乐观更新
  const originalIsCollected = props.post.isCollected
  const originalCollectCount = props.post.collectCount || 0
  
  // 立即更新UI
  props.post.isCollected = !originalIsCollected
  props.post.collectCount = originalIsCollected ? originalCollectCount - 1 : originalCollectCount + 1
  
  collectLoading.value = true
  
  try {
    emit('collect', props.post)
    
    // 触发震动反馈
    if (props.post.isCollected) {
      uni.vibrateShort({
        type: 'light'
      })
    }
  } catch (error) {
    // 如果操作失败，回滚状态
    props.post.isCollected = originalIsCollected
    props.post.collectCount = originalCollectCount
    
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  } finally {
    // 延迟重置loading状态，给用户视觉反馈
    setTimeout(() => {
      collectLoading.value = false
    }, 300)
  }
}

const handleShare = () => {
  emit('share', props.post)
}

const handleActionSelect = (action: { name: string }) => {
  showActionSheet.value = false
  
  if (action.name === '删除') {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除这条帖子吗？',
      success: (res) => {
        if (res.confirm) {
          emit('delete', props.post)
        }
      }
    })
  } else if (action.name === '举报') {
    uni.showToast({
      title: '举报功能开发中',
      icon: 'none'
    })
  }
}

// 媒体预览相关
const showMediaPreview = ref(false)
const previewMediaList = ref<string[]>([])
const previewInitialIndex = ref(0)

// 预览媒体文件（支持图片和视频混合）
const previewMedia = (mediaList: string[], current: number) => {
  previewMediaList.value = mediaList
  previewInitialIndex.value = current
  showMediaPreview.value = true
}

// 关闭媒体预览
const closeMediaPreview = () => {
  showMediaPreview.value = false
}

// 媒体预览索引变化
const handleMediaPreviewChange = (index: number) => {
  previewInitialIndex.value = index
}

// 检测媒体文件类型
const detectMediaType = (url: string): 'image' | 'video' | 'unknown' => {
  if (!url) return 'unknown'
  
  const lowerUrl = url.toLowerCase()
  
  // 检测图片格式
  if (lowerUrl.match(/\.(jpg|jpeg|png|gif|webp|bmp)(\?.*)?$/)) {
    return 'image'
  }
  
  // 检测视频格式
  if (lowerUrl.match(/\.(mp4|mov|avi|wmv|flv|webm|mkv)(\?.*)?$/)) {
    return 'video'
  }
  
  return 'unknown'
}

// 获取视频缩略图
const getVideoPoster = (videoUrl: string): string => {
  if (!videoUrl) return ''
  
  // 方案1: 如果服务器提供缩略图服务，可以通过URL参数获取
  // 例如: videoUrl + '?thumbnail=true' 或 videoUrl.replace('.mp4', '_thumb.jpg')
  
  // 方案2: 使用默认的视频缩略图占位符
  // 这里可以返回一个默认的视频封面图片URL
  
  // 方案3: 让video组件自动生成第一帧作为缩略图（当前方案）
  // 返回空字符串，video组件会自动显示第一帧
  return ''
  
  // 如果有专门的缩略图服务，可以这样实现：
  // const baseUrl = videoUrl.substring(0, videoUrl.lastIndexOf('.'))
  // return baseUrl + '_thumb.jpg'
}

// 兼容旧的预览图片方法
const previewImage = (images: string[], current: number) => {
  previewMedia(images, current)
}

// 高亮关键词
const getHighlightedContent = (content: string): string => {
  if (!props.highlightKeyword || !content) {
    return content
  }
  
  const keyword = props.highlightKeyword.trim()
  if (!keyword) {
    return content
  }
  
  // 使用正则表达式进行全局替换，忽略大小写
  const regex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return content.replace(regex, '<span class="highlight">$1</span>')
}

// 获取状态样式类
const getStatusClass = (status: string): string => {
  switch (status) {
    case '报名中':
    case '团购中':
    case '投票中':
    case '调查中':
      return 'active'
    case '报名截止':
    case '已截止':
    case '已结束':
      return 'expired'
    default:
      return 'normal'
  }
}

// 获取费用文本
const getFeeText = (fee: any): string => {
  if (!fee) return ''
  
  if (typeof fee === 'string') {
    return fee
  }
  
  if (fee.type === 'free') {
    return '免费'
  } else if (fee.type === 'aa') {
    return 'AA制'
  } else if (fee.type === 'fixed' && fee.amount) {
    return `¥${fee.amount}`
  }
  
  return ''
}

// 格式化数字显示
const formatCount = (count: number): string => {
  if (count < 1000) {
    return count.toString()
  } else if (count < 10000) {
    return (count / 1000).toFixed(1) + 'k'
  } else {
    return (count / 10000).toFixed(1) + 'w'
  }
}

// 处理内容中的链接点击
const handleContentLinkClick = (url: string) => {
  // 阻止事件冒泡，避免触发帖子点击
  event?.stopPropagation?.()
  
  if (url.startsWith('/') || url.includes('your-domain.com')) {
    uni.navigateTo({
      url: url
    })
  } else {
    uni.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(url)}`
    })
  }
}

// 处理内容中的图片点击
const handleContentImageClick = (src: string) => {
  // 阻止事件冒泡，避免触发帖子点击
  event?.stopPropagation?.()
  
  uni.previewImage({
    urls: [src],
    current: src
  })
}
</script>

<style scoped lang="scss">
.post-list-item {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.post-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.user-info {
  flex: 1;
  margin-left: 12px;
}

.user-name-row {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.user-name {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.post-time {
  font-size: 12px;
  color: #999;
}

.category-name {
  font-size: 12px;
  color: #007bff;
  background-color: #e3f2fd;
  padding: 2px 6px;
  border-radius: 4px;
}

.top-badge {
  display: flex;
  align-items: center;
  gap: 2px;
}

.top-text {
  font-size: 10px;
  color: #ff6b6b;
}

.post-actions {
  padding: 4px;
}

.post-content {
  margin-bottom: 12px;
}

.post-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8px;
}

.post-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8px;
}

.template-preview {
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  
  &.template-activity {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-color: #4CAF50;
  }
  
  &.template-groupbuy {
    background: linear-gradient(135deg, #fff3e0 0%, #fef7ed 100%);
    border-color: #FF9800;
  }
  
  &.template-vote {
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
    border-color: #2196F3;
  }
  
  &.template-survey {
    background: linear-gradient(135deg, #f3e5f5 0%, #faf4fb 100%);
    border-color: #9C27B0;
  }
  
  &.template-announcement {
    background: linear-gradient(135deg, #ffebee 0%, #fef5f5 100%);
    border-color: #F44336;
  }
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.preview-type {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  margin-left: auto;
  
  &.active {
    background-color: #4CAF50;
    color: white;
  }
  
  &.expired {
    background-color: #999;
    color: white;
  }
  
  &.normal {
    background-color: #007bff;
    color: white;
  }
}

.importance-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  
  &.importance-high {
    background-color: #F44336;
    color: white;
  }
  
  &.importance-medium {
    background-color: #FF9800;
    color: white;
  }
  
  &.importance-low {
    background-color: #4CAF50;
    color: white;
  }
}

.urgent-badge {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 2px 6px;
  background-color: rgba(255, 71, 87, 0.1);
  border-radius: 4px;
  margin-left: 8px;
}

.urgent-text {
  font-size: 10px;
  color: #FF4757;
  font-weight: 600;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.activity-name {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.activity-time, .activity-location {
  font-size: 11px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 2px;
}

.activity-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.activity-participants, .activity-fee {
  font-size: 11px;
  color: #666;
}

.product-name {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.original-price {
  font-size: 11px;
  color: #999;
  text-decoration: line-through;
}

.group-price {
  font-size: 13px;
  font-weight: 600;
  color: #FF9800;
}

.group-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-progress, .pickup-location {
  font-size: 11px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.vote-title, .survey-title, .announcement-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.vote-stats, .survey-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.vote-info, .vote-count, .survey-info, .survey-responses {
  font-size: 11px;
  color: #666;
}

.vote-anonymous, .survey-anonymous {
  display: flex;
  align-items: center;
  gap: 4px;
}

.anonymous-text {
  font-size: 10px;
  color: #999;
}

.announcement-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.publisher, .effective-date, .target-audience {
  font-size: 11px;
  color: #666;
}

.post-media {
  margin-bottom: 8px;
}

.single-media {
  border-radius: 8px;
  overflow: hidden;
}

.multi-media {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
}

.media-item {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}

.media-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  
  &.small {
    border-radius: 4px;
  }
  
  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  
  &.small {
    background: rgba(0, 0, 0, 0.4);
  }
}

.play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.small {
    transform: scale(0.8);
  }
}

.video-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 12px;
  
  &.small {
    top: 4px;
    right: 4px;
    padding: 2px 6px;
    border-radius: 8px;
  }
}

.video-text {
  color: white;
  font-size: 10px;
  font-weight: 500;
}

.more-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.more-text {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 6px;
  border-radius: 4px;
  transition: all 0.3s ease;
  
  &:active {
    background-color: #f5f5f5;
    transform: scale(0.95);
  }
  
  &.like-item.active {
    background-color: rgba(255, 107, 107, 0.1);
  }
  
  &.collect-item.active {
    background-color: rgba(255, 193, 7, 0.1);
  }
  
  &.loading {
    opacity: 0.7;
    pointer-events: none;
  }
}

.stat-text {
  font-size: 12px;
  color: #666;
  transition: color 0.3s ease;
  
  &.active {
    font-weight: 600;
  }
}

.actions {
  padding: 4px;
}

// 高亮样式
:deep(.highlight) {
  background-color: #fff3cd;
  color: #856404;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 600;
}

// 点赞动画
.like-icon.animate-like {
  animation: likeAnimation 0.6s ease-in-out;
}

@keyframes likeAnimation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

// 收藏动画
.collect-icon.animate-collect {
  animation: collectAnimation 0.6s ease-in-out;
}

@keyframes collectAnimation {
  0% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.2) rotate(-10deg);
  }
  50% {
    transform: scale(1.3) rotate(10deg);
  }
  75% {
    transform: scale(1.2) rotate(-5deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}
</style>