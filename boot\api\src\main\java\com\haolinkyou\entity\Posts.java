/*
 * @Author: Rock
 * @Date: 2025-05-01 00:44:47
 * @LastEditors: Rock
 * @LastEditTime: 2025-05-05 14:27:26
 * @Description: 
 */
package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@TableName("posts")
@EqualsAndHashCode(callSuper = true)
// 在序列化时会忽略所有 null 值的字段
// @JsonInclude(JsonInclude.Include.NON_NULL)
public class Posts extends BaseEntity{
    
    private Long userId;
    
    private Long categoryId;
    
    private String title;
    
    private String content;
    
    private String fileList;

    private Integer viewCount;

    private Integer likeCount;

    private Integer commentCount;

    private Integer collectCount;

    private Integer status;

    private Date reviewTime;

    private Long reviewUserId;

    private String reviewNote;

    private Boolean isTop;

    private Date topExpireTime;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 模板类型
     */
    private String templateType;

    /**
     * 模板数据JSON
     * 存储根据模板填写的结构化数据
     */
    private String templateData;
}