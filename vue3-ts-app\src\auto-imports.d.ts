/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const MEDIA_TYPE_CONFIG: typeof import('./types/publish')['MEDIA_TYPE_CONFIG']
  const MediaType: typeof import('./types/publish')['MediaType']
  const PublishStatus: typeof import('./types/publish')['PublishStatus']
  const UploadStatus: typeof import('./types/publish')['UploadStatus']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const createPinia: typeof import('pinia')['createPinia']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const detectFileType: typeof import('./types/publish')['detectFileType']
  const effectScope: typeof import('vue')['effectScope']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const h: typeof import('vue')['h']
  const inject: typeof import('vue')['inject']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onAddToFavorites: typeof import('@dcloudio/uni-app')['onAddToFavorites']
  const onBackPress: typeof import('@dcloudio/uni-app')['onBackPress']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onError: typeof import('@dcloudio/uni-app')['onError']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onHide: typeof import('@dcloudio/uni-app')['onHide']
  const onLaunch: typeof import('@dcloudio/uni-app')['onLaunch']
  const onLoad: typeof import('@dcloudio/uni-app')['onLoad']
  const onMounted: typeof import('vue')['onMounted']
  const onNavigationBarButtonTap: typeof import('@dcloudio/uni-app')['onNavigationBarButtonTap']
  const onNavigationBarSearchInputChanged: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputChanged']
  const onNavigationBarSearchInputClicked: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputClicked']
  const onNavigationBarSearchInputConfirmed: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputConfirmed']
  const onNavigationBarSearchInputFocusChanged: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputFocusChanged']
  const onPageNotFound: typeof import('@dcloudio/uni-app')['onPageNotFound']
  const onPageScroll: typeof import('@dcloudio/uni-app')['onPageScroll']
  const onPullDownRefresh: typeof import('@dcloudio/uni-app')['onPullDownRefresh']
  const onReachBottom: typeof import('@dcloudio/uni-app')['onReachBottom']
  const onReady: typeof import('@dcloudio/uni-app')['onReady']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onResize: typeof import('@dcloudio/uni-app')['onResize']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onShareAppMessage: typeof import('@dcloudio/uni-app')['onShareAppMessage']
  const onShareTimeline: typeof import('@dcloudio/uni-app')['onShareTimeline']
  const onShow: typeof import('@dcloudio/uni-app')['onShow']
  const onTabItemTap: typeof import('@dcloudio/uni-app')['onTabItemTap']
  const onThemeChange: typeof import('@dcloudio/uni-app')['onThemeChange']
  const onUnhandledRejection: typeof import('@dcloudio/uni-app')['onUnhandledRejection']
  const onUnload: typeof import('@dcloudio/uni-app')['onUnload']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const processFileSelection: typeof import('./types/publish')['processFileSelection']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useId: typeof import('vue')['useId']
  const useModel: typeof import('vue')['useModel']
  const useSlots: typeof import('vue')['useSlots']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const validateFileFormat: typeof import('./types/publish')['validateFileFormat']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
  // @ts-ignore
  export type { ApiResponse, Result } from './types/ApiResponse'
  import('./types/ApiResponse')
  // @ts-ignore
  export type { Categories } from './types/Categories'
  import('./types/Categories')
  // @ts-ignore
  export type { TopConfig, TopCheckResult, TopResult, TopCostResult } from './types/PostTop'
  import('./types/PostTop')
  // @ts-ignore
  export type { PostsList, Comments } from './types/PostsList'
  import('./types/PostsList')
  // @ts-ignore
  export type { AuthStatus, IdentityType, AuthUser, AuthApplication, FeedbackStatus, FeedbackItem, ProcessType, UserManagement, PostManagement, ActionOption, AdminProduct, ShareConfig, TabType, AdminStats, ReviewAction, BatchAction, AdminSearchParams, SystemConfig } from './types/admin'
  import('./types/admin')
  // @ts-ignore
  export type { BaseResponse, PaginationResponse, DictItem, UploadFile, FormRules, ActionButton, NavigationConfig, EventHandler, AsyncEventHandler, LoadingState, StateManager, ListState, SearchParams, BaseUser, Timestamp, Color, Size, Position, Dimensions, Rectangle, Option, TreeNode, TableColumn, TableData, MediaFile, Location, DeviceInfo, NetworkType, PermissionStatus, Theme, Language, Environment, LogLevel, CacheStrategy, HttpMethod, ContentType } from './types/common'
  import('./types/common')
  // @ts-ignore
  export type { User, UserRole, PointRecord, RedemptionRecord, UserAuthApplication, Feedback, UserStatusTag, LoginForm, LoginResponse, ProfileEditForm } from './types/user'
  import('./types/user')
  // @ts-ignore
  export type { Product, ProductForm, ProductListParams, RedeemForm } from './types/product'
  import('./types/product')
  // @ts-ignore
  export type { MediaType, UploadStatus, PublishStatus, FileTypeResult, UploadFileItem, PublishForm, LocationInfo, PublishResponse, UploadConfig, MediaType, UploadStatus, PublishStatus } from './types/publish'
  import('./types/publish')
  // @ts-ignore
  export type { PostDetail, CommentDetail, CurrentUser, PostStatusResponse, CommentSubmitParams, CommentDeleteParams, ImagePreviewOptions, SwiperChangeEvent, UniSwiperChangeEvent, UviewSwiperChangeEvent, SwiperChangeHandler, MediaFileType, OperationStatus, ExpandedCommentsState, ReplyTarget, PopupState, PostDetailPageState, TimeFormatter, ReplyContentFormatter, PermissionChecker, PostDetailApiResponse, CommentsApiResponse, CommentSubmitApiResponse, CommentDeleteApiResponse } from './types/postDetail'
  import('./types/postDetail')
  // @ts-ignore
  export type { LoadStatus, ButtonType, ButtonSize, TagType, ModalConfig, ToastConfig, PickerOption, FormRule, FormField, ListItem, TabItem, NavBarConfig, SearchConfig, PaginationConfig, PullUpConfig, PullDownConfig, EmptyConfig, SkeletonConfig, SwiperConfig, ImagePreviewConfig, ActionSheetItem, ActionSheetConfig } from './types/ui'
  import('./types/ui')
  // @ts-ignore
  export type { LoginResult, ProfileDetail, Gender, ProfileParams } from './types/member.d'
  import('./types/member.d')
  // @ts-ignore
  export type { ID, Optional, Nullable, PageParams, PageResult, SortParams, TimeRange, BaseSearchParams, OperationResult, FileInfo, GeoLocation, NetworkInfo, AppConfig, ThemeConfig, Permission, Role, CacheConfig, ErrorInfo, LogInfo } from './types/index'
  import('./types/index')
}
