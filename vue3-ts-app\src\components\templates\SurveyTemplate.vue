<template>
  <view class="survey-template">
    <!-- 基础模板 -->
    <BaseTemplate
      ref="baseTemplateRef"
      :titleRequired="true"
      :titlePlaceholder="`请填写${formData.surveyType === 'survey' ? '调查问卷' : '投票表决'}主题`"
      :contentPlaceholder="`请详细说明${formData.surveyType === 'survey' ? '调查问卷' : '投票表决'}的发起背景和目的，以便大家更好的参与`"
      :showMediaUpload="false"
      :showContact="false"
      :initialData="formData"
      @update:data="handleBaseDataChange"
      @update:valid="handleBaseValidChange"
    />

    <!-- 调查/投票特有字段 -->
    <view class="survey-fields">
      <!-- 类型选择 -->
      <view class="form-item">
        <view class="form-label">
          <text>类型</text>
          <text class="required">*</text>
        </view>
        <up-radio-group v-model="formData.surveyType" @change="handleTypeChange">
          <up-radio
            v-for="option in typeOptions"
            :key="option.value"
            :name="option.value"
            :label="option.label"
            :customStyle="{ marginBottom: '8px' }"
          />
        </up-radio-group>
        <view v-if="errors.surveyType" class="error-text">{{ errors.surveyType }}</view>
      </view>

      <!-- 选项设置 -->
      <view class="options-section">
        <view class="section-title">
          <text>{{ formData.surveyType === 'survey' ? '调查选项' : '投票选项' }}</text>
          <text class="required">*</text>
        </view>
        
        <view class="options-list">
          <view
            v-for="(option, index) in formData.options"
            :key="index"
            class="option-item"
          >
            <view class="option-input-wrapper">
              <up-input
                v-model="option.text"
                :placeholder="`选项 ${index + 1}`"
                @input="handleOptionChange"
                @blur="validateOptions"
              />
              <view
                v-if="formData.options.length > 2"
                class="delete-btn"
                @click="removeOption(index)"
              >
                <up-icon name="close-circle-fill" color="#ff4757" size="20"></up-icon>
              </view>
            </view>
          </view>
        </view>
        
        <view class="option-actions">
          <up-button
            v-if="formData.options.length < 10"
            type="primary"
            size="small"
            @click="addOption"
          >
            添加选项
          </up-button>
        </view>
        
        <view v-if="errors.options" class="error-text">{{ errors.options }}</view>
        <view class="help-text">至少需要2个选项，最多10个选项</view>
      </view>

      <!-- 投票设置 -->
      <view v-if="formData.surveyType === 'vote'" class="vote-settings">
        <view class="section-title">投票设置</view>
        
        <view class="form-item">
          <view class="form-label">
            <text>选择方式</text>
          </view>
          <up-radio-group v-model="formData.multiSelect" @change="handleMultiSelectChange">
            <up-radio
              name="false"
              label="单选（只能选择一个选项）"
              :customStyle="{ marginBottom: '8px' }"
            />
            <up-radio
              name="true"
              label="多选（可以选择多个选项）"
              :customStyle="{ marginBottom: '8px' }"
            />
          </up-radio-group>
        </view>

        <view class="form-item">
          <view class="form-label">
            <text>是否匿名</text>
          </view>
          <up-radio-group v-model="formData.anonymous" @change="handleAnonymousChange">
            <up-radio
              name="true"
              label="匿名投票（不显示投票人）"
              :customStyle="{ marginBottom: '8px' }"
            />
            <up-radio
              name="false"
              label="实名投票（显示投票人）"
              :customStyle="{ marginBottom: '8px' }"
            />
          </up-radio-group>
        </view>
      </view>

      <!-- 截止时间 -->
      <view class="form-item">
        <view class="form-label">
          <text>截止时间</text>
        </view>
        <up-datetime-picker
          hasInput
          v-model:show="showDeadlinePicker"
          v-model="deadlineValue"
          mode="datetime"
          :minDate="minDate"
          placeholder="选择截止时间（可选）"
          format="YYYY-MM-DD HH:mm"
          @confirm="handleDeadlineConfirm"
          @cancel="() => showDeadlinePicker = false"
        />
        <view class="help-text">不填则长期有效</view>
      </view>

      <!-- 仅对谁可见 -->
      <view class="form-item">
        <view class="form-label">
          <text>仅对谁可见</text>
        </view>
        <up-checkbox-group v-model="formData.targetAudience" @change="handleTargetChange">
          <up-checkbox
            v-for="option in targetOptions"
            :key="option.value"
            :name="option.value"
            :label="option.label"
            :customStyle="{ marginBottom: '8px' }"
          />
        </up-checkbox-group>
        <view v-if="errors.targetAudience" class="error-text">{{ errors.targetAudience }}</view>
        <view class="help-text">不选择则所有用户可参与，选择特定对象则仅其可参与</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BaseTemplate from './BaseTemplate.vue'
import { getTargetAudienceOptionsAPI, type TargetAudienceOption } from '@/services/roleService'

interface SurveyOption {
  text: string
}

interface Emits {
  (e: 'update:data', data: Record<string, any>): void
  (e: 'update:valid', valid: boolean): void
}

const emit = defineEmits<Emits>()

// 基础模板引用
const baseTemplateRef = ref()

// 表单数据
const formData = ref({
  title: '',
  content: '',
  images: [] as any[], // 兼容旧版本
  fileList: [] as any[], // 新版本文件列表
  surveyType: 'survey',
  options: [
    { text: '' },
    { text: '' }
  ] as SurveyOption[],
  multiSelect: 'false',
  anonymous: 'true',
  deadline: '',
  targetAudience: [] as string[] // 默认不选择（即所有用户可参与）
})

// 错误信息
const errors = ref<Record<string, string>>({})

// 基础模板是否有效
const baseValid = ref(false)

// 日期选择器
const showDeadlinePicker = ref(false)
const deadlineValue = ref<number>(Date.now() + 7 * 24 * 60 * 60 * 1000) // 默认一周后
const minDate = Date.now()

// 确保时间值始终是有效的时间戳
const ensureValidTimestamp = (value: any): number => {
  if (typeof value === 'number' && !isNaN(value) && value > 0) {
    return value
  }
  if (typeof value === 'string' && value) {
    const timestamp = new Date(value).getTime()
    if (!isNaN(timestamp)) {
      return timestamp
    }
  }
  return Date.now()
}

// 类型选项
const typeOptions = [
  { label: '调查问卷', value: 'survey' },
  { label: '投票表决', value: 'vote' }
]

// 目标对象选项（从接口动态获取）
const targetOptions = ref<TargetAudienceOption[]>([])

// 加载目标对象选项
const loadTargetOptions = async () => {
  try {
    const options = await getTargetAudienceOptionsAPI()
    targetOptions.value = options
  } catch (error) {
    console.error('加载目标对象选项失败:', error)
    // 使用默认选项作为后备
    targetOptions.value = [
      { label: '全体业主', value: 'owner' },
      { label: '租户', value: 'tenant' },
      { label: '物业人员', value: 'property' },
      { label: '业委会成员', value: 'committee' }
    ]
  }
}

// 验证表单是否有效
const isValid = computed(() => {
  const hasValidOptions = formData.value.options.length >= 2 &&
                         formData.value.options.every(opt => opt.text.trim() !== '')

  return baseValid.value &&
         formData.value.surveyType !== '' &&
         hasValidOptions &&
         Object.keys(errors.value).length === 0
})

// 监听数据变化
watch(formData, (newData) => {
  console.log('SurveyTemplate: 数据变化', newData)
  emit('update:data', newData)
}, { deep: true, immediate: true })

// 监听验证状态变化
watch(isValid, (valid) => {
  console.log('SurveyTemplate: 验证状态变化', valid)
  emit('update:valid', valid)
}, { immediate: true })

// 监听时间值变化，确保始终是有效的时间戳
watch(deadlineValue, (newValue) => {
  if (typeof newValue !== 'number' || isNaN(newValue) || newValue <= 0) {
    deadlineValue.value = Date.now() + 7 * 24 * 60 * 60 * 1000
  }
})

// 组件挂载时加载目标对象选项
onMounted(() => {
  loadTargetOptions()

  // 确保时间值是有效的
  deadlineValue.value = ensureValidTimestamp(deadlineValue.value)
})

// 处理基础模板数据变化
const handleBaseDataChange = (data: Record<string, any>) => {
  Object.assign(formData.value, data)
}

// 处理基础模板验证状态变化
const handleBaseValidChange = (valid: boolean) => {
  baseValid.value = valid
}

// 处理类型变化
const handleTypeChange = () => {
  if (formData.value.surveyType) {
    delete errors.value.surveyType
  }
}

// 处理选项变化
const handleOptionChange = () => {
  validateOptions()
}

// 处理多选变化
const handleMultiSelectChange = () => {
  // 多选设置变化时的处理
}

// 处理匿名变化
const handleAnonymousChange = () => {
  // 匿名设置变化时的处理
}

// 处理目标对象变化
const handleTargetChange = (selectedValues: string[]) => {
  formData.value.targetAudience = selectedValues

  // 目标对象现在是非必填的，所以不需要验证
  delete errors.value.targetAudience
}

// 处理截止时间确认
const handleDeadlineConfirm = (event: any) => {
  try {
    const timestamp = ensureValidTimestamp(event?.value)
    deadlineValue.value = timestamp

    // 格式化显示时间
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    const dateTimeStr = `${year}-${month}-${day} ${hour}:${minute}`

    formData.value.deadline = dateTimeStr
    showDeadlinePicker.value = false
  } catch (error) {
    console.error('截止时间确认处理失败:', error)
    showDeadlinePicker.value = false
  }
}

// 添加选项
const addOption = () => {
  if (formData.value.options.length < 10) {
    formData.value.options.push({ text: '' })
  }
}

// 删除选项
const removeOption = (index: number) => {
  if (formData.value.options.length > 2) {
    formData.value.options.splice(index, 1)
    validateOptions()
  }
}

// 验证选项
const validateOptions = () => {
  const validOptions = formData.value.options.filter(opt => opt.text.trim() !== '')
  
  if (validOptions.length < 2) {
    errors.value.options = '至少需要2个有效选项'
  } else {
    delete errors.value.options
  }
}

// 验证调查特有字段
const validateSurveyFields = () => {
  // 验证类型
  if (!formData.value.surveyType) {
    errors.value.surveyType = '请选择类型'
  } else {
    delete errors.value.surveyType
  }

  // 验证选项
  validateOptions()

  // 目标对象现在是非必填的，不需要验证
}

// 暴露验证方法
const validate = () => {
  const baseValidResult = baseTemplateRef.value?.validate() || false
  validateSurveyFields()
  
  return baseValidResult && isValid.value
}

// 暴露给父组件
defineExpose({
  validate,
  formData,
  isValid
})
</script>

<style scoped lang="scss">
.survey-template {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.survey-fields {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  margin-bottom: 12px;
}

.option-input-wrapper {
  display: flex;
  align-items: center;
  position: relative;
}

.option-input-wrapper :deep(.up-input) {
  flex: 1;
  padding-right: 40px;
}

.delete-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  padding: 4px;
  cursor: pointer;
  z-index: 10;

  &:hover {
    opacity: 0.7;
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }
}

.option-actions {
  margin-top: 12px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff3b30;
}

.error-text {
  font-size: 12px;
  color: #ff3b30;
}

.help-text {
  font-size: 12px;
  color: #999;
}
</style>
