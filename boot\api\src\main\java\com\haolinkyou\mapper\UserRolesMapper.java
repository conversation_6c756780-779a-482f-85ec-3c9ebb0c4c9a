package com.haolinkyou.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haolinkyou.entity.UserRoles;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface UserRolesMapper extends BaseMapper<UserRoles> {

    /**
     * 根据角色代码获取角色信息
     * @param roleCode 角色代码
     * @return 角色信息
     */
    @Select("SELECT * FROM user_roles WHERE role_code = #{roleCode} AND status = 1 AND del_flag = 0")
    UserRoles getRoleByCode(String roleCode);

    /**
     * 获取所有启用的角色
     * @return 角色列表
     */
    @Select("SELECT * FROM user_roles WHERE status = 1 AND del_flag = 0 ORDER BY sort_order ASC")
    List<UserRoles> getAllActiveRoles();

    /**
     * 根据权限获取角色列表
     * @param permission 权限代码
     * @return 角色列表
     */
    @Select("SELECT * FROM user_roles WHERE JSON_CONTAINS(permissions, JSON_QUOTE(#{permission})) AND status = 1 AND del_flag = 0")
    List<UserRoles> getRolesByPermission(String permission);
}
