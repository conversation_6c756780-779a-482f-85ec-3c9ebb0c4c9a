<template>
  <view class="vote-results-page">
    <!-- 页面头部 -->
    <up-navbar
      title="投票结果"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 内容区域 -->
    <view class="content-area" :style="{ marginTop: mainContentPaddingTop }">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <up-loading-icon mode="spinner" size="40"></up-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 结果内容 -->
      <view v-else-if="voteData" class="results-container">
        <!-- 投票概览 -->
        <view class="vote-overview">
          <view class="overview-title">{{ voteData.title }}</view>
          <view class="overview-info">
            <text class="info-text">发起人：{{ voteData.creatorName }}</text>
            <text class="info-text" v-if="voteData.endTime">
              截止时间：{{ formatDateTime(voteData.endTime) }}
            </text>
            <text class="info-text">
              投票方式：{{ voteData.voteType === 'single' ? '单选' : '多选' }}
            </text>
            <text class="info-text">
              {{ voteData.isAnonymous ? '匿名投票' : '实名投票' }}
            </text>
          </view>
          <view class="overview-stats">
            <view class="stat-item">
              <text class="stat-number">{{ totalVotes }}</text>
              <text class="stat-label">总票数</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ voteData.participantCount || 0 }}</text>
              <text class="stat-label">参与人数</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ voteData.options?.length || 0 }}</text>
              <text class="stat-label">选项数量</text>
            </view>
          </view>
        </view>

        <!-- 投票结果图表 -->
        <view class="vote-chart">
          <view class="chart-title">投票结果统计</view>
          <view class="options-results">
            <view
              v-for="(option, index) in voteData.options"
              :key="option.id || index"
              class="option-result"
            >
              <view class="option-header">
                <view class="option-rank">{{ index + 1 }}</view>
                <view class="option-info">
                  <text class="option-text">{{ option.text }}</text>
                  <text class="option-votes">{{ getOptionVotes(option.id || index) }}票</text>
                </view>
              </view>
              
              <view class="option-progress">
                <view 
                  class="progress-bar"
                  :class="{ 'winner': index === 0 && getOptionVotes(option.id || index) > 0 }"
                  :style="{ width: getOptionPercentage(option.id || index) + '%' }"
                ></view>
              </view>
              
              <view class="option-stats">
                <text class="option-percentage">{{ getOptionPercentage(option.id || index) }}%</text>
                <text v-if="index === 0 && getOptionVotes(option.id || index) > 0" class="winner-badge">领先</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 参与者列表（实名投票时显示） -->
        <view v-if="!voteData.isAnonymous && participantsList.length > 0" class="participants-section">
          <view class="participants-title">参与者列表</view>
          <view class="participants-list">
            <view
              v-for="participant in participantsList"
              :key="participant.id"
              class="participant-item"
            >
              <up-avatar :text="participant.username" :size="32" randomBgColor></up-avatar>
              <view class="participant-info">
                <text class="participant-name">{{ participant.username }}</text>
                <text class="participant-choice">选择：{{ getParticipantChoice(participant.selectedOptions) }}</text>
                <text class="participant-time">{{ formatDateTime(participant.voteTime) }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 投票趋势（如果有时间数据） -->
        <view v-if="trendData.length > 0" class="trend-section">
          <view class="trend-title">投票趋势</view>
          <view class="trend-chart">
            <!-- 这里可以集成图表库显示趋势图 -->
            <text class="trend-placeholder">投票趋势图（开发中）</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="actions-container">
          <up-button
            type="default"
            size="large"
            @click="handleExport"
            :loading="exporting"
            customStyle="border-radius: 12px; margin-bottom: 12px;"
          >
            {{ exporting ? '导出中...' : '导出结果' }}
          </up-button>
          
          <up-button
            type="primary"
            size="large"
            @click="handleParticipate"
            customStyle="background-color: #5677fc; border-radius: 12px;"
          >
            参与投票
          </up-button>
        </view>
      </view>

      <!-- 错误状态 -->
      <view v-else class="error-container">
        <up-icon name="warning-fill" size="60" color="#ff6b6b"></up-icon>
        <text class="error-text">{{ errorMessage || '加载失败，请重试' }}</text>
        <up-button
          type="primary"
          size="small"
          @click="loadVoteResults"
          customStyle="margin-top: 20px;"
        >
          重新加载
        </up-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useSafeArea } from '@/utils/safeArea'
import { get } from '@/utils/http'
import { formatDateTime } from '@/utils/timeFormat'

// 安全区域
const { mainContentPaddingTop } = useSafeArea()

// 页面参数
const postId = ref<number>(0)

// 页面状态
const loading = ref(true)
const exporting = ref(false)
const errorMessage = ref('')

// 数据
const voteData = ref<any>(null)
const resultsData = ref<any>(null)
const participantsList = ref<any[]>([])
const trendData = ref<any[]>([])

// 获取页面参数
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.id) {
    postId.value = parseInt(options.id)
    loadVoteResults()
  } else {
    errorMessage.value = '参数错误'
    loading.value = false
  }
})

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 加载投票结果
const loadVoteResults = async () => {
  loading.value = true
  errorMessage.value = ''

  try {
    // 获取投票基本信息
    const postResponse = await get(`/posts/detail?id=${postId.value}`)
    
    if (postResponse.success && postResponse.data) {
      const postData = postResponse.data
      
      // 解析模板数据
      let templateData = {}
      if (typeof postData.templateData === 'string') {
        try {
          templateData = JSON.parse(postData.templateData)
        } catch (error) {
          console.error('解析模板数据失败:', error)
        }
      } else if (postData.templateData) {
        templateData = postData.templateData
      }

      voteData.value = {
        id: postData.id,
        title: postData.title || '投票表决',
        description: templateData.description || postData.content,
        creatorName: postData.nickname,
        endTime: templateData.endTime,
        participantCount: templateData.participantCount || 0,
        options: templateData.options || [],
        voteType: templateData.voteType || 'single',
        isAnonymous: templateData.isAnonymous || false
      }
    }

    // 获取投票结果数据
    const resultsResponse = await get(`/vote/results?postId=${postId.value}`)
    
    if (resultsResponse.success) {
      resultsData.value = resultsResponse.data || {}
      
      // 如果是实名投票，获取参与者列表
      if (!voteData.value.isAnonymous) {
        participantsList.value = resultsData.value.participants || []
      }
      
      // 获取趋势数据（如果有）
      trendData.value = resultsData.value.trend || []
      
      console.log('投票结果加载成功:', resultsData.value)
    } else {
      // 如果没有结果数据，创建空的结果结构
      resultsData.value = {
        optionVotes: {},
        totalVotes: 0,
        participants: [],
        trend: []
      }
    }

  } catch (error) {
    console.error('加载投票结果失败:', error)
    errorMessage.value = error.message || '网络错误，请重试'
  } finally {
    loading.value = false
  }
}

// 计算总票数
const totalVotes = computed(() => {
  if (!resultsData.value?.optionVotes) return 0
  return Object.values(resultsData.value.optionVotes).reduce((sum: number, count: any) => sum + (count || 0), 0)
})

// 获取选项票数
const getOptionVotes = (optionId: number | string): number => {
  if (!resultsData.value?.optionVotes) return 0
  return resultsData.value.optionVotes[optionId] || 0
}

// 获取选项百分比
const getOptionPercentage = (optionId: number | string): number => {
  const votes = getOptionVotes(optionId)
  const total = totalVotes.value
  
  if (total === 0) return 0
  return Math.round((votes / total) * 100)
}

// 获取参与者选择的选项文本
const getParticipantChoice = (selectedOptions: any[]): string => {
  if (!Array.isArray(selectedOptions) || !voteData.value?.options) return '未知'
  
  const choiceTexts = selectedOptions.map(optionId => {
    const option = voteData.value.options.find((opt: any) => opt.id === optionId || opt.id === optionId.toString())
    return option?.text || '未知选项'
  })
  
  return choiceTexts.join(', ')
}

// 导出结果
const handleExport = async () => {
  exporting.value = true
  
  try {
    // 这里可以调用导出API或者生成本地文件
    uni.showToast({
      title: '导出功能开发中',
      icon: 'none'
    })
  } catch (error) {
    uni.showToast({
      title: '导出失败',
      icon: 'none'
    })
  } finally {
    exporting.value = false
  }
}

// 参与投票
const handleParticipate = () => {
  uni.navigateTo({
    url: `/pages/vote/participate?id=${postId.value}`
  })
}
</script>

<style scoped lang="scss">
.vote-results-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.content-area {
  padding: 0 20px 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #666;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-text {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}

.results-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.vote-overview {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.overview-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  text-align: center;
}

.overview-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.info-text {
  font-size: 12px;
  color: #666;
}

.overview-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #5677fc;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.vote-chart {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.options-results {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-result {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #5677fc;
  color: white;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.option-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.option-votes {
  font-size: 14px;
  color: #666;
}

.option-progress {
  height: 12px;
  background-color: #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #5677fc;
  transition: width 0.5s ease;
  
  &.winner {
    background: linear-gradient(90deg, #5677fc 0%, #4CAF50 100%);
  }
}

.option-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-percentage {
  font-size: 14px;
  color: #5677fc;
  font-weight: 600;
}

.winner-badge {
  font-size: 12px;
  color: #4CAF50;
  font-weight: 600;
  padding: 2px 8px;
  background-color: #E8F5E8;
  border-radius: 12px;
}

.participants-section {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.participants-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.participants-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.participant-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.participant-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.participant-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.participant-choice {
  font-size: 12px;
  color: #5677fc;
}

.participant-time {
  font-size: 12px;
  color: #999;
}

.trend-section {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.trend-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.trend-chart {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.trend-placeholder {
  font-size: 14px;
  color: #666;
}

.actions-container {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
</style>