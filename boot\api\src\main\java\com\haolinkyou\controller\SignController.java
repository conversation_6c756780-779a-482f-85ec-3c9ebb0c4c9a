package com.haolinkyou.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.UserSignRecords;
import com.haolinkyou.service.ISignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 签到控制器
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@RestController
@RequestMapping("/api/sign")
public class SignController {

    @Autowired
    private ISignService signService;

    /**
     * 每日签到
     */
    @PostMapping("/daily")
    public Result<ISignService.SignResult> dailySign(HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            ISignService.SignResult result = signService.dailySign(userId);
            if (result.isSuccess()) {
                return Result.success(result, result.getMessage());
            } else {
                return Result.error(result.getMessage());
            }
        } catch (Exception e) {
            return Result.error("签到失败: " + e.getMessage());
        }
    }

    /**
     * 获取签到状态
     */
    @GetMapping("/status")
    public Result<ISignService.SignStatus> getSignStatus(HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            ISignService.SignStatus status = signService.getSignStatus(userId);
            return Result.success(status);
        } catch (Exception e) {
            return Result.error("获取签到状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取签到记录（分页）
     */
    @GetMapping("/records")
    public Result<Page<UserSignRecords>> getSignRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 参数验证
            if (page < 1) {
                return Result.error("页码必须大于0");
            }
            if (size < 1 || size > 100) {
                return Result.error("每页大小必须在1-100之间");
            }

            Page<UserSignRecords> records = signService.getUserSignRecords(userId, page, size);
            return Result.success(records);
        } catch (Exception e) {
            return Result.error("获取签到记录失败: " + e.getMessage());
        }
    }

    /**
     * 检查今日是否已签到
     */
    @GetMapping("/check")
    public Result<Boolean> checkTodaySign(HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            boolean hasSignedToday = signService.hasSignedToday(userId);
            return Result.success(hasSignedToday);
        } catch (Exception e) {
            return Result.error("检查签到状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取连续签到天数
     */
    @GetMapping("/continuous")
    public Result<Integer> getContinuousDays(HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            int continuousDays = signService.calculateContinuousDays(userId);
            return Result.success(continuousDays);
        } catch (Exception e) {
            return Result.error("获取连续签到天数失败: " + e.getMessage());
        }
    }
}