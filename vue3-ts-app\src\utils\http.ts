/**
 * 添加拦截器:
 *   拦截 request 请求
 *   拦截 uploadFile 文件上传
 *
 * TODO:
 *   1. 非 http 开头需拼接地址
 *   2. 请求超时
 *   3. 添加小程序端请求头标识
 *   4. 添加 token 请求头标识
 */

import { useMemberStore } from "@/stores";

const baseURL = "/api";

// 添加拦截器
const httpInterceptor = {
  // 拦截前触发
  invoke(options: UniApp.RequestOptions) {
    // 1. 非 http 开头需拼接地址
    if (!options.url.startsWith("http")) {
      options.url = baseURL + options.url;
    }
    // 2. 请求超时, 默认 60s
    options.timeout = 10000;
    // 3. 添加小程序端请求头标识
    options.header = {
      ...options.header,
      "source-client": "miniapp",
    };

    // 4. 处理Content-Type
    if (!options.header["Content-Type"]) {
      options.header["Content-Type"] = "application/json";
    }

    // 5. 处理表单数据
    if (options.header["Content-Type"] === "application/x-www-form-urlencoded" && options.data) {
      // 将对象转换为表单字符串
      const formData = Object.keys(options.data)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options.data[key])}`)
        .join('&');
      options.data = formData;
    }

    // 6. 添加 token 请求头标识
    const memberStore = useMemberStore();
    const token = memberStore.profile?.token;
    if (token) {
      // 添加Bearer前缀，符合JWT标准
      options.header.Authorization = `Bearer ${token}`;
      console.log('HTTP请求添加token:', options.url, token ? '有token' : '无token');
    } else {
      console.log('HTTP请求无token:', options.url);
    }
  },
};
uni.addInterceptor("request", httpInterceptor);
uni.addInterceptor("uploadFile", httpInterceptor);

/**
 * 请求函数
 * @param  UniApp.RequestOptions
 * @returns Promise
 *  1. 返回 Promise 对象
 *  2. 获取数据成功
 *    2.1 提取核心数据 res.data
 *    2.2 添加类型，支持泛型
 *  3. 获取数据失败
 *    3.1 401错误  -> 清理用户信息，跳转到登录页
 *    3.2 其他错误 -> 根据后端错误信息轻提示
 *    3.3 网络错误 -> 提示用户换网络
 */

// 2.2 添加类型，支持泛型
export const http = <T>(options: UniApp.RequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<T>((resolve, reject) => {
    uni.request({
      ...options,
      // 响应成功
      success(res) {
        // 状态码 2xx， axios 就是这样设计的
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 2.1 提取核心数据 res.data
          resolve(res.data as T);
        } else if (res.statusCode === 401) {
          // 401错误  -> 清理用户信息，跳转到登录页
          const memberStore = useMemberStore();
          memberStore.clearProfile();
          uni.navigateTo({ url: "/pages/login/login" });
          reject(res);
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          uni.showToast({
            icon: "none",
            title: (res.data as any).msg || "请求错误",
          });
          reject(res);
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: "none",
          title: "网络错误，换个网络试试",
        });
        reject(err);
      },
    });
  });
};

// 封装GET请求
export const get = <T>(url: string, data?: any) => {
  return http<T>({
    url,
    method: "GET",
    data,
  });
};

// 封装POST请求（自动处理表单格式）
export const post = <T>(url: string, data?: any) => {
  // 对于点赞收藏相关的API，使用表单格式
  const isFormAPI = url.includes('/post-likes/') || url.includes('/user-collects/');

  return http<T>({
    url,
    method: "POST",
    data,
    header: isFormAPI ? {
      'Content-Type': 'application/x-www-form-urlencoded'
    } : undefined
  });
};

// 封装PUT请求
export const put = <T>(url: string, data?: any) => {
  return http<T>({
    url,
    method: "PUT",
    data
  });
};

// 封装DELETE请求
export const del = <T>(url: string, data?: any) => {
  return http<T>({
    url,
    method: "DELETE",
    ...(data ? { data } : {})
  });
};
