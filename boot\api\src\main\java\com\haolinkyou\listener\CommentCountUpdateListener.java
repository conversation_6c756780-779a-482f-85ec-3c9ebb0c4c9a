package com.haolinkyou.listener;

import com.haolinkyou.event.CommentCountUpdateEvent;
import com.haolinkyou.service.IPostsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 评论数更新事件监听器
 */
@Component
public class CommentCountUpdateListener {
    
    @Autowired
    private IPostsService postsService;
    
    /**
     * 处理评论数更新事件
     */
    @EventListener
    @Async
    public void handleCommentCountUpdate(CommentCountUpdateEvent event) {
        Long postId = event.getPostId();
        if (postId != null) {
            postsService.updateCommentCount(postId);
        }
    }
}
