/*
 * @Author: Rock
 * @Date: 2025-05-01 00:46:30
 * @LastEditors: Rock <EMAIL>
 * @LastEditTime: 2025-07-30 10:28:42
 * @Description: 
 */
package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.api.dto.PageDto;
import com.haolinkyou.api.dto.PostListAllDto;
import com.haolinkyou.api.vo.PostDetailVo;
import com.haolinkyou.api.vo.PostsListVo;
import com.haolinkyou.entity.Comments;
import com.haolinkyou.entity.Posts;
import com.haolinkyou.entity.Users;
import com.haolinkyou.entity.AuthApplications;
import com.haolinkyou.mapper.CommentsMapper;
import com.haolinkyou.mapper.PostsMapper;
import com.haolinkyou.mapper.AuthApplicationsMapper;
import com.haolinkyou.service.IPostsService;
import com.haolinkyou.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class PostsServiceImpl extends ServiceImpl<PostsMapper, Posts> implements IPostsService {

    @Autowired
    private PostsMapper postsMapper;

    @Autowired
    private CommentsMapper commentsMapper;
    
    @Autowired
    private AuthApplicationsMapper authApplicationsMapper;
    
    @Autowired
    private IUserService userService;

    @Override
    public IPage<PostsListVo> selectByPage(PageDto dto) {
        if (dto.getPage() == null || dto.getPageSize() == null) {
            // 如果分页参数为空，则查询所有数据
            Page<PostsListVo> pageObj = new Page<>(1, 9999);
            return baseMapper.selectByPage(pageObj);
        }
        Page<PostsListVo> pageObj = new Page<>(dto.getPage(), dto.getPageSize());
        return baseMapper.selectByPage(pageObj);
    }

    @Override
    public IPage<PostsListVo> listAllPosts(PostListAllDto dto) {
        QueryWrapper<Posts> queryWrapper = new QueryWrapper<>();

        if (dto.getUserId() != null) {
            queryWrapper.eq("p.user_id", dto.getUserId());
        }

        if (dto.getCategoryId() != null) {
            queryWrapper.eq("p.category_id", dto.getCategoryId());
        }

        if (dto.getTitle() != null && !dto.getTitle().isEmpty()) {
            queryWrapper.like("title", dto.getTitle());
        }

        queryWrapper.eq("p.del_flag", 0); // 只查未删除的帖子

        // 游客只能看到已审核通过的帖子
        queryWrapper.eq("p.status", 1);

        // 置顶帖子优先，然后按创建时间倒序
        queryWrapper.orderByDesc("p.is_top")
                   .orderByDesc("p.created_time");

        Page<PostsListVo> pageQuery = new Page<>(dto.getPage(), dto.getPageSize());

        return baseMapper.listAllPosts(pageQuery, queryWrapper);
    }

    @Override
    public IPage<PostsListVo> listAllPostsWithUserStatus(PostListAllDto dto, Long userId) {
        QueryWrapper<Posts> queryWrapper = new QueryWrapper<>();

        // 如果DTO中的userId不为null，则过滤特定用户发布的帖子
        if (dto.getUserId() != null) {
            queryWrapper.eq("p.user_id", dto.getUserId());
        }

        if (dto.getCategoryId() != null) {
            queryWrapper.eq("p.category_id", dto.getCategoryId());
        }

        if (dto.getTitle() != null && !dto.getTitle().isEmpty()) {
            queryWrapper.like("title", dto.getTitle());
        }

        queryWrapper.eq("p.del_flag", 0); // 只查未删除的帖子

        // 权限控制：用户可以看到自己的所有状态帖子 + 其他人已审核通过的帖子
        // 这里需要在Controller层判断用户是否为管理员，如果是管理员则不添加此限制

        // 置顶帖子优先，然后按创建时间倒序
        queryWrapper.orderByDesc("p.is_top")
                   .orderByDesc("p.created_time");

        Page<PostsListVo> pageQuery = new Page<>(dto.getPage(), dto.getPageSize());
        // 禁用COUNT查询优化，避免ORDER BY在子查询中的问题
        pageQuery.setOptimizeCountSql(false);

        return baseMapper.listAllPostsWithUserStatus(pageQuery, queryWrapper, userId);
    }

    @Override
    public IPage<PostsListVo> listAllPostsWithPermission(PostListAllDto dto, Long userId, boolean isAdmin) {
        QueryWrapper<Posts> queryWrapper = new QueryWrapper<>();

        // 如果DTO中的userId不为null，则过滤特定用户发布的帖子
        if (dto.getUserId() != null) {
            queryWrapper.eq("p.user_id", dto.getUserId());
        }

        if (dto.getCategoryId() != null) {
            queryWrapper.eq("p.category_id", dto.getCategoryId());
        }

        if (dto.getTitle() != null && !dto.getTitle().isEmpty()) {
            queryWrapper.like("title", dto.getTitle());
        }

        queryWrapper.eq("p.del_flag", 0); // 只查未删除的帖子

        // 权限控制逻辑
        if (!isAdmin) {
            // 非管理员用户：只能看到已审核通过的帖子 + 自己的所有状态帖子
            queryWrapper.and(wrapper ->
                wrapper.eq("p.status", 1) // 已审核通过的帖子
                    .or()
                    .eq("p.user_id", userId) // 或者是自己发布的帖子
            );
            
            // 添加目标对象权限过滤
            if (userId != null) {
                // 获取当前用户的角色信息
                Users currentUser = userService.getById(userId);
                String tempUserRole = "guest"; // 默认游客
                
                if (currentUser != null) {
                    // 检查用户认证状态
                    QueryWrapper<AuthApplications> authQuery = new QueryWrapper<>();
                    authQuery.eq("user_id", userId)
                            .eq("status", 1) // 已认证
                            .eq("del_flag", 0);
                    
                    AuthApplications authApp = authApplicationsMapper.selectOne(authQuery);
                    if (authApp != null) {
                        tempUserRole = authApp.getIdentityType();
                    } else {
                        tempUserRole = currentUser.getUserRole();
                    }
                }
                
                final String userRole = tempUserRole; // 声明为final以便在lambda中使用
                final Long finalUserId = userId; // 声明为final以便在lambda中使用
                
                // 添加目标对象权限过滤
                queryWrapper.and(targetWrapper -> {
                    // 没有设置目标对象的帖子（对所有人可见）
                    targetWrapper.isNull("p.template_data")
                        .or()
                        .notLike("p.template_data", "\"targetAudience\"")
                        .or()
                        .like("p.template_data", "\"targetAudience\":[]")
                        .or()
                        .like("p.template_data", "\"targetAudience\":\"[]\"")
                        // 或者目标对象包含当前用户角色
                        .or()
                        .like("p.template_data", "\"" + userRole + "\"")
                        // 或者是自己发布的帖子
                        .or()
                        .eq("p.user_id", finalUserId);
                });
            }
        }
        // 管理员可以看到所有帖子，不添加状态限制

        // 置顶帖子优先，然后按创建时间倒序
        queryWrapper.orderByDesc("p.is_top")
                   .orderByDesc("p.created_time");

        Page<PostsListVo> pageQuery = new Page<>(dto.getPage(), dto.getPageSize());
        // 禁用COUNT查询优化，避免ORDER BY在子查询中的问题
        pageQuery.setOptimizeCountSql(false);

        return baseMapper.listAllPostsWithUserStatus(pageQuery, queryWrapper, userId);
    }

    @Override
    public IPage<PostsListVo> getUserPosts(PostListAllDto dto, Integer status) {
        QueryWrapper<Posts> queryWrapper = new QueryWrapper<>();

        // 必须指定用户ID
        if (dto.getUserId() != null) {
            queryWrapper.eq("p.user_id", dto.getUserId());
        }

        // 如果指定了状态，则添加状态过滤
        if (status != null) {
            queryWrapper.eq("p.status", status);
        }

        // 可以查询用户自己的所有状态的动态（包括待审核、已发布、已拒绝等）
        queryWrapper.eq("p.del_flag", 0); // 只查未删除的帖子
        queryWrapper.orderByDesc("p.created_time"); // 按创建时间倒序

        Page<PostsListVo> pageQuery = new Page<>(dto.getPage(), dto.getPageSize());

        // 使用普通的查询方法，因为用户查看自己的动态不需要点赞收藏状态
        return baseMapper.listAllPosts(pageQuery, queryWrapper);
    }

    @Override
    public PostDetailVo selectPostDetailById(Long id) {
        return baseMapper.selectPostDetailById(id);
    }

    @Override
    public boolean add(Posts post) {
        return postsMapper.insert(post) > 0;
    }

    @Override
    public Posts selectById(Long id) {
        return postsMapper.selectById(id);
    }

    @Override
    public boolean updateById(Posts post) {
        return postsMapper.updateById(post) >0;
    }


    @Override
    public boolean removeById(Long id) {
       return postsMapper.deleteById(id) >0;
    }

    @Override
    public boolean updateCommentCount(Long postId) {
        if (postId == null) {
            return false;
        }

        // 查询该帖子的评论数
        QueryWrapper<Posts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", postId);
        Posts post = this.getOne(queryWrapper);

        if (post == null) {
            return false;
        }

        // 统计该帖子的所有评论数（包括回复）
        QueryWrapper<Comments> commentQueryWrapper = new QueryWrapper<>();
        commentQueryWrapper.eq("post_id", postId)
                          .eq("del_flag", 0); // 只统计未删除的评论

        // 直接使用CommentsMapper统计，避免循环依赖
        long totalCommentCount = commentsMapper.selectCount(commentQueryWrapper);

        // 更新帖子的评论数
        UpdateWrapper<Posts> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", postId)
                    .set("comment_count", totalCommentCount);

        return this.update(updateWrapper);
    }

    @Override
    public IPage<PostsListVo> listAllPostsForAdmin(PostListAllDto dto, Integer status) {
        System.out.println("=== 管理员帖子查询 ===");
        System.out.println("分类ID: " + dto.getCategoryId());
        System.out.println("标题搜索: " + dto.getTitle());
        System.out.println("状态筛选: " + status);

        QueryWrapper<Posts> queryWrapper = new QueryWrapper<>();

        // 分类筛选
        if (dto.getCategoryId() != null) {
            queryWrapper.eq("p.category_id", dto.getCategoryId());
            System.out.println("添加分类筛选: " + dto.getCategoryId());
        }

        // 标题搜索 - 搜索内容而不是标题
        if (dto.getTitle() != null && !dto.getTitle().trim().isEmpty()) {
            queryWrapper.like("p.content", dto.getTitle().trim());
            System.out.println("添加内容搜索: " + dto.getTitle().trim());
        }

        // 状态筛选 - 管理员可以看到所有状态
        if (status != null) {
            queryWrapper.eq("p.status", status);
            System.out.println("添加状态筛选: " + status);
        }

        // 只查询未删除的帖子
        queryWrapper.eq("p.del_flag", 0);

        // 按创建时间倒序排列
        queryWrapper.orderByDesc("p.created_time");

        // 分页查询
        Page<PostsListVo> pageQuery = new Page<>(dto.getPage(), dto.getPageSize());

        // 禁用COUNT查询优化，避免ORDER BY在子查询中的问题
        pageQuery.setOptimizeCountSql(false);

        IPage<PostsListVo> result = baseMapper.listAllPosts(pageQuery, queryWrapper);
        System.out.println("查询结果数量: " + result.getRecords().size());

        return result;
    }
}