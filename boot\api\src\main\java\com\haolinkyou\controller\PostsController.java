/*
 * @Author: Rock
 * @Date: 2025-05-01 00:47:00
 * @LastEditors: Rock <EMAIL>
 * @LastEditTime: 2025-07-29 16:59:14
 * @Description: 
 */
package com.haolinkyou.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.haolinkyou.api.dto.PageDto;
import com.haolinkyou.api.dto.PostListAllDto;
import com.haolinkyou.api.vo.PostDetailVo;
import com.haolinkyou.api.vo.PostsListVo;
import com.haolinkyou.common.result.Result;
import com.haolinkyou.entity.Posts;
import com.haolinkyou.entity.Users;
import com.haolinkyou.service.*;
import com.haolinkyou.util.PermissionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/api/posts")
public class PostsController {

    private static final Logger log = LoggerFactory.getLogger(PostsController.class); // 手动声明日志记录器

    @Autowired
    private IPostsService postsService;

    @Autowired
    private IPostFilesService postFilesService;



    @Autowired
    private IPostLikesService postLikesService;

    @Autowired
    private IUserCollectsService userCollectsService;

    @Autowired
    private IUserService userService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private PermissionUtil permissionUtil;

    @Autowired
    private IPointsService pointsService;

    @Autowired
    private IPostTopService postTopService;

    // 获取帖子列表
    @GetMapping("/list")
    public Result<IPage<PostsListVo>> list(PageDto pageDto) {
        IPage<PostsListVo> postsList = postsService.selectByPage(pageDto);
        return Result.success(postsList);
    }

    @GetMapping("/listAll")
    public Result<IPage<PostsListVo>> listAll(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Long authorUserId, // 作者用户ID，用于过滤特定用户的帖子
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            HttpServletRequest request) {

        // 从token中获取当前用户ID（如果已登录）
        Long currentUserId = (Long) request.getAttribute("userId");

        // 构建DTO，注意这里不设置userId，避免过滤
        PostListAllDto postListAllDto = new PostListAllDto();
        postListAllDto.setCategoryId(categoryId);
        postListAllDto.setTitle(title);
        postListAllDto.setUserId(authorUserId); // 只有明确指定authorUserId时才过滤
        postListAllDto.setPage(page);
        postListAllDto.setPageSize(pageSize);

        IPage<PostsListVo> postsList;
        if (currentUserId != null) {
            // 判断用户是否为管理员
            boolean isAdmin = false;
            try {
                // 获取用户信息判断是否为管理员
                Users user = userService.getById(currentUserId);
                isAdmin = user != null && "admin".equals(user.getUserRole());
            } catch (Exception e) {
                log.warn("获取用户角色信息失败，用户ID: {}", currentUserId, e);
            }

            // 如果用户已登录，则查询包含用户状态和权限控制的帖子列表
            postsList = postsService.listAllPostsWithPermission(postListAllDto, currentUserId, isAdmin);
        } else {
            // 否则查询普通的帖子列表（游客访问，只能看到已审核通过的帖子）
            postsList = postsService.listAllPosts(postListAllDto);
        }
        return Result.success(postsList);
    }

    /**
     * 获取用户个人发布的动态列表
     */
    @GetMapping("/my")
    public Result<IPage<PostsListVo>> getMyPosts(
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            HttpServletRequest request) {

        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            log.info("获取用户个人动态，用户ID: {}, 状态: {}, 页码: {}, 页大小: {}", userId, status, page, pageSize);

            // 构建查询条件
            PostListAllDto postListAllDto = new PostListAllDto();
            postListAllDto.setUserId(userId); // 使用从token中获取的用户ID，忽略前端传递的userId
            postListAllDto.setPage(page);
            postListAllDto.setPageSize(pageSize);

            // 获取用户自己的动态（包括所有状态：待审核、已发布、已拒绝等）
            IPage<PostsListVo> postsList = postsService.getUserPosts(postListAllDto, status);

            return Result.success(postsList);
        } catch (Exception e) {
            log.error("获取用户个人动态失败", e);
            return Result.error("获取个人动态失败：" + e.getMessage());
        }
    }

    @PostMapping("/add")
    public ResponseEntity<Posts> add(
            @RequestParam(value = "category_id", required = false) Long categoryId,
            @RequestParam(value = "content", required = false) String content,
            @RequestParam(value = "template_id", required = false) Long templateId,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "currentFileType", required = false) Integer currentFileType,
            @RequestParam(value = "filesList", required = false) MultipartFile[] filesList,
            HttpServletRequest request
    ) {
        // 从token中获取用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            System.out.println("用户未登录，无法发布帖子");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }

        // 添加调试日志
        System.out.println("=== 发布帖子接口调试 ===");
        System.out.println("categoryId: " + categoryId);
        System.out.println("content: " + content);
        System.out.println("title: " + title);
        System.out.println("templateId: " + templateId);
        System.out.println("userId: " + userId + " (从token获取)");
        System.out.println("currentFileType: " + currentFileType);
        System.out.println("filesList: " + (filesList != null ? filesList.length : "null"));

        // 打印所有请求参数
        System.out.println("所有请求参数:");
        request.getParameterMap().forEach((key, values) -> {
            System.out.println(key + ": " + String.join(", ", values));
        });

        // 打印请求头信息
        System.out.println("Content-Type: " + request.getContentType());
        System.out.println("Method: " + request.getMethod());
        // 参数校验
        if (categoryId == null || content == null || content.trim().isEmpty()) {
            System.out.println("参数校验失败:");
            System.out.println("categoryId: " + categoryId);
            System.out.println("content: " + content);
            return ResponseEntity.badRequest().body(null);
        }

        System.out.println("参数校验通过，开始创建帖子...");

        // 第一步：先创建帖子
        Posts post = new Posts();
        post.setCategoryId(categoryId);
        post.setUserId(userId);
        post.setStatus(0); // 待审核状态

        // 处理模板数据
        if (templateId != null) {
            post.setTemplateId(templateId);

            // 收集模板数据
            Map<String, Object> templateData = new HashMap<>();
            request.getParameterMap().forEach((key, values) -> {
                if (!key.equals("category_id") && !key.equals("template_id") &&
                    !key.equals("currentFileType") && !key.equals("filesList")) {
                    templateData.put(key, values.length == 1 ? values[0] : values);
                }
            });

            // 将模板数据转换为JSON存储
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                post.setTemplateData(objectMapper.writeValueAsString(templateData));
            } catch (Exception e) {
                System.out.println("模板数据序列化失败: " + e.getMessage());
            }

            // 设置标题和内容
            if (title != null && !title.trim().isEmpty()) {
                post.setTitle(title);
            }
            if (content != null && !content.trim().isEmpty()) {
                post.setContent(content);
            } else {
                // 如果没有内容，使用标题作为内容
                post.setContent(title != null ? title : "通过模板发布的内容");
            }
        } else {
            // 非模板模式，使用原有逻辑
            post.setContent(content);
            if (title != null && !title.trim().isEmpty()) {
                post.setTitle(title);
            }
        }

        // 位置信息功能暂未实现，跳过位置相关处理

        System.out.println("第一步：保存帖子到数据库...");
        boolean isPostSaved = postsService.add(post);
        if (!isPostSaved) {
            System.out.println("帖子保存失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }

        Long postId = post.getId();
        System.out.println("帖子保存成功，获得帖子ID: " + postId);

        // 发帖成功后给用户增加积分
        try {
            IPointsService.PointsRules rules = pointsService.getPointsRules();
            boolean pointsAdded = pointsService.addPoints(userId, rules.getPostCreate(), "post", "发布帖子", postId);
            if (pointsAdded) {
                System.out.println("发帖积分奖励发放成功，获得积分: " + rules.getPostCreate());
            } else {
                System.out.println("发帖积分奖励发放失败（可能已达每日上限）");
            }
        } catch (Exception e) {
            System.out.println("发帖积分奖励发放异常: " + e.getMessage());
            // 积分发放失败不影响发帖流程
        }

        // 第二步：如果有文件，上传文件并关联到帖子
        if (filesList != null && filesList.length > 0 && currentFileType != null) {
            System.out.println("第二步：开始处理文件上传，文件数量: " + filesList.length);

            boolean allEmpty = true;
            for (MultipartFile file : filesList) {
                if (!file.isEmpty()) {
                    allEmpty = false;
                    System.out.println("文件名: " + file.getOriginalFilename() + ", 大小: " + file.getSize());
                    break;
                }
            }

            if (!allEmpty) {
                try {
                    // 上传文件到磁盘
                    List<String> paths = postFilesService.uploadPostFiles(postId, userId, currentFileType, filesList);
                    System.out.println("文件上传成功，路径: " + paths);

                    // 直接将文件路径存储到posts表的file_list字段
                    String fileListStr = String.join(",", paths);
                    post.setFileList(fileListStr);

                    // 更新帖子的file_list字段
                    boolean updateResult = postsService.updateById(post);
                    System.out.println("更新帖子fileList结果: " + updateResult + ", fileList: " + fileListStr);

                } catch (IOException e) {
                    log.error("附件上传失败: ", e);
                    System.out.println("附件上传IO异常: " + e.getMessage());
                    // 文件上传失败，但帖子已创建，标记状态
                    post.setStatus(-1); // 标记为附件上传失败
                    postsService.updateById(post);
                    return ResponseEntity.ok(post);
                } catch (Exception e) {
                    log.error("未知异常发生在附件上传阶段: ", e);
                    System.out.println("附件上传未知异常: " + e.getMessage());
                    // 文件上传失败，但帖子已创建，标记状态
                    post.setStatus(-1);
                    postsService.updateById(post);
                    return ResponseEntity.ok(post);
                }
            } else {
                System.out.println("所有文件都为空，跳过文件上传");
            }
        } else {
            System.out.println("没有文件需要上传");
        }

        System.out.println("帖子发布完成，最终状态: " + post.getStatus());
        return ResponseEntity.ok(post);
    }

    /**
     * 向已存在的帖子添加文件
     */
    @PostMapping("/addFiles/{postId}")
    public ResponseEntity<Posts> addFilesToPost(
            @PathVariable Long postId,
            @RequestParam(value = "currentFileType", required = false) Integer currentFileType,
            @RequestParam(value = "filesList", required = false) MultipartFile[] filesList,
            HttpServletRequest request
    ) {
        // 从token中获取用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            System.out.println("用户未登录，无法上传文件");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }

        System.out.println("=== 向帖子添加文件接口 ===");
        System.out.println("postId: " + postId);
        System.out.println("userId: " + userId);
        System.out.println("currentFileType: " + currentFileType);
        System.out.println("filesList: " + (filesList != null ? filesList.length : "null"));

        // 验证帖子是否存在
        Posts post = postsService.getById(postId);
        if (post == null) {
            System.out.println("帖子不存在: " + postId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }

        // 验证帖子所有者
        if (!post.getUserId().equals(userId)) {
            System.out.println("用户无权限向此帖子添加文件");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        // 验证参数
        if (filesList == null || filesList.length == 0) {
            System.out.println("没有文件需要上传");
            return ResponseEntity.badRequest().body(null);
        }

        // 打印文件详细信息
        for (int i = 0; i < filesList.length; i++) {
            MultipartFile file = filesList[i];
            System.out.println("文件 " + (i + 1) + ":");
            System.out.println("  - 文件名: " + file.getOriginalFilename());
            System.out.println("  - 大小: " + file.getSize());
            System.out.println("  - 类型: " + file.getContentType());
            System.out.println("  - 是否为空: " + file.isEmpty());

            // 检查文件是否真的为空
            if (file.isEmpty()) {
                System.out.println("  - 错误：文件为空，可能是前端传递了无效的文件数据");
                return ResponseEntity.badRequest().body(null);
            }
        }

        try {
            // 获取现有文件路径
            List<String> allPaths = new ArrayList<>();
            String existingFileList = post.getFileList();

            if (existingFileList != null && !existingFileList.trim().isEmpty()) {
                // 解析现有文件路径
                String[] existingPaths = existingFileList.split(",");
                for (String path : existingPaths) {
                    if (path != null && !path.trim().isEmpty()) {
                        allPaths.add(path.trim());
                    }
                }
                System.out.println("现有文件路径: " + Arrays.toString(existingPaths));
            }

            // 上传新文件（只保存到磁盘）
            List<String> newPaths = postFilesService.uploadPostFiles(postId, userId, currentFileType, filesList);
            System.out.println("新文件上传成功，路径: " + newPaths);

            // 合并所有文件路径
            allPaths.addAll(newPaths);
            System.out.println("合并后的所有文件路径: " + allPaths);

            // 直接更新帖子的file_list字段
            String allPathsStr = String.join(",", allPaths);
            post.setFileList(allPathsStr);
            postsService.updateById(post);
            System.out.println("更新帖子fileList: " + allPathsStr);

            return ResponseEntity.ok(post);

        } catch (Exception e) {
            System.out.println("文件上传失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }


    // 获取单个帖子详情
    @GetMapping("/detail")
    public Result<PostDetailVo> selectById(@RequestParam Long id) {
        if (id == null) {
            throw new IllegalArgumentException("ID cannot be null");
        }

        return Result.success(postsService.selectPostDetailById(id));
    }

    // 更新帖子
    @PutMapping("/{id}")
    public boolean update(@RequestBody Posts post) {
        return postsService.updateById(post);
    }

    // 删除帖子
    @DeleteMapping("/delete")
    public Result<Boolean> delete(@RequestParam(name="id",required=true) Long id,
                                  javax.servlet.http.HttpServletRequest request) {
        try {
            // 1. 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 2. 检查帖子是否存在
            Posts post = postsService.selectById(id);
            if (post == null) {
                return Result.error("帖子不存在");
            }

            // 3. 检查用户权限
            Users currentUser = userService.getById(userId);
            if (currentUser == null) {
                return Result.error("用户不存在");
            }

            // 4. 权限验证：只有帖子作者或管理员可以删除
            boolean isAuthor = post.getUserId().equals(userId);
            boolean isAdmin = "admin".equals(currentUser.getUserRole()) || "community".equals(currentUser.getUserRole());

            // 添加调试日志
            log.info("删除权限检查 - 帖子ID: {}, 帖子作者ID: {}, 当前用户ID: {}, 用户角色: {}, 是否作者: {}, 是否管理员: {}",
                    id, post.getUserId(), userId, currentUser.getUserRole(), isAuthor, isAdmin);

            if (!isAuthor && !isAdmin) {
                return Result.error("无权限删除此帖子");
            }

            // 5. 执行删除
            boolean result = postsService.removeById(id);
            if (result) {
                return Result.success(true, "删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除帖子失败", e);
            return Result.error("系统错误，请稍后重试");
        }
    }

    /**
     * 编辑帖子
     */
    @PostMapping("/edit")
    public Result<Posts> editPost(
            @RequestParam(value = "id", required = true) Long postId,
            @RequestParam(value = "content", required = false) String content,
            @RequestParam(value = "category_id", required = false) Long categoryId,
            @RequestParam(value = "currentFileType", required = false) Integer currentFileType,
            @RequestParam(value = "filesList", required = false) MultipartFile[] filesList,
            @RequestParam(value = "existingFiles", required = false) String existingFiles,
            HttpServletRequest request
    ) {
        try {
            // 1. 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                System.out.println("用户未登录，无法编辑帖子");
                return Result.error("用户未登录");
            }

            // 2. 参数验证
            if (postId == null) {
                return Result.error("帖子ID不能为空");
            }
            if (content == null || content.trim().isEmpty()) {
                return Result.error("帖子内容不能为空");
            }
            if (categoryId == null) {
                return Result.error("分类ID不能为空");
            }

            System.out.println("=== 开始编辑帖子 ===");
            System.out.println("帖子ID: " + postId);
            System.out.println("用户ID: " + userId);
            System.out.println("内容: " + content);
            System.out.println("分类ID: " + categoryId);
            System.out.println("文件类型: " + currentFileType);
            System.out.println("已存在文件: " + existingFiles);

            // 3. 检查帖子是否存在
            Posts existingPost = postsService.selectById(postId);
            if (existingPost == null) {
                return Result.error("帖子不存在");
            }

            // 4. 权限验证：只有帖子作者可以编辑
            if (!existingPost.getUserId().equals(userId)) {
                return Result.error("无权限编辑此帖子");
            }

            System.out.println("权限验证通过，开始更新帖子...");

            // 5. 更新帖子基本信息
            existingPost.setContent(content.trim());
            existingPost.setCategoryId(categoryId);
            existingPost.setStatus(0); // 重新设置为待审核状态
            existingPost.setReviewTime(null); // 清空审核时间
            existingPost.setReviewUserId(null); // 清空审核用户
            existingPost.setReviewNote(null); // 清空审核备注

            // 6. 处理文件列表
            List<String> allPaths = new ArrayList<>();

            // 6.1 处理已存在的文件
            if (existingFiles != null && !existingFiles.trim().isEmpty()) {
                String[] existingFilePaths = existingFiles.split(",");
                for (String path : existingFilePaths) {
                    if (!path.trim().isEmpty()) {
                        allPaths.add(path.trim());
                    }
                }
                System.out.println("保留的已存在文件: " + allPaths);
            }

            // 6.2 处理新上传的文件
            if (filesList != null && filesList.length > 0) {
                // 检查是否所有文件都为空
                boolean allEmpty = true;
                for (MultipartFile file : filesList) {
                    if (file != null && !file.isEmpty()) {
                        allEmpty = false;
                        break;
                    }
                }

                if (!allEmpty) {
                    try {
                        // 上传新文件到磁盘
                        List<String> newPaths = postFilesService.uploadPostFiles(postId, userId, currentFileType, filesList);
                        System.out.println("新文件上传成功，路径: " + newPaths);

                        // 合并所有文件路径
                        allPaths.addAll(newPaths);
                        System.out.println("合并后的所有文件路径: " + allPaths);

                    } catch (IOException e) {
                        log.error("新文件上传失败: ", e);
                        System.out.println("新文件上传IO异常: " + e.getMessage());
                        return Result.error("文件上传失败: " + e.getMessage());
                    } catch (Exception e) {
                        log.error("新文件上传未知异常: ", e);
                        System.out.println("新文件上传未知异常: " + e.getMessage());
                        return Result.error("文件上传失败: " + e.getMessage());
                    }
                } else {
                    System.out.println("所有新文件都为空，跳过新文件上传");
                }
            } else {
                System.out.println("没有新文件需要上传");
            }

            // 7. 更新帖子的文件列表
            String allPathsStr = String.join(",", allPaths);
            existingPost.setFileList(allPathsStr);
            System.out.println("最终文件列表: " + allPathsStr);

            // 8. 保存更新
            boolean updateResult = postsService.updateById(existingPost);
            if (!updateResult) {
                return Result.error("帖子更新失败");
            }

            System.out.println("帖子编辑完成，重新进入待审核状态");
            return Result.success(existingPost, "帖子编辑成功，重新进入审核");

        } catch (Exception e) {
            log.error("编辑帖子失败", e);
            System.out.println("编辑帖子异常: " + e.getMessage());
            e.printStackTrace();
            return Result.error("系统错误，请稍后重试");
        }
    }

    /**
     * 获取帖子的点赞和收藏状态
     * @param postId 帖子ID
     * @param request HTTP请求（从中获取用户ID）
     * @return 状态信息
     */
    @GetMapping("/status")
    public Result<Map<String, Object>> getPostStatus(@RequestParam Long postId, javax.servlet.http.HttpServletRequest request) {
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            // 查询最新状态
            boolean isLiked = postLikesService.isUserLiked(postId, userId);
            boolean isCollected = userCollectsService.isUserCollected(postId, userId);
            int likeCount = postLikesService.getPostLikeCount(postId);
            int collectCount = userCollectsService.getPostCollectCount(postId);

            Map<String, Object> result = new HashMap<>();
            result.put("isLiked", isLiked);
            result.put("isCollected", isCollected);
            result.put("likeCount", likeCount);
            result.put("collectCount", collectCount);

            return Result.success(result);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 简化的文件上传接口 - 专门处理前端uni.uploadFile的单文件上传
     */
    @PostMapping("/uploadFile/{postId}")
    public ResponseEntity<String> uploadSingleFile(
            @PathVariable Long postId,
            @RequestParam(value = "currentFileType", required = false) Integer currentFileType,
            @RequestParam(value = "file", required = false) MultipartFile file,
            HttpServletRequest request
    ) {
        System.out.println("=== 接收到文件上传请求 ===");
        System.out.println("所有请求参数:");
        request.getParameterMap().forEach((key, values) -> {
            System.out.println("  " + key + ": " + Arrays.toString(values));
        });

        System.out.println("文件信息:");
        if (file != null) {
            System.out.println("  文件名: " + file.getOriginalFilename());
            System.out.println("  文件大小: " + file.getSize());
            System.out.println("  文件类型: " + file.getContentType());
            System.out.println("  是否为空: " + file.isEmpty());
        } else {
            System.out.println("  文件为null");
        }
        // 从token中获取用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            System.out.println("用户未登录，无法上传文件");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("用户未登录");
        }

        System.out.println("=== 单文件上传接口 ===");
        System.out.println("postId: " + postId);
        System.out.println("userId: " + userId);
        System.out.println("currentFileType: " + currentFileType);
        System.out.println("file: " + (file != null ? file.getOriginalFilename() : "null"));

        // 验证帖子是否存在
        Posts post = postsService.getById(postId);
        if (post == null) {
            System.out.println("帖子不存在: " + postId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("帖子不存在");
        }

        // 验证帖子所有者
        if (!post.getUserId().equals(userId)) {
            System.out.println("用户无权限向此帖子添加文件");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body("无权限");
        }

        // 验证文件
        if (file == null || file.isEmpty()) {
            System.out.println("没有有效的文件");
            return ResponseEntity.badRequest().body("没有有效的文件");
        }

        try {
            // 上传单个文件
            MultipartFile[] files = {file};
            List<String> newPaths = postFilesService.uploadPostFiles(postId, userId, currentFileType, files);
            System.out.println("文件上传成功，路径: " + newPaths);

            // 获取现有文件路径
            List<String> allPaths = new ArrayList<>();
            String existingFileList = post.getFileList();

            if (existingFileList != null && !existingFileList.trim().isEmpty()) {
                String[] existingPaths = existingFileList.split(",");
                for (String path : existingPaths) {
                    if (path != null && !path.trim().isEmpty()) {
                        allPaths.add(path.trim());
                    }
                }
            }

            // 添加新文件路径
            allPaths.addAll(newPaths);

            // 直接更新帖子的file_list字段
            String allPathsStr = String.join(",", allPaths);
            post.setFileList(allPathsStr);
            postsService.updateById(post);
            System.out.println("更新帖子fileList: " + allPathsStr);

            return ResponseEntity.ok("文件上传成功");

        } catch (Exception e) {
            System.out.println("文件上传失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 重新计算指定帖子的评论数
     */
    @PostMapping("/updateCommentCount")
    public Result<String> updateCommentCount(@RequestBody Map<String, Object> request) {
        Long postId = Long.valueOf(request.get("postId").toString());

        boolean success = postsService.updateCommentCount(postId);
        if (success) {
            return Result.success("评论数更新成功");
        } else {
            return Result.error("评论数更新失败");
        }
    }

    /**
     * 智能发布接口 - 统一处理各种模板类型的发布
     */
    @PostMapping("/smart-publish")
    public ResponseEntity<Map<String, Object>> smartPublish(
            @RequestParam(value = "category_id", required = false) Long categoryId,
            @RequestParam(value = "content", required = false) String content,
            @RequestParam(value = "template_id", required = false) Long templateId,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "files", required = false) MultipartFile[] files,
            HttpServletRequest request
    ) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从token中获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                log.warn("智能发布 - 用户未登录");
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            log.info("智能发布开始 - 用户ID: {}, 分类ID: {}, 模板ID: {}", userId, categoryId, templateId);

            // 参数验证 - 对于模板类型的帖子，放宽content验证
            if (categoryId == null) {
                response.put("success", false);
                response.put("message", "分类不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            // 对于非模板类型或者content为空的情况进行验证
            if (templateId == null && (content == null || content.trim().isEmpty())) {
                response.put("success", false);
                response.put("message", "内容不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            // 创建帖子对象
            Posts post = new Posts();
            post.setCategoryId(categoryId);
            post.setUserId(userId);
            post.setStatus(0); // 待审核状态

            // 处理标题
            if (title != null && !title.trim().isEmpty()) {
                post.setTitle(title);
            }

            // 处理内容 - 确保不为空
            String finalContent = content;
            if (finalContent == null || finalContent.trim().isEmpty()) {
                finalContent = title != null ? title : "通过模板发布的内容";
            }
            post.setContent(finalContent);

            // 处理模板数据
            if (templateId != null) {
                post.setTemplateId(templateId);
                
                // 根据模板ID设置模板类型
                String templateType = getTemplateTypeById(templateId);
                post.setTemplateType(templateType);

                // 收集模板数据
                Map<String, Object> templateData = new HashMap<>();
                request.getParameterMap().forEach((key, values) -> {
                    // 排除基础字段
                    if (!Arrays.asList("category_id", "template_id", "title", "content", "files").contains(key)) {
                        String value = values.length == 1 ? values[0] : String.join(",", values);
                        templateData.put(key, value);
                        log.debug("智能发布 - 收集模板数据: {} = {}", key, value);
                    }
                });

                // 将模板数据转换为JSON存储
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    post.setTemplateData(objectMapper.writeValueAsString(templateData));
                    log.info("智能发布 - 模板数据序列化成功，类型: {}", templateType);
                } catch (Exception e) {
                    log.error("智能发布 - 模板数据序列化失败", e);
                    response.put("success", false);
                    response.put("message", "模板数据处理失败");
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
                }
            }

            // 保存帖子到数据库
            boolean isPostSaved = postsService.add(post);
            if (!isPostSaved) {
                log.error("智能发布 - 帖子保存失败");
                response.put("success", false);
                response.put("message", "帖子保存失败");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            }

            Long postId = post.getId();
            log.info("智能发布 - 帖子保存成功，ID: {}", postId);

            // 处理文件上传
            if (files != null && files.length > 0) {
                // 检查是否有有效文件
                boolean hasValidFiles = false;
                for (MultipartFile file : files) {
                    if (file != null && !file.isEmpty()) {
                        hasValidFiles = true;
                        break;
                    }
                }

                if (hasValidFiles) {
                    try {
                        // 默认文件类型为混合类型（图片+视频）
                        Integer fileType = 3; // 3表示混合类型
                        List<String> filePaths = postFilesService.uploadPostFiles(postId, userId, fileType, files);
                        
                        // 更新帖子的文件列表
                        String fileListStr = String.join(",", filePaths);
                        post.setFileList(fileListStr);
                        postsService.updateById(post);
                        
                        log.info("智能发布 - 文件上传成功，数量: {}", filePaths.size());
                    } catch (Exception e) {
                        log.error("智能发布 - 文件上传失败", e);
                        // 文件上传失败不影响帖子发布，只记录错误
                        response.put("fileUploadError", "文件上传失败: " + e.getMessage());
                    }
                }
            }

            // 发帖积分奖励
            try {
                IPointsService.PointsRules rules = pointsService.getPointsRules();
                boolean pointsAdded = pointsService.addPoints(userId, rules.getPostCreate(), "post", "发布帖子", postId);
                if (pointsAdded) {
                    log.info("智能发布 - 积分奖励发放成功: {}", rules.getPostCreate());
                }
            } catch (Exception e) {
                log.warn("智能发布 - 积分奖励发放失败", e);
                // 积分发放失败不影响发帖流程
            }

            // 返回成功响应
            response.put("success", true);
            response.put("message", "发布成功");
            response.put("data", post);
            
            log.info("智能发布完成 - 帖子ID: {}, 状态: {}", postId, post.getStatus());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("智能发布异常", e);
            response.put("success", false);
            response.put("message", "发布失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 根据模板ID获取模板类型
     */
    private String getTemplateTypeById(Long templateId) {
        if (templateId == null) return null;
        
        switch (templateId.intValue()) {
            case 1: return "survey";      // 调查问卷
            case 2: return "vote";        // 投票表决
            case 3: return "activity";    // 活动发起
            case 4: return "groupbuy";    // 团购模板
            case 5: return "announcement"; // 公告模板
            default: return "basic";      // 基础模板
        }
    }

    /**
     * 重新计算所有帖子的评论数（管理员功能）
     */
    @PostMapping("/updateAllCommentCounts")
    public Result<String> updateAllCommentCounts(javax.servlet.http.HttpServletRequest request) {
        // 获取当前用户ID
        Long currentUserId = (Long) request.getAttribute("userId");
        if (currentUserId == null) {
            return Result.error("用户未登录");
        }

        // 检查管理员权限
        if (!isAdmin(currentUserId)) {
            return Result.error("无权限执行此操作");
        }

        // 获取所有帖子
        List<Posts> allPosts = postsService.list();
        int successCount = 0;
        StringBuilder details = new StringBuilder();

        for (Posts post : allPosts) {
            if (postsService.updateCommentCount(post.getId())) {
                successCount++;
                // 获取更新后的帖子信息
                Posts updatedPost = postsService.getById(post.getId());
                details.append(String.format("帖子ID: %d, 评论数: %d\n", 
                    post.getId(), updatedPost.getCommentCount()));
            }
        }

        String message = String.format("成功更新 %d/%d 个帖子的评论数", successCount, allPosts.size());
        log.info("批量更新评论数完成: {}", message);
        
        return Result.success(message + "\n详情:\n" + details.toString());
    }

    /**
     * 检查用户是否为管理员
     */
    private boolean isAdmin(Long userId) {
        try {
            Users user = userService.getById(userId);
            return user != null && ("admin".equals(user.getUserRole()) || "community".equals(user.getUserRole()));
        } catch (Exception e) {
            log.error("检查管理员权限失败", e);
            return false;
        }
    }



    /**
     * 管理员获取帖子列表（包含所有状态）
     */
    @GetMapping("/admin/list")
    public Result<IPage<PostsListVo>> getAdminPostList(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Integer status, // 帖子状态过滤
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            HttpServletRequest request) {

        log.info("管理员帖子列表查询参数 - categoryId: {}, title: {}, status: {}, page: {}, pageSize: {}",
                categoryId, title, status, page, pageSize);

        // 验证管理员权限
        if (!permissionUtil.isAdmin(request)) {
            return Result.error("权限不足，仅管理员可访问");
        }

        try {
            // 构建查询条件
            PostListAllDto postListAllDto = new PostListAllDto();
            postListAllDto.setCategoryId(categoryId);
            postListAllDto.setTitle(title);
            postListAllDto.setPage(page);
            postListAllDto.setPageSize(pageSize);

            // 管理员可以看到所有状态的帖子，支持按状态筛选
            IPage<PostsListVo> postsList = postsService.listAllPostsForAdmin(postListAllDto, status);

            return Result.success(postsList);
        } catch (Exception e) {
            log.error("获取管理员帖子列表失败", e);
            return Result.error("获取帖子列表失败: " + e.getMessage());
        }
    }

    /**
     * 管理员审核帖子（通过）
     */
    @PostMapping("/admin/approve/{postId}")
    public Result<String> approvePost(
            @PathVariable Long postId,
            HttpServletRequest request) {

        // 验证管理员权限
        if (!permissionUtil.isAdmin(request)) {
            return Result.error("权限不足，仅管理员可操作");
        }

        try {
            // 获取当前管理员用户ID
            Long adminUserId = (Long) request.getAttribute("userId");

            // 检查帖子是否存在
            Posts post = postsService.selectById(postId);
            if (post == null) {
                return Result.error("帖子不存在");
            }

            // 更新帖子状态为已通过
            post.setStatus(1); // 1-已通过
            post.setReviewTime(new Date());
            post.setReviewUserId(adminUserId);
            post.setReviewNote("管理员审核通过");

            boolean result = postsService.updateById(post);
            if (result) {
                return Result.success("帖子审核通过");
            } else {
                return Result.error("审核操作失败");
            }
        } catch (Exception e) {
            log.error("审核帖子失败", e);
            return Result.error("审核操作失败: " + e.getMessage());
        }
    }

    /**
     * 管理员审核帖子（拒绝）
     */
    @PostMapping("/admin/reject/{postId}")
    public Result<String> rejectPost(
            @PathVariable Long postId,
            @RequestBody Map<String, String> requestBody,
            HttpServletRequest request) {

        // 验证管理员权限
        if (!permissionUtil.isAdmin(request)) {
            return Result.error("权限不足，仅管理员可操作");
        }

        String reason = requestBody.get("reason");
        if (reason == null || reason.trim().isEmpty()) {
            return Result.error("拒绝原因不能为空");
        }

        try {
            // 获取当前管理员用户ID
            Long adminUserId = (Long) request.getAttribute("userId");

            // 检查帖子是否存在
            Posts post = postsService.selectById(postId);
            if (post == null) {
                return Result.error("帖子不存在");
            }

            // 更新帖子状态为已拒绝
            post.setStatus(2); // 2-已拒绝
            post.setReviewTime(new Date());
            post.setReviewUserId(adminUserId);
            post.setReviewNote(reason.trim());

            boolean result = postsService.updateById(post);
            if (result) {
                return Result.success("帖子审核拒绝");
            } else {
                return Result.error("审核操作失败");
            }
        } catch (Exception e) {
            log.error("拒绝帖子失败", e);
            return Result.error("审核操作失败: " + e.getMessage());
        }
    }

    /**
     * 管理员获取帖子统计数据
     */
    @GetMapping("/admin/stats")
    public Result<Map<String, Object>> getPostStats(HttpServletRequest request) {
        // 验证管理员权限
        if (!permissionUtil.isAdmin(request)) {
            return Result.error("权限不足，仅管理员可访问");
        }

        try {
            Map<String, Object> stats = new HashMap<>();

            // 总帖子数
            QueryWrapper<Posts> totalWrapper = new QueryWrapper<>();
            totalWrapper.eq("del_flag", 0);
            long total = postsService.count(totalWrapper);

            // 待审核帖子数
            QueryWrapper<Posts> pendingWrapper = new QueryWrapper<>();
            pendingWrapper.eq("del_flag", 0).eq("status", 0);
            long pending = postsService.count(pendingWrapper);

            // 已通过帖子数
            QueryWrapper<Posts> approvedWrapper = new QueryWrapper<>();
            approvedWrapper.eq("del_flag", 0).eq("status", 1);
            long approved = postsService.count(approvedWrapper);

            // 已拒绝帖子数
            QueryWrapper<Posts> rejectedWrapper = new QueryWrapper<>();
            rejectedWrapper.eq("del_flag", 0).eq("status", 2);
            long rejected = postsService.count(rejectedWrapper);

            stats.put("total", total);
            stats.put("pending", pending);
            stats.put("approved", approved);
            stats.put("rejected", rejected);

            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取帖子统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }



    // ==================== 帖子置顶相关接口 ====================

    /**
     * 获取置顶配置信息
     */
    @GetMapping("/top/config")
    public Result<IPostTopService.TopConfig> getTopConfig(HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            IPostTopService.TopConfig config = postTopService.getTopConfig();
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取置顶配置失败", e);
            return Result.error("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 检查帖子是否可以置顶
     */
    @GetMapping("/{postId}/top/check")
    public Result<IPostTopService.TopCheckResult> checkCanTop(
            @PathVariable Long postId,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            IPostTopService.TopCheckResult result = postTopService.checkCanTop(postId, userId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("检查置顶权限失败", e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }





    /**
     * 置顶帖子
     */
    @PostMapping("/{postId}/top")
    public Result<IPostTopService.TopResult> topPost(
            @PathVariable Long postId,
            @RequestBody Map<String, Integer> requestBody,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            Integer hours = requestBody.get("hours");
            if (hours == null || hours <= 0) {
                return Result.error("置顶时长必须大于0");
            }

            // 检查时长限制
            IPostTopService.TopConfig config = postTopService.getTopConfig();
            if (hours < config.getMinHours() || hours > config.getMaxHours()) {
                return Result.error("置顶时长必须在" + config.getMinHours() + "-" + config.getMaxHours() + "小时之间");
            }

            IPostTopService.TopResult result = postTopService.topPost(postId, userId, hours);
            return Result.success(result);
        } catch (Exception e) {
            log.error("置顶帖子失败", e);
            return Result.error("置顶失败: " + e.getMessage());
        }
    }

    /**
     * 取消置顶
     */
    @DeleteMapping("/{postId}/top")
    public Result<Boolean> cancelTop(
            @PathVariable Long postId,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            boolean result = postTopService.cancelTop(postId, userId);
            if (result) {
                return Result.success(true, "取消置顶成功");
            } else {
                return Result.error("取消置顶失败");
            }
        } catch (Exception e) {
            log.error("取消置顶失败", e);
            return Result.error("取消置顶失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的置顶帖子列表
     */
    @GetMapping("/top/my")
    public Result<List<Posts>> getMyTopPosts(HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            List<Posts> topPosts = postTopService.getUserTopPosts(userId);
            return Result.success(topPosts);
        } catch (Exception e) {
            log.error("获取用户置顶帖子失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 计算置顶费用
     */
    @GetMapping("/top/cost")
    public Result<Map<String, Object>> getTopCost(
            @RequestParam Integer hours,
            HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return Result.error("用户未登录");
            }

            if (hours == null || hours <= 0) {
                return Result.error("时长参数无效");
            }

            Integer cost = postTopService.getTopCost(hours);
            Integer userPoints = pointsService.getUserPointsBalance(userId);

            Map<String, Object> result = new HashMap<>();
            result.put("cost", cost);
            result.put("userPoints", userPoints);
            result.put("canAfford", userPoints >= cost);

            return Result.success(result);
        } catch (Exception e) {
            log.error("计算置顶费用失败", e);
            return Result.error("计算失败: " + e.getMessage());
        }
    }

}
