<template>
  <view class="group-buy-template">
    <!-- 基础模板 -->
    <BaseTemplate
      ref="baseTemplateRef"
      :titleRequired="true"
      titlePlaceholder="请输入商品名称"
      contentPlaceholder="请详细描述商品信息，包括规格、产地等"
      :showMediaUpload="true"
      :mediaRequired="true"
      mediaLabel="商品图片"
      mediaHelpText="图片：最多9张，单张不超过10MB；视频：最多1个，不超过100MB"
      :maxImageCount="9"
      :showMediaTypeSwitch="true"
      :defaultMediaType="0"
      :showContact="false"
      :initialData="formData"
      @update:data="handleBaseDataChange"
      @update:valid="handleBaseValidChange"
    />

    <!-- 团购特有字段 -->
    <view class="group-buy-fields">
      <!-- 价格信息 -->
      <view class="price-section">
        <view class="section-title">价格信息</view>
        
        <view class="form-row">
          <view class="form-item half">
            <view class="form-label">
              <text>原价</text>
              <text class="required">*</text>
            </view>
            <up-input
              v-model="formData.originalPrice"
              type="number"
              placeholder="请输入原价"
              @input="handlePriceChange"
              @blur="validatePrice"
            />
            <view v-if="errors.originalPrice" class="error-text">{{ errors.originalPrice }}</view>
          </view>
          
          <view class="form-item half">
            <view class="form-label">
              <text>团购价</text>
              <text class="required">*</text>
            </view>
            <up-input
              v-model="formData.groupPrice"
              type="number"
              placeholder="请输入团购价"
              @input="handlePriceChange"
              @blur="validatePrice"
            />
            <view v-if="errors.groupPrice" class="error-text">{{ errors.groupPrice }}</view>
          </view>
        </view>
      </view>

      <!-- 团购设置 -->
      <view class="group-settings">
        <view class="section-title">团购设置</view>
        
        <view class="form-item">
          <view class="form-label">
            <text>起团数量</text>
            <text class="required">*</text>
          </view>
          <up-input
            v-model="formData.minQuantity"
            type="number"
            placeholder="达到此数量才能成团"
            @input="handleQuantityChange"
            @blur="validateQuantity"
          />
          <view v-if="errors.minQuantity" class="error-text">{{ errors.minQuantity }}</view>
        </view>

        <view class="form-item">
          <view class="form-label">
            <text>团购时间</text>
            <text class="required">*</text>
          </view>

          <!-- 开始时间 -->
          <view class="time-row">
            <view class="time-label">开始时间：</view>
            <up-datetime-picker
              hasInput
              v-model:show="showStartTimePicker"
              v-model="startTimeValue"
              mode="datetime"
              :minDate="minDate"
              placeholder="选择开始时间"
              format="YYYY-MM-DD HH:mm"
              @confirm="handleStartTimeConfirm"
              @cancel="() => showStartTimePicker = false"
            />
          </view>

          <!-- 截止时间 -->
          <view class="time-row">
            <view class="time-label">截止时间：</view>
            <up-datetime-picker
              hasInput
              v-model:show="showDeadlinePicker"
              v-model="deadlineValue"
              mode="datetime"
              :minDate="startTimeMinDate"
              placeholder="选择截止时间"
              format="YYYY-MM-DD HH:mm"
              @confirm="handleDeadlineConfirm"
              @cancel="() => showDeadlinePicker = false"
            />
          </view>

          <view v-if="errors.startTime" class="error-text">{{ errors.startTime }}</view>
          <view v-if="errors.deadline" class="error-text">{{ errors.deadline }}</view>
          <view class="help-text">团购时间区间，开始时间不能晚于截止时间</view>
        </view>
      </view>

      <!-- 联系信息 -->
      <view class="contact-section">
        <view class="section-title">联系信息</view>
        
        <view class="form-item">
          <view class="form-label">
            <text>联系人</text>
            <text class="required">*</text>
          </view>
          <up-input
            v-model="formData.contactPerson"
            placeholder="请输入联系人姓名"
            @input="handleContactChange"
            @blur="validateContact"
          />
          <view v-if="errors.contactPerson" class="error-text">{{ errors.contactPerson }}</view>
        </view>

        <view class="form-item">
          <view class="form-label">
            <text>联系电话</text>
            <text class="required">*</text>
          </view>
          <up-input
            v-model="formData.contactPhone"
            placeholder="请输入联系电话"
            @input="handleContactChange"
            @blur="validateContact"
          />
          <view v-if="errors.contactPhone" class="error-text">{{ errors.contactPhone }}</view>
        </view>

        <view class="form-item">
          <view class="form-label">
            <text>取货地点</text>
            <text class="required">*</text>
          </view>
          <up-input
            v-model="formData.pickupLocation"
            placeholder="请输入详细的取货地址"
            @input="handleContactChange"
            @blur="validateContact"
          />
          <view v-if="errors.pickupLocation" class="error-text">{{ errors.pickupLocation }}</view>
        </view>
      </view>

      <!-- 付款方式 -->
      <view class="form-item">
        <view class="form-label">
          <text>付款方式</text>
          <text class="required">*</text>
        </view>

        <view class="payment-methods">
          <!-- 微信支付 -->
          <view class="payment-method-item">
            <view class="payment-header">
              <up-icon name="weixin-fill" size="20" color="#07C160"></up-icon>
              <text class="payment-title">微信支付</text>
            </view>
            <view class="payment-content">
              <view class="qr-upload">
                <up-upload
                  :fileList="formData.wechatQrCode"
                  @afterRead="(event) => handleQrCodeUpload(event, 'wechat')"
                  @delete="(event) => handleQrCodeDelete(event, 'wechat')"
                  :maxCount="1"
                  :previewFullImage="true"
                  width="100"
                  height="100"
                >
                  <view class="upload-placeholder">
                    <up-icon name="camera" size="20" color="#999"></up-icon>
                    <text class="upload-text">上传收款码</text>
                  </view>
                </up-upload>
              </view>
            </view>
          </view>

          <!-- 支付宝支付 -->
          <view class="payment-method-item">
            <view class="payment-header">
              <up-icon name="zhifubao" size="20" color="#1677FF"></up-icon>
              <text class="payment-title">支付宝支付</text>
            </view>
            <view class="payment-content">
              <view class="qr-upload">
                <up-upload
                  :fileList="formData.alipayQrCode"
                  @afterRead="(event) => handleQrCodeUpload(event, 'alipay')"
                  @delete="(event) => handleQrCodeDelete(event, 'alipay')"
                  :maxCount="1"
                  :previewFullImage="true"
                  width="100"
                  height="100"
                >
                  <view class="upload-placeholder">
                    <up-icon name="camera" size="20" color="#999"></up-icon>
                    <text class="upload-text">上传收款码</text>
                  </view>
                </up-upload>
              </view>
            </view>
          </view>
        </view>

        <view v-if="errors.paymentMethod" class="error-text">{{ errors.paymentMethod }}</view>
        <view class="help-text">至少选择一种付款方式并上传对应的收款码</view>
      </view>

      <!-- 备注说明 -->
      <view class="form-item">
        <view class="form-label">
          <text>备注说明</text>
        </view>
        <up-textarea
          v-model="formData.notes"
          placeholder="其他需要说明的事项，如退换货政策等"
          :maxlength="500"
          :autoHeight="true"
          :height="80"
        />
        <view class="help-text">补充说明，如退换货政策等</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { validateField } from '@/utils/validation'
import BaseTemplate from './BaseTemplate.vue'

interface Emits {
  (e: 'update:data', data: Record<string, any>): void
  (e: 'update:valid', valid: boolean): void
}

const emit = defineEmits<Emits>()

// 基础模板引用
const baseTemplateRef = ref()

// 表单数据
const formData = ref({
  title: '',
  content: '',
  images: [] as any[], // 兼容旧版本
  fileList: [] as any[], // 新版本文件列表
  originalPrice: '',
  groupPrice: '',
  minQuantity: '',
  startTime: '',
  deadline: '',
  contactPerson: '',
  contactPhone: '',
  pickupLocation: '',
  wechatEnabled: false,
  wechatQrCode: [] as any[],
  alipayEnabled: false,
  alipayQrCode: [] as any[],
  notes: ''
})

// 错误信息
const errors = ref<Record<string, string>>({})

// 基础模板是否有效
const baseValid = ref(false)



// 日期选择器
const showStartTimePicker = ref(false)
const showDeadlinePicker = ref(false)
const startTimeValue = ref<number>(Date.now())
const deadlineValue = ref<number>(Date.now() + 24 * 60 * 60 * 1000) // 默认明天
const minDate = Date.now()

// 确保时间值始终是有效的时间戳
const ensureValidTimestamp = (value: any): number => {
  if (typeof value === 'number' && !isNaN(value) && value > 0) {
    return value
  }
  if (typeof value === 'string' && value) {
    const timestamp = new Date(value).getTime()
    if (!isNaN(timestamp)) {
      return timestamp
    }
  }
  return Date.now()
}

// 开始时间的最小日期（当前时间）
const startTimeMinDate = computed(() => {
  if (formData.value.startTime) {
    return new Date(formData.value.startTime).getTime()
  }
  return minDate
})

// 处理付款方式变化（现在不需要了，因为默认显示两种方式）

// 处理收款码上传
const handleQrCodeUpload = (event: any, type: 'wechat' | 'alipay') => {
  const file = event.file
  if (type === 'wechat') {
    formData.value.wechatQrCode = [file]
  } else {
    formData.value.alipayQrCode = [file]
  }
  validatePaymentMethod()
}

// 处理收款码删除
const handleQrCodeDelete = (event: any, type: 'wechat' | 'alipay') => {
  if (type === 'wechat') {
    formData.value.wechatQrCode = []
  } else {
    formData.value.alipayQrCode = []
  }
  validatePaymentMethod()
}

// 验证付款方式
const validatePaymentMethod = () => {
  const hasWechat = formData.value.wechatQrCode.length > 0
  const hasAlipay = formData.value.alipayQrCode.length > 0

  if (!hasWechat && !hasAlipay) {
    errors.value.paymentMethod = '请至少上传一种收款码'
    return false
  }

  delete errors.value.paymentMethod
  return true
}

// 验证表单是否有效
const isValid = computed(() => {
  const hasValidPayment = formData.value.wechatQrCode.length > 0 || formData.value.alipayQrCode.length > 0

  return baseValid.value &&
         formData.value.originalPrice !== '' &&
         formData.value.groupPrice !== '' &&
         formData.value.minQuantity !== '' &&
         formData.value.startTime !== '' &&
         formData.value.deadline !== '' &&
         formData.value.contactPerson !== '' &&
         formData.value.contactPhone !== '' &&
         formData.value.pickupLocation !== '' &&
         hasValidPayment &&
         Object.keys(errors.value).length === 0
})

// 监听数据变化
watch(formData, (newData) => {
  emit('update:data', newData)
}, { deep: true })

// 监听验证状态变化
watch(isValid, (valid) => {
  emit('update:valid', valid)
})



// 监听时间值变化，确保始终是有效的时间戳
watch(startTimeValue, (newValue) => {
  if (typeof newValue !== 'number' || isNaN(newValue) || newValue <= 0) {
    startTimeValue.value = Date.now()
  }
})

watch(deadlineValue, (newValue) => {
  if (typeof newValue !== 'number' || isNaN(newValue) || newValue <= 0) {
    deadlineValue.value = Date.now() + 24 * 60 * 60 * 1000
  }
})

// 处理基础模板数据变化
const handleBaseDataChange = (data: Record<string, any>) => {
  Object.assign(formData.value, data)
}

// 处理基础模板验证状态变化
const handleBaseValidChange = (valid: boolean) => {
  baseValid.value = valid
}

// 验证价格
const validatePrice = () => {
  const originalResult = validateField(formData.value.originalPrice, {
    required: true,
    type: 'number',
    min: 0,
    fieldName: '原价'
  })
  
  const groupResult = validateField(formData.value.groupPrice, {
    required: true,
    type: 'number',
    min: 0,
    fieldName: '团购价'
  })
  
  if (!originalResult.valid && originalResult.message) {
    errors.value.originalPrice = originalResult.message
  } else {
    delete errors.value.originalPrice
  }
  
  if (!groupResult.valid && groupResult.message) {
    errors.value.groupPrice = groupResult.message
  } else {
    delete errors.value.groupPrice
  }
  
  // 验证团购价不能高于原价
  if (originalResult.valid && groupResult.valid) {
    const original = Number(formData.value.originalPrice)
    const group = Number(formData.value.groupPrice)
    
    if (group > original) {
      errors.value.groupPrice = '团购价不能高于原价'
    }
  }
}

// 验证数量
const validateQuantity = () => {
  const result = validateField(formData.value.minQuantity, {
    required: true,
    type: 'number',
    min: 1,
    fieldName: '起团数量'
  })
  
  if (!result.valid && result.message) {
    errors.value.minQuantity = result.message
  } else {
    delete errors.value.minQuantity
  }
}

// 验证联系信息
const validateContact = () => {
  const personResult = validateField(formData.value.contactPerson, {
    required: true,
    maxLength: 50,
    fieldName: '联系人'
  })
  
  const phoneResult = validateField(formData.value.contactPhone, {
    required: true,
    type: 'phone',
    fieldName: '联系电话'
  })
  
  const locationResult = validateField(formData.value.pickupLocation, {
    required: true,
    maxLength: 200,
    fieldName: '取货地点'
  })
  
  if (!personResult.valid && personResult.message) {
    errors.value.contactPerson = personResult.message
  } else {
    delete errors.value.contactPerson
  }
  
  if (!phoneResult.valid && phoneResult.message) {
    errors.value.contactPhone = phoneResult.message
  } else {
    delete errors.value.contactPhone
  }
  
  if (!locationResult.valid && locationResult.message) {
    errors.value.pickupLocation = locationResult.message
  } else {
    delete errors.value.pickupLocation
  }
}

// 处理价格变化
const handlePriceChange = () => {
  validatePrice()
}

// 处理数量变化
const handleQuantityChange = () => {
  validateQuantity()
}

// 处理联系信息变化
const handleContactChange = () => {
  validateContact()
}



// 格式化时间显示
const formatDateTime = (timestamp: number) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hour}:${minute}`
}

// 处理开始时间确认
const handleStartTimeConfirm = (event: any) => {
  try {
    const timestamp = ensureValidTimestamp(event?.value)
    startTimeValue.value = timestamp

    const dateTimeStr = formatDateTime(timestamp)
    formData.value.startTime = dateTimeStr
    showStartTimePicker.value = false

    // 验证开始时间
    if (formData.value.startTime) {
      delete errors.value.startTime

      // 如果截止时间早于开始时间，清空截止时间
      if (formData.value.deadline && new Date(formData.value.deadline) <= new Date(formData.value.startTime)) {
        formData.value.deadline = ''
        errors.value.deadline = '截止时间必须晚于开始时间'
      }
    }
  } catch (error) {
    console.error('开始时间确认处理失败:', error)
    showStartTimePicker.value = false
  }
}

// 处理截止时间确认
const handleDeadlineConfirm = (event: any) => {
  try {
    const timestamp = ensureValidTimestamp(event?.value)
    deadlineValue.value = timestamp

    const dateTimeStr = formatDateTime(timestamp)
    formData.value.deadline = dateTimeStr
    showDeadlinePicker.value = false

    // 验证截止时间
    if (formData.value.deadline) {
      if (formData.value.startTime && new Date(formData.value.deadline) <= new Date(formData.value.startTime)) {
        errors.value.deadline = '截止时间必须晚于开始时间'
      } else {
        delete errors.value.deadline
      }
    }
  } catch (error) {
    console.error('截止时间确认处理失败:', error)
    showDeadlinePicker.value = false
  }
}

// 验证团购特有字段
const validateGroupBuyFields = () => {
  validatePrice()
  validateQuantity()
  validateContact()

  // 验证开始时间
  if (!formData.value.startTime) {
    errors.value.startTime = '请选择开始时间'
  } else {
    delete errors.value.startTime
  }

  // 验证截止时间
  if (!formData.value.deadline) {
    errors.value.deadline = '请选择截止时间'
  } else if (formData.value.startTime && new Date(formData.value.deadline) <= new Date(formData.value.startTime)) {
    errors.value.deadline = '截止时间必须晚于开始时间'
  } else {
    delete errors.value.deadline
  }

  // 验证付款方式
  validatePaymentMethod()
}

// 暴露验证方法
const validate = () => {
  const baseValidResult = baseTemplateRef.value?.validate() || false
  validateGroupBuyFields()
  
  return baseValidResult && isValid.value
}

// 重置表单数据
const resetForm = () => {
  formData.value = {
    title: '',
    content: '',
    images: [],
    fileList: [],
    originalPrice: '',
    groupPrice: '',
    minQuantity: '',
    startTime: '',
    deadline: '',
    contactPerson: '',
    contactPhone: '',
    pickupLocation: '',
    wechatEnabled: false,
    wechatQrCode: [],
    alipayEnabled: false,
    alipayQrCode: [],
    notes: ''
  }

  // 清空错误信息
  errors.value = {}

  // 重置时间值
  startTimeValue.value = Date.now()
  deadlineValue.value = Date.now() + 7 * 24 * 60 * 60 * 1000

  // 重置基础模板
  if (baseTemplateRef.value && baseTemplateRef.value.resetForm) {
    baseTemplateRef.value.resetForm()
  }
}

// 暴露给父组件
// 组件挂载时初始化
onMounted(() => {
  // 确保时间值是有效的
  startTimeValue.value = ensureValidTimestamp(startTimeValue.value)
  deadlineValue.value = ensureValidTimestamp(deadlineValue.value)
})

defineExpose({
  validate,
  formData,
  isValid,
  resetForm
})
</script>

<style scoped lang="scss">
.group-buy-template {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.group-buy-fields {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.form-row {
  display: flex;
  gap: 12px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  &.half {
    flex: 1;
  }
}

.form-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff3b30;
}

.error-text {
  font-size: 12px;
  color: #ff3b30;
}

.help-text {
  font-size: 12px;
  color: #999;
}

.time-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.time-label {
  width: 80px;
  font-size: 14px;
  color: #666;
  flex-shrink: 0;
}

.payment-methods {
  display: flex;
  gap: 16px;
}

.payment-method-item {
  flex: 1;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  background-color: #fafafa;
}

.payment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.payment-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.payment-content {
  display: flex;
  justify-content: center;
}

.qr-upload {
  display: flex;
  justify-content: center;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  background-color: white;
}

.upload-text {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  text-align: center;
}
</style>
