import { http } from '@/utils/http'
import type { Result } from '@/types/ApiResponse'
import type { AuthApplication } from '@/types/user'

/**
 * 身份类型选项
 */
export interface IdentityTypeOption {
  code: string
  name: string
  description?: string
  sortOrder?: number
}

/**
 * 认证申请参数
 */
export interface VerificationApplicationParams {
  realName: string
  phone?: string
  houseNumber?: string
  identityType: string // owner-业主 tenant-租户 property-物业 committee-业委会
  documents?: string[] // 证明文件列表
  remark?: string
}

/**
 * 认证申请响应
 */
export interface VerificationApplicationResponse {
  id: number
  status: number // 0-待审核 1-已通过 2-已拒绝
  message: string
}

/**
 * 获取身份类型选项列表
 */
export const getIdentityTypesAPI = () => {
  return http<Result<IdentityTypeOption[]>>({
    method: 'GET',
    url: '/verification/identity-types'
  })
}

/**
 * 提交认证申请
 */
export const submitVerificationAPI = (data: VerificationApplicationParams) => {
  return http<Result<VerificationApplicationResponse>>({
    method: 'POST',
    url: '/verification/apply',
    data
  })
}

/**
 * 获取当前用户的认证申请状态
 */
export const getVerificationStatusAPI = () => {
  return http<Result<AuthApplication>>({
    method: 'GET',
    url: '/verification/status'
  })
}

/**
 * 获取认证申请历史记录
 */
export const getVerificationHistoryAPI = () => {
  return http<Result<AuthApplication[]>>({
    method: 'GET',
    url: '/verification/history'
  })
}

/**
 * 撤销认证申请
 */
export const cancelVerificationAPI = (applicationId: number) => {
  return http<Result<boolean>>({
    method: 'DELETE',
    url: `/verification/cancel/${applicationId}`
  })
}

/**
 * 根据用户ID获取认证状态（用于其他页面显示用户认证状态）
 */
export const getUserAuthStatusAPI = (userId: number) => {
  return http<Result<AuthApplication | null>>({
    method: 'GET',
    url: `/verification/user-status/${userId}`
  })
}

/**
 * 获取认证申请列表（管理员使用）
 */
export const getAuthApplicationsAPI = (params?: {
  status?: number;
  page?: number;
  pageSize?: number;
}) => {
  return http<Result<AuthApplication[]>>({
    method: 'GET',
    url: '/verification/applications',
    data: params
  })
}

/**
 * 文件上传响应
 */
export interface FileUploadResponse {
  url: string
  filename: string
}

/**
 * uni-app 认证文件上传封装
 */
export const uploadVerificationFileUni = (filePath: string) => {
  return new Promise<FileUploadResponse>((resolve, reject) => {
    // 获取token
    const token = uni.getStorageSync('token') || ''
    console.log('上传认证文件，token:', token)
    console.log('文件路径:', filePath)

    uni.uploadFile({
      url: '/files/userUpload',
      filePath,
      name: 'file',
      formData: {
        fileType: 'verification' // 认证文件类型
      },
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
      },
      success: (res) => {
        console.log('认证文件上传响应:', res)

        // 检查HTTP状态码
        if (res.statusCode === 200) {
          // 成功响应，res.data直接是文件路径字符串
          const filePath = res.data
          console.log('认证文件上传成功，文件路径:', filePath)

          resolve({
            url: filePath,
            filename: filePath.split('/').pop() || ''
          })
        } else {
          // 错误响应
          console.error('认证文件上传失败，状态码:', res.statusCode)
          console.error('错误信息:', res.data)
          reject(new Error(res.data || '上传失败'))
        }
      },
      fail: (error) => {
        console.error('认证文件上传失败:', error)
        reject(error)
      }
    })
  })
}
