package com.haolinkyou.service;

import com.haolinkyou.entity.UserPoints;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 积分服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
public interface IPointsService extends IService<UserPoints> {

    /**
     * 获取用户积分余额
     * 
     * @param userId 用户ID
     * @return 积分余额
     */
    Integer getUserPointsBalance(Long userId);

    /**
     * 增加用户积分
     * 
     * @param userId 用户ID
     * @param points 积分数量
     * @param type 积分类型
     * @param description 积分说明
     * @return 是否成功
     */
    boolean addPoints(Long userId, Integer points, String type, String description);

    /**
     * 增加用户积分（带关联ID）
     * 
     * @param userId 用户ID
     * @param points 积分数量
     * @param type 积分类型
     * @param description 积分说明
     * @param relatedId 关联ID
     * @return 是否成功
     */
    boolean addPoints(Long userId, Integer points, String type, String description, Long relatedId);

    /**
     * 扣除用户积分
     * 
     * @param userId 用户ID
     * @param points 积分数量
     * @param type 积分类型
     * @param description 积分说明
     * @return 是否成功
     */
    boolean deductPoints(Long userId, Integer points, String type, String description);

    /**
     * 扣除用户积分（带关联ID）
     * 
     * @param userId 用户ID
     * @param points 积分数量
     * @param type 积分类型
     * @param description 积分说明
     * @param relatedId 关联ID
     * @return 是否成功
     */
    boolean deductPoints(Long userId, Integer points, String type, String description, Long relatedId);

    /**
     * 检查每日积分限额
     * 
     * @param userId 用户ID
     * @param points 要增加的积分
     * @return 是否在限额内
     */
    boolean checkDailyLimit(Long userId, Integer points);

    /**
     * 获取今日已获得积分总量
     * 
     * @param userId 用户ID
     * @return 今日积分总量
     */
    Integer getTodayPointsTotal(Long userId);

    /**
     * 获取今日剩余可获得积分
     * 
     * @param userId 用户ID
     * @return 剩余积分
     */
    Integer getRemainingDailyPoints(Long userId);

    /**
     * 防刷检查
     * 
     * @param userId 用户ID
     * @param action 操作类型
     * @return 是否通过检查
     */
    boolean checkAntiSpam(Long userId, String action);

    /**
     * 获取用户积分记录（分页）
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 积分记录分页
     */
    Page<UserPoints> getUserPointsRecords(Long userId, Integer page, Integer size);

    /**
     * 获取用户积分记录（分页，支持类型筛选）
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param type 积分类型（可选）
     * @return 积分记录分页
     */
    Page<UserPoints> getUserPointsRecords(Long userId, Integer page, Integer size, String type);

    /**
     * 获取积分规则配置
     * 
     * @return 积分规则
     */
    PointsRules getPointsRules();

    /**
     * 积分规则配置类
     */
    class PointsRules {
        private Integer dailyLimit;
        private Integer signDaily;
        private Integer postCreate;
        private Integer commentCreate;
        private Integer likeGive;
        private Integer collectGive;
        private Integer continuousSignBonus;
        private Integer postTopCost;
        private Integer postTopDuration;

        // Getters and Setters
        public Integer getDailyLimit() { return dailyLimit; }
        public void setDailyLimit(Integer dailyLimit) { this.dailyLimit = dailyLimit; }

        public Integer getSignDaily() { return signDaily; }
        public void setSignDaily(Integer signDaily) { this.signDaily = signDaily; }

        public Integer getPostCreate() { return postCreate; }
        public void setPostCreate(Integer postCreate) { this.postCreate = postCreate; }

        public Integer getCommentCreate() { return commentCreate; }
        public void setCommentCreate(Integer commentCreate) { this.commentCreate = commentCreate; }

        public Integer getLikeGive() { return likeGive; }
        public void setLikeGive(Integer likeGive) { this.likeGive = likeGive; }

        public Integer getCollectGive() { return collectGive; }
        public void setCollectGive(Integer collectGive) { this.collectGive = collectGive; }

        public Integer getContinuousSignBonus() { return continuousSignBonus; }
        public void setContinuousSignBonus(Integer continuousSignBonus) { this.continuousSignBonus = continuousSignBonus; }

        public Integer getPostTopCost() { return postTopCost; }
        public void setPostTopCost(Integer postTopCost) { this.postTopCost = postTopCost; }

        public Integer getPostTopDuration() { return postTopDuration; }
        public void setPostTopDuration(Integer postTopDuration) { this.postTopDuration = postTopDuration; }
    }
}