/*
 * @Author: Rock
 * @Date: 2025-05-06 11:14:09
 * @LastEditors: Rock
 * @LastEditTime: 2025-05-06 11:18:05
 * @Description: 
 */
package com.haolinkyou.common.utils;

import java.security.MessageDigest;

public class Md5Util {

    /**
     * 生成字符串的MD5值
     * @param input 输入字符串
     * @param bitLength 16 或 32，表示返回16位还是32位MD5
     * @return MD5加密后的字符串
     */
    public static String md5(String input, int bitLength) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] array = md.digest(input.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : array) {
                sb.append(String.format("%02x", b & 0xff));
            }
            String md5Str = sb.toString();
            if (bitLength == 16) {
                // 16位MD5为32位MD5的9~24位（下标8~24）
                return md5Str.substring(8, 24);
            } else {
                // 默认返回32位
                return md5Str;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 默认生成32位MD5
     */
    public static String md5(String input) {
        return md5(input, 32);
    }

    /**
     * 生成MD5值（兼容方法）
     */
    public static String getMD5(String input) {
        return md5(input, 32);
    }
}