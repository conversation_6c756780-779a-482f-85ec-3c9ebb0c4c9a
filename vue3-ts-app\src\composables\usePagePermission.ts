import { ref, onMounted } from 'vue'
import { checkPagePermission } from '@/utils/pagePermission'

/**
 * 页面权限验证组合式函数
 * 用于在页面加载前进行权限验证，只有验证通过才渲染页面内容
 * 
 * @param pagePath 页面路径
 * @param initCallback 权限验证通过后的初始化回调函数
 * @returns 权限状态和相关方法
 */
export function usePagePermission(pagePath: string, initCallback?: () => void | Promise<void>) {
  // 权限验证状态
  const permissionLoading = ref(true)
  const hasPagePermission = ref(false)
  const permissionError = ref<string | null>(null)

  /**
   * 执行权限检查和页面初始化
   */
  const initPageWithPermission = async () => {
    try {
      permissionLoading.value = true
      permissionError.value = null
      
      console.log(`开始检查页面权限: ${pagePath}`)
      
      // 检查页面权限
      const hasPermission = await checkPagePermission(pagePath)
      hasPagePermission.value = hasPermission
      
      if (hasPermission) {
        console.log(`页面权限验证通过: ${pagePath}`)
        
        // 权限验证通过，执行初始化回调
        if (initCallback) {
          await initCallback()
        }
      } else {
        console.log(`页面权限验证失败: ${pagePath}`)
      }
    } catch (error) {
      console.error(`页面权限检查失败: ${pagePath}`, error)
      hasPagePermission.value = false
      permissionError.value = error instanceof Error ? error.message : '权限验证失败'
    } finally {
      permissionLoading.value = false
    }
  }

  /**
   * 重新检查权限
   */
  const recheckPermission = () => {
    initPageWithPermission()
  }

  // 页面挂载时自动检查权限
  onMounted(() => {
    initPageWithPermission()
  })

  return {
    // 状态
    permissionLoading,
    hasPagePermission,
    permissionError,
    
    // 方法
    recheckPermission,
    initPageWithPermission
  }
}

/**
 * 创建权限页面模板
 * 返回用于模板的条件渲染逻辑
 * 
 * @param pagePath 页面路径
 * @param initCallback 初始化回调
 * @returns 模板渲染状态
 */
export function createPermissionPageTemplate(pagePath: string, initCallback?: () => void | Promise<void>) {
  const {
    permissionLoading,
    hasPagePermission,
    permissionError,
    recheckPermission
  } = usePagePermission(pagePath, initCallback)

  return {
    permissionLoading,
    hasPagePermission,
    permissionError,
    recheckPermission,
    
    // 模板渲染条件
    showLoading: permissionLoading,
    showContent: () => !permissionLoading.value && hasPagePermission.value,
    showError: () => !permissionLoading.value && !hasPagePermission.value && permissionError.value
  }
}

/**
 * 权限页面装饰器
 * 用于类组件的权限控制
 * 
 * @param pagePath 页面路径
 * @returns 装饰器函数
 */
export function withPagePermission(pagePath: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      try {
        const hasPermission = await checkPagePermission(pagePath)
        if (hasPermission) {
          return originalMethod.apply(this, args)
        } else {
          console.warn(`页面权限不足，无法执行方法: ${propertyKey}`)
        }
      } catch (error) {
        console.error(`权限检查失败，无法执行方法: ${propertyKey}`, error)
      }
    }
    
    return descriptor
  }
}

/**
 * 批量页面权限检查
 * 用于检查多个页面的权限状态
 * 
 * @param pagePaths 页面路径数组
 * @returns 权限检查结果
 */
export async function checkMultiplePagePermissions(pagePaths: string[]) {
  const results: Record<string, boolean> = {}
  
  const promises = pagePaths.map(async (path) => {
    try {
      const hasPermission = await checkPagePermission(path)
      results[path] = hasPermission
    } catch (error) {
      console.error(`检查页面权限失败: ${path}`, error)
      results[path] = false
    }
  })
  
  await Promise.all(promises)
  return results
}

/**
 * 权限状态常量
 */
export const PERMISSION_STATUS = {
  LOADING: 'loading',
  GRANTED: 'granted',
  DENIED: 'denied',
  ERROR: 'error'
} as const

export type PermissionStatus = typeof PERMISSION_STATUS[keyof typeof PERMISSION_STATUS]

/**
 * 获取权限状态
 * 
 * @param permissionLoading 是否正在加载
 * @param hasPagePermission 是否有权限
 * @param permissionError 权限错误
 * @returns 权限状态
 */
export function getPermissionStatus(
  permissionLoading: boolean,
  hasPagePermission: boolean,
  permissionError: string | null
): PermissionStatus {
  if (permissionLoading) {
    return PERMISSION_STATUS.LOADING
  }
  
  if (permissionError) {
    return PERMISSION_STATUS.ERROR
  }
  
  return hasPagePermission ? PERMISSION_STATUS.GRANTED : PERMISSION_STATUS.DENIED
}
