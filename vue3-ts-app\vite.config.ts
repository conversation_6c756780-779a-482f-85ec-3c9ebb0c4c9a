/*
 * @Author: Rock
 * @Date: 2025-04-09 19:56:09
 * @LastEditors: Rock
 * @LastEditTime: 2025-07-27 13:57:30
 * @Description:
 */
import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";

export default defineConfig(async () => {
  const AutoImport = await import("unplugin-auto-import/vite").then(
    (m) => m.default
  );

  return {
    plugins: [
      uni(),
      AutoImport({
        imports: ["vue", "pinia", "uni-app"],
        dirs: ["./src/types/"],
        dts: "src/auto-imports.d.ts",
      }),
    ],
    server: {
      proxy: {
        "/api": {
          target: "http://localhost:3205",
          changeOrigin: true,
          secure: false,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods":
              "GET, POST, PUT, DELETE, PATCH, OPTIONS",
            "Access-Control-Allow-Headers":
              "X-Requested-With, content-type, Authorization",
          },
          rewrite(path) {
            return path.replace(/^\/api/, "/api");
          },
        },
      },
    },
  };
});
