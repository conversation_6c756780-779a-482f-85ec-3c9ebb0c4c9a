package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分类模板配置实体类
 */
@Data
@TableName("category_templates")
@EqualsAndHashCode(callSuper = true)
public class CategoryTemplates extends BaseEntity {

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 模板配置JSON
     * 包含字段定义、验证规则等
     */
    private String templateConfig;

    /**
     * 是否为默认模板
     */
    private Boolean isDefault;

    /**
     * 模板状态 (0-禁用 1-启用)
     */
    private Integer status;

    /**
     * 排序顺序
     */
    private Integer sortOrder;
}
