<template>
  <view class="post-detail-content">
    <!-- 根据帖子类型动态渲染对应的详情组件 -->
    <component 
      :is="detailComponent" 
      :post="processedPostData"
      @participate="handleParticipate"
      @vote="handleVote"
      @viewResults="handleViewResults"
    />
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BasePostDetail from './BasePostDetail.vue'
import AnnouncementPostDetail from './AnnouncementPostDetail.vue'
import GroupBuyPostDetail from './GroupBuyPostDetail.vue'
import ActivityPostDetail from './ActivityPostDetail.vue'
import SurveyPostDetail from './SurveyPostDetail.vue'
import VotePostDetail from './VotePostDetail.vue'

interface Props {
  post: {
    id?: number
    title?: string
    content: string
    images?: string[]
    templateData?: string | object
    templateId?: number
    postType?: string
    categoryName?: string
    createdTime: string
    updatedTime?: string
    fileListData?: string[]
    [key: string]: any
  }
}

const props = defineProps<Props>()

// 解析模板数据
const parsedTemplateData = computed(() => {
  if (!props.post.templateData) {
    console.log('PostDetailContent: 没有模板数据')
    return {}
  }
  
  let parsed = {}
  if (typeof props.post.templateData === 'string') {
    try {
      parsed = JSON.parse(props.post.templateData)
      console.log('PostDetailContent: 解析模板数据成功:', parsed)
    } catch (error) {
      console.error('PostDetailContent: 解析模板数据失败:', error)
      return {}
    }
  } else {
    parsed = props.post.templateData
    console.log('PostDetailContent: 模板数据已是对象:', parsed)
  }
  
  // 处理嵌套的 JSON 字符串字段
  if (parsed && typeof parsed === 'object') {
    // 处理 questions 字段（调查问卷）
    if (parsed.questions && typeof parsed.questions === 'string') {
      try {
        parsed.questions = JSON.parse(parsed.questions)
        console.log('PostDetailContent: 解析 questions 字段成功:', parsed.questions)
      } catch (error) {
        console.error('PostDetailContent: 解析 questions 字段失败:', error)
        parsed.questions = []
      }
    }
    
    // 处理 options 字段（投票）
    if (parsed.options && typeof parsed.options === 'string') {
      try {
        parsed.options = JSON.parse(parsed.options)
        console.log('PostDetailContent: 解析 options 字段成功:', parsed.options)
      } catch (error) {
        console.error('PostDetailContent: 解析 options 字段失败:', error)
        parsed.options = []
      }
    }
    
    // 处理其他可能的嵌套 JSON 字段
    const jsonFields = ['targetAudience', 'existingFiles', 'newFiles', 'participants']
    jsonFields.forEach(field => {
      if (parsed[field] && typeof parsed[field] === 'string') {
        try {
          parsed[field] = JSON.parse(parsed[field])
          console.log(`PostDetailContent: 解析 ${field} 字段成功:`, parsed[field])
        } catch (error) {
          console.error(`PostDetailContent: 解析 ${field} 字段失败:`, error)
          parsed[field] = []
        }
      }
    })
    
    // 处理布尔值字段
    const booleanFields = ['anonymous', 'multiSelect', 'isAnonymous']
    booleanFields.forEach(field => {
      if (parsed[field] !== undefined) {
        if (typeof parsed[field] === 'string') {
          parsed[field] = parsed[field] === 'true'
        } else {
          parsed[field] = Boolean(parsed[field])
        }
        console.log(`PostDetailContent: 处理布尔字段 ${field}:`, parsed[field])
      }
    })
  }
  
  return parsed
})

// 根据帖子类型和分类确定详情组件
const detailComponent = computed(() => {
  const templateData = parsedTemplateData.value
  const categoryName = props.post.categoryName
  
  console.log('PostDetailContent: 选择详情组件', {
    templateData,
    categoryName,
    hasQuestions: templateData.questions && Array.isArray(templateData.questions),
    hasOptions: templateData.options && Array.isArray(templateData.options),
    voteType: templateData.voteType
  })
  
  // 根据模板数据中的特定字段判断类型
  if (templateData.activityType || templateData.location || templateData.activityDate) {
    console.log('PostDetailContent: 选择ActivityPostDetail')
    return ActivityPostDetail
  }
  
  if (templateData.originalPrice || templateData.groupPrice || templateData.minQuantity) {
    console.log('PostDetailContent: 选择GroupBuyPostDetail')
    return GroupBuyPostDetail
  }
  
  if (templateData.questions && Array.isArray(templateData.questions)) {
    console.log('PostDetailContent: 选择SurveyPostDetail')
    return SurveyPostDetail
  }
  
  if (templateData.options && Array.isArray(templateData.options)) {
    console.log('PostDetailContent: 选择VotePostDetail')
    return VotePostDetail
  }
  
  if (templateData.priority || categoryName === '三期公告') {
    console.log('PostDetailContent: 选择AnnouncementPostDetail')
    return AnnouncementPostDetail
  }
  
  // 默认使用基础详情组件
  console.log('PostDetailContent: 选择BasePostDetail（默认）')
  return BasePostDetail
})

// 处理后的帖子数据，合并基础数据和模板数据
const processedPostData = computed(() => {
  const baseData = {
    id: props.post.id,
    title: props.post.title || '',
    content: props.post.content || '', // 确保content不为undefined
    images: props.post.fileListData || props.post.images || [],
    createdTime: props.post.createdTime,
    updatedTime: props.post.updatedTime,
    categoryName: props.post.categoryName
  }
  
  const templateData = parsedTemplateData.value
  
  // 对于投票类型，需要处理multiSelect字段转换为voteType
  if (templateData.options && Array.isArray(templateData.options)) {
    const voteType = templateData.multiSelect === 'true' || templateData.multiSelect === true ? 'multiple' : 'single'
    templateData.voteType = voteType
    
    // 处理匿名设置
    if (templateData.anonymous !== undefined) {
      templateData.isAnonymous = templateData.anonymous === 'true' || templateData.anonymous === true
    }
  }
  
  // 对于调查类型，处理匿名设置
  if (templateData.questions && Array.isArray(templateData.questions)) {
    if (templateData.anonymous !== undefined) {
      templateData.isAnonymous = templateData.anonymous === 'true' || templateData.anonymous === true
    }
  }
  
  // 对于团购类型，处理数值字段
  if (templateData.originalPrice || templateData.groupPrice || templateData.minQuantity) {
    // 确保价格字段是字符串格式（组件期望的格式）
    if (templateData.originalPrice && typeof templateData.originalPrice === 'number') {
      templateData.originalPrice = templateData.originalPrice.toString()
    }
    if (templateData.groupPrice && typeof templateData.groupPrice === 'number') {
      templateData.groupPrice = templateData.groupPrice.toString()
    }
    if (templateData.minQuantity && typeof templateData.minQuantity === 'number') {
      templateData.minQuantity = templateData.minQuantity.toString()
    }
  }
  
  // 对于活动类型，处理数值字段
  if (templateData.activityType || templateData.location || templateData.activityDate) {
    if (templateData.minParticipants && typeof templateData.minParticipants === 'number') {
      templateData.minParticipants = templateData.minParticipants.toString()
    }
    if (templateData.maxParticipants && typeof templateData.maxParticipants === 'number') {
      templateData.maxParticipants = templateData.maxParticipants.toString()
    }
  }
  
  // 合并基础数据和模板数据
  return {
    ...baseData,
    ...templateData
  }
})

// 事件处理
const emit = defineEmits<{
  participate: [postId: number]
  vote: [postId: number]
  viewResults: [postId: number]
}>()

const handleParticipate = () => {
  if (props.post.id) {
    emit('participate', props.post.id)
  }
}

const handleVote = () => {
  if (props.post.id) {
    emit('vote', props.post.id)
  }
}

const handleViewResults = () => {
  if (props.post.id) {
    emit('viewResults', props.post.id)
  }
}
</script>

<style scoped lang="scss">
.post-detail-content {
  width: 100%;
}
</style>