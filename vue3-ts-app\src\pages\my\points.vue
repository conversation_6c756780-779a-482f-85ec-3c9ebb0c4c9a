<template>
  <view class="points-page">
    <!-- 积分概览 -->
    <view class="points-overview">
      <view class="points-card">
        <view class="points-header">
          <view class="points-info">
            <text class="points-title">我的积分</text>
            <text class="points-value">{{ userPoints.total }}</text>
          </view>
          <view class="points-icon">
            <up-icon name="gift-fill" color="#ff6b35" size="40"></up-icon>
          </view>
        </view>
        <view class="points-stats">
          <view class="stat-item">
            <text class="stat-value">+{{ userPoints.todayEarned }}</text>
            <text class="stat-label">今日获得</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ userPoints.monthEarned }}</text>
            <text class="stat-label">本月获得</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ userPoints.used }}</text>
            <text class="stat-label">已使用</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 积分任务 -->
    <view class="tasks-section">
      <view class="section-header">
        <text class="section-title">每日任务</text>
        <text class="section-subtitle">完成任务获得积分奖励</text>
      </view>
      
      <view class="tasks-list">
        <view v-for="task in dailyTasks" :key="task.id" class="task-item">
          <view class="task-icon">
            <up-icon :name="task.icon" :color="task.completed ? '#52c41a' : '#5677fc'" size="24"></up-icon>
          </view>
          <view class="task-content">
            <text class="task-name">{{ task.name }}</text>
            <text class="task-desc">{{ task.description }}</text>
            <view class="task-progress">
              <text class="progress-text">{{ task.current }}/{{ task.target }}</text>
              <view class="progress-bar">
                <view 
                  class="progress-fill" 
                  :style="{ width: (task.current / task.target * 100) + '%' }"
                ></view>
              </view>
            </view>
          </view>
          <view class="task-reward">
            <view v-if="task.completed" class="completed-badge">
              <up-icon name="checkmark" color="white" size="12"></up-icon>
              <text class="completed-text">已完成</text>
            </view>
            <view v-else class="reward-points">
              <text class="points-text">+{{ task.points }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 积分商城入口 -->
    <view class="mall-section">
      <view class="mall-card" @click="goToMall">
        <view class="mall-content">
          <view class="mall-info">
            <text class="mall-title">积分商城</text>
            <text class="mall-desc">用积分兑换精美礼品</text>
          </view>
          <view class="mall-icon">
            <up-icon name="shop" color="#5677fc" size="24"></up-icon>
            <up-icon name="arrow-right" color="#999" size="16"></up-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 积分记录 -->
    <view class="records-section">
      <view class="section-header">
        <text class="section-title">积分记录</text>
        <view class="filter-tabs">
          <text 
            v-for="(tab, index) in recordTabs" 
            :key="index"
            class="tab-item"
            :class="{ active: currentTab === index }"
            @click="switchTab(index)"
          >
            {{ tab }}
          </text>
        </view>
      </view>

      <view class="records-list">
        <view v-if="pointRecords.length === 0 && !loading" class="empty-state">
          <up-empty
            mode="data"
            text="暂无记录"
            textSize="14"
          ></up-empty>
        </view>

        <view v-for="record in pointRecords" :key="record.id" class="record-item">
          <view class="record-icon">
            <up-icon 
              :name="getRecordIcon(record.type)" 
              :color="getRecordColor(record.type)" 
              size="20"
            ></up-icon>
          </view>
          <view class="record-content">
            <text class="record-title">{{ record.title }}</text>
            <text class="record-time">{{ formatTime(record.createdTime) }}</text>
          </view>
          <view class="record-points">
            <text 
              class="points-change"
              :class="{ positive: record.points > 0, negative: record.points < 0 }"
            >
              {{ record.points > 0 ? '+' : '' }}{{ record.points }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <up-loadmore
      :status="loadStatus"
      :loading-text="loadingText"
      :loadmore-text="loadmoreText"
      :nomore-text="nomoreText"
      @loadmore="loadMore"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useMemberStore } from '@/stores'
import type { PointRecord, Product, RedemptionRecord } from '@/types'
import { get, post } from '@/utils/http'

const memberStore = useMemberStore()

// 用户积分信息
const userPoints = reactive({
  total: 0,
  todayEarned: 0,
  monthEarned: 0,
  used: 0
})

// 每日任务
const dailyTasks = ref([
  {
    id: 1,
    name: '每日签到',
    description: '每天签到获得积分',
    icon: 'calendar',
    points: 10,
    current: 1,
    target: 1,
    completed: true
  },
  {
    id: 2,
    name: '发布动态',
    description: '发布一条动态',
    icon: 'edit-pen',
    points: 20,
    current: 0,
    target: 1,
    completed: false
  },
  {
    id: 3,
    name: '互动交流',
    description: '点赞或评论5次',
    icon: 'thumb-up',
    points: 15,
    current: 2,
    target: 5,
    completed: false
  },
  {
    id: 4,
    name: '邻里互助',
    description: '在互助板块发布或回复',
    icon: 'heart',
    points: 25,
    current: 0,
    target: 1,
    completed: false
  }
])

// 积分记录
const pointRecords = ref<any[]>([])
const loading = ref(false)
const currentTab = ref(0)
const recordTabs = ref(['全部', '获得', '消费'])

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)
const loadStatus = ref('loadmore')
const loadingText = ref('正在加载...')
const loadmoreText = ref('上拉加载更多')
const nomoreText = ref('已经到底了')

// 获取用户积分信息
const fetchUserPoints = async () => {
  try {
    // 调用获取用户积分API
    const res = await get('/user-points/info')

    if (res.success) {
      Object.assign(userPoints, res.data)
    }
  } catch (error) {
    console.error('获取积分信息失败:', error)
  }
}

// 获取积分记录
const fetchPointRecords = async (isRefresh = false) => {
  if (isRefresh) {
    currentPage.value = 1
    hasMore.value = true
    loadStatus.value = 'loading'
  }

  loading.value = true

  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      type: currentTab.value === 0 ? '' : (currentTab.value === 1 ? 'earn' : 'spend')
    }

    // 调用获取积分记录API
    const res = await get('/user-points/records', params)

    if (res.success) {
      const newRecords = res.data.records || []

      if (isRefresh) {
        pointRecords.value = newRecords
      } else {
        pointRecords.value.push(...newRecords)
      }

      // 判断是否还有更多数据
      if (newRecords.length < pageSize.value) {
        hasMore.value = false
        loadStatus.value = 'nomore'
      } else {
        loadStatus.value = 'loadmore'
      }
    }
  } catch (error) {
    console.error('获取积分记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 切换记录标签
const switchTab = (index: number) => {
  currentTab.value = index
  fetchPointRecords(true)
}

// 加载更多
const loadMore = () => {
  if (!hasMore.value || loading.value) return
  
  currentPage.value++
  fetchPointRecords()
}

// 获取记录图标
const getRecordIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    sign: 'calendar',
    post: 'edit-pen',
    like: 'thumb-up',
    comment: 'chat',
    exchange: 'gift',
    task: 'checkmark-circle'
  }
  return iconMap[type] || 'info-circle'
}

// 获取记录颜色
const getRecordColor = (type: string) => {
  const colorMap: Record<string, string> = {
    sign: '#52c41a',
    post: '#5677fc',
    like: '#ff4757',
    comment: '#3742fa',
    exchange: '#ff6b35',
    task: '#2ed573'
  }
  return colorMap[type] || '#666'
}

// 格式化时间
const formatTime = (time: string) => {
  const date = new Date(time)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 86400000)
  
  if (date >= today) {
    return `今天 ${date.toTimeString().slice(0, 5)}`
  } else if (date >= yesterday) {
    return `昨天 ${date.toTimeString().slice(0, 5)}`
  } else {
    return `${date.getMonth() + 1}月${date.getDate()}日`
  }
}

// 跳转到积分商城
const goToMall = () => {
  uni.navigateTo({
    url: '/pages/my/mall'
  })
}

onMounted(() => {
  fetchUserPoints()
  fetchPointRecords(true)
})
</script>

<style scoped lang="scss">
.points-page {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.points-overview {
  padding: 20px;
}

.points-card {
  background: linear-gradient(135deg, #5677fc 0%, #7c4dff 100%);
  border-radius: 16px;
  padding: 20px;
  color: white;
}

.points-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.points-title {
  font-size: 14px;
  opacity: 0.9;
  display: block;
  margin-bottom: 8px;
}

.points-value {
  font-size: 32px;
  font-weight: bold;
}

.points-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.tasks-section, .mall-section, .records-section {
  background-color: white;
  margin: 10px;
  border-radius: 12px;
  padding: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  font-size: 12px;
  color: #999;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.task-icon {
  margin-right: 12px;
}

.task-content {
  flex: 1;
}

.task-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4px;
}

.task-desc {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 8px;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 40px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #5677fc;
  transition: width 0.3s ease;
}

.task-reward {
  margin-left: 12px;
}

.completed-badge {
  background-color: #52c41a;
  border-radius: 12px;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.completed-text {
  font-size: 10px;
  color: white;
}

.reward-points {
  .points-text {
    font-size: 14px;
    color: #ff6b35;
    font-weight: 600;
  }
}

.mall-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
}

.mall-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mall-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4px;
}

.mall-desc {
  font-size: 12px;
  color: #999;
}

.mall-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-tabs {
  display: flex;
  gap: 16px;
}

.tab-item {
  font-size: 14px;
  color: #999;
  padding: 4px 0;
  position: relative;
  
  &.active {
    color: #5677fc;
    font-weight: 500;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #5677fc;
      border-radius: 1px;
    }
  }
}

.record-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.record-icon {
  margin-right: 12px;
}

.record-content {
  flex: 1;
}

.record-title {
  font-size: 14px;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.record-time {
  font-size: 12px;
  color: #999;
}

.record-points {
  .points-change {
    font-size: 14px;
    font-weight: 600;
    
    &.positive {
      color: #52c41a;
    }
    
    &.negative {
      color: #ff4757;
    }
  }
}

.empty-state {
  padding: 40px 20px;
}
</style>
