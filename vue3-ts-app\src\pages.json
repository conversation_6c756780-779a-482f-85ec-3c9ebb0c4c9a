{
  "easycom": {
    "autoscan": true,
    // 注意一定要放在custom里，否则无效，https://ask.dcloud.net.cn/question/131175
    "custom": {
      "^u--(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue",
      // 引入自定义组件
      "^Hly(.*)": "@/components/Hly$1.vue"
    }
  },
  "pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "大唐世家三期好邻友",
        "navigationStyle": "custom",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/post/detail",
      "style": {
        "navigationBarTitleText": "帖子详情"
      }
    },
    {
      "path": "pages/post/edit",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/my/index",
      "style": {
        "navigationBarTitleText": "我的",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/login/index",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/publish/index",
      "style": {
        "navigationBarTitleText": "动态发布",
        "enablePullDownRefresh": false,
        "navigationBarBackgroundColor": "#5677fc",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/profile/edit",
      "style": {
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/my/posts",
      "style": {
        "navigationBarTitleText": "我的动态",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/my/collections",
      "style": {
        "navigationBarTitleText": "我的收藏",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/my/points",
      "style": {
        "navigationBarTitleText": "我的积分",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/my/sign-records",
      "style": {
        "navigationBarTitleText": "签到记录",
        "navigationStyle": "custom",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/my/mall",
      "style": {
        "navigationBarTitleText": "积分商城",
        "navigationStyle": "custom",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/my/redemptions",
      "style": {
        "navigationBarTitleText": "兑换记录",
        "navigationStyle": "custom",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/owner-verification/index",
      "style": {
        "navigationBarTitleText": "业主认证",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/admin/index",
      "style": {
        "navigationBarTitleText": "管理后台",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/admin/user-management",
      "style": {
        "navigationBarTitleText": "用户管理",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/admin/post-management",
      "style": {
        "navigationBarTitleText": "动态管理",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/admin/auth-management",
      "style": {
        "navigationBarTitleText": "认证管理",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/admin/feedback-management",
      "style": {
        "navigationBarTitleText": "建议反馈",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/admin/product-management",
      "style": {
        "navigationBarTitleText": "商品管理",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/admin/product-add",
      "style": {
        "navigationBarTitleText": "添加商品",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/admin/share-config",
      "style": {
        "navigationBarTitleText": "分享配置",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/admin/pending-reviews",
      "style": {
        "navigationBarTitleText": "待审核管理",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/admin/system-config",
      "style": {
        "navigationBarTitleText": "系统配置管理",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/media/video-player",
      "style": {
        "navigationBarTitleText": "视频播放",
        "navigationStyle": "custom",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/posts/list",
      "style": {
        "navigationBarTitleText": "帖子列表",
        "navigationStyle": "custom",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/search/search",
      "style": {
        "navigationBarTitleText": "搜索",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/webview/index",
      "style": {
        "navigationBarTitleText": "网页浏览",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/survey/participate",
      "style": {
        "navigationBarTitleText": "参与调查",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/survey/results",
      "style": {
        "navigationBarTitleText": "调查结果",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/vote/participate",
      "style": {
        "navigationBarTitleText": "参与投票",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/vote/results",
      "style": {
        "navigationBarTitleText": "投票结果",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "大唐世家三期好邻友",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#44ADFF",
    "backgroundColor": "#F8F8F8",
    "borderStyle": "white", // 仅支持 black/white
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "/static/images/tabBar/home.png",
        "selectedIconPath": "/static/images/tabBar/home-active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/publish/index",
        "iconPath": "/static/images/tabBar/add.png",
        "selectedIconPath": "/static/images/tabBar/add.png",
        "text": "发布"
      },
      {
        "pagePath": "pages/my/index",
        "iconPath": "/static/images/tabBar/user.png",
        "selectedIconPath": "/static/images/tabBar/user-active.png",
        "text": "我的"
      }
    ]
  }
}