<template>
  <view class="custom-tabbar">
    <block v-for="item in tabBarList" :key="item.pagePath">
      <navigator :url="item.pagePath" class="tabbar-item">
        <image :src="isCurrentPage(item.pagePath) ? item.selectedIconPath : item.iconPath"></image>
        <text :style="{ color: isCurrentPage(item.pagePath) ? selectedColor : color }">{{ item.text }}</text>
      </navigator>
    </block>
  </view>
</template>

<script setup>

const props = defineProps({
  color: {
    type: String,
    default: '#999999'
  },
  selectedColor: {
    type: String,
    default: '#4396F7'
  },
  tabBarList: {
    type: Array,
    default: () => [
      {
        pagePath: '/pages/home',
        iconPath: '/static/icons/home.png',
        selectedIconPath: '/static/icons/home-selected.png',
        text: '首页'
      },
      {
        pagePath: '/pages/profile',
        iconPath: '/static/icons/profile.png',
        selectedIconPath: '/static/icons/profile-selected.png',
        text: '我的'
      }
    ]
  }
});

const isCurrentPage = (pagePath) => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  return currentPage.route === pagePath.replace('pages/', '');
};
</script>

<style scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  background-color: #F8F8F8;
  padding: 10rpx 0;
}

.tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
}

.tabbar-item image {
  width: 40rpx;
  height: 40rpx;
}

.tabbar-item text {
  font-size: 24rpx;
}
</style>