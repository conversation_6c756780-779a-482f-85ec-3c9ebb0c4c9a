package com.haolinkyou.controller;

import com.haolinkyou.common.utils.Md5Util;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 批量上传响应类
class BatchUploadResponse {
    private boolean success;
    private String message;
    private List<String> filePaths;
    private Map<String, Object> metadata;

    public BatchUploadResponse() {}

    public BatchUploadResponse(boolean success, String message, List<String> filePaths) {
        this.success = success;
        this.message = message;
        this.filePaths = filePaths;
        this.metadata = new HashMap<>();
    }

    // Getters and Setters
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    public List<String> getFilePaths() { return filePaths; }
    public void setFilePaths(List<String> filePaths) { this.filePaths = filePaths; }
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
}

@RestController
@RequestMapping("/api/files")
@CrossOrigin(
    origins = {
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:8080",
        "http://127.0.0.1:8080"
    },
    methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.OPTIONS},
    allowedHeaders = "*",
    allowCredentials = "false"
)
public class PostFilesController {

    @Value("${file.upload.dir}")
    private String uploadDir;

    /**
     * 智能批量文件上传接口
     * 支持自动文件类型检测和批量处理
     */
    @PostMapping("/batch")
    public ResponseEntity<BatchUploadResponse> batchUpload(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "postId", required = false) Long postId,
            HttpServletRequest request) {

        System.out.println("=== 智能批量文件上传 ===");
        System.out.println("文件数量: " + (files != null ? files.length : 0));

        try {
            if (files == null || files.length == 0) {
                return ResponseEntity.badRequest()
                    .body(new BatchUploadResponse(false, "请选择要上传的文件", new ArrayList<>()));
            }

            List<String> uploadedPaths = new ArrayList<>();
            Map<String, Integer> fileTypeCount = new HashMap<>();
            fileTypeCount.put("image", 0);
            fileTypeCount.put("video", 0);
            fileTypeCount.put("other", 0);

            // 生成保存路径
            String dateFolder = LocalDate.now().toString();
            String saveDir = uploadDir + dateFolder;
            File dir = new File(saveDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 处理每个文件
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                if (file.isEmpty()) {
                    continue;
                }

                String originalFilename = file.getOriginalFilename();
                if (originalFilename == null || originalFilename.trim().isEmpty()) {
                    continue;
                }

                // 检测文件类型
                String fileType = detectFileType(originalFilename, file.getContentType());
                fileTypeCount.put(fileType, fileTypeCount.get(fileType) + 1);

                // 验证文件类型和大小
                if (!isValidFile(file, fileType)) {
                    continue;
                }

                // 生成唯一文件名
                String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
                String raw = originalFilename + "_" + System.currentTimeMillis() + "_" + System.nanoTime() + "_" + i;
                String newFileName = Md5Util.md5(raw, 16) + "." + suffix;

                // 确保文件名唯一
                File dest = new File(dir, newFileName);
                int attempt = 0;
                while (dest.exists() && attempt < 10) {
                    raw = originalFilename + "_" + System.currentTimeMillis() + "_" + System.nanoTime() + "_" + i + "_" + attempt;
                    newFileName = Md5Util.md5(raw, 16) + "." + suffix;
                    dest = new File(dir, newFileName);
                    attempt++;
                }

                // 保存文件
                file.transferTo(dest);
                String relativePath = dateFolder + "/" + newFileName;
                uploadedPaths.add(relativePath);

                System.out.println("文件上传成功: " + originalFilename + " -> " + relativePath + " (类型: " + fileType + ")");
            }

            // 构建响应
            BatchUploadResponse response = new BatchUploadResponse(true, "文件上传成功", uploadedPaths);
            response.getMetadata().put("fileTypeCount", fileTypeCount);
            response.getMetadata().put("totalFiles", uploadedPaths.size());

            // 智能提示信息
            String smartMessage = generateSmartMessage(fileTypeCount, uploadedPaths.size());
            response.setMessage(smartMessage);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.out.println("批量文件上传失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new BatchUploadResponse(false, "上传失败: " + e.getMessage(), new ArrayList<>()));
        }
    }

    /**
     * 智能文件类型检测
     */
    private String detectFileType(String filename, String contentType) {
        if (filename == null) return "other";

        String extension = "";
        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex > 0) {
            extension = filename.substring(lastDotIndex + 1).toLowerCase();
        }

        // 优先通过文件扩展名判断
        switch (extension) {
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
            case "webp":
                return "image";
            case "mp4":
            case "mov":
            case "avi":
            case "wmv":
            case "flv":
            case "webm":
            case "mkv":
                return "video";
            default:
                // 通过MIME类型判断
                if (contentType != null) {
                    if (contentType.startsWith("image/")) return "image";
                    if (contentType.startsWith("video/")) return "video";
                }
                return "other";
        }
    }

    /**
     * 验证文件是否有效
     */
    private boolean isValidFile(MultipartFile file, String fileType) {
        // 检查文件大小
        long maxSize;
        switch (fileType) {
            case "image":
                maxSize = 10 * 1024 * 1024; // 10MB
                break;
            case "video":
                maxSize = 100 * 1024 * 1024; // 100MB
                break;
            default:
                maxSize = 10 * 1024 * 1024; // 默认10MB
        }

        if (file.getSize() > maxSize) {
            System.out.println("文件大小超出限制: " + file.getOriginalFilename() + " (" + file.getSize() + " bytes)");
            return false;
        }

        return true;
    }

    /**
     * 生成智能提示信息
     */
    private String generateSmartMessage(Map<String, Integer> fileTypeCount, int totalFiles) {
        if (totalFiles == 0) {
            return "没有文件上传成功";
        }

        StringBuilder message = new StringBuilder();
        message.append("成功上传 ").append(totalFiles).append(" 个文件");

        int imageCount = fileTypeCount.get("image");
        int videoCount = fileTypeCount.get("video");
        int otherCount = fileTypeCount.get("other");

        if (imageCount > 0 && videoCount > 0) {
            message.append("（图片 ").append(imageCount).append(" 张，视频 ").append(videoCount).append(" 个）");
        } else if (imageCount > 0) {
            message.append("（图片 ").append(imageCount).append(" 张）");
        } else if (videoCount > 0) {
            message.append("（视频 ").append(videoCount).append(" 个）");
        }

        if (otherCount > 0) {
            message.append("，其他文件 ").append(otherCount).append(" 个");
        }

        return message.toString();
    }

    /**
     * 测试单文件上传 - 完全模仿发帖页面的方式
     */
    @PostMapping("/testSingle")
    public Map<String, Object> testSingleUpload(
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam(value = "fileType", required = false) String fileType,
            HttpServletRequest request) {

        Map<String, Object> result = new HashMap<>();

        System.out.println("=== 测试单文件上传 ===");
        System.out.println("Content-Type: " + request.getContentType());
        System.out.println("fileType: " + fileType);
        System.out.println("file: " + (file != null ? file.getOriginalFilename() : "null"));

        if (file != null) {
            System.out.println("文件大小: " + file.getSize());
            System.out.println("文件类型: " + file.getContentType());
        }

        result.put("code", 0);
        result.put("msg", "测试成功");
        result.put("data", "单文件上传正常");

        return result;
    }

    /**
     * 用户文件上传接口 - 完全模仿发帖页面的实现
     */
    @PostMapping("/userUpload")
    public ResponseEntity<String> userFileUpload(
            @RequestParam(value = "fileType", required = false) String fileType,
            @RequestParam(value = "file", required = false) MultipartFile file,
            HttpServletRequest request
    ) {
        System.out.println("=== 用户文件上传请求 ===");
        System.out.println("所有请求参数:");
        request.getParameterMap().forEach((key, values) -> {
            System.out.println("  " + key + ": " + java.util.Arrays.toString(values));
        });

        System.out.println("文件信息:");
        if (file != null) {
            System.out.println("  文件名: " + file.getOriginalFilename());
            System.out.println("  文件大小: " + file.getSize());
            System.out.println("  文件类型: " + file.getContentType());
            System.out.println("  是否为空: " + file.isEmpty());
        } else {
            System.out.println("  文件为null");
        }

        // 从token中获取用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            System.out.println("用户未登录，无法上传文件");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("用户未登录");
        }

        System.out.println("用户ID: " + userId);
        System.out.println("文件类型: " + fileType);

        // 验证文件
        if (file == null || file.isEmpty()) {
            System.out.println("没有有效的文件");
            return ResponseEntity.badRequest().body("没有有效的文件");
        }

        try {
            // 验证文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !originalFilename.contains(".")) {
                return ResponseEntity.badRequest().body("文件名不合法");
            }

            String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!isValidFileType(suffix)) {
                return ResponseEntity.badRequest().body("不支持的文件类型: " + suffix);
            }

            // 创建日期文件夹
            String dateFolder = LocalDate.now().toString();
            String saveDir = uploadDir + dateFolder;
            File dir = new File(saveDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成新文件名
            String raw = originalFilename + "_" + System.currentTimeMillis() + "_" + System.nanoTime() + "_" + userId;
            String newFileName = Md5Util.md5(raw, 16) + "." + suffix;

            // 确保文件名唯一
            File dest = new File(dir, newFileName);
            int attempt = 0;
            while (dest.exists() && attempt < 10) {
                raw = originalFilename + "_" + System.currentTimeMillis() + "_" + System.nanoTime() + "_" + userId + "_" + attempt;
                newFileName = Md5Util.md5(raw, 16) + "." + suffix;
                dest = new File(dir, newFileName);
                attempt++;
            }

            System.out.println("保存文件: " + originalFilename + " -> " + newFileName);
            file.transferTo(dest);

            // 返回相对路径
            String relativePath = dateFolder + "/" + newFileName;
            System.out.println("文件上传成功，路径: " + relativePath);

            return ResponseEntity.ok(relativePath);

        } catch (Exception e) {
            System.out.println("文件上传失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("上传失败: " + e.getMessage());
        }
    }

    /**
     * 多文件上传接口（保持原有功能）
     * 只负责保存文件到磁盘，返回文件路径
     * 不涉及数据库操作，由发帖接口统一处理
     */
    @PostMapping("/upload")
    public Map<String, Object> upload(@RequestParam("files") MultipartFile[] files, HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            System.out.println("=== 文件上传请求开始 ===");
            System.out.println("请求URI: " + request.getRequestURI());
            System.out.println("请求方法: " + request.getMethod());
            System.out.println("Content-Type: " + request.getContentType());

            // 检查是否是multipart请求
            String contentType = request.getContentType();
            if (contentType == null || !contentType.toLowerCase().contains("multipart/form-data")) {
                System.out.println("错误：不是multipart请求");
                result.put("code", 1);
                result.put("msg", "请求格式错误：不是multipart请求，Content-Type: " + contentType);
                return result;
            }

            // JWT认证检查
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                System.out.println("错误：用户未登录");
                result.put("code", 1);
                result.put("msg", "用户未登录");
                return result;
            }

            System.out.println("用户ID: " + userId);
            System.out.println("文件数量: " + (files != null ? files.length : 0));
            if (files == null || files.length == 0) {
                result.put("code", 1);
                result.put("msg", "请选择要上传的文件");
                return result;
            }

            List<String> filePaths = new ArrayList<>();

            // 创建日期文件夹
            String dateFolder = LocalDate.now().toString();
            String saveDir = uploadDir + dateFolder;
            File dir = new File(saveDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 处理每个文件
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    continue;
                }

                // 验证文件类型
                String originalFilename = file.getOriginalFilename();
                if (originalFilename == null || !originalFilename.contains(".")) {
                    result.put("code", 1);
                    result.put("msg", "文件名不合法");
                    return result;
                }

                String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
                if (!isValidFileType(suffix)) {
                    result.put("code", 1);
                    result.put("msg", "不支持的文件类型: " + suffix);
                    return result;
                }

                // 验证文件大小 (单个文件最大50MB)
                if (file.getSize() > 50 * 1024 * 1024) {
                    result.put("code", 1);
                    result.put("msg", "文件大小不能超过50MB");
                    return result;
                }

                // 生成新文件名
                String raw = System.currentTimeMillis() + String.valueOf((int)(Math.random() * 1000));
                String newFileName = Md5Util.md5(raw, 16) + "." + suffix;

                // 保存文件
                File destFile = new File(dir, newFileName);
                file.transferTo(destFile);

                // 添加相对路径到结果列表
                filePaths.add(dateFolder + "/" + newFileName);
            }

            result.put("code", 0);
            result.put("msg", "上传成功");
            result.put("data", filePaths);

        } catch (IOException e) {
            result.put("code", 1);
            result.put("msg", "文件保存失败: " + e.getMessage());
        } catch (Exception e) {
            result.put("code", 1);
            result.put("msg", "上传失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 验证文件类型
     */
    private boolean isValidFileType(String suffix) {
        // 支持的图片格式
        String[] imageTypes = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
        // 支持的视频格式
        String[] videoTypes = {"mp4", "avi", "mov", "wmv", "flv", "mkv"};

        for (String type : imageTypes) {
            if (type.equals(suffix)) return true;
        }
        for (String type : videoTypes) {
            if (type.equals(suffix)) return true;
        }

        return false;
    }


}