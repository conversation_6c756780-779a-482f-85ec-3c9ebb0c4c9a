<template>
  <view class="auth-management-page">
    <!-- 页面头部 -->
    <up-navbar
      title="认证管理"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- Tab切换 -->
    <view class="tab-section" :style="{ marginTop: mainContentPaddingTop }">
      <view class="tab-container">
        <view 
          class="tab-item"
          :class="{ active: currentTab === 'pending' }"
          @click="switchTab('pending')"
        >
          <text class="tab-text">待审核</text>
          <view v-if="currentTab === 'pending'" class="tab-line"></view>
        </view>
        <view 
          class="tab-item"
          :class="{ active: currentTab === 'approved' }"
          @click="switchTab('approved')"
        >
          <text class="tab-text">已审核</text>
          <view v-if="currentTab === 'approved'" class="tab-line"></view>
        </view>
      </view>
    </view>

    <!-- 认证申请列表 -->
    <view class="auth-list">
      <view 
        class="auth-item" 
        v-for="auth in authList" 
        :key="auth.id"
        @click="handleAuthDetail(auth)"
      >
        <!-- 用户信息 -->
        <view class="auth-header">
          <up-avatar src="/static/images/default-avatar.png" shape="circle" :size="40"></up-avatar>
          <view class="user-info">
            <view class="user-name-row">
              <text class="user-name">{{ auth.realName }}</text>
              <view v-if="auth.status === 1" class="status-badge approved">已通过</view>
              <view v-else-if="auth.status === 2" class="status-badge rejected">已拒绝</view>
              <view v-else class="status-badge pending">待审核</view>
            </view>
            <text class="auth-time">{{ formatTime(auth.createdTime) }}</text>
          </view>
        </view>

        <!-- 认证信息 -->
        <view class="auth-info">
          <view class="info-row">
            <text class="info-label">姓名:</text>
            <text class="info-value">{{ auth.realName }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">手机号:</text>
            <text class="info-value">{{ auth.phone || '未填写' }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">身份:</text>
            <text class="info-value">{{ getIdentityTypeName(auth.identityType) }}</text>
          </view>
          <view class="info-row" v-if="auth.houseNumber">
            <text class="info-label">房屋号码:</text>
            <text class="info-value">{{ auth.houseNumber }}</text>
          </view>
        </view>

        <!-- 证明材料 -->
        <view class="auth-documents" v-if="auth.documents && auth.documents.length > 0">
          <image 
            v-for="(doc, index) in auth.documents.slice(0, 3)" 
            :key="index"
            :src="doc"
            class="document-image"
            mode="aspectFill"
            @click.stop="previewImage(auth.documents, index)"
          ></image>
          <view v-if="auth.documents.length > 3" class="more-docs">
            +{{ auth.documents.length - 3 }}
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="auth-actions" v-if="currentTab === 'pending'">
          <up-button 
            text="拒绝" 
            type="error" 
            size="mini"
            plain
            @click.stop="handleReject(auth)"
          ></up-button>
          <up-button 
            text="通过" 
            type="success" 
            size="mini"
            @click.stop="handleApprove(auth)"
          ></up-button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore">
      <up-loadmore 
        :status="loadStatus"
        @loadmore="loadMoreAuth"
      ></up-loadmore>
    </view>

    <!-- 认证详情弹窗 -->
    <up-popup 
      v-model:show="showAuthDetailModal" 
      mode="center" 
      :round="10"
      :closeable="true"
      @close="closeAuthDetailModal"
    >
      <view class="auth-detail-modal" v-if="selectedAuth">
        <view class="modal-header">
          <text class="modal-title">认证详情</text>
          <up-icon name="close" @click="closeAuthDetailModal" size="20" color="#999"></up-icon>
        </view>
        <view class="modal-content">
          <view class="detail-item">
            <text class="detail-label">申请人:</text>
            <text class="detail-value">{{ selectedAuth.user.nickname }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">真实姓名:</text>
            <text class="detail-value">{{ selectedAuth.name }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">手机号:</text>
            <text class="detail-value">{{ selectedAuth.phone || '未填写' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">身份:</text>
            <text class="detail-value">{{ selectedAuth.identity === 'owner' ? '业主' : '家属' }}</text>
          </view>
          <view class="detail-item" v-if="selectedAuth.houseNumber">
            <text class="detail-label">房屋号码:</text>
            <text class="detail-value">{{ selectedAuth.houseNumber }}</text>
          </view>
          <view class="detail-item" v-if="selectedAuth.remark">
            <text class="detail-label">备注说明:</text>
            <text class="detail-value">{{ selectedAuth.remark }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">申请时间:</text>
            <text class="detail-value">{{ formatDateTime(selectedAuth.createdTime) }}</text>
          </view>
          <view class="detail-item" v-if="selectedAuth.status !== 'pending'">
            <text class="detail-label">审核时间:</text>
            <text class="detail-value">{{ formatDateTime(selectedAuth.reviewTime) }}</text>
          </view>
          <view class="detail-item" v-if="selectedAuth.rejectReason">
            <text class="detail-label">拒绝原因:</text>
            <text class="detail-value">{{ selectedAuth.rejectReason }}</text>
          </view>
        </view>
        <view class="modal-footer" v-if="selectedAuth.status === 'pending'">
          <up-button 
            text="拒绝" 
            type="error" 
            size="small"
            plain
            @click="handleReject(selectedAuth)"
          ></up-button>
          <up-button 
            text="通过" 
            type="success" 
            size="small"
            @click="handleApprove(selectedAuth)"
          ></up-button>
        </view>
      </view>
    </up-popup>

    <!-- 拒绝原因输入弹窗 -->
    <up-popup 
      v-model:show="showRejectModal" 
      mode="center" 
      :round="10"
      :closeable="true"
      @close="closeRejectModal"
    >
      <view class="reject-modal">
        <view class="modal-header">
          <text class="modal-title">拒绝原因</text>
          <up-icon name="close" @click="closeRejectModal" size="20" color="#999"></up-icon>
        </view>
        <view class="modal-content">
          <up-textarea 
            v-model="rejectReason"
            placeholder="请输入拒绝原因..."
            :maxlength="200"
            count
            :autoHeight="true"
            :height="100"
          ></up-textarea>
        </view>
        <view class="modal-footer">
          <up-button 
            text="确认拒绝" 
            type="error" 
            :disabled="!rejectReason.trim()"
            @click="confirmReject"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useSafeArea } from '@/utils/safeArea';
import { getAuthApplicationsAPI } from '@/services/verification';
import type { AuthApplication, LoadStatus, TabType } from '@/types/admin';

const { mainContentPaddingTop } = useSafeArea();

// 当前选中的tab
const currentTab = ref<TabType>('pending');

// 认证申请列表
const authList = ref<AuthApplication[]>([]);

// 分页相关
const hasMore = ref<boolean>(true);
const loadStatus = ref<LoadStatus>('loadmore');

// 弹窗相关
const showAuthDetailModal = ref<boolean>(false);
const showRejectModal = ref<boolean>(false);
const selectedAuth = ref<AuthApplication | null>(null);
const rejectReason = ref<string>('');

// 获取身份类型名称
const getIdentityTypeName = (identityType: string) => {
  const typeMap: Record<string, string> = {
    'owner': '业主',
    'tenant': '租户',
    'property': '物业',
    'committee': '业委会'
  };
  return typeMap[identityType] || identityType;
};

// 初始化数据
const initData = async () => {
  loadAuthList();
};

// 加载认证申请列表
const loadAuthList = async () => {
  try {
    console.log('=== 加载认证申请列表 ===');
    console.log('当前tab:', currentTab.value);

    // 将tab转换为状态值
    let status: number | undefined;
    switch (currentTab.value) {
      case 'pending':
        status = 0; // 待审核
        break;
      case 'approved':
        status = 1; // 已通过
        break;
      case 'rejected':
        status = 2; // 已拒绝
        break;
      default:
        status = undefined; // 全部
    }

    const res = await getAuthApplicationsAPI({
      status: status,
      page: 1,
      pageSize: 50
    });

    if (res.success && res.data) {
      authList.value = res.data;
      console.log('获取认证申请列表成功:', res.data);
    } else {
      console.log('获取认证申请列表失败:', res.message);
      authList.value = [];
    }
  } catch (error) {
    console.error('加载认证申请列表失败:', error);
    authList.value = [];
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  }
};

onMounted(() => {
  initData();
});

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 切换tab
const switchTab = (tab: TabType) => {
  currentTab.value = tab;
  loadAuthList();
};

// 加载更多认证申请
const loadMoreAuth = () => {
  loadStatus.value = 'loading';
  // 模拟加载更多
  setTimeout(() => {
    loadStatus.value = 'loadmore';
    hasMore.value = false; // 模拟没有更多数据
  }, 1000);
};

// 查看认证详情
const handleAuthDetail = (auth: AuthApplication) => {
  selectedAuth.value = auth;
  showAuthDetailModal.value = true;
};

// 关闭认证详情弹窗
const closeAuthDetailModal = () => {
  showAuthDetailModal.value = false;
  selectedAuth.value = null;
};

// 通过认证
const handleApprove = (auth: AuthApplication) => {
  uni.showModal({
    title: '确认通过',
    content: `确定要通过"${auth.name}"的认证申请吗？`,
    success: (res) => {
      if (res.confirm) {
        // 这里应该调用实际的API
        auth.status = 'approved';
        auth.reviewTime = new Date().toLocaleString();
        uni.showToast({
          title: '认证通过',
          icon: 'success'
        });
        closeAuthDetailModal();
        // 重新加载列表
        loadAuthList();
      }
    }
  });
};

// 拒绝认证
const handleReject = (auth: AuthApplication) => {
  selectedAuth.value = auth;
  showRejectModal.value = true;
  closeAuthDetailModal();
};

// 关闭拒绝弹窗
const closeRejectModal = () => {
  showRejectModal.value = false;
  rejectReason.value = '';
};

// 确认拒绝
const confirmReject = () => {
  if (!rejectReason.value.trim()) {
    uni.showToast({
      title: '请输入拒绝原因',
      icon: 'none'
    });
    return;
  }

  if (!selectedAuth.value) {
    return;
  }

  // 这里应该调用实际的API
  selectedAuth.value.status = 'rejected';
  selectedAuth.value.rejectReason = rejectReason.value;
  selectedAuth.value.reviewTime = new Date().toLocaleString();

  uni.showToast({
    title: '已拒绝认证',
    icon: 'success'
  });

  closeRejectModal();
  // 重新加载列表
  loadAuthList();
};

// 预览图片
const previewImage = (images: string[], current: number) => {
  uni.previewImage({
    urls: images,
    current: current
  });
};

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  return timeStr;
};

// 格式化日期时间
const formatDateTime = (dateStr?: string) => {
  if (!dateStr) return '未知';
  return dateStr;
};
</script>

<style lang="scss">
.auth-management-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  // Tab切换区域
  .tab-section {
    background: #fff;
    padding: 0 20px;

    .tab-container {
      display: flex;
      position: relative;

      .tab-item {
        flex: 1;
        padding: 15px 0;
        text-align: center;
        position: relative;
        cursor: pointer;

        .tab-text {
          font-size: 16px;
          color: #666;

          &.active {
            color: #1890ff;
            font-weight: bold;
          }
        }

        &.active .tab-text {
          color: #1890ff;
          font-weight: bold;
        }

        .tab-line {
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 30px;
          height: 3px;
          background: #1890ff;
          border-radius: 2px;
        }
      }
    }
  }

  // 认证申请列表
  .auth-list {
    padding: 10px 20px;

    .auth-item {
      background: #fff;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }

      .auth-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .user-info {
          flex: 1;
          margin-left: 12px;

          .user-name-row {
            display: flex;
            align-items: center;
            margin-bottom: 4px;

            .user-name {
              font-size: 16px;
              font-weight: bold;
              color: #333;
              margin-right: 10px;
            }

            .status-badge {
              padding: 2px 8px;
              border-radius: 10px;
              font-size: 12px;

              &.pending {
                background: #fff7e6;
                color: #fa8c16;
              }

              &.approved {
                background: #f6ffed;
                color: #52c41a;
              }

              &.rejected {
                background: #fff1f0;
                color: #ff4d4f;
              }
            }
          }

          .auth-time {
            font-size: 12px;
            color: #999;
          }
        }
      }

      .auth-info {
        margin-bottom: 12px;

        .info-row {
          display: flex;
          margin-bottom: 6px;

          .info-label {
            font-size: 14px;
            color: #666;
            width: 80px;
            flex-shrink: 0;
          }

          .info-value {
            font-size: 14px;
            color: #333;
            flex: 1;
          }
        }
      }

      .auth-documents {
        display: flex;
        gap: 8px;
        margin-bottom: 12px;
        position: relative;

        .document-image {
          width: 60px;
          height: 60px;
          border-radius: 4px;
        }

        .more-docs {
          position: absolute;
          right: 0;
          top: 0;
          width: 60px;
          height: 60px;
          background: rgba(0, 0, 0, 0.5);
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          font-size: 12px;
        }
      }

      .auth-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;

        :deep(.u-button) {
          height: 32px;
          padding: 0 16px;
        }
      }
    }
  }

  // 加载更多
  .load-more {
    padding: 20px;
  }

  // 认证详情弹窗
  .auth-detail-modal {
    width: 350px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;

      .modal-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }

    .modal-content {
      padding: 20px;
      max-height: 400px;
      overflow-y: auto;

      .detail-item {
        display: flex;
        margin-bottom: 12px;

        .detail-label {
          font-size: 14px;
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .detail-value {
          font-size: 14px;
          color: #333;
          flex: 1;
          line-height: 1.4;
        }
      }
    }

    .modal-footer {
      display: flex;
      gap: 10px;
      padding: 0 20px 20px;

      :deep(.u-button) {
        flex: 1;
        height: 36px;
      }
    }
  }

  // 拒绝原因弹窗
  .reject-modal {
    width: 300px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;

      .modal-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }

    .modal-content {
      padding: 20px;

      :deep(.u-textarea) {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        padding: 10px;
      }
    }

    .modal-footer {
      padding: 0 20px 20px;

      :deep(.u-button) {
        width: 100%;
        height: 44px;
        border-radius: 22px;
      }
    }
  }
}
</style>
