/*
 * @Author: Rock
 * @Date: 2025-05-01 00:45:44
 * @LastEditors: Rock
 * @LastEditTime: 2025-05-01 00:45:57
 * @Description: 
 */
package com.haolinkyou.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haolinkyou.api.vo.PostCommentsVo;
import com.haolinkyou.api.vo.PostDetailVo;
import com.haolinkyou.api.vo.PostsListVo;
import com.haolinkyou.entity.Posts;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PostsMapper extends BaseMapper<Posts> {
    IPage<PostsListVo> selectByPage(IPage<PostsListVo> page);

    IPage<PostsListVo> listAllPosts(IPage<PostsListVo> page, @Param("ew") Wrapper<Posts> wrapper);

    IPage<PostsListVo> listAllPostsWithUserStatus(IPage<PostsListVo> page, @Param("ew") Wrapper<Posts> wrapper, @Param("userId") Long userId);

    PostDetailVo selectPostDetailById(Long id);

}