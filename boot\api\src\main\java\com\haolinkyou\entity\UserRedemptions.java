package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 用户兑换记录实体类
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@TableName("user_redemptions")
public class UserRedemptions extends BaseEntity {

    private Long userId;

    private Long productId;

    private String productName;

    private Integer pointsUsed;

    private Integer quantity;

    private Integer status;

    private String shippingAddress;

    private String contactPhone;

    private String remark;

    // Constructors
    public UserRedemptions() {}

    // Getters and Setters
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public Long getProductId() { return productId; }
    public void setProductId(Long productId) { this.productId = productId; }

    public String getProductName() { return productName; }
    public void setProductName(String productName) { this.productName = productName; }

    public Integer getPointsUsed() { return pointsUsed; }
    public void setPointsUsed(Integer pointsUsed) { this.pointsUsed = pointsUsed; }

    public Integer getQuantity() { return quantity; }
    public void setQuantity(Integer quantity) { this.quantity = quantity; }

    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }

    public String getShippingAddress() { return shippingAddress; }
    public void setShippingAddress(String shippingAddress) { this.shippingAddress = shippingAddress; }

    public String getContactPhone() { return contactPhone; }
    public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }

    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }

    @Override
    public String toString() {
        return "UserRedemptions{" +
                "id=" + getId() +
                ", userId=" + userId +
                ", productId=" + productId +
                ", productName='" + productName + '\'' +
                ", pointsUsed=" + pointsUsed +
                ", quantity=" + quantity +
                ", status=" + status +
                ", shippingAddress='" + shippingAddress + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", remark='" + remark + '\'' +
                ", createdTime=" + getCreatedTime() +
                ", updatedTime=" + getUpdatedTime() +
                ", delFlag=" + getDelFlag() +
                '}';
    }
}