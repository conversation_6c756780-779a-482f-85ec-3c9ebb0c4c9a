package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@TableName("user_roles")
@EqualsAndHashCode(callSuper = true)
public class UserRoles extends BaseEntity {

    private String roleCode;

    private String roleName;

    private String roleDescription;

    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> permissions;

    private Integer sortOrder;

    private Integer status;
}
