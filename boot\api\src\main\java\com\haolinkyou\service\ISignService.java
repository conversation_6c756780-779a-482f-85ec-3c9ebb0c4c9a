package com.haolinkyou.service;

import com.haolinkyou.entity.UserSignRecords;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.time.LocalDate;

/**
 * 签到服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
public interface ISignService extends IService<UserSignRecords> {

    /**
     * 每日签到
     * 
     * @param userId 用户ID
     * @return 签到结果
     */
    SignResult dailySign(Long userId);

    /**
     * 检查今日是否已签到
     * 
     * @param userId 用户ID
     * @return 是否已签到
     */
    boolean hasSignedToday(Long userId);

    /**
     * 获取签到状态
     * 
     * @param userId 用户ID
     * @return 签到状态
     */
    SignStatus getSignStatus(Long userId);

    /**
     * 计算连续签到天数
     * 
     * @param userId 用户ID
     * @return 连续签到天数
     */
    int calculateContinuousDays(Long userId);

    /**
     * 获取用户签到记录（分页）
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 签到记录分页
     */
    Page<UserSignRecords> getUserSignRecords(Long userId, Integer page, Integer size);

    /**
     * 计算签到奖励积分
     * 
     * @param continuousDays 连续签到天数
     * @return 奖励积分
     */
    int calculateSignBonus(int continuousDays);

    /**
     * 签到结果类
     */
    class SignResult {
        private boolean success;
        private String message;
        private Integer pointsEarned;
        private Integer continuousDays;

        public SignResult() {}

        public SignResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public SignResult(boolean success, String message, Integer pointsEarned, Integer continuousDays) {
            this.success = success;
            this.message = message;
            this.pointsEarned = pointsEarned;
            this.continuousDays = continuousDays;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public Integer getPointsEarned() { return pointsEarned; }
        public void setPointsEarned(Integer pointsEarned) { this.pointsEarned = pointsEarned; }

        public Integer getContinuousDays() { return continuousDays; }
        public void setContinuousDays(Integer continuousDays) { this.continuousDays = continuousDays; }
    }

    /**
     * 签到状态类
     */
    class SignStatus {
        private boolean hasSignedToday;
        private Integer continuousDays;
        private LocalDate lastSignDate;
        private Integer todayPoints;
        private Integer nextDayBonus;

        // Getters and Setters
        public boolean isHasSignedToday() { return hasSignedToday; }
        public void setHasSignedToday(boolean hasSignedToday) { this.hasSignedToday = hasSignedToday; }

        public Integer getContinuousDays() { return continuousDays; }
        public void setContinuousDays(Integer continuousDays) { this.continuousDays = continuousDays; }

        public LocalDate getLastSignDate() { return lastSignDate; }
        public void setLastSignDate(LocalDate lastSignDate) { this.lastSignDate = lastSignDate; }

        public Integer getTodayPoints() { return todayPoints; }
        public void setTodayPoints(Integer todayPoints) { this.todayPoints = todayPoints; }

        public Integer getNextDayBonus() { return nextDayBonus; }
        public void setNextDayBonus(Integer nextDayBonus) { this.nextDayBonus = nextDayBonus; }
    }
}