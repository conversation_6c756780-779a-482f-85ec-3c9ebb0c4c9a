/**
 * 全局表单验证工具
 */

// 手机号验证
export const validatePhone = (phone: string): { valid: boolean; message?: string } => {
  if (!phone) {
    return { valid: false, message: '请输入手机号' }
  }
  
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(phone)) {
    return { valid: false, message: '请输入正确的手机号格式' }
  }
  
  return { valid: true }
}

// 邮箱验证
export const validateEmail = (email: string): { valid: boolean; message?: string } => {
  if (!email) {
    return { valid: false, message: '请输入邮箱地址' }
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    return { valid: false, message: '请输入正确的邮箱格式' }
  }
  
  return { valid: true }
}

// 必填验证
export const validateRequired = (value: any, fieldName: string): { valid: boolean; message?: string } => {
  if (!value || (typeof value === 'string' && !value.trim())) {
    return { valid: false, message: `${fieldName}是必填项` }
  }
  
  return { valid: true }
}

// 长度验证
export const validateLength = (
  value: string, 
  min: number = 0, 
  max: number = 1000, 
  fieldName: string = '内容'
): { valid: boolean; message?: string } => {
  if (!value) {
    return { valid: true } // 长度验证不检查必填，由 validateRequired 处理
  }
  
  if (value.length < min) {
    return { valid: false, message: `${fieldName}长度不能少于${min}个字符` }
  }
  
  if (value.length > max) {
    return { valid: false, message: `${fieldName}长度不能超过${max}个字符` }
  }
  
  return { valid: true }
}

// 数字验证
export const validateNumber = (
  value: any, 
  min?: number, 
  max?: number, 
  fieldName: string = '数值'
): { valid: boolean; message?: string } => {
  if (!value && value !== 0) {
    return { valid: true } // 数字验证不检查必填
  }
  
  const num = Number(value)
  if (isNaN(num)) {
    return { valid: false, message: `${fieldName}必须是数字` }
  }
  
  if (min !== undefined && num < min) {
    return { valid: false, message: `${fieldName}不能小于${min}` }
  }
  
  if (max !== undefined && num > max) {
    return { valid: false, message: `${fieldName}不能大于${max}` }
  }
  
  return { valid: true }
}

// 日期验证
export const validateDate = (
  value: string, 
  fieldName: string = '日期'
): { valid: boolean; message?: string } => {
  if (!value) {
    return { valid: true } // 日期验证不检查必填
  }
  
  const date = new Date(value)
  if (isNaN(date.getTime())) {
    return { valid: false, message: `${fieldName}格式不正确` }
  }
  
  return { valid: true }
}

// 组合验证器
export const validateField = (
  value: any,
  rules: {
    required?: boolean
    type?: 'phone' | 'email' | 'number' | 'date'
    min?: number
    max?: number
    minLength?: number
    maxLength?: number
    fieldName: string
  }
): { valid: boolean; message?: string } => {
  const { required, type, min, max, minLength, maxLength, fieldName } = rules
  
  // 必填验证
  if (required) {
    const requiredResult = validateRequired(value, fieldName)
    if (!requiredResult.valid) {
      return requiredResult
    }
  }
  
  // 如果值为空且不是必填，则通过验证
  if (!value && !required) {
    return { valid: true }
  }
  
  // 类型验证
  switch (type) {
    case 'phone':
      return validatePhone(value)
    case 'email':
      return validateEmail(value)
    case 'number':
      return validateNumber(value, min, max, fieldName)
    case 'date':
      return validateDate(value, fieldName)
  }
  
  // 长度验证
  if (typeof value === 'string' && (minLength !== undefined || maxLength !== undefined)) {
    return validateLength(value, minLength || 0, maxLength || 1000, fieldName)
  }
  
  return { valid: true }
}

// 批量验证
export const validateForm = (
  formData: Record<string, any>,
  rules: Record<string, any>
): { valid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {}
  
  for (const fieldName in rules) {
    const fieldRules = rules[fieldName]
    const fieldValue = formData[fieldName]
    
    const result = validateField(fieldValue, {
      ...fieldRules,
      fieldName: fieldRules.label || fieldName
    })
    
    if (!result.valid && result.message) {
      errors[fieldName] = result.message
    }
  }
  
  return {
    valid: Object.keys(errors).length === 0,
    errors
  }
}
