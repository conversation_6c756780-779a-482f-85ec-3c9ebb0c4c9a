<template>
  <view class="survey-results-page">
    <!-- 页面头部 -->
    <up-navbar
      title="调查结果"
      :border="false"
      :background="{ backgroundColor: '#fff' }"
      @leftClick="handleBack"
      :safeAreaInsetTop="true"
    ></up-navbar>

    <!-- 内容区域 -->
    <view class="content-area" :style="{ marginTop: mainContentPaddingTop }">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <up-loading-icon mode="spinner" size="40"></up-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 调查结果内容 -->
      <view v-else-if="resultsData" class="results-container">
        <!-- 调查基本信息 -->
        <view class="survey-header">
          <view class="survey-title">{{ resultsData.title }}</view>
          <view v-if="resultsData.description" class="survey-description">
            {{ resultsData.description }}
          </view>
          <view class="survey-stats">
            <view class="stat-item">
              <text class="stat-number">{{ resultsData.totalParticipants }}</text>
              <text class="stat-label">总参与人数</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ resultsData.questions?.length || 0 }}</text>
              <text class="stat-label">问题数量</text>
            </view>
          </view>
        </view>

        <!-- 问题结果列表 -->
        <view class="questions-results">
          <view
            v-for="(question, index) in resultsData.questions"
            :key="question.id"
            class="question-result-item"
          >
            <view class="question-header">
              <text class="question-number">{{ index + 1 }}.</text>
              <text class="question-text">{{ question.questionText }}</text>
            </view>

            <!-- 选择题结果 -->
            <view v-if="question.questionType !== 'text'" class="choice-results">
              <view
                v-for="(option, optionIndex) in question.options"
                :key="option.id"
                class="option-result"
              >
                <view class="option-info">
                  <text class="option-text">{{ option.text }}</text>
                  <text class="option-count">{{ getOptionCount(question.id, optionIndex) }}票</text>
                </view>
                <view class="option-bar">
                  <view 
                    class="option-fill" 
                    :style="{ width: getOptionPercentage(question.id, optionIndex) + '%' }"
                  ></view>
                </view>
                <text class="option-percentage">{{ getOptionPercentage(question.id, optionIndex) }}%</text>
              </view>
            </view>

            <!-- 文本题结果 -->
            <view v-else class="text-results">
              <view v-if="getTextAnswers(question.id).length > 0" class="text-answers">
                <view
                  v-for="(answer, answerIndex) in getTextAnswers(question.id)"
                  :key="answerIndex"
                  class="text-answer-item"
                >
                  <view class="answer-header">
                    <text class="answer-user">{{ answer.username }}</text>
                    <text class="answer-time">{{ formatTime(answer.createdTime) }}</text>
                  </view>
                  <text class="answer-content">{{ answer.content }}</text>
                </view>
              </view>
              <view v-else class="no-answers">
                <text class="no-answers-text">暂无回答</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 错误状态 -->
      <view v-else class="error-container">
        <up-icon name="warning-fill" size="60" color="#ff6b6b"></up-icon>
        <text class="error-text">{{ errorMessage || '加载失败，请重试' }}</text>
        <up-button
          type="primary"
          size="small"
          @click="loadResults"
          customStyle="margin-top: 20px;"
        >
          重新加载
        </up-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSafeArea } from '@/utils/safeArea'
import { get } from '@/utils/http'
import { useMemberStore } from '@/stores'

// 安全区域
const { mainContentPaddingTop } = useSafeArea()

// 用户状态
const memberStore = useMemberStore()

// 页面参数
const postId = ref<number>(0)

// 页面状态
const loading = ref(true)
const errorMessage = ref('')

// 结果数据
const resultsData = ref<any>(null)

// 获取页面参数
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.id) {
    postId.value = parseInt(options.id)
    loadResults()
  } else {
    errorMessage.value = '参数错误'
    loading.value = false
  }
})

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 加载调查结果
const loadResults = async () => {
  loading.value = true
  errorMessage.value = ''

  try {
    const response = await get(`/survey/results?postId=${postId.value}`)
    
    if (response.success && response.data) {
      resultsData.value = response.data
      console.log('调查结果加载成功:', resultsData.value)
    } else {
      throw new Error(response.message || '获取调查结果失败')
    }
  } catch (error) {
    console.error('加载调查结果失败:', error)
    errorMessage.value = error.message || '网络错误，请重试'
  } finally {
    loading.value = false
  }
}

// 获取选项投票数
const getOptionCount = (questionId: number, optionIndex: number) => {
  if (!resultsData.value.questionResults) return 0
  const questionResult = resultsData.value.questionResults[questionId.toString()]
  if (!questionResult) return 0
  return questionResult[optionIndex.toString()] || 0
}

// 获取选项百分比
const getOptionPercentage = (questionId: number, optionIndex: number) => {
  const count = getOptionCount(questionId, optionIndex)
  if (resultsData.value.totalParticipants === 0) return 0
  return Math.round((count / resultsData.value.totalParticipants) * 100)
}

// 获取文本答案
const getTextAnswers = (questionId: number) => {
  if (!resultsData.value.textAnswers) return []
  return resultsData.value.textAnswers[questionId.toString()] || []
}

// 格式化时间
const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped lang="scss">
.survey-results-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.content-area {
  padding: 0 20px 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #666;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-text {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}

.results-container {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.survey-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 24px;
}

.survey-title {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 12px;
}

.survey-description {
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.6;
  margin-bottom: 20px;
}

.survey-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.questions-results {
  padding: 0;
}

.question-result-item {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.question-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.question-number {
  font-size: 16px;
  font-weight: 600;
  color: #28a745;
  margin-right: 8px;
  flex-shrink: 0;
}

.question-text {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

.choice-results {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-result {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-text {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.option-count {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.option-bar {
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.option-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
  transition: width 0.5s ease;
}

.option-percentage {
  font-size: 12px;
  color: #666;
  text-align: right;
}

.text-results {
  margin-left: 24px;
}

.text-answers {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.text-answer-item {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.answer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.answer-user {
  font-size: 12px;
  color: #28a745;
  font-weight: 500;
}

.answer-time {
  font-size: 12px;
  color: #999;
}

.answer-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.no-answers {
  text-align: center;
  padding: 40px 20px;
}

.no-answers-text {
  font-size: 14px;
  color: #999;
}
</style>