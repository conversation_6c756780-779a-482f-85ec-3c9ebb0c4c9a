package com.haolinkyou.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haolinkyou.entity.Users;
import com.haolinkyou.mapper.UserMapper;
import com.haolinkyou.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, Users> implements IUserService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public Map<String, Object> getUserStats(Long userId) {
        Map<String, Object> stats = new HashMap<>();

        if (userId == null) {
            stats.put("postCount", 0);
            stats.put("favoriteCount", 0);
            stats.put("pointCount", 0);
            return stats;
        }

        // 获取用户发布的动态数量
        int postCount = userMapper.getUserPostCount(userId);

        // 获取用户收藏的帖子数量
        int favoriteCount = userMapper.getUserCollectCount(userId);

        // 获取用户积分（从用户表中获取）
        Users user = userMapper.selectById(userId);
        int pointCount = user != null && user.getPoints() != null ? user.getPoints() : 0;

        stats.put("postCount", postCount);
        stats.put("favoriteCount", favoriteCount);
        stats.put("pointCount", pointCount);

        return stats;
    }
}
