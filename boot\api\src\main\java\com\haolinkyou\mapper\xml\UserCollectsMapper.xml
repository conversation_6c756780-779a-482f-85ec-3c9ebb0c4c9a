<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haolinkyou.mapper.UserCollectsMapper">

    <!-- 检查用户是否已收藏该帖子 -->
    <select id="checkUserCollected" resultType="int">
        SELECT COUNT(*)
        FROM user_collects
        WHERE post_id = #{postId}
          AND user_id = #{userId}
          AND del_flag = 0
    </select>

    <!-- 获取帖子的收藏数 -->
    <select id="getPostCollectCount" resultType="int">
        SELECT COUNT(*)
        FROM user_collects
        WHERE post_id = #{postId}
          AND del_flag = 0
    </select>

    <!-- 添加收藏记录 -->
    <insert id="addCollect">
        INSERT INTO user_collects (post_id, user_id, created_time, del_flag)
        VALUES (#{postId}, #{userId}, NOW(), 0)
        ON DUPLICATE KEY UPDATE del_flag = 0
    </insert>

    <!-- 取消收藏（逻辑删除） -->
    <update id="removeCollect">
        UPDATE user_collects
        SET del_flag = 1
        WHERE post_id = #{postId}
          AND user_id = #{userId}
          AND del_flag = 0
    </update>

    <!-- 获取用户收藏列表 -->
    <select id="getUserCollections" resultType="com.haolinkyou.vo.UserCollectVo">
        SELECT
            uc.id,
            uc.post_id as postId,
            p.title,
            p.content as postContent,
            p.file_list as postImages,
            c.category_name as categoryName,
            u.avatar as userAvatar,
            u.nickname,
            p.like_count as likeCount,
            p.comment_count as commentCount,
            p.collect_count as collectCount,
            p.created_time as createdTime,
            uc.created_time as collectTime,
            p.status
        FROM user_collects uc
        LEFT JOIN posts p ON uc.post_id = p.id AND p.del_flag = 0
        LEFT JOIN users u ON p.user_id = u.id AND u.del_flag = 0
        LEFT JOIN categories c ON p.category_id = c.id AND c.del_flag = 0
        WHERE uc.user_id = #{userId}
          AND uc.del_flag = 0
          AND p.id IS NOT NULL
        <if test="categoryId != null and categoryId != ''">
          AND p.category_id = #{categoryId}
        </if>
        <choose>
            <when test="sortBy == 'create'">
                ORDER BY p.created_time DESC
            </when>
            <when test="sortBy == 'hot'">
                ORDER BY (p.like_count + p.comment_count + p.collect_count) DESC, uc.created_time DESC
            </when>
            <otherwise>
                ORDER BY uc.created_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 获取用户今日收藏数 -->
    <select id="getUserTodayCollectCount" resultType="int">
        SELECT COUNT(*)
        FROM user_collects
        WHERE user_id = #{userId}
          AND del_flag = 0
          AND DATE(created_time) = CURDATE()
    </select>

    <!-- 获取用户本月收藏数 -->
    <select id="getUserMonthCollectCount" resultType="int">
        SELECT COUNT(*)
        FROM user_collects
        WHERE user_id = #{userId}
          AND del_flag = 0
          AND YEAR(created_time) = YEAR(CURDATE())
          AND MONTH(created_time) = MONTH(CURDATE())
    </select>

</mapper>
