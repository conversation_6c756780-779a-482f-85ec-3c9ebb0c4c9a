package com.haolinkyou.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("vote_records")
public class VoteRecords {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long voteId;
    
    private Long userId;
    
    private String selectedOptions; // JSON格式存储选择的选项
    
    private Boolean isAnonymous;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
    
    @TableLogic
    private Integer delFlag;
}