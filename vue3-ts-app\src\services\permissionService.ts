import { http } from '@/utils/http'
import type { Result } from '@/types/ApiResponse'
import { useMemberStore } from '@/stores'
import { PERMISSIONS } from '@/utils/rolePermissions'

/**
 * 权限校验服务
 * 提供基于后端接口的权限验证功能，确保权限控制的安全性
 */

// 权限校验缓存，避免频繁请求
const permissionCache = new Map<string, { result: boolean; timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

/**
 * 清除权限缓存
 */
export const clearPermissionCache = () => {
  permissionCache.clear()
}

/**
 * 检查用户是否有指定权限（通过后端接口验证）
 * @param permission 权限代码
 * @returns Promise<boolean> 是否有权限
 */
export const checkPermission = async (permission: string): Promise<boolean> => {
  try {
    const memberStore = useMemberStore()
    const userRole = memberStore.profile?.userRole
    
    // 如果用户未登录，直接返回false
    if (!userRole) {
      return false
    }
    
    // 检查缓存
    const cacheKey = `${userRole}_${permission}`
    const cached = permissionCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.result
    }
    
    // 调用后端接口验证权限
    const response = await http<Result<boolean>>({
      method: 'GET',
      url: `/roles/check-permission?userRole=${encodeURIComponent(userRole)}&permission=${encodeURIComponent(permission)}`
    })
    
    const hasPermission = response.success && response.data === true
    
    // 缓存结果
    permissionCache.set(cacheKey, {
      result: hasPermission,
      timestamp: Date.now()
    })
    
    return hasPermission
  } catch (error) {
    console.error('权限检查失败:', error)
    return false
  }
}

/**
 * 检查用户是否有管理员权限
 * @returns Promise<boolean> 是否为管理员
 */
export const checkAdminPermission = async (): Promise<boolean> => {
  return await checkPermission(PERMISSIONS.ADMIN_ACCESS)
}

/**
 * 检查用户是否可以访问管理后台
 * @returns Promise<boolean> 是否可以访问管理后台
 */
export const checkBackendAccess = async (): Promise<boolean> => {
  try {
    const memberStore = useMemberStore()
    const userRole = memberStore.profile?.userRole
    
    if (!userRole) {
      return false
    }
    
    // 检查缓存
    const cacheKey = `backend_access_${userRole}`
    const cached = permissionCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.result
    }
    
    // 调用后端接口验证管理后台访问权限
    const response = await http<Result<boolean>>({
      method: 'GET',
      url: '/admin/check-access'
    })
    
    const hasAccess = response.success && response.data === true
    
    // 缓存结果
    permissionCache.set(cacheKey, {
      result: hasAccess,
      timestamp: Date.now()
    })
    
    return hasAccess
  } catch (error) {
    console.error('管理后台权限检查失败:', error)
    return false
  }
}

/**
 * 批量检查多个权限
 * @param permissions 权限代码数组
 * @returns Promise<Record<string, boolean>> 权限检查结果映射
 */
export const checkMultiplePermissions = async (permissions: string[]): Promise<Record<string, boolean>> => {
  const results: Record<string, boolean> = {}
  
  // 并发检查所有权限
  const promises = permissions.map(async (permission) => {
    const hasPermission = await checkPermission(permission)
    results[permission] = hasPermission
  })
  
  await Promise.all(promises)
  return results
}

/**
 * 获取用户的所有权限列表
 * @returns Promise<string[]> 用户权限列表
 */
export const getUserPermissions = async (): Promise<string[]> => {
  try {
    const memberStore = useMemberStore()
    const userRole = memberStore.profile?.userRole
    
    if (!userRole) {
      return []
    }
    
    // 检查缓存
    const cacheKey = `user_permissions_${userRole}`
    const cached = permissionCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.result as any
    }
    
    const response = await http<Result<string[]>>({
      method: 'GET',
      url: `/roles/permissions/${userRole}`
    })
    
    const permissions = response.success ? response.data || [] : []
    
    // 缓存结果
    permissionCache.set(cacheKey, {
      result: permissions as any,
      timestamp: Date.now()
    })
    
    return permissions
  } catch (error) {
    console.error('获取用户权限失败:', error)
    return []
  }
}

/**
 * 权限校验装饰器工厂
 * @param permission 需要的权限
 * @returns 装饰器函数
 */
export const requirePermission = (permission: string) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const hasPermission = await checkPermission(permission)
      if (!hasPermission) {
        uni.showToast({
          title: '权限不足',
          icon: 'none'
        })
        return
      }
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

/**
 * 页面权限验证中间件
 * @param requiredPermissions 页面需要的权限列表
 * @returns Promise<boolean> 是否有权限访问页面
 */
export const validatePagePermissions = async (requiredPermissions: string[]): Promise<boolean> => {
  if (requiredPermissions.length === 0) {
    return true
  }
  
  const results = await checkMultiplePermissions(requiredPermissions)
  
  // 检查是否有任一权限（OR逻辑）
  const hasAnyPermission = Object.values(results).some(hasPermission => hasPermission)
  
  if (!hasAnyPermission) {
    uni.showToast({
      title: '权限不足，无法访问此页面',
      icon: 'none'
    })
    
    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack({
        fail: () => {
          // 如果无法返回，跳转到首页
          uni.switchTab({
            url: '/pages/index/index'
          })
        }
      })
    }, 1500)
  }
  
  return hasAnyPermission
}

/**
 * 权限状态响应式对象
 */
export interface PermissionState {
  loading: boolean
  permissions: Record<string, boolean>
  isAdmin: boolean
  canAccessBackend: boolean
}

/**
 * 创建权限状态管理
 * @param initialPermissions 初始权限列表
 * @returns 权限状态对象
 */
export const createPermissionState = (initialPermissions: string[] = []): PermissionState => {
  const state: PermissionState = {
    loading: true,
    permissions: {},
    isAdmin: false,
    canAccessBackend: false
  }
  
  // 初始化权限状态
  const initPermissions = async () => {
    state.loading = true
    
    try {
      // 检查管理员权限
      state.isAdmin = await checkAdminPermission()
      
      // 检查管理后台访问权限
      state.canAccessBackend = await checkBackendAccess()
      
      // 检查指定权限
      if (initialPermissions.length > 0) {
        state.permissions = await checkMultiplePermissions(initialPermissions)
      }
    } catch (error) {
      console.error('初始化权限状态失败:', error)
    } finally {
      state.loading = false
    }
  }
  
  // 立即初始化
  initPermissions()
  
  return state
}
