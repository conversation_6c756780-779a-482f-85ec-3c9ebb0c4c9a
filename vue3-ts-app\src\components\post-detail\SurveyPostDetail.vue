<template>
  <view class="survey-post-detail">
    <!-- 调查问卷标题和状态 -->
    <view class="survey-header">
      <view class="title-row">
        <view class="survey-title">{{ post.title }}</view>
        <view class="survey-status-badge" :class="statusClass">
          {{ statusText }}
        </view>
      </view>
      
      <!-- 问卷信息 -->
      <view class="survey-meta">
        <view class="meta-item">
          <up-icon name="list" size="14" color="rgba(255,255,255,0.8)"></up-icon>
          <text class="meta-text">{{ post.questions?.length || 0 }}个问题</text>
        </view>
        <view class="meta-item">
          <up-icon name="account" size="14" color="rgba(255,255,255,0.8)"></up-icon>
          <text class="meta-text">{{ post.participantCount || 0 }}人参与</text>
        </view>
        <view v-if="post.isAnonymous" class="meta-item">
          <up-icon name="eye-off" size="14" color="rgba(255,255,255,0.8)"></up-icon>
          <text class="meta-text">匿名调查</text>
        </view>
      </view>
      
      <!-- 截止时间 -->
      <view v-if="post.deadline" class="deadline-info">
        <text class="deadline-label">截止时间：</text>
        <text class="deadline-value">{{ formatDateTime(post.deadline) }}</text>
        <text v-if="isExpired" class="expired-tag">已截止</text>
      </view>
      
      <view v-if="!isExpired && timeRemaining" class="time-remaining">
        <text class="remaining-text">剩余时间：{{ timeRemaining }}</text>
      </view>
    </view>
    
    <!-- 基础内容 -->
    <BasePostDetail :post="post" />
    
    <!-- 问题列表预览 -->
    <view v-if="displayQuestions && displayQuestions.length > 0" class="questions-preview">
      <view class="section-title">问题预览</view>
      <view class="questions-list">
        <view v-for="(question, index) in displayQuestions" :key="index" class="question-item">
          <view class="question-header">
            <text class="question-number">{{ index + 1 }}.</text>
            <text class="question-text">{{ question.title || question.questionText || question.text }}</text>
            <view v-if="question.required || question.isRequired" class="required-badge">必答</view>
          </view>
          
          <!-- 选择题选项预览 -->
          <view v-if="question.type !== 'text' && question.questionType !== 'text'" class="options-preview">
            <view v-for="(option, optionIndex) in question.options" :key="optionIndex" class="option-item">
              <view class="option-indicator" :class="question.type || question.questionType">
                <view v-if="(question.type || question.questionType) === 'single'" class="radio-dot"></view>
                <view v-else-if="(question.type || question.questionType) === 'multiple'" class="checkbox-square"></view>
              </view>
              <text class="option-text">{{ typeof option === 'string' ? option : option.text }}</text>
            </view>
          </view>
          
          <!-- 文本题预览 -->
          <view v-else class="text-input-preview">
            <view class="text-placeholder">请在此输入您的答案...</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 如果没有问题，显示选项预览（简单调查） -->
    <view v-else-if="post.options && post.options.length > 0" class="simple-survey-preview">
      <view class="section-title">调查选项</view>
      <view class="options-preview">
        <view v-for="(option, optionIndex) in post.options" :key="optionIndex" class="option-item">
          <view class="option-indicator" :class="post.multiSelect === 'true' || post.multiSelect === true ? 'multiple' : 'single'">
            <view v-if="!(post.multiSelect === 'true' || post.multiSelect === true)" class="radio-dot"></view>
            <view v-else class="checkbox-square"></view>
          </view>
          <text class="option-text">{{ typeof option === 'string' ? option : option.text }}</text>
        </view>
      </view>
    </view>
    
    <!-- 参与统计 -->
    <view v-if="post.participantCount > 0" class="participation-stats">
      <view class="section-title">参与统计</view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{ post.participantCount }}</text>
          <text class="stat-label">总参与人数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ completionRate }}%</text>
          <text class="stat-label">完成率</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ averageTime }}</text>
          <text class="stat-label">平均用时</text>
        </view>
      </view>
    </view>
    
    <!-- 目标对象 -->
    <view v-if="post.targetAudience && post.targetAudience.length > 0" class="target-section">
      <view class="section-title">目标对象</view>
      <view class="target-tags">
        <up-tag
          v-for="target in post.targetAudience"
          :key="target"
          :text="formatTargetAudience(target)"
          color="#007bff"
          size="mini"
          plain
          plainFill
          borderColor="transparent"
        />
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-section">
      <up-button
        v-if="!isExpired && !hasParticipated"
        text="参与调查"
        type="primary"
        size="large"
        @click="handleParticipate"
        :customStyle="{ borderRadius: '8px' }"
      />
      <up-button
        v-else-if="hasParticipated"
        text="查看我的答案"
        type="info"
        size="large"
        plain
        @click="handleViewMyAnswer"
        :customStyle="{ borderRadius: '8px' }"
      />
      <view v-if="canViewResults" class="view-results-btn">
        <up-button
          text="查看统计结果"
          type="success"
          size="large"
          plain
          @click="handleViewResults"
          :customStyle="{ borderRadius: '8px', marginTop: '12px' }"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import BasePostDetail from './BasePostDetail.vue'
import { getTargetAudienceOptionsAPI, type TargetAudienceOption } from '@/services/roleService'
import { useMemberStore } from '@/stores'

interface Props {
  post: {
    id?: number
    title: string
    content: string
    images?: string[]
    questions: Array<{
      title: string
      type: 'single' | 'multiple' | 'text'
      options?: Array<{ text: string }>
      required: boolean
    }>
    isAnonymous: boolean
    deadline?: string
    targetAudience?: string[]
    participantCount?: number
    completionCount?: number
    averageTimeMinutes?: number
    createdTime: string
    updatedTime?: string
    hasParticipated?: boolean
    canViewResults?: boolean
    [key: string]: any
  }
}

const props = defineProps<Props>()
const memberStore = useMemberStore()

// 目标对象映射（动态获取）
const targetAudienceMap = ref<Map<string, string>>(new Map())

// 加载目标对象映射
const loadTargetAudienceMap = async () => {
  try {
    const options = await getTargetAudienceOptionsAPI()
    const map = new Map<string, string>()
    options.forEach(option => {
      map.set(option.value, option.label)
    })
    targetAudienceMap.value = map
  } catch (error) {
    console.error('加载目标对象映射失败:', error)
    // 使用默认映射作为后备
    targetAudienceMap.value = new Map([
      ['owner', '全体业主'],
      ['tenant', '租户'],
      ['property', '物业人员'],
      ['committee', '业委会成员'],
      ['all', '所有用户']
    ])
  }
}

// 是否过期
const isExpired = computed(() => {
  if (!props.post.deadline) return false
  return new Date(props.post.deadline) < new Date()
})

// 状态文本和样式
const statusText = computed(() => {
  if (isExpired.value) return '已截止'
  if (props.post.participantCount && props.post.participantCount > 0) return '进行中'
  return '待参与'
})

const statusClass = computed(() => {
  if (isExpired.value) return 'expired'
  if (props.post.participantCount && props.post.participantCount > 0) return 'active'
  return 'pending'
})

// 剩余时间
const timeRemaining = computed(() => {
  if (!props.post.deadline || isExpired.value) return null
  
  const now = new Date()
  const deadline = new Date(props.post.deadline)
  const diff = deadline.getTime() - now.getTime()
  
  if (diff <= 0) return null
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) return `${days}天${hours}小时`
  if (hours > 0) return `${hours}小时${minutes}分钟`
  return `${minutes}分钟`
})

// 完成率
const completionRate = computed(() => {
  if (!props.post.participantCount || props.post.participantCount === 0) return 0
  const completed = props.post.completionCount || props.post.participantCount
  return Math.round((completed / props.post.participantCount) * 100)
})

// 平均用时
const averageTime = computed(() => {
  if (!props.post.averageTimeMinutes) return '未知'
  const minutes = props.post.averageTimeMinutes
  if (minutes < 60) return `${minutes}分钟`
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return `${hours}小时${remainingMinutes}分钟`
})

// 是否已参与
const hasParticipated = computed(() => {
  return props.post.hasParticipated || false
})

// 是否可以查看结果
const canViewResults = computed(() => {
  // 创建者、管理员或已参与的用户可以查看结果
  return props.post.canViewResults || 
         memberStore.profile?.isAdmin || 
         hasParticipated.value
})

// 显示的问题列表
const displayQuestions = computed(() => {
  if (props.post.questions && Array.isArray(props.post.questions) && props.post.questions.length > 0) {
    return props.post.questions
  }
  
  // 如果没有questions字段，但有options，构建一个简单的问题
  if (props.post.options && Array.isArray(props.post.options) && props.post.options.length > 0) {
    return [{
      title: props.post.title,
      questionText: props.post.title,
      type: props.post.multiSelect === 'true' || props.post.multiSelect === true ? 'multiple' : 'single',
      questionType: props.post.multiSelect === 'true' || props.post.multiSelect === true ? 'multiple' : 'single',
      options: props.post.options,
      required: true,
      isRequired: true
    }]
  }
  
  return []
})

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化目标对象
const formatTargetAudience = (audience: string) => {
  return targetAudienceMap.value.get(audience) || audience
}

// 处理参与调查
const handleParticipate = () => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  // 直接跳转到参与页面，不再显示弹窗
  uni.navigateTo({
    url: `/pages/survey/participate?id=${props.post.id}`
  })
}

// 查看我的答案
const handleViewMyAnswer = () => {
  uni.navigateTo({
    url: `/pages/survey/participate?id=${props.post.id}`
  })
}

// 查看统计结果
const handleViewResults = () => {
  // 直接跳转到结果页面
  uni.navigateTo({
    url: `/pages/survey/results?id=${props.post.id}`
  })
}

// 定义事件
const emit = defineEmits<{
  participate: [postId: number]
  viewResults: [postId: number]
}>()

// 组件挂载时加载目标对象映射
onMounted(() => {
  loadTargetAudienceMap()
})

// 自动导航到参与页面
const hasAttemptedNav = ref(false)
watch(
  () => props.post,
  (newPost) => {
    if (newPost && newPost.id && !hasAttemptedNav.value) {
      const isReadyToParticipate =
        !isExpired.value && !hasParticipated.value && memberStore.profile?.id

      if (isReadyToParticipate) {
        hasAttemptedNav.value = true // 防止用户返回时重复导航
        uni.navigateTo({
          url: `/pages/survey/participate?id=${props.post.id}`,
        })
      }
    }
  },
  { immediate: true, deep: true },
)
</script>

<style scoped lang="scss">
.survey-post-detail {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.survey-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 20px 16px;
}

.title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.survey-title {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.survey-status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
  
  &.pending {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  &.active {
    background-color: #ffc107;
    color: #333;
  }
  
  &.expired {
    background-color: #dc3545;
    color: white;
  }
}

.survey-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-text {
  font-size: 14px;
  opacity: 0.9;
}

.deadline-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.deadline-label {
  opacity: 0.8;
  margin-right: 8px;
}

.deadline-value {
  font-weight: 500;
}

.expired-tag {
  margin-left: 8px;
  padding: 2px 6px;
  background-color: #dc3545;
  color: white;
  border-radius: 4px;
  font-size: 12px;
}

.time-remaining {
  text-align: center;
}

.remaining-text {
  font-size: 14px;
  font-weight: bold;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 12px;
}

.questions-preview {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-item {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.question-header {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
}

.question-number {
  font-size: 14px;
  font-weight: bold;
  color: #007bff;
  flex-shrink: 0;
}

.question-text {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

.required-badge {
  padding: 2px 6px;
  background-color: #dc3545;
  color: white;
  border-radius: 4px;
  font-size: 10px;
  flex-shrink: 0;
}

.options-preview {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-left: 20px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-indicator {
  width: 16px;
  height: 16px;
  border: 2px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  &.single {
    border-radius: 50%;
  }
  
  &.multiple {
    border-radius: 2px;
  }
}

.radio-dot {
  width: 6px;
  height: 6px;
  background-color: #ccc;
  border-radius: 50%;
}

.checkbox-square {
  width: 8px;
  height: 8px;
  background-color: #ccc;
}

.option-text {
  font-size: 13px;
  color: #666;
}

.text-input-preview {
  margin-left: 20px;
  padding: 8px 12px;
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.text-placeholder {
  font-size: 13px;
  color: #999;
  font-style: italic;
}

.participation-stats {
  padding: 16px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #28a745;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.target-section {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.target-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.action-section {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

.view-results-btn {
  margin-top: 12px;
}
</style>