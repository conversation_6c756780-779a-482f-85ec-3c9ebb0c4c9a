<template>
  <view class="collections-page">
    <!-- 顶部统计 -->
    <view class="stats-header">
      <view class="stats-item">
        <text class="stats-number">{{ totalCollections }}</text>
        <text class="stats-label">总收藏</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{ todayCollections }}</text>
        <text class="stats-label">今日收藏</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{ monthCollections }}</text>
        <text class="stats-label">本月收藏</text>
      </view>
    </view>

    <!-- 筛选和排序 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <up-tabs
          :list="categoryTabs"
          :current="currentCategory"
          @change="onCategoryChange"
          :scrollable="true"
        ></up-tabs>
      </view>
      <view class="sort-actions">
        <view class="dropdown-container">
          <view class="custom-dropdown" @click="toggleDropdown">
            <text class="dropdown-text">{{ getCurrentSortLabel() }}</text>
            <up-icon
              :name="dropdownVisible ? 'arrow-up' : 'arrow-down'"
              size="14"
              color="#999"
              class="dropdown-arrow"
            ></up-icon>
          </view>
          <view v-if="dropdownVisible" class="dropdown-menu">
            <view
              v-for="option in sortOptions"
              :key="option.value"
              class="dropdown-item"
              :class="{ 'dropdown-item-active': currentSort === option.value }"
              @click="selectSort(option.value)"
            >
              <text class="dropdown-item-text">{{ option.label }}</text>
              <text v-if="currentSort === option.value" class="dropdown-check">✓</text>
            </view>
          </view>
        </view>
        <view class="edit-btn" @click="toggleEditMode" v-if="false">
          <text class="edit-text">{{ editMode ? '完成' : '编辑' }}</text>
        </view>
      </view>
    </view>

    <!-- 收藏列表 -->
    <view class="collections-list">
      <view v-if="collectionList.length === 0 && !loading" class="empty-state">
        <up-empty
          mode="data"
          text="暂无收藏"
          textSize="14"
        ></up-empty>
      </view>

      <view v-for="item in collectionList" :key="item.id" class="collection-item">
        <!-- 编辑模式选择框 -->
        <view v-if="editMode" class="select-box" @click="toggleSelect(item)">
          <up-icon
            :name="item.selected ? 'checkbox-fill' : 'checkbox'"
            :color="item.selected ? '#5677fc' : '#ccc'"
            size="20"
          ></up-icon>
        </view>

        <!-- 收藏内容 -->
        <view class="collection-content" @click="goToDetail(item.postId)">
          <view class="post-header">
            <up-avatar
              :src="item.userAvatar"
              :size="32"
              shape="circle"
            ></up-avatar>
            <view class="post-meta">
              <text class="author-name">{{ item.nickname }}</text>
              <text class="collect-time">{{ formatTime(item.collectTime) }}</text>
            </view>
          </view>

          <view class="post-content">
            <up-parse :content="item.postContent" class="content-text"></up-parse>
            
            <!-- 图片展示 -->
            <view v-if="item.postImages && item.postImages.length > 0" class="post-images">
              <view class="image-preview">
                <up-image
                  :src="item.postImages[0]"
                  width="60px"
                  height="60px"
                  radius="6"
                  mode="aspectFill"
                ></up-image>
                <view v-if="item.postImages.length > 1" class="image-count">
                  <text class="count-text">+{{ item.postImages.length - 1 }}</text>
                </view>
              </view>
            </view>
          </view>

          <view class="post-footer">
            <view class="post-stats">
              <view class="stat-item">
                <up-icon name="thumb-up" size="18" color="#999"></up-icon>
                <text class="stat-text">{{ item.likeCount || 0 }}</text>
              </view>
              <view class="stat-item">
                <up-icon name="chat" size="18" color="#999"></up-icon>
                <text class="stat-text">{{ item.commentCount || 0 }}</text>
              </view>
            </view>
            <view class="post-category">
              <up-tag
                :text="item.categoryName"
                size="mini"
                type="primary"
                plain plainFill
            borderColor="transparent" :autoBgColor="95"
              ></up-tag>
            </view>
          </view>
        </view>

        <!-- 快捷操作 -->
        <view v-if="!editMode" class="quick-actions">
          <view class="action-btn" @click="uncollect(item)">
            <up-icon name="star-fill" color="#ffa940" size="16"></up-icon>
          </view>
          <view class="action-btn" @click="sharePost(item)">
            <up-icon name="share" color="#666" size="16"></up-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 编辑模式底部操作栏 -->
    <view v-if="editMode" class="edit-toolbar">
      <view class="select-all" @click="selectAll">
        <up-icon
          :name="isAllSelected ? 'checkbox-fill' : 'checkbox'"
          :color="isAllSelected ? '#5677fc' : '#ccc'"
          size="18"
        ></up-icon>
        <text class="select-text">全选</text>
      </view>
      <view class="toolbar-actions">
        <up-button
          text="取消收藏"
          type="error"
          size="small"
          :disabled="selectedCount === 0"
          @click="batchUncollect"
        ></up-button>
      </view>
    </view>

    <!-- 加载状态 -->
    <up-loadmore
      :status="loadStatus"
      :loading-text="loadingText"
      :loadmore-text="loadmoreText"
      :nomore-text="nomoreText"
      @loadmore="loadMore"
    />


  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMemberStore } from '@/stores'
import { get } from '@/utils/http'
import { setFileList } from '@/utils/util'
import { toggleCollect } from '@/services/likeCollectService'
import { getCategoryListAPI, formatCategoriesForTabs } from '@/services/categoryService'
import type { Categories } from '@/types/Categories'

// 收藏项类型定义
interface CollectionItem {
  id: number
  postId: number
  title: string
  content: string
  postContent: string
  categoryName: string
  postImages?: string | string[]
  userAvatar: string
  nickname: string
  likeCount: number
  commentCount: number
  createdTime: string
  collectTime: string
  selected: boolean
}





// API响应类型
interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
}

// 收藏列表响应类型
interface CollectionListResponse {
  records: CollectionItem[]
  total: number
  todayCount: number
  monthCount: number
}

const memberStore = useMemberStore()

// 数据
const collectionList = ref<CollectionItem[]>([])
const totalCollections = ref<number>(0)
const todayCollections = ref<number>(0)
const monthCollections = ref<number>(0)
const loading = ref<boolean>(false)
const editMode = ref<boolean>(false)
const currentCategory = ref<number>(0)
const currentSort = ref<string>('time')
const dropdownVisible = ref<boolean>(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const loadStatus = ref<'loadmore' | 'loading' | 'nomore'>('loadmore')
const loadingText = ref('正在加载...')
const loadmoreText = ref('上拉加载更多')
const nomoreText = ref('已经到底了')

// 获取分类列表
const fetchCategories = async (): Promise<void> => {
  try {
    const res = await getCategoryListAPI()
    if (res.success && res.data) {
      categoriesData.value = res.data
      // 格式化为选项卡数据
      categoryTabs.value = formatCategoriesForTabs(res.data)
      console.log('获取分类列表成功:', categoryTabs.value)
    } else {
      console.error('获取分类列表失败:', res.message)
    }
  } catch (error) {
    console.error('获取分类列表异常:', error)
  }
}

// 分类选项
const categoryTabs = ref([
  { name: '全部', id: 0 }
])

// 分类原始数据
const categoriesData = ref<Categories[]>([])

// 排序选项 - 适配 up-dropdown-item 的数据格式
const sortOptions = ref([
  { label: '收藏时间', value: 'time' },
  { label: '发布时间', value: 'create' },
  { label: '热度排序', value: 'hot' }
])

// 计算属性
const selectedCount = computed(() => {
  return collectionList.value.filter(item => item.selected).length
})

const isAllSelected = computed(() => {
  return collectionList.value.length > 0 && selectedCount.value === collectionList.value.length
})

// 获取统计数据
const fetchStats = async (): Promise<void> => {
  if (!memberStore.profile?.id) return

  try {
    // 获取所有收藏统计数据（不受分类筛选影响）- 使用不带分类筛选的请求
    const statsRes = await get<ApiResponse<CollectionListResponse>>('/user-collects/my?page=1&pageSize=1&sortBy=time')

    if (statsRes.success && statsRes.data) {
      totalCollections.value = statsRes.data.total || 0
      todayCollections.value = statsRes.data.todayCount || 0
      monthCollections.value = statsRes.data.monthCount || 0
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取收藏列表
const fetchCollections = async (isRefresh: boolean = false): Promise<void> => {
  if (!memberStore.profile?.id) return

  if (isRefresh) {
    currentPage.value = 1
    hasMore.value = true
    loadStatus.value = 'loading'
  }

  loading.value = true

  try {
    // 构建查询参数
    const queryParams = new URLSearchParams()
    queryParams.append('page', currentPage.value.toString())
    queryParams.append('pageSize', pageSize.value.toString())
    queryParams.append('sortBy', currentSort.value)

    // 获取当前选中分类的真实ID
    const categoryId = getCurrentCategoryId()
    if (categoryId > 0) {
      queryParams.append('categoryId', categoryId.toString())
    }

    const queryString = queryParams.toString()
    const url = `/user-collects/my?${queryString}`

    // 调用获取收藏列表的真实API
    const res = await get<ApiResponse<CollectionListResponse>>(url)

    if (res.success) {
      const newCollections: CollectionItem[] = res.data.records || []

      // 处理图片列表
      newCollections.forEach((item: CollectionItem) => {
        if (item.postImages && typeof item.postImages === 'string') {
          item.postImages = setFileList(item.postImages)
        }
        item.selected = false
      })

      if (isRefresh) {
        collectionList.value = newCollections
      } else {
        collectionList.value.push(...newCollections)
      }

      // 注意：这里不再更新统计数据，因为统计数据应该显示所有收藏的总数

      // 判断是否还有更多数据
      if (newCollections.length < pageSize.value) {
        hasMore.value = false
        loadStatus.value = 'nomore'
      } else {
        loadStatus.value = 'loadmore'
      }
    }
  } catch (error) {
    console.error('获取收藏失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 分类切换
const onCategoryChange = (item: { index: number }): void => {
  currentCategory.value = item.index
  fetchCollections(true)
}

// 获取当前选中分类的ID
const getCurrentCategoryId = (): number => {
  if (currentCategory.value === 0) return 0 // 全部
  const selectedTab = categoryTabs.value[currentCategory.value]
  return selectedTab?.id || 0
}

// 获取当前排序选项的标签
const getCurrentSortLabel = (): string => {
  const option = sortOptions.value.find(opt => opt.value === currentSort.value)
  return option ? option.label : '排序方式'
}

// 切换下拉菜单显示状态
const toggleDropdown = (): void => {
  dropdownVisible.value = !dropdownVisible.value
}

// 选择排序方式
const selectSort = (value: string): void => {
  currentSort.value = value
  dropdownVisible.value = false
  fetchCollections(true)
}



// 切换编辑模式
const toggleEditMode = (): void => {
  editMode.value = !editMode.value
  if (!editMode.value) {
    // 退出编辑模式时清除选择
    collectionList.value.forEach((item: CollectionItem) => {
      item.selected = false
    })
  }
}

// 切换选择状态
const toggleSelect = (item: CollectionItem): void => {
  item.selected = !item.selected
}

// 全选/取消全选
const selectAll = (): void => {
  const selectAll = !isAllSelected.value
  collectionList.value.forEach((item: CollectionItem) => {
    item.selected = selectAll
  })
}

// 批量取消收藏
const batchUncollect = (): void => {
  const selectedItems: CollectionItem[] = collectionList.value.filter(item => item.selected)
  
  uni.showModal({
    title: '确认操作',
    content: `确定要取消收藏这${selectedItems.length}条内容吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          // 调用批量取消收藏API
          // const postIds: number[] = selectedItems.map(item => item.postId)
          // await batchUncollectAPI(postIds)

          // 从列表中移除
          collectionList.value = collectionList.value.filter(item => !item.selected)
          // 重新获取统计数据以保持准确性
          await fetchStats()
          
          uni.showToast({
            title: '取消收藏成功',
            icon: 'success'
          })
          
          editMode.value = false
        } catch (error) {
          console.error('取消收藏失败:', error)
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 取消单个收藏
const uncollect = async (item: CollectionItem): Promise<void> => {
  try {
    // 调用取消收藏API
    const res = await toggleCollect(item.postId)

    if (res.success) {
      // 从列表中移除
      const index: number = collectionList.value.findIndex(c => c.id === item.id)
      if (index > -1) {
        collectionList.value.splice(index, 1)
        // 重新获取统计数据以保持准确性
        await fetchStats()
      }

      uni.showToast({
        title: '取消收藏成功',
        icon: 'success'
      })
    } else {
      throw new Error(res.message || '取消收藏失败')
    }
  } catch (error) {
    console.error('取消收藏失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  }
}

// 分享帖子
const sharePost = (_item: CollectionItem): void => {
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  })
}

// 跳转到详情
const goToDetail = (postId: number): void => {
  uni.navigateTo({
    url: `/pages/post/detail?id=${postId}`
  })
}

// 格式化时间
const formatTime = (time: string): string => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 86400000) {
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前收藏`
    return `${Math.floor(diff / 3600000)}小时前收藏`
  }

  return `${date.getMonth() + 1}月${date.getDate()}日收藏`
}

// 加载更多
const loadMore = (): void => {
  if (!hasMore.value || loading.value) return

  currentPage.value++
  fetchCollections()
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: Event): void => {
  const target = event.target as HTMLElement
  const dropdown = document.querySelector('.dropdown-container')
  if (dropdown && !dropdown.contains(target)) {
    dropdownVisible.value = false
  }
}

onMounted(async () => {
  // 先获取分类数据
  await fetchCategories()
  // 获取统计数据（不受分类影响）
  await fetchStats()
  // 再获取收藏列表
  fetchCollections(true)

  // 添加点击外部关闭下拉菜单的事件监听
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // 移除事件监听
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped lang="scss">
.collections-page {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 60px;
}

.stats-header {
  background-color: white;
  display: flex;
  padding: 20px;
  margin-bottom: 10px;
}

.stats-item {
  flex: 1;
  text-align: center;
  
  .stats-number {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
  }
  
  .stats-label {
    font-size: 12px;
    color: #999;
  }
}

.filter-section {
  background-color: white;
  margin-bottom: 10px;
}

.sort-actions {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  gap: 12px;
}

.dropdown-container {
  min-width: 0;
  position: relative;
}

/* 自定义下拉菜单样式 */
.custom-dropdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 4px;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 32px;
}

.dropdown-text {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

.dropdown-arrow {
  margin-left: 6px;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: -4px;
  right: -4px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  margin-top: 4px;
  overflow: hidden;
  min-width: 120px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item-active {
  background-color: #f0f7ff;
}

.dropdown-item-active:hover {
  background-color: #e6f3ff;
}

.dropdown-item-text {
  font-size: 14px;
  color: #333;
}

.dropdown-item-active .dropdown-item-text {
  color: #1890ff;
  font-weight: 500;
}

.dropdown-check {
  font-size: 12px;
  color: #1890ff;
  font-weight: bold;
}

.edit-btn {
  flex-shrink: 0; /* 防止编辑按钮被压缩 */
  padding: 6px 12px;
  background: #f8f9fa;
  border-radius: 16px;
  border: 1px solid #e9ecef;
  min-width: 60px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.edit-btn:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.edit-btn:active {
  transform: translateY(0);
  background: #dee2e6;
}

.edit-text {
  font-size: 14px;
  color: #5677fc;
  font-weight: 500;
}

/* 响应式优化 */
@media (max-width: 375px) {
  .sort-actions {
    padding: 8px 12px;
    gap: 8px;
  }

  .edit-btn {
    padding: 4px 8px;
    min-width: 50px;
  }

  .edit-text {
    font-size: 13px;
  }

  .custom-dropdown {
    padding: 6px 2px;
    min-height: 30px;
  }

  .dropdown-text {
    font-size: 13px;
  }

  .dropdown-arrow {
    margin-left: 4px;
  }

  .dropdown-item {
    padding: 8px 10px;
  }

  .dropdown-item-text {
    font-size: 13px;
  }
}

/* 确保下拉菜单在小屏幕上也能正常显示 */
@media (max-width: 320px) {
  .sort-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .dropdown-container {
    width: 100%;
  }

  .edit-btn {
    align-self: flex-end;
    min-width: auto;
  }

  .dropdown-menu {
    left: 0;
    right: 0;
  }
}

.collections-list {
  padding: 0 10px;
}

.collection-item {
  background-color: white;
  border-radius: 12px;
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
  overflow: hidden;
}

.select-box {
  padding: 16px 12px;
  display: flex;
  align-items: center;
}

.collection-content {
  flex: 1;
  padding: 16px;
  padding-left: 0;
}

.post-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.post-meta {
  flex: 1;
}

.author-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 2px;
}

.collect-time {
  font-size: 12px;
  color: #999;
}

.post-content {
  margin-bottom: 8px;
}

.content-text {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-images {
  .image-preview {
    position: relative;
    display: inline-block;
  }
  
  .image-count {
    position: absolute;
    bottom: 2px;
    right: 2px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 8px;
    padding: 2px 4px;
    
    .count-text {
      font-size: 10px;
      color: white;
    }
  }
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.post-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-text {
  font-size: 12px;
  color: #999;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px 12px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 8px;
}

.select-text {
  font-size: 14px;
  color: #333;
}

.empty-state {
  padding: 60px 20px;
}
</style>
