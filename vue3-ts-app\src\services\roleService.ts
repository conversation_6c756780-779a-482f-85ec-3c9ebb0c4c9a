/*
 * @Author: Rock
 * @Date: 2025-07-26 19:39:56
 * @LastEditors: Rock
 * @LastEditTime: 2025-07-26 19:51:34
 * @Description: 
 */
import { get } from '@/utils/http'

/**
 * 用户角色接口
 */
export interface UserRole {
  id: number
  roleCode: string
  roleName: string
  roleDescription: string
  permissions: string[]
  sortOrder: number
  status: number
  createdTime: string
  updatedTime: string
}

/**
 * 目标对象选项接口
 */
export interface TargetAudienceOption {
  value: string // role_code
  label: string // role_name
}

/**
 * 获取所有角色列表
 */
export const getRoleListAPI = () => {
  return get<any>('/roles/list')
}

/**
 * 获取可用于目标对象选择的角色列表
 * 排除游客和管理员角色
 */
export const getTargetAudienceOptionsAPI = async (): Promise<TargetAudienceOption[]> => {
  try {
    const res = await getRoleListAPI()
    
    if (res.success && res.data) {
      // 过滤出可用于目标对象选择的角色（排除 guest 和 admin）
      const excludeRoles = ['guest', 'admin', 'community']

      // 按角色名称排序
      // .sort((a: any, b: any) => a.label.localeCompare(b.label))
      
      return res.data
        .filter((role: any) => !excludeRoles.includes(role.roleCode))
        .map((role: any) => ({
          value: role.roleCode,
          label: role.roleName
        }))
    }
    
    return []
  } catch (error) {
    console.error('获取目标对象选项失败:', error)
    return []
  }
}

/**
 * 根据角色代码获取角色信息
 */
export const getRoleByCodeAPI = (roleCode: string) => {
  return get<any>(`/roles/${roleCode}`)
}

/**
 * 格式化目标对象显示文本
 */
export const formatTargetAudience = async (roleCodes?: string[]): Promise<string> => {
  if (!roleCodes || roleCodes.length === 0) {
    return '所有用户'
  }
  
  try {
    const options = await getTargetAudienceOptionsAPI()
    const roleMap = new Map(options.map(option => [option.value, option.label]))
    
    return roleCodes
      .map((code: string) => roleMap.get(code) || code)
      .join('、')
  } catch (error) {
    console.error('格式化目标对象失败:', error)
    return roleCodes.join('、')
  }
}
