<template>
  <view class="video-player-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: safeAreaTop + 'px' }">
      <view class="navbar-content">
        <view class="nav-left" @click="goBack">
          <up-icon name="arrow-left" size="20" color="white" />
        </view>
        <view class="nav-title">{{ videoTitle }}</view>
        <view class="nav-right">
          <up-icon name="more-dot-fill" size="20" color="white" @click="showMoreActions" />
        </view>
      </view>
    </view>

    <!-- 视频播放器 -->
    <view class="video-container">
      <video
        :src="videoUrl"
        :poster="videoPoster"
        controls
        :autoplay="false"
        :loop="false"
        :muted="false"
        :show-center-play-btn="true"
        :show-play-btn="true"
        :show-fullscreen-btn="true"
        :show-progress="true"
        :show-loading="true"
        :enable-progress-gesture="true"
        :object-fit="objectFit"
        @play="handlePlay"
        @pause="handlePause"
        @ended="handleEnded"
        @error="handleError"
        @fullscreenchange="handleFullscreenChange"
        @loadstart="handleLoadStart"
        @loadeddata="handleLoadedData"
      />
      
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-overlay">
        <up-loading-icon mode="spinner" size="40" color="white" />
        <text class="loading-text">视频加载中...</text>
      </view>
      
      <!-- 错误状态 -->
      <view v-if="error" class="error-overlay">
        <up-icon name="warning" size="60" color="#ff6b6b" />
        <text class="error-text">视频加载失败</text>
        <up-button text="重新加载" type="primary" size="small" @click="reloadVideo" />
      </view>
    </view>

    <!-- 视频信息 -->
    <view class="video-info">
      <view class="video-title">{{ videoTitle }}</view>
      <view class="video-meta">
        <text class="meta-item">时长: {{ formatDuration(duration) }}</text>
        <text class="meta-item">大小: {{ formatFileSize(fileSize) }}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="action-btn" @click="toggleObjectFit">
        <up-icon name="crop" size="20" color="#666" />
        <text>{{ objectFit === 'contain' ? '适应' : '填充' }}</text>
      </view>
      <view class="action-btn" @click="downloadVideo">
        <up-icon name="download" size="20" color="#666" />
        <text>下载</text>
      </view>
      <view class="action-btn" @click="shareVideo">
        <up-icon name="share" size="20" color="#666" />
        <text>分享</text>
      </view>
    </view>

    <!-- 更多操作弹窗 -->
    <up-action-sheet
      :show="showActions"
      :actions="actionList"
      @close="showActions = false"
      @select="handleActionSelect"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { useSafeArea } from '@/utils/safeArea'

const { safeAreaTop } = useSafeArea()

// 页面参数
const videoUrl = ref('')
const videoTitle = ref('视频播放')
const videoPoster = ref('')

// 播放状态
const loading = ref(true)
const error = ref(false)
const playing = ref(false)
const duration = ref(0)
const fileSize = ref(0)
const objectFit = ref<'contain' | 'fill' | 'cover'>('contain')

// 操作相关
const showActions = ref(false)
const actionList = [
  { name: '复制链接' },
  { name: '举报视频' },
  { name: '视频信息' }
]

// 页面加载时获取参数
onLoad((options) => {
  if (options?.url) {
    videoUrl.value = decodeURIComponent(options.url)
  }
  if (options?.title) {
    videoTitle.value = decodeURIComponent(options.title)
  }
  if (options?.poster) {
    videoPoster.value = decodeURIComponent(options.poster)
  }
  
  console.log('视频播放器参数:', { url: videoUrl.value, title: videoTitle.value })
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 显示更多操作
const showMoreActions = () => {
  showActions.value = true
}

// 处理操作选择
const handleActionSelect = (action: { name: string }) => {
  showActions.value = false
  
  switch (action.name) {
    case '复制链接':
      uni.setClipboardData({
        data: videoUrl.value,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          })
        }
      })
      break
    case '举报视频':
      uni.showToast({
        title: '举报功能开发中',
        icon: 'none'
      })
      break
    case '视频信息':
      showVideoInfo()
      break
  }
}

// 显示视频信息
const showVideoInfo = () => {
  uni.showModal({
    title: '视频信息',
    content: `标题: ${videoTitle.value}\n链接: ${videoUrl.value}\n时长: ${formatDuration(duration.value)}`,
    showCancel: false
  })
}

// 视频事件处理
const handlePlay = () => {
  playing.value = true
  loading.value = false
  console.log('视频开始播放')
}

const handlePause = () => {
  playing.value = false
  console.log('视频暂停')
}

const handleEnded = () => {
  playing.value = false
  console.log('视频播放结束')
}

const handleError = (e: any) => {
  loading.value = false
  error.value = true
  console.error('视频播放错误:', e)
  uni.showToast({
    title: '视频加载失败',
    icon: 'none'
  })
}

const handleFullscreenChange = (e: any) => {
  console.log('全屏状态变化:', e)
}

const handleLoadStart = () => {
  loading.value = true
  error.value = false
  console.log('视频开始加载')
}

const handleLoadedData = (e: any) => {
  loading.value = false
  if (e.detail?.duration) {
    duration.value = e.detail.duration
  }
  console.log('视频数据加载完成:', e)
}

// 重新加载视频
const reloadVideo = () => {
  error.value = false
  loading.value = true
  // 触发视频重新加载
  const tempUrl = videoUrl.value
  videoUrl.value = ''
  setTimeout(() => {
    videoUrl.value = tempUrl
  }, 100)
}

// 切换显示模式
const toggleObjectFit = () => {
  objectFit.value = objectFit.value === 'contain' ? 'fill' : 'contain'
  uni.showToast({
    title: `已切换到${objectFit.value === 'contain' ? '适应' : '填充'}模式`,
    icon: 'none'
  })
}

// 下载视频
const downloadVideo = () => {
  uni.showLoading({
    title: '准备下载...'
  })
  
  uni.downloadFile({
    url: videoUrl.value,
    success: (res) => {
      uni.hideLoading()
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载完成',
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    },
    fail: () => {
      uni.hideLoading()
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    }
  })
}

// 分享视频
const shareVideo = () => {
  uni.showActionSheet({
    itemList: ['复制链接', '分享到微信', '分享到QQ'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          uni.setClipboardData({
            data: videoUrl.value,
            success: () => {
              uni.showToast({
                title: '链接已复制',
                icon: 'success'
              })
            }
          })
          break
        case 1:
        case 2:
          uni.showToast({
            title: '分享功能开发中',
            icon: 'none'
          })
          break
      }
    }
  })
}

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds || seconds <= 0) return '00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (!bytes || bytes <= 0) return '未知'
  
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`
}

// 页面卸载时清理
onUnload(() => {
  playing.value = false
})
</script>

<style scoped lang="scss">
.video-player-page {
  min-height: 100vh;
  background-color: #000;
  position: relative;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.nav-left, .nav-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  background: rgba(0, 0, 0, 0.3);
}

.nav-title {
  flex: 1;
  text-align: center;
  color: white;
  font-size: 16px;
  font-weight: 500;
  margin: 0 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  
  video {
    width: 100%;
    height: 100%;
    background-color: #000;
  }
}

.loading-overlay, .error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  z-index: 100;
}

.loading-text, .error-text {
  color: white;
  font-size: 14px;
  margin-top: 12px;
  margin-bottom: 16px;
}

.video-info {
  position: absolute;
  bottom: 120px;
  left: 0;
  right: 0;
  padding: 16px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
}

.video-title {
  color: white;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  line-height: 1.4;
}

.video-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.action-buttons {
  position: absolute;
  bottom: 40px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 0 16px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.5);
  min-width: 60px;
  
  text {
    color: white;
    font-size: 12px;
  }
  
  &:active {
    background: rgba(0, 0, 0, 0.7);
  }
}
</style>