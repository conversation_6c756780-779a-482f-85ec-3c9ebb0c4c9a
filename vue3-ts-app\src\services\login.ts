import type { LoginResult } from "@/types/member";
import { http } from "@/utils/http";

type LoginWxMinParams = {
  code: string;
  encryptedData?: string;
  iv?: string;
};
/**
 * 小程序登录
 * @param data 请求参数
 */
export const postLoginWxMinAPI = (data: LoginWxMinParams) => {
  return http<LoginResult>({
    method: "POST",
    url: "/login/wxMin",
    data,
  });
};

/**
 * 小程序登录_内测版
 * @param phoneNumber 模拟手机号码
 */
export const postLoginWxMinSimpleAPI = (phoneNumber: string) => {
  return http<LoginResult>({
    method: "POST",
    url: "/login/wxMin/simple",
    data: {
      phoneNumber,
    },
  });
};

type LoginParams = {
  account: string;
  password: string;
};
/**
 * 传统登录-用户名+密码
 * @param data 请求参数
 */
export const postLoginAPI = (data: LoginParams) => {
  return http<LoginResult>({
    method: "POST",
    url: "/login",
    data,
  });
};

/**
 * 测试用户类型定义
 */
export interface TestUser {
  id: number
  username: string
  nickname: string
  avatar: string
  mobile: string
  userRole: string
  isVerified: boolean
  verificationType: string
  houseNumber: string
  realName: string
  isAdmin: boolean
}

/**
 * 获取所有测试用户列表（用于模拟登录）
 */
export const getTestUsersAPI = () => {
  return http<TestUser[]>({
    method: 'GET',
    url: '/login/users',
  })
}

/**
 * 用户统计信息类型定义
 */
export interface UserStats {
  postCount: number
  favoriteCount: number
  pointCount: number
}

/**
 * 获取用户统计信息
 */
export const getUserStatsAPI = () => {
  return http<{success: boolean, data: UserStats}>({
    method: 'GET',
    url: '/user/stats',
  })
}
