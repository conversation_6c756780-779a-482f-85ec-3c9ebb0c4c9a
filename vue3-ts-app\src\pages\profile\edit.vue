<template>
  <view class="edit-profile-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <up-icon name="arrow-left" size="20" color="#fff"></up-icon>
        </view>
        <view class="navbar-title">编辑资料</view>
      </view>
    </view>

    <!-- 头像编辑 -->
    <view class="avatar-section">
      <view class="avatar-container">
        <up-avatar
          :src="formData.avatar"
          shape="circle"
          :size="100"
          @click="chooseAvatar"
          :customStyle="{ border: '3px solid #fff', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }"
        ></up-avatar>
        <view class="camera-icon" @click="chooseAvatar">
          <up-icon name="camera" size="20" color="#fff"></up-icon>
        </view>
      </view>
    </view>

    <!-- 基本信息编辑 -->
    <view class="form-section">
      <up-cell-group :border="false">
        <up-cell
          title="名字"
          :border="false"
        >
          <template #value>
            <up-input
              v-model="formData.nickname"
              placeholder="听香百草堂"
              border="none"
              :maxlength="20"
              textAlign="right"
            ></up-input>
          </template>
        </up-cell>

        <up-cell
          title="邻友ID"
          :border="false"
        >
          <template #value>
            <up-input
              v-model="formData.redBookId"
              placeholder="573020919"
              border="none"
              :maxlength="20"
              textAlign="right"
            ></up-input>
          </template>
        </up-cell>

        <up-cell
          title="背景图"
          :border="false"
          isLink
          @click="chooseBackground"
        >
          <template #value>
            <view class="background-preview">
              <image
                v-if="formData.backgroundImage"
                :src="formData.backgroundImage"
                class="background-thumb"
                mode="aspectFill"
              />
              <text v-else class="placeholder-text">选择背景图</text>
            </view>
          </template>
        </up-cell>

        <up-cell
          title="简介"
          :border="false"
        >
          <template #value>
            <up-textarea
              v-model="formData.bio"
              placeholder="百草为引，香醉入骨，古方今制，手捻天香。三分热情，七分冲动，十分折腾，还是放下！"
              :maxlength="100"
              :autoHeight="true"
              :showWordLimit="false"
              textAlign="right"
              :customStyle="{ textAlign: 'right', fontSize: '14px' }"
            ></up-textarea>
          </template>
        </up-cell>

        <up-cell
          title="性别"
          :value="getGenderText()"
          isLink
          @click="showGenderPicker"
          :border="false"
        ></up-cell>

        <up-cell
          title="生日"
          :value="formatBirthday(formData.birthday)"
          isLink
          @click="showDatePicker"
          :border="false"
        ></up-cell>

        <up-cell
          title="地区"
          :value="formData.region || '选择所在的地区'"
          isLink
          @click="showRegionPicker"
          :border="false"
        ></up-cell>

        <up-cell
          title="职业"
          :value="formData.profession || '选择职业'"
          isLink
          @click="showProfessionPicker"
          :border="false"
        ></up-cell>
      </up-cell-group>
    </view>

    <!-- 保存按钮 -->
    <view class="save-section">
      <up-button
        text="保存"
        type="primary"
        size="large"
        :loading="saving"
        @click="saveProfile"
      ></up-button>
    </view>

    <!-- 性别选择器 -->
    <up-picker
      ref="genderPicker"
      :show="showGender"
      :columns="genderColumns"
      @confirm="onGenderConfirm"
      @cancel="showGender = false"
      @close="showGender = false"
    ></up-picker>

    <!-- 地区选择器 -->
    <up-picker
      ref="regionPicker"
      :show="showRegion"
      :columns="regionColumns"
      @confirm="onRegionConfirm"
      @cancel="showRegion = false"
      @close="showRegion = false"
    ></up-picker>

    <!-- 职业选择器 -->
    <up-picker
      ref="professionPicker"
      :show="showProfession"
      :columns="professionColumns"
      @confirm="onProfessionConfirm"
      @cancel="showProfession = false"
      @close="showProfession = false"
    ></up-picker>

    <!-- 日期选择器 -->
    <up-datetime-picker
      ref="datePicker"
      :show="showDate"
      v-model="dateValue"
      mode="date"
      @confirm="onDateConfirm"
      @cancel="showDate = false"
      @close="showDate = false"
    ></up-datetime-picker>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useMemberStore } from '@/stores'
import { getCurrentUserAPI, updateProfileAPI, uploadFileUni } from '@/services/profile'

const memberStore = useMemberStore()

// 表单数据
const formData = reactive({
  avatar: '',
  nickname: '',
  mobile: '',
  redBookId: '',
  backgroundImage: '',
  gender: 0, // 0-未知, 1-男, 2-女
  birthday: '',
  bio: '',
  region: '',
  profession: '',
  school: ''
})

// 临时文件路径（用于保存选择的图片，等保存时再上传）
const tempFiles = reactive({
  avatarPath: '', // 头像临时路径
  backgroundPath: '' // 背景图临时路径
})

// 状态
const saving = ref(false)
const showGender = ref(false)
const showDate = ref(false)
const showRegion = ref(false)
const showProfession = ref(false)
const dateValue = ref(Number(new Date()))

// 性别选项
const genderColumns = ref([
  [
    { text: '保密', value: 0 },
    { text: '男', value: 1 },
    { text: '女', value: 2 }
  ]
])

// 地区选项（简化版，实际应该从API获取）
const regionColumns = ref([
  [
    { text: '北京市', value: '北京市' },
    { text: '上海市', value: '上海市' },
    { text: '广州市', value: '广州市' },
    { text: '深圳市', value: '深圳市' },
    { text: '杭州市', value: '杭州市' },
    { text: '成都市', value: '成都市' },
    { text: '重庆市', value: '重庆市' },
    { text: '西安市', value: '西安市' },
    { text: '武汉市', value: '武汉市' },
    { text: '南京市', value: '南京市' }
  ]
])

// 职业选项
const professionColumns = ref([
  [
    { text: '学生', value: '学生' },
    { text: '程序员', value: '程序员' },
    { text: '设计师', value: '设计师' },
    { text: '教师', value: '教师' },
    { text: '医生', value: '医生' },
    { text: '律师', value: '律师' },
    { text: '销售', value: '销售' },
    { text: '市场营销', value: '市场营销' },
    { text: '金融', value: '金融' },
    { text: '自由职业', value: '自由职业' },
    { text: '其他', value: '其他' }
  ]
])

// 初始化数据
const initData = async () => {
  try {
    // 从API获取最新的用户信息
    const res = await getCurrentUserAPI()
    if (res.success && res.data) {
      const user = res.data
      formData.avatar = (user as any).avatarUrl || user.avatar || ''
      formData.nickname = user.nickname || ''
      formData.mobile = user.mobile || ''
      formData.redBookId = (user as any).redBookId || ''
      formData.backgroundImage = (user as any).backgroundImage || ''
      formData.gender = user.gender || 0
      formData.birthday = user.birthday ? new Date(user.birthday).toISOString().split('T')[0] : ''
      formData.bio = (user as any).bio || ''
      formData.region = (user as any).region || ''
      formData.profession = (user as any).profession || ''
      formData.school = (user as any).school || ''
    } else {
      // 如果API调用失败，使用本地存储的数据作为备用
      const profile = memberStore.profile
      if (profile) {
        formData.avatar = profile.avatar || ''
        formData.nickname = profile.nickname || ''
        formData.mobile = profile.mobile || ''
        formData.gender = profile.gender || 0
        formData.birthday = profile.birthday || ''
        formData.bio = profile.bio || ''
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    })
  }
}

// 获取性别文本
const getGenderText = () => {
  const genderMap = { 0: '保密', 1: '男', 2: '女' }
  return genderMap[formData.gender as keyof typeof genderMap] || '保密'
}

// 格式化生日显示
const formatBirthday = (birthday: string) => {
  if (!birthday) return '请选择生日'
  return birthday
}

// 选择头像
const chooseAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const filePath = res.tempFilePaths[0]
      // 保存临时路径，用于预览
      tempFiles.avatarPath = filePath
      formData.avatar = filePath // 用于预览显示

      uni.showToast({
        title: '头像已选择',
        icon: 'success'
      })
    }
  })
}

// 选择背景图
const chooseBackground = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const filePath = res.tempFilePaths[0]
      // 保存临时路径，用于预览
      tempFiles.backgroundPath = filePath
      formData.backgroundImage = filePath // 用于预览显示

      uni.showToast({
        title: '背景图已选择',
        icon: 'success'
      })
    }
  })
}

// 显示性别选择器
const showGenderPicker = () => {
  showGender.value = true
}

// 性别选择确认
const onGenderConfirm = (e: any) => {
  const { value } = e
  if (value && value.length > 0) {
    formData.gender = value[0].value
  }
  showGender.value = false
}

// 显示地区选择器
const showRegionPicker = () => {
  showRegion.value = true
}

// 地区选择确认
const onRegionConfirm = (e: any) => {
  const { value } = e
  if (value && value.length > 0) {
    formData.region = value[0].value
  }
  showRegion.value = false
}

// 显示职业选择器
const showProfessionPicker = () => {
  showProfession.value = true
}

// 职业选择确认
const onProfessionConfirm = (e: any) => {
  const { value } = e
  if (value && value.length > 0) {
    formData.profession = value[0].value
  }
  showProfession.value = false
}

// 显示日期选择器
const showDatePicker = () => {
  if (formData.birthday) {
    dateValue.value = new Date(formData.birthday).getTime()
  }
  showDate.value = true
}

// 日期选择确认
const onDateConfirm = () => {
  const date = new Date(dateValue.value)
  formData.birthday = date.toISOString().split('T')[0]
  showDate.value = false
}

// 保存资料
const saveProfile = async () => {
  if (!formData.nickname.trim()) {
    uni.showToast({
      title: '请输入昵称',
      icon: 'none'
    })
    return
  }

  saving.value = true

  try {
    uni.showLoading({
      title: '保存中...'
    })

    // 1. 先上传图片文件
    let avatarUrl = formData.avatar
    let backgroundUrl = formData.backgroundImage

    // 上传头像（如果有新选择的）
    if (tempFiles.avatarPath) {
      try {
        const avatarResult = await uploadFileUni(tempFiles.avatarPath, 'avatar')
        avatarUrl = avatarResult.url
        console.log('头像上传成功:', avatarUrl)
      } catch (error) {
        console.error('头像上传失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: '头像上传失败',
          icon: 'none'
        })
        return
      }
    }

    // 上传背景图（如果有新选择的）
    if (tempFiles.backgroundPath) {
      try {
        const backgroundResult = await uploadFileUni(tempFiles.backgroundPath, 'background')
        backgroundUrl = backgroundResult.url
        console.log('背景图上传成功:', backgroundUrl)
      } catch (error) {
        console.error('背景图上传失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: '背景图上传失败',
          icon: 'none'
        })
        return
      }
    }

    // 2. 准备更新数据
    const updateData = {
      nickname: formData.nickname,
      gender: formData.gender,
      birthday: formData.birthday,
      bio: formData.bio,
      region: formData.region,
      profession: formData.profession,
      school: formData.school,
      redBookId: formData.redBookId,
      backgroundImage: backgroundUrl
    }

    // 3. 调用更新接口
    const result = await updateProfileAPI(updateData)

    if (result.success) {
      // 更新本地存储的用户信息
      const updatedProfile = {
        ...memberStore.profile,
        avatar: avatarUrl,
        nickname: formData.nickname,
        mobile: formData.mobile,
        gender: formData.gender,
        birthday: formData.birthday,
        bio: formData.bio
      }
      memberStore.setProfile(updatedProfile as any)

      // 清空临时文件路径
      tempFiles.avatarPath = ''
      tempFiles.backgroundPath = ''

      uni.hideLoading()
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      throw new Error(result.message || '保存失败')
    }

  } catch (error) {
    uni.hideLoading()
    console.error('保存失败:', error)
    uni.showToast({
      title: error instanceof Error ? error.message : '保存失败',
      icon: 'none'
    })
  } finally {
    saving.value = false
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

onMounted(() => {
  initData()
})
</script>

<style scoped lang="scss">
.edit-profile-page {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.custom-navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-top: var(--status-bar-height);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.navbar-left,
.navbar-right {
  width: 60px;
  display: flex;
  align-items: center;
}

.navbar-left {
  justify-content: flex-start;
}

.navbar-right {
  justify-content: flex-end;
}

.navbar-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.avatar-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
  margin-top: calc(44px + var(--status-bar-height));
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.avatar-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.camera-icon {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  background-color: #007aff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid #fff;
}

.form-section {
  background-color: white;
  margin: 10px 0;
  border-radius: 12px;
  overflow: hidden;
}

.background-preview {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.background-thumb {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.placeholder-text {
  color: #999;
  font-size: 14px;
}

.save-section {
  margin: 20px;
  display: flex;
  justify-content: center;
  align-items: center;

  :deep(.up-button) {
    width: 100%;
    max-width: 300px;
    height: 50px;
    border-radius: 25px;

    .up-button__content {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }

    .up-button__text {
      font-size: 16px;
      font-weight: 600;
    }
  }
}

</style>
