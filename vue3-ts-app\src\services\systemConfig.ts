import { http } from '@/utils/http'
import type { SystemConfig } from '@/types/admin'
import type { Result } from '@/types/ApiResponse'

// 系统配置分页查询参数
export interface SystemConfigPageParams {
  page: number
  pageSize: number
  groupName?: string
  keyword?: string
}

// 系统配置分页响应
export interface SystemConfigPageResponse {
  records: SystemConfig[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 分页查询系统配置
 */
export const getSystemConfigPageAPI = (params: SystemConfigPageParams) => {
  return http<Result<SystemConfigPageResponse>>({
    method: 'GET',
    url: '/system-config/page',
    data: params
  })
}

/**
 * 根据分组获取配置列表
 */
export const getConfigsByGroupAPI = (groupName: string) => {
  return http<Result<SystemConfig[]>>({
    method: 'GET',
    url: `/system-config/group/${groupName}`
  })
}

/**
 * 根据配置键获取配置值
 */
export const getConfigValueAPI = (configKey: string) => {
  return http<Result<string>>({
    method: 'GET',
    url: `/system-config/value/${configKey}`
  })
}

/**
 * 获取所有配置分组
 */
export const getAllGroupsAPI = () => {
  return http<Result<string[]>>({
    method: 'GET',
    url: '/system-config/groups'
  })
}

/**
 * 获取配置的键值对映射
 */
export const getConfigMapAPI = (groupName?: string) => {
  return http<Result<Record<string, string>>>({
    method: 'GET',
    url: '/system-config/map',
    data: groupName ? { groupName } : undefined
  })
}

/**
 * 添加新配置
 */
export const addSystemConfigAPI = (config: Omit<SystemConfig, 'id'>) => {
  return http<Result<boolean>>({
    method: 'POST',
    url: '/system-config',
    data: config
  })
}

/**
 * 更新配置
 */
export const updateSystemConfigAPI = (id: number, config: Partial<SystemConfig>) => {
  return http<Result<boolean>>({
    method: 'PUT',
    url: `/system-config/${id}`,
    data: config
  })
}

/**
 * 批量更新配置
 */
export const batchUpdateConfigsAPI = (configs: SystemConfig[]) => {
  return http<Result<boolean>>({
    method: 'PUT',
    url: '/system-config/batch',
    data: configs
  })
}

/**
 * 删除配置
 */
export const deleteSystemConfigAPI = (id: number) => {
  return http<Result<boolean>>({
    method: 'DELETE',
    url: `/system-config/${id}`
  })
}

// 配置类型选项
export const CONFIG_TYPE_OPTIONS = [
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '布尔值', value: 'boolean' },
  { label: 'JSON', value: 'json' }
]

// 常用配置分组
export const CONFIG_GROUPS = [
  { label: '基础配置', value: 'basic' },
  { label: '内容管理', value: 'content' },
  { label: '文件上传', value: 'upload' },
  { label: '邮件配置', value: 'email' },
  { label: '短信配置', value: 'sms' },
  { label: '支付配置', value: 'payment' },
  { label: '其他配置', value: 'other' }
]
