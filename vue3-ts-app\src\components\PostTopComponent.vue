<template>
  <view class="post-top-component">
    <!-- 置顶按钮 -->
    <up-button
      v-if="!isTopped"
      text="置顶"
      type="warning"
      size="mini"
      :loading="loading"
      @click="handleTopClick"
    >
      <template #icon>
        <up-icon name="arrow-up-circle" size="14"></up-icon>
      </template>
    </up-button>

    <!-- 取消置顶按钮 -->
    <up-button
      v-else
      text="取消置顶"
      type="info"
      size="mini"
      :loading="loading"
      @click="handleCancelTop"
    >
      <template #icon>
        <up-icon name="arrow-down-circle" size="14"></up-icon>
      </template>
    </up-button>

    <!-- 置顶确认弹窗 -->
    <up-popup
      v-model:show="showTopModal"
      mode="center"
      :round="10"
      :closeable="true"
    >
      <view class="top-modal">
        <view class="modal-header">
          <text class="modal-title">帖子置顶</text>
        </view>
        
        <view class="modal-content">
          <view class="info-section">
            <text class="info-text">置顶后您的帖子将在列表中优先显示，获得更多曝光机会。</text>
          </view>
          
          <view class="form-section">
            <view class="form-item">
              <text class="form-label">置顶时长</text>
              <view class="duration-options">
                <view 
                  v-for="option in durationOptions" 
                  :key="option.hours"
                  class="duration-option"
                  :class="{ active: selectedHours === option.hours }"
                  @click="selectDuration(option.hours)"
                >
                  <text class="duration-text">{{ option.label }}</text>
                  <text class="duration-cost">{{ option.cost }}积分</text>
                </view>
              </view>
            </view>
            
            <view class="custom-duration" v-if="showCustomDuration">
              <text class="form-label">自定义时长（小时）</text>
              <up-input
                v-model="customHours"
                type="number"
                placeholder="请输入小时数"
                :min="config?.minHours"
                :max="config?.maxHours"
                @input="onCustomHoursChange"
              ></up-input>
            </view>
          </view>
          
          <view class="cost-section">
            <view class="cost-info">
              <text class="cost-label">所需积分：</text>
              <text class="cost-value">{{ totalCost }}积分</text>
            </view>
            <view class="balance-info">
              <text class="balance-label">我的积分：</text>
              <text class="balance-value" :class="{ insufficient: !canAfford }">{{ userPoints }}积分</text>
            </view>
            <view v-if="!canAfford" class="insufficient-tip">
              <text class="tip-text">积分不足，无法置顶</text>
            </view>
          </view>
        </view>
        
        <view class="modal-actions">
          <up-button
            text="取消"
            type="info"
            size="normal"
            :customStyle="{ marginRight: '12px', flex: 1 }"
            @click="closeTopModal"
          ></up-button>
          <up-button
            text="确认置顶"
            type="primary"
            size="normal"
            :loading="topping"
            :disabled="!canAfford || selectedHours <= 0"
            :customStyle="{ flex: 1 }"
            @click="confirmTop"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMemberStore } from '@/stores'
import { getTopConfigAPI, checkCanTopAPI, topPostAPI, cancelTopAPI, getTopCostAPI } from '@/services/postTopService'
import type { TopConfig } from '@/types/PostTop'

interface Props {
  postId: number
  isTopped?: boolean
  onTopSuccess?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  isTopped: false
})

const emit = defineEmits<{
  topSuccess: []
  cancelSuccess: []
}>()

const memberStore = useMemberStore()

// 状态
const loading = ref(false)
const topping = ref(false)
const showTopModal = ref(false)
const showCustomDuration = ref(false)

// 配置和数据
const config = ref<TopConfig | null>(null)
const userPoints = ref(0)
const selectedHours = ref(24)
const customHours = ref('')
const totalCost = ref(0)

// 时长选项
const durationOptions = computed(() => {
  if (!config.value) return []
  
  const options = [
    { hours: 1, label: '1小时', cost: config.value.costPerHour * 1 },
    { hours: 6, label: '6小时', cost: config.value.costPerHour * 6 },
    { hours: 24, label: '1天', cost: config.value.costPerHour * 24 },
    { hours: 72, label: '3天', cost: config.value.costPerHour * 72 },
    { hours: 168, label: '7天', cost: config.value.costPerHour * 168 }
  ]
  
  // 过滤超出最大时长的选项
  return options.filter(option => option.hours <= config.value!.maxHours)
})

// 是否可以承担费用
const canAfford = computed(() => {
  return userPoints.value >= totalCost.value
})

// 获取置顶配置
const fetchTopConfig = async () => {
  try {
    const res = await getTopConfigAPI()
    if (res.success) {
      config.value = res.data
      selectedHours.value = res.data.defaultHours
      calculateCost()
    }
  } catch (error) {
    console.error('获取置顶配置失败:', error)
  }
}

// 计算费用
const calculateCost = async () => {
  if (!config.value || selectedHours.value <= 0) {
    totalCost.value = 0
    return
  }
  
  try {
    const res = await getTopCostAPI(selectedHours.value)
    if (res.success) {
      totalCost.value = res.data.cost
      userPoints.value = res.data.userPoints
    }
  } catch (error) {
    console.error('计算置顶费用失败:', error)
    totalCost.value = config.value.costPerHour * selectedHours.value
  }
}

// 选择时长
const selectDuration = (hours: number) => {
  selectedHours.value = hours
  showCustomDuration.value = false
  customHours.value = ''
  calculateCost()
}

// 自定义时长变化
const onCustomHoursChange = (value: string) => {
  const hours = parseInt(value)
  if (!isNaN(hours) && hours > 0) {
    selectedHours.value = hours
    calculateCost()
  }
}

// 处理置顶点击
const handleTopClick = async () => {
  if (!memberStore.profile?.id) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  loading.value = true
  
  try {
    // 检查是否可以置顶
    const checkRes = await checkCanTopAPI(props.postId)
    if (!checkRes.success) {
      uni.showToast({
        title: checkRes.message || '无法置顶',
        icon: 'none'
      })
      return
    }
    
    if (!checkRes.data.canTop) {
      uni.showToast({
        title: checkRes.data.reason,
        icon: 'none'
      })
      return
    }
    
    // 显示置顶弹窗
    showTopModal.value = true
    
  } catch (error) {
    console.error('检查置顶权限失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 确认置顶
const confirmTop = async () => {
  if (!canAfford.value || selectedHours.value <= 0) {
    return
  }
  
  topping.value = true
  
  try {
    const res = await topPostAPI(props.postId, selectedHours.value)
    
    if (res.success) {
      uni.showToast({
        title: '置顶成功',
        icon: 'success'
      })
      
      closeTopModal()
      emit('topSuccess')
      props.onTopSuccess?.()
    } else {
      uni.showToast({
        title: res.message || '置顶失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('置顶失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    topping.value = false
  }
}

// 取消置顶
const handleCancelTop = async () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消置顶吗？',
    success: async (res) => {
      if (res.confirm) {
        loading.value = true
        
        try {
          const response = await cancelTopAPI(props.postId)
          
          if (response.success) {
            uni.showToast({
              title: '取消置顶成功',
              icon: 'success'
            })
            
            emit('cancelSuccess')
          } else {
            uni.showToast({
              title: response.message || '取消失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('取消置顶失败:', error)
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          })
        } finally {
          loading.value = false
        }
      }
    }
  })
}

// 关闭置顶弹窗
const closeTopModal = () => {
  showTopModal.value = false
  showCustomDuration.value = false
  customHours.value = ''
}

onMounted(() => {
  fetchTopConfig()
})
</script>

<style scoped lang="scss">
.post-top-component {
  display: inline-block;
}

.top-modal {
  width: 340px;
  background: white;
  border-radius: 10px;
  overflow: hidden;
}

.modal-header {
  padding: 20px 20px 0;
  text-align: center;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.modal-content {
  padding: 20px;
}

.info-section {
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.info-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.form-section {
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.duration-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.duration-option {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  
  &.active {
    border-color: #5677fc;
    background-color: #f0f4ff;
  }
}

.duration-text {
  font-size: 14px;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.duration-cost {
  font-size: 12px;
  color: #ff6b35;
  font-weight: 500;
}

.custom-duration {
  margin-top: 12px;
}

.cost-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.cost-info,
.balance-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.cost-label,
.balance-label {
  font-size: 14px;
  color: #666;
}

.cost-value {
  font-size: 16px;
  color: #ff6b35;
  font-weight: 600;
}

.balance-value {
  font-size: 14px;
  color: #333;
  
  &.insufficient {
    color: #ff3b30;
  }
}

.insufficient-tip {
  margin-top: 8px;
  text-align: center;
}

.tip-text {
  font-size: 12px;
  color: #ff3b30;
}

.modal-actions {
  display: flex;
  padding: 0 20px 20px;
  gap: 12px;
}
</style>
