import { http } from '@/utils/http'
import type { User, UserRole } from '@/types/user'
import type { PageResult, BaseSearchParams } from '@/types'
import type { Result } from '@/types/ApiResponse'

/**
 * 用户管理搜索参数
 */
export interface UserSearchParams extends BaseSearchParams {
  userRole?: string
  status?: number
  isVerified?: boolean
}

/**
 * 用户统计数据
 */
export interface UserStatsData {
  total: number
  verified: number
  unverified: number
  banned: number
}

/**
 * 管理员统计数据
 */
export interface AdminStatsData {
  totalUsers: number
  totalPosts: number
  pendingReviews: number
  todayActive: number
  pendingAuth: number
  feedbackCount: number
}

/**
 * 获取用户列表（管理员）
 */
export const getUserListAPI = (params: UserSearchParams) => {
  // 构建查询字符串
  const queryParams = new URLSearchParams()
  if (params.page) queryParams.append('page', params.page.toString())
  if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
  if (params.keyword) queryParams.append('keyword', params.keyword)
  if (params.userRole) queryParams.append('userRole', params.userRole)
  if (params.status !== undefined) queryParams.append('status', params.status.toString())
  if (params.isVerified !== undefined) queryParams.append('isVerified', params.isVerified.toString())

  const queryString = queryParams.toString()
  const url = queryString ? `/user/admin/list?${queryString}` : '/user/admin/list'

  return http<Result<PageResult<User>>>({
    method: 'GET',
    url: url
  })
}

/**
 * 获取用户统计数据（管理员）
 */
export const getUserStatsAPI = () => {
  return http<Result<UserStatsData>>({
    method: 'GET',
    url: '/user/admin/stats'
  })
}

/**
 * 获取管理员统计数据
 */
export const getAdminStatsAPI = () => {
  return http<Result<AdminStatsData>>({
    method: 'GET',
    url: '/admin/stats'
  })
}

/**
 * 获取用户详情（管理员）
 */
export const getUserDetailAPI = (userId: number) => {
  return http<Result<User>>({
    method: 'GET',
    url: `/user/admin/${userId}`
  })
}

/**
 * 更新用户状态（管理员）
 */
export const updateUserStatusAPI = (userId: number, status: number) => {
  return http<Result<string>>({
    method: 'PUT',
    url: `/user/admin/${userId}/status`,
    data: { status }
  })
}

/**
 * 更新用户角色（管理员）
 */
export const updateUserRoleAPI = (userId: number, userRole: string) => {
  return http<Result<string>>({
    method: 'PUT',
    url: `/user/admin/${userId}/role`,
    data: { userRole }
  })
}

/**
 * 删除用户（管理员）
 */
export const deleteUserAPI = (userId: number) => {
  return http<Result<string>>({
    method: 'DELETE',
    url: `/user/admin/${userId}`
  })
}

/**
 * 获取所有角色列表
 */
export const getRoleListAPI = () => {
  return http<Result<UserRole[]>>({
    method: 'GET',
    url: '/roles/list'
  })
}

/**
 * 根据角色代码获取角色信息
 */
export const getRoleByCodeAPI = (roleCode: string) => {
  return http<Result<UserRole>>({
    method: 'GET',
    url: `/roles/${roleCode}`
  })
}

/**
 * 检查用户权限
 */
export const checkPermissionAPI = (userRole: string, permission: string) => {
  return http<Result<boolean>>({
    method: 'GET',
    url: `/roles/check-permission?userRole=${encodeURIComponent(userRole)}&permission=${encodeURIComponent(permission)}`
  })
}

/**
 * 获取用户权限列表
 */
export const getUserPermissionsAPI = (userRole: string) => {
  return http<Result<string[]>>({
    method: 'GET',
    url: `/roles/permissions/${userRole}`
  })
}
