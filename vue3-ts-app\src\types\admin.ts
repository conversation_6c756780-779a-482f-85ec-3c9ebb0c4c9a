// 管理员相关类型定义

// 认证申请状态
export type AuthStatus = 'pending' | 'approved' | 'rejected';

// 身份类型
export type IdentityType = 'owner' | 'family';

// 用户基本信息
export interface AuthUser {
  id: number;
  nickname: string;
  avatar: string;
}

// 认证申请数据
export interface AuthApplication {
  id: number;
  name: string;
  phone: string;
  identity: IdentityType;
  houseNumber?: string;
  documents: string[];
  remark?: string;
  status: AuthStatus;
  createdTime: string;
  reviewTime?: string;
  rejectReason?: string;
  user: AuthUser;
}

// 反馈状态
export type FeedbackStatus = 'pending' | 'accepted' | 'rejected' | 'processed';

// 反馈数据
export interface FeedbackItem {
  id: number;
  content: string;
  status: FeedbackStatus;
  createdTime: string;
  processTime?: string;
  processNote?: string;
  user: AuthUser;
}

// 处理类型
export type ProcessType = 'accept' | 'reject';

// 用户管理数据
export interface UserManagement {
  id: number;
  username?: string;
  nickname: string;
  avatar: string;
  mobile?: string;
  email?: string;
  gender?: number;
  isVerified?: boolean;
  authType?: number;
  isAdmin?: boolean;
  status: string | number;
  postCount?: number;
  points?: number;
  createdTime: string;
  lastLoginTime?: string;
}

// 帖子管理数据
export interface PostManagement {
  id: number;
  userId: number;
  title?: string;
  content: string;
  status: string | number;
  createdTime: string;
  updatedTime?: string;
  user: AuthUser;
  categoryId?: number;
  categoryName?: string;
  category?: string | number;
  viewCount?: number;
  likeCount: number;
  commentCount: number;
  collectCount?: number;
  images?: string[];
  fileList?: string;
  rejectReason?: string;
  reviewTime?: string;
  reviewUserId?: number;
  reviewNote?: string;
  isTop?: boolean;
  topExpireTime?: string;
}

// 操作选项
export interface ActionOption {
  name: string;
  value: string;
  color?: string;
}

// 管理后台产品数据
export interface AdminProduct {
  id: number;
  name: string;
  description?: string;
  price?: number;
  points?: number;
  stock?: number;
  status: string | number;
  image?: string;
  createdTime: string;
  updatedTime?: string;
}

// 分享配置数据
export interface ShareConfig {
  id: number;
  title: string;
  description: string;
  imageUrl?: string;
  url: string;
  status: number;
  createdTime: string;
}

// 导入加载状态类型
import type { LoadStatus } from './ui';

// Tab类型
export type TabType = string;

// 管理员统计数据
export interface AdminStats {
  totalUsers: number;
  totalPosts: number;
  totalComments: number;
  pendingReviews: number;
  todayNewUsers: number;
  todayNewPosts: number;
}

// 审核操作
export interface ReviewAction {
  id: number;
  status: number; // 1-通过 2-拒绝
  note?: string;
}

// 批量操作
export interface BatchAction {
  ids: number[];
  action: string; // approve/reject/delete/enable/disable
  note?: string;
}

// 搜索参数
export interface AdminSearchParams {
  page: number;
  pageSize: number;
  keyword?: string;
  status?: number;
  startTime?: string;
  endTime?: string;
  userRole?: string;
  identityType?: string;
}

// 系统配置
export interface SystemConfig {
  id: number;
  configKey: string;
  configValue: string;
  configType: string; // string/number/boolean/json
  description?: string;
  groupName: string;
  sortOrder: number;
  isSystem: boolean;
}
