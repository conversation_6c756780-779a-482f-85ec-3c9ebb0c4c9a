package com.haolinkyou.mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haolinkyou.entity.Users;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UserMapper extends BaseMapper<Users> {
    List<Users> selectByName(String name);

    IPage<Users> selectByNamePage(IPage<Users> page, String name);
    int delByUserId(@Param("userId") Long userId);

    /**
     * 获取用户发布的动态数量
     * @param userId 用户ID
     * @return 动态数量
     */
    int getUserPostCount(@Param("userId") Long userId);

    /**
     * 获取用户收藏的帖子数量
     * @param userId 用户ID
     * @return 收藏数量
     */
    int getUserCollectCount(@Param("userId") Long userId);
}
